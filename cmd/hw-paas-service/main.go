package main

import (
	"flag"
	"fmt"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/spf13/cast"
	"hw-paas-service/internal/pkg/dayu_trace"
	logger2 "hw-paas-service/internal/pkg/logger"
	"hw-paas-service/pkg/zlog"
	"os"
	"strings"

	"github.com/nacos-group/nacos-sdk-go/clients/config_client"

	nacosConfig "github.com/go-kratos/kratos/contrib/config/nacos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/nacos-group/nacos-sdk-go/clients"
	"github.com/nacos-group/nacos-sdk-go/common/constant"
	"github.com/nacos-group/nacos-sdk-go/vo"
	"hw-paas-service/internal/conf"
	_ "net/http/pprof"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = "hw-paas-service"
	// Version is the version of the compiled software.
	Version = "v0.0.1"
	// App Env
	env string
	// flagconf is the config flag.
	flagconf string

	nacosName        string
	nacosPassword    string
	nacos            bool
	nacosLogDir      string
	nacosCacheDir    string
	nacosServer      string
	nacosPort        uint64
	nacosNamespaceId string
	nacosGroupId     = "DEFAULT_GROUP"
	dataIds          = []string{
		"hw-paas-service.yaml",
	}
	id, _ = os.Hostname()
)

// CMD ["/app/hw-paas-service-api", "-nacos=true", "-nacosServer=**********", "-nacosPort=8848", "-nacosNamespaceId=public", "-nacosCacheDir=/home/<USER>/xeslog/", "-nacosLogDir=/home/<USER>/xeslog/"]
// http://**************:8848/nacos/
func init() {
	flag.StringVar(&flagconf, "conf", "./configs", "config path, eg: -conf=config.yaml")
	flag.BoolVar(&nacos, "nacos", false, "use nacos, eg: -nacos=true")
	flag.StringVar(&env, "env", "dev", "use env, eg: -env=dev")
	flag.StringVar(&nacosServer, "nacosServer", "**************:8848,**************:8848,**************:8848", "nacos host, eg: -nacosServer=127.0.0.1")
	//flag.Uint64Var(&nacosPort, "nacosPort", 8848, "nacos port, eg: -nacosPort 8488")
	flag.StringVar(&nacosNamespaceId, "nacosNamespaceId", "public", "nacos namespaceId, eg: -nacosNamespaceId=id")
	flag.StringVar(&nacosName, "nacosName", "nacos", "nacos nacosName, eg: -nacosName=nacos")
	flag.StringVar(&nacosPassword, "nacosPassword", "J3dEGf5SCT", "nacos nacosPassword, eg: -nacosPassword=***")
	flag.StringVar(&nacosCacheDir, "nacosCacheDir", "./logs", "nacos cacheDir, eg: -nacosCacheDir=./logs")
	flag.StringVar(&nacosLogDir, "nacosLogDir", "./logs", "nacos logDir, eg: -nacoslogDir=./logs")
	flag.Parse()
}

func main() {
	var c config.Config
	if nacos {
		nacosClient := NewConfigClient(nacosServer, nacosNamespaceId, nacosLogDir, nacosCacheDir, nacosName, nacosPassword)
		configSources := make([]config.Source, 0)
		for _, dataId := range dataIds {
			configSources = append(configSources, nacosConfig.NewConfigSource(nacosClient, nacosConfig.WithGroup(nacosGroupId), nacosConfig.WithDataID(dataId)))
		}
		c = config.New(
			config.WithSource(configSources...),
		)
	} else {
		c = config.New(
			config.WithSource(
				file.NewSource(flagconf),
			),
		)
	}
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}

	zlog.Init(Name, bc.Log.Filename, int(bc.Log.MaxSize), int(bc.Log.MaxBackup), int(bc.Log.MaxAge), bc.Log.Compress)
	defer zlog.Sync()
	logger := log.With(zlog.NewZapLogger(zlog.STDInstance()),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
		"dayu_trace_id", dayu_trace.TraceID(),
	)
	logger2.NewLogger(logger)

	c.Watch("biz", func(key string, value config.Value) {
		value.Scan(&bc.Biz)
		fmt.Printf("biz changed: %+v", bc.Biz)
	})
	app, cleanup, err := initApp(bc.Server, bc.Sign, bc.Error, bc.Biz, bc.Nacos, bc.Data, bc.Services, logger, bc.Auth)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}

func NewConfigClient(addresses string, namespaceId string, logDir string, cacheDir string, nacosName string, paasword string) config_client.IConfigClient {
	sc := []constant.ServerConfig{}
	addressList := strings.Split(addresses, ",")
	for _, addr := range addressList {
		addrPorts := strings.Split(addr, ":")
		if len(addrPorts) != 2 {
			panic("nacos server address error")
		}
		sc = append(sc, *constant.NewServerConfig(addrPorts[0], cast.ToUint64(addrPorts[1])))
	}

	cc := &constant.ClientConfig{
		NamespaceId:         namespaceId, //namespace id
		TimeoutMs:           5000,
		NotLoadCacheAtStart: true,
		LogDir:              logDir,
		CacheDir:            cacheDir,
		LogLevel:            "debug",
		Username:            nacosName,
		Password:            paasword,
	}
	client, err := clients.NewConfigClient(
		vo.NacosClientParam{
			ClientConfig:  cc,
			ServerConfigs: sc,
		},
	)
	if err != nil {
		panic(err)
	}
	return client
}

func newApp(logger log.Logger, hs *http.Server) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			hs,
		),
	)
}
