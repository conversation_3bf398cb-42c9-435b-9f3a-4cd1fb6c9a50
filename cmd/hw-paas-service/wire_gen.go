// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"hw-paas-service/internal/biz"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/internal/server"
	"hw-paas-service/internal/service"
	"hw-paas-service/internal/service/evaluate"
)

import (
	_ "net/http/pprof"
)

// Injectors from wire.go:

// initApp init kratos application.
func initApp(confServer *conf.Server, arg map[string]*conf.SignConf, errorHandle *conf.ErrorHandle, confBiz *conf.Biz, confNacos *conf.Nacos, confData *conf.Data, confServices *conf.Services, logger log.Logger, auth *conf.Auth) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	feedTraceDao := dao.NewFeedTraceDao(dataData, logger)
	servicesServices := services.NewServices(confServices, logger)
	correctUseCase := biz.NewCorrectUseCase(confBiz, logger, feedTraceDao, servicesServices)
	aiJzxQuestionDao := dao.NewAiJzxQuestionDao(dataData, logger)
	jzxQuestionUseCase := biz.NewJzxQuestionUseCase(aiJzxQuestionDao, servicesServices, confBiz, logger)
	eduDao := dao.NewEduDao(dataData, logger)
	eduUseCase := biz.NewEduUseCase(eduDao, servicesServices, confBiz, logger)
	ucenterRepo := data.NewUcenterRepo(confServices, dataData, logger)
	accountDao := dao.NewAccountDao(dataData, logger)
	codeLoginUseCase := biz.NewCodeLoginUseCase(ucenterRepo, accountDao, logger)
	aiService := service.NewAiService(correctUseCase, jzxQuestionUseCase, eduUseCase, logger, codeLoginUseCase)
	es := dao.NewEsRepo(confData, logger)
	fingerWordsDao := dao.NewFingerWordsDao(dataData, logger)
	fingerWordsUseCase := biz.NewFingerWordsUseCase(es, fingerWordsDao, servicesServices, confBiz, logger)
	skillUsecase := biz.NewSkillUsecase(logger, servicesServices)
	fingerWordsService := service.NewFingerWordsService(fingerWordsUseCase, skillUsecase, logger)
	skillService := service.NewSkillService(skillUsecase)
	queryWordsUseCase := biz.NewQueryWordsUseCase(es, confBiz, logger)
	queryWordsService := service.NewQueryWordsService(queryWordsUseCase)
	evalService := evaluate.NewEvaluateService(confBiz, servicesServices)
	readingBookUseCase := biz.NewReadingBookUseCase(confBiz, servicesServices, logger)
	readingBookService := service.NewReadingBookService(readingBookUseCase, logger)
	eduResourceDao := dao.NewEduResourceDao(dataData, logger, confBiz)
	resourceUseCase := biz.NewResourceUseCase(eduResourceDao, eduDao, logger)
	resourceService := service.NewResourceService(logger, resourceUseCase)
	adminRepo := dao.NewAdminRepo(dataData, logger)
	adminUseCase := biz.NewAdminUseCase(confBiz, logger, eduDao, adminRepo)
	adminService := service.NewAdminService(adminUseCase)
	commentRepo := dao.NewCommentRepo(dataData, logger)
	commentUseCase := biz.NewCommentUseCase(logger, commentRepo)
	commentService := service.NewCommentService(commentUseCase, logger)
	httpServer := server.NewHTTPServer(confServer, aiService, fingerWordsService, skillService, queryWordsService, evalService, readingBookService, errorHandle, logger, auth, ucenterRepo, resourceService, adminService, commentService)
	app := newApp(logger, httpServer)
	return app, func() {
		cleanup()
	}, nil
}
