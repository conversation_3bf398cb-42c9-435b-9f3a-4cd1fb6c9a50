//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"hw-paas-service/internal/biz"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/internal/server"
	"hw-paas-service/internal/service"
)

// initApp init kratos application.
func initApp(*conf.Server, map[string]*conf.SignConf, *conf.ErrorHandle, *conf.Biz, *conf.Nacos, *conf.Data, *conf.Services, log.Logger, *conf.Auth) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, dao.ProviderSet, biz.ProviderSet, service.ProviderSet, services.ProviderSet, newApp))
}
