server:
  http:
    addr: 0.0.0.0:8002
    timeout: 100s
  grpc:
    addr: 0.0.0.0:9002
    timeout: 1s
data:
  database:
    driver: mysql
    source: ai_tools_rw:jJ9yO_uD7lS4dK0q@tcp(sea-pad-mysql-test.mysql.database.chinacloudapi.cn:3306)/ai_tools?timeout=1s&readTimeout=1s&writeTimeout=1s&parseTime=true&loc=Local&charset=utf8mb4,utf8
  es:
    host: "http://**********:9200"
    username: "superuser"
    password: "d737da0188348DD1d^hkda"
  redis:
    network: tcp
    addr: 127.0.0.1:6379
    db: 0
    dial_timeout: 2s
    read_timeout: 2s
    write_timeout: 2s
    pool_size: 20

auth:
  protected_urls:
    - "/api.ai.v1.Ai/demo01"

log:
  filename: "./log/test.log"
  max_size: 100
  max_backup: 5
  max_age: 10
  compress: true

sign:
  "200081": #海外学习机
    sign_key: "da3f384bda76419e8da3baf071eae4e2"

services:
  # 指查ocr
  hybrid_ocr:
    host: "http://**********:8089/det"
    timeout: "5s"
  # 英文纠错
  en_correct:
    host: "http://***********:80"
    timeout: "5s"
  #rn版本控制
  rn_version_control:
    host: "https://overseas-background.chengjiukehu.com/rn-version-control-be"
    timeout: "5s"
  #base64图片上传,测试使用
  base64_upload:
    host: "https://hmi.chengjiukehu.com"
    timeout: "5s"
  #海外内容审核
  safety:
    host: "https://overseas-pad.chengjiukehu.com"
    timeout: "5s"
  #海外oss上传
  oss:
    host: "https://overseas-pad.chengjiukehu.com"
    timeout: "5s"
  #八斗平台
  badou:
    host: "http://test-leap-plat.chengjiukehu.com"
    timeout: "5s"
  #新手任务驱动
  task_drive:
    host: "https://overseas-pad.chengjiukehu.com"
    timeout: "5s"
  llm:
    host: "http://ai-service-test.tal.com"
    key: "1000080463:6508e2da7e86bd083db7d840b6b3642b"
  ucenter:
    public_key: "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCx386gxhxkh01lZyceRXqcv8SG\nWATo+dM3rnJHRmV+vrtWDGR6EjymTlnOyugMqKqJpfqEVlgGqII3Bdq32JwJgUS3\nXg4UKRWU9HyERmLH+EJP+WoVouDZgFWNNE16JFRKvcrak2itk4duNb8PHoVU0U8m\nZ7Af/y2+mVTWkkEN7wIDAQAB\n-----END PUBLIC KEY-----"
    domain: "edu.com"
    env: "sandbox_overseas_chengjiukehu"
    suffix: ""
    client_id: "1081101"
    ticket_url: "https://test-api-user.chengjiukehu.com/v1/ticket/get"
    host: "https://test-api-user.chengjiukehu.com"
    app_info:
      app_id: "1080001"
      app_secret: "l1kv1pvg5w37y9pmzgg9m234oo737w40"

biz:
  kousuan:
    host: "http://************:5030"
    timeout: 10s
  #英文纠错 0:关闭 1:开启
  en_correct_switch: 0
  #base64图片上传 0:关闭 1:开启
  base64_upload_switch: 1
  en_word_index: "oversea_en_word"
  tal_en_standard:
    url: "ws://openai-us-sea.100tal.com/aispeech/evl-realtime/en-standard-next"
    appkey: "1313896727794679808"
    secret: "8376f56ddea4472297d3d3e41e7063c6"
  reading_book_ocr:
    host: "http://*************:8090/"
    timeout: 10s
  english_scale: 90
  #读绘本上传开关
  reading_ocr_switch: 1
  en_standard:
    pron_weight: 0.35
    fluency_weight: 0.25
    integrity_weight: 0.30
    volume_weight: 0.10
    need_url: false
    vad_max_sec: 30.0
    vad_pause_sec: -1.0
    vad_st_sil_sec: 10.0
    suffix_penal_quick: -1
    high_score_threshold: 50.0
    high_stop_low_threshold: 30.0
  #aes加密key
  aes_key: "k9A;h>X[(c()daqe"
  blog_categories:
    - Literacy Instruction
    - Lesson Plan
    - Classroom Management
    - Teaching Strategies
    - Assessment
    - Family & Relationships
    - Entertainment Learning
    - Classroom Activities
    - Personal Development
    - Math Instruction
    - Free Resources
    - Education Trends
    - Quotes & Memes for Kids
    - Kids Development

  common_config:
    ranges:
      - word: "Excellent"
        min: 75
        max: 100
        include_min: false
        include_max: true
      - word: "Wonderful"
        min: 50
        max: 75
        include_min: false
        include_max: true
      - word: "Great"
        min: 25
        max: 50
        include_min: false
        include_max: true
      - word: "Nice Try"
        min: 0
        max: 25
        include_min: true
        include_max: true
    right_line: 60
  blog_set_enabled: false
  grades:
    - name: "Pre-Kindergarten"
      value: "Pre-K"
    - name: "Kindergarten"
      value: "K"
    - name: "Grade 1"
      value: "1"
    - name: "Grade 2"
      value: "2"
    - name: "Grade 3"
      value: "3"
    - name: "Grade 4"
      value: "4"
    - name: "Grade 5"
      value: "5"
    - name: "Grade 6"
      value: "6"
error: #强制更改api级别error错误到客户端(仅BFF修改即可，service服务无需更改)
  default: "服务错误" #通用错误术语
  handle:
    "GET#/api/v1/demo": #特殊api 特殊error_reason错误术语提示，只需添加配置即可实时生效
      error_messages:
        - error_reason: "error_01"
          message: "哎呀1"
        - error_reason: "error_11"
          message: "嗨1"
    "POST#/api/v1/demo2":
      error_messages:
        - error_reason: "error_02"
          message: "changed"
        - error_reason: "error_1"
          message: "嗨"