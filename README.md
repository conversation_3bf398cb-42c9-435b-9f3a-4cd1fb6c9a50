# Kratos Project Template

## Install Kratos
```
go get -u git.100tal.com/aielearning_hmi_golang/kratos/cmd/kratos/v2@latest
```
## Create a service
```
# 创建项目模板
kratos new helloworld

cd helloworld
# 拉取项目依赖
go mod download
# 下载wire cmd
go get github.com/google/wire/cmd/wire@v0.5.0
# 修改configs 
config.yaml内
mysql和redis链接地址
# 生成proto模板
kratos proto add api/helloworld/helloworld.proto
# 生成proto源码
kratos proto client api/helloworld/helloworld.proto
# 生成server模板
kratos proto server api/helloworld/helloworld.proto -t internal/service

# 生成所有proto源码、wire等等
go generate ./...

# 运行程序
kratos run(不使用nacos配置中心,使用本地配置文件)
go run cmd/server/main.go cmd/server/wire_gen.go -nacos=true -nacosServer=39.105.177.205 -nacosPort=8848 -nacosNamespaceId=genie-service-dev -nacosCacheDir=./log -nacosLogDir=./log  
```
## 客户端error msg处理方式
```
修改configs/config.yaml error相关即可覆盖，只需要bff层处理error,service层无需关注

error: #强制更改api级别error错误到客户端(仅BFF修改即可，service服务无需更改)
  default: "服务错误" #通用错误术语
  handle:
    "GET#/api/v1/demo": #特殊api 特殊error_reason错误术语提示，只需添加配置即可实时生效
      error_messages:
        - error_reason: "error_01"
          message: "哎呀1"
        - error_reason: "error_11"
          message: "嗨1"
    "POST#/api/v1/demo2":
      error_messages:
        - error_reason: "error_02"
          message: "changed"
        - error_reason: "error_1"
          message: "嗨"
```

## Generate other auxiliary files by Makefile
```
# Download and update dependencies
make init
# Generate API files (include: pb.go, http, grpc, validate, swagger) by proto file
make api
# Generate all files
make all
```
## Automated Initialization (wire)
```
# install wire
go get github.com/google/wire/cmd/wire

# generate wire
cd cmd/server
wire
```

## Docker
```bash
# build
docker build -t <your-docker-image-name> .

# run
docker run --rm -p 8000:8000 -p 9000:9000 -v </path/to/your/configs>:/data/conf <your-docker-image-name>
```

