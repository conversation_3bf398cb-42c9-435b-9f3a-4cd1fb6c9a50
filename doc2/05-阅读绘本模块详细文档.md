# 阅读绘本模块详细文档

## 模块概述

阅读绘本模块专注于英文绘本的智能识别和语音评测功能。通过OCR技术识别绘本中的英文句子，结合语音评测技术为用户提供发音指导和评分，帮助提升英文阅读和口语能力。

## 技术架构

### 核心技术栈
- **OCR识别**: 专用绘本OCR服务，优化文本识别准确率
- **语音评测**: 好未来英文标准语音评测引擎
- **WebSocket**: 实时语音评测通信
- **图像处理**: Base64图像编码和预处理
- **坐标定位**: 精确的手指指向句子定位算法

### 业务流程
```mermaid
graph TD
    A[用户上传绘本图片] --> B[图片预处理]
    B --> C[OCR句子识别]
    C --> D[手指坐标定位]
    D --> E[目标句子提取]
    E --> F[句子标准化]
    F --> G[返回识别结果]
    
    H[用户开始语音评测] --> I[建立WebSocket连接]
    I --> J[实时音频流传输]
    J --> K[语音识别和评测]
    K --> L[发音评分]
    L --> M[返回评测结果]
```

## API接口详细说明

### 服务定义
```protobuf
service ReadingBook {
  rpc SentenceIdentify (SentenceIdentifyRequest) returns (SentenceIdentifyReply);
}
```

### 句子识别接口

#### 接口信息
- **路径**: `POST /intelligence/v1/sentence_identify`
- **功能**: 识别绘本图片中用户手指指向的英文句子
- **业务场景**: 用户在阅读英文绘本时，用手指指向想要学习的句子并拍照

#### 请求参数
```json
{
  "image_base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
  "client_trace_id": "reading_trace_123456",
  "finger_pos": [150, 200]
}
```

#### 参数说明
- `image_base64`: Base64编码的绘本图片数据
- `client_trace_id`: 客户端追踪ID，用于问题排查
- `finger_pos`: 手指坐标数组 [x, y]，至少包含2个元素

#### 参数验证
```protobuf
message SentenceIdentifyRequest{
  string image_base64 = 1;
  string client_trace_id = 2;
  repeated int32 finger_pos = 3[(validate.rules).repeated.min_items = 2];
}
```

#### 响应示例
```json
{
  "sentence": "The little rabbit loves to eat carrots in the garden.",
  "result_code": 0
}
```

#### 响应字段说明
- `sentence`: 识别到的英文句子
- `result_code`: 处理结果码
  - `0`: 成功
  - `1`: 图片格式错误
  - `2`: OCR识别失败
  - `3`: 未找到目标句子
  - `4`: 坐标定位失败

#### 错误处理
```json
{
  "sentence": "",
  "result_code": 2
}
```

## 语音评测功能

### WebSocket连接
- **路径**: `ws://openai-us-sea.100tal.com/aispeech/evl-realtime/en-standard-next`
- **功能**: 实时英文语音评测
- **认证**: 基于appkey和secret的签名认证

### 评测参数配置
```yaml
tal_en_standard:
  url: "ws://openai-us-sea.100tal.com/aispeech/evl-realtime/en-standard-next"
  appkey: "1313896727794679808"
  secret: "8376f56ddea4472297d3d3e41e7063c6"

en_standard:
  pron_weight: 0.35      # 发音权重
  fluency_weight: 0.25   # 流利度权重
  integrity_weight: 0.30 # 完整性权重
  volume_weight: 0.10    # 音量权重
  need_url: false        # 是否需要音频URL
  vad_max_sec: 30.0      # 最大录音时长
  vad_pause_sec: -1.0    # 暂停检测时长
  vad_st_sil_sec: 10.0   # 开始静音时长
  suffix_penal_quick: -1  # 后缀惩罚
  high_score_threshold: 50.0    # 高分阈值
  high_stop_low_threshold: 30.0 # 低分停止阈值
```

### 评测结果示例
```json
{
  "overall_score": 85,
  "pronunciation_score": 88,
  "fluency_score": 82,
  "integrity_score": 87,
  "volume_score": 90,
  "word_details": [
    {
      "word": "little",
      "score": 90,
      "pronunciation": "correct",
      "start_time": 0.5,
      "end_time": 0.8
    },
    {
      "word": "rabbit",
      "score": 85,
      "pronunciation": "slight_accent",
      "start_time": 0.9,
      "end_time": 1.3
    }
  ],
  "feedback": "Great pronunciation! Try to speak a bit more fluently.",
  "grade": "Excellent"
}
```

## 业务逻辑实现

### Service层实现
```go
type ReadingBookService struct {
    pb.UnimplementedReadingBookServer
    uc  *biz.ReadingBookUseCase
    log *log.Helper
}

func (s *ReadingBookService) SentenceIdentify(ctx context.Context, req *pb.SentenceIdentifyRequest) (*pb.SentenceIdentifyReply, error) {
    return s.uc.SentenceIdentify(ctx, req)
}
```

### Business层核心逻辑
```go
func (uc *ReadingBookUseCase) SentenceIdentify(ctx context.Context, req *pb.SentenceIdentifyRequest) (*pb.SentenceIdentifyReply, error) {
    traceId := req.ClientTraceId
    
    // 1. 参数验证
    if err := uc.validateRequest(req); err != nil {
        uc.log.WithContext(ctx).Errorf("SentenceIdentify validation failed, traceId: %s, error: %v", traceId, err)
        return &pb.SentenceIdentifyReply{
            Sentence:   "",
            ResultCode: 1, // 参数错误
        }, nil
    }
    
    // 2. 图片解码
    imageData, err := base64.StdEncoding.DecodeString(req.ImageBase64)
    if err != nil {
        uc.log.WithContext(ctx).Errorf("SentenceIdentify decode failed, traceId: %s, error: %v", traceId, err)
        return &pb.SentenceIdentifyReply{
            Sentence:   "",
            ResultCode: 1, // 图片格式错误
        }, nil
    }
    
    // 3. 调用绘本OCR服务
    ocrResult, err := uc.callReadingBookOCR(ctx, imageData, traceId)
    if err != nil {
        uc.log.WithContext(ctx).Errorf("SentenceIdentify OCR failed, traceId: %s, error: %v", traceId, err)
        return &pb.SentenceIdentifyReply{
            Sentence:   "",
            ResultCode: 2, // OCR识别失败
        }, nil
    }
    
    // 4. 句子定位和提取
    targetSentence, err := uc.locateTargetSentence(ocrResult, req.FingerPos)
    if err != nil {
        uc.log.WithContext(ctx).Errorf("SentenceIdentify locate failed, traceId: %s, error: %v", traceId, err)
        return &pb.SentenceIdentifyReply{
            Sentence:   "",
            ResultCode: 3, // 未找到目标句子
        }, nil
    }
    
    // 5. 句子标准化处理
    normalizedSentence := uc.normalizeSentence(targetSentence)
    
    // 6. 记录成功日志
    uc.log.WithContext(ctx).Infof("SentenceIdentify success, traceId: %s, sentence: %s", traceId, normalizedSentence)
    
    return &pb.SentenceIdentifyReply{
        Sentence:   normalizedSentence,
        ResultCode: 0, // 成功
    }, nil
}
```

### OCR服务调用
```go
func (uc *ReadingBookUseCase) callReadingBookOCR(ctx context.Context, imageData []byte, traceId string) (*OCRResult, error) {
    // 1. 检查OCR开关
    if uc.confBiz.ReadingOcrSwitch != 1 {
        return nil, errors.New("reading OCR service is disabled")
    }
    
    // 2. 构建请求
    client := resty.New().SetTimeout(uc.confBiz.ReadingBookOcr.Timeout)
    
    resp, err := client.R().
        SetContext(ctx).
        SetHeader("Content-Type", "multipart/form-data").
        SetHeader("X-Trace-ID", traceId).
        SetFileReader("image", "reading.jpg", bytes.NewReader(imageData)).
        Post(uc.confBiz.ReadingBookOcr.Host + "/api/sentence_detect")
    
    if err != nil {
        return nil, fmt.Errorf("OCR request failed: %w", err)
    }
    
    // 3. 解析响应
    var result OCRResult
    if err := json.Unmarshal(resp.Body(), &result); err != nil {
        return nil, fmt.Errorf("OCR response parse failed: %w", err)
    }
    
    // 4. 验证结果
    if result.Code != 0 {
        return nil, fmt.Errorf("OCR service error: code=%d, message=%s", result.Code, result.Message)
    }
    
    return &result, nil
}
```

### 句子定位算法
```go
func (uc *ReadingBookUseCase) locateTargetSentence(ocrResult *OCRResult, fingerPos []int32) (string, error) {
    if len(fingerPos) < 2 {
        return "", errors.New("invalid finger position")
    }
    
    fingerX, fingerY := int(fingerPos[0]), int(fingerPos[1])
    
    // 1. 查找最近的文本行
    var targetLine *TextLine
    minDistance := math.MaxFloat64
    
    for _, line := range ocrResult.Lines {
        // 计算手指到文本行的距离
        distance := uc.calculateLineDistance(fingerX, fingerY, line)
        if distance < minDistance {
            minDistance = distance
            targetLine = &line
        }
    }
    
    if targetLine == nil {
        return "", errors.New("no text line found")
    }
    
    // 2. 提取完整句子
    sentence := uc.extractCompleteSentence(targetLine, ocrResult.Lines)
    
    if sentence == "" {
        return "", errors.New("no complete sentence found")
    }
    
    return sentence, nil
}

func (uc *ReadingBookUseCase) calculateLineDistance(fingerX, fingerY int, line TextLine) float64 {
    // 计算点到矩形的最短距离
    centerX := (line.BoundingBox.X1 + line.BoundingBox.X2) / 2
    centerY := (line.BoundingBox.Y1 + line.BoundingBox.Y2) / 2
    
    dx := float64(fingerX - centerX)
    dy := float64(fingerY - centerY)
    
    return math.Sqrt(dx*dx + dy*dy)
}

func (uc *ReadingBookUseCase) extractCompleteSentence(targetLine *TextLine, allLines []TextLine) string {
    sentence := targetLine.Text
    
    // 1. 检查句子是否完整（以句号、问号、感叹号结尾）
    if uc.isCompleteSentence(sentence) {
        return sentence
    }
    
    // 2. 向后查找句子结尾
    for i, line := range allLines {
        if line.Text == targetLine.Text {
            // 从当前行开始向后查找
            for j := i + 1; j < len(allLines); j++ {
                sentence += " " + allLines[j].Text
                if uc.isCompleteSentence(sentence) {
                    break
                }
            }
            break
        }
    }
    
    return sentence
}

func (uc *ReadingBookUseCase) isCompleteSentence(text string) bool {
    text = strings.TrimSpace(text)
    if len(text) == 0 {
        return false
    }
    
    lastChar := text[len(text)-1]
    return lastChar == '.' || lastChar == '!' || lastChar == '?'
}
```

### 句子标准化处理
```go
func (uc *ReadingBookUseCase) normalizeSentence(sentence string) string {
    // 1. 去除多余空格
    sentence = regexp.MustCompile(`\s+`).ReplaceAllString(sentence, " ")
    sentence = strings.TrimSpace(sentence)
    
    // 2. 首字母大写
    if len(sentence) > 0 {
        sentence = strings.ToUpper(sentence[:1]) + sentence[1:]
    }
    
    // 3. 确保句子以标点符号结尾
    if len(sentence) > 0 {
        lastChar := sentence[len(sentence)-1]
        if lastChar != '.' && lastChar != '!' && lastChar != '?' {
            sentence += "."
        }
    }
    
    // 4. 修正常见OCR错误
    sentence = uc.fixCommonOCRErrors(sentence)
    
    return sentence
}

func (uc *ReadingBookUseCase) fixCommonOCRErrors(sentence string) string {
    // 常见OCR错误修正映射
    corrections := map[string]string{
        "0": "o",  // 数字0误识别为字母o
        "1": "l",  // 数字1误识别为字母l
        "5": "s",  // 数字5误识别为字母s
        "rn": "m", // rn误识别为m
        "vv": "w", // vv误识别为w
    }
    
    for wrong, correct := range corrections {
        sentence = strings.ReplaceAll(sentence, wrong, correct)
    }
    
    return sentence
}
```

## 语音评测集成

### WebSocket客户端实现
```go
type VoiceEvaluationClient struct {
    conn     *websocket.Conn
    config   *conf.EnStandard
    appkey   string
    secret   string
    log      *log.Helper
}

func (c *VoiceEvaluationClient) Connect(ctx context.Context, sentence string) error {
    // 1. 构建认证参数
    timestamp := time.Now().Unix()
    signature := c.generateSignature(timestamp)
    
    // 2. 建立WebSocket连接
    u := url.URL{
        Scheme: "ws",
        Host:   "openai-us-sea.100tal.com",
        Path:   "/aispeech/evl-realtime/en-standard-next",
    }
    
    header := http.Header{}
    header.Set("X-Appkey", c.appkey)
    header.Set("X-Timestamp", strconv.FormatInt(timestamp, 10))
    header.Set("X-Signature", signature)
    
    conn, _, err := websocket.DefaultDialer.Dial(u.String(), header)
    if err != nil {
        return fmt.Errorf("websocket dial failed: %w", err)
    }
    
    c.conn = conn
    
    // 3. 发送初始化消息
    initMsg := map[string]interface{}{
        "action": "start",
        "text":   sentence,
        "config": map[string]interface{}{
            "pron_weight":              c.config.PronWeight,
            "fluency_weight":           c.config.FluencyWeight,
            "integrity_weight":         c.config.IntegrityWeight,
            "volume_weight":            c.config.VolumeWeight,
            "vad_max_sec":             c.config.VadMaxSec,
            "high_score_threshold":     c.config.HighScoreThreshold,
            "high_stop_low_threshold":  c.config.HighStopLowThreshold,
        },
    }
    
    return c.conn.WriteJSON(initMsg)
}

func (c *VoiceEvaluationClient) SendAudio(audioData []byte) error {
    return c.conn.WriteMessage(websocket.BinaryMessage, audioData)
}

func (c *VoiceEvaluationClient) ReceiveResult() (*EvaluationResult, error) {
    var result EvaluationResult
    err := c.conn.ReadJSON(&result)
    return &result, err
}

func (c *VoiceEvaluationClient) Close() error {
    if c.conn != nil {
        return c.conn.Close()
    }
    return nil
}
```

### 评分等级映射
```go
func (uc *ReadingBookUseCase) mapScoreToGrade(score float64) string {
    for _, rang := range uc.confBiz.CommonConfig.Ranges {
        if uc.isScoreInRange(score, rang) {
            return rang.Word
        }
    }
    return "Nice Try" // 默认等级
}

func (uc *ReadingBookUseCase) isScoreInRange(score float64, rang conf.Range) bool {
    minCheck := rang.IncludeMin && score >= float64(rang.Min) || !rang.IncludeMin && score > float64(rang.Min)
    maxCheck := rang.IncludeMax && score <= float64(rang.Max) || !rang.IncludeMax && score < float64(rang.Max)
    return minCheck && maxCheck
}
```

## 性能优化策略

### 1. 图片处理优化
```go
func (uc *ReadingBookUseCase) optimizeImage(imageData []byte) ([]byte, error) {
    // 1. 图片尺寸检查
    if len(imageData) > 5*1024*1024 { // 5MB限制
        return nil, errors.New("image too large")
    }
    
    // 2. 图片格式验证
    contentType := http.DetectContentType(imageData)
    if !strings.HasPrefix(contentType, "image/") {
        return nil, errors.New("invalid image format")
    }
    
    // 3. 图片压缩（如果需要）
    if len(imageData) > 1*1024*1024 { // 1MB以上进行压缩
        return uc.compressImage(imageData)
    }
    
    return imageData, nil
}
```

### 2. 缓存策略
```go
const (
    OCRResultCacheTTL = 1 * time.Hour    // OCR结果缓存1小时
    SentenceCacheTTL  = 24 * time.Hour   // 句子缓存24小时
)

func (uc *ReadingBookUseCase) cacheOCRResult(ctx context.Context, imageHash string, result *OCRResult) {
    key := fmt.Sprintf("reading_book:ocr:%s", imageHash)
    data, _ := json.Marshal(result)
    uc.rdb.Set(ctx, key, data, OCRResultCacheTTL)
}
```

### 3. 并发控制
```go
// OCR请求限流
var ocrLimiter = rate.NewLimiter(rate.Limit(10), 20) // 每秒10个请求，突发20个

func (uc *ReadingBookUseCase) callReadingBookOCRWithLimit(ctx context.Context, imageData []byte, traceId string) (*OCRResult, error) {
    // 等待限流器许可
    if err := ocrLimiter.Wait(ctx); err != nil {
        return nil, fmt.Errorf("rate limit exceeded: %w", err)
    }
    
    return uc.callReadingBookOCR(ctx, imageData, traceId)
}
```

## 监控和日志

### 1. 关键指标监控
- 句子识别成功率
- OCR服务响应时间
- 语音评测连接成功率
- 图片处理耗时
- 错误类型分布

### 2. 详细日志记录
```go
func (uc *ReadingBookUseCase) logSentenceIdentify(ctx context.Context, req *pb.SentenceIdentifyRequest, resp *pb.SentenceIdentifyReply, duration time.Duration) {
    logData := map[string]interface{}{
        "trace_id":      req.ClientTraceId,
        "image_size":    len(req.ImageBase64),
        "finger_pos":    req.FingerPos,
        "result_code":   resp.ResultCode,
        "sentence_len":  len(resp.Sentence),
        "duration_ms":   duration.Milliseconds(),
        "success":       resp.ResultCode == 0,
    }
    
    logJson, _ := json.Marshal(logData)
    uc.log.WithContext(ctx).Infof("SentenceIdentify: %s", string(logJson))
}
```

阅读绘本模块通过精准的OCR识别、智能的句子定位算法、实时的语音评测功能，为用户提供了完整的英文绘本学习体验，有效提升了英语阅读和口语能力。
