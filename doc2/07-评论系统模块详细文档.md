# 评论系统模块详细文档

## 模块概述

评论系统模块为海外教育平台提供完整的用户评论功能，支持多级评论、回复、点赞等社交互动功能。该模块采用树形结构设计，支持无限层级的评论嵌套，为用户提供丰富的交流体验。

## 技术架构

### 核心功能
- **多级评论**: 支持评论和回复的树形结构
- **实时交互**: 评论发布、点赞、回复的实时更新
- **内容管理**: 评论的增删改查和状态管理
- **用户关联**: 与用户系统集成，支持用户身份验证
- **分页加载**: 高效的分页和懒加载机制

### 数据模型关系
```mermaid
graph TD
    A[Comment 评论] --> B[User 用户]
    A --> C[Parent Comment 父评论]
    A --> D[Child Comments 子评论]
    A --> E[Resource 关联资源]
    A --> F[Like 点赞]
    
    G[CommentLike 点赞记录] --> A
    G --> B
```

## API接口详细说明

### 服务定义
```protobuf
service Comment {
  // 评论列表查询
  rpc GetComments (GetCommentsRequest) returns (GetCommentsReply);
  
  // 发布评论
  rpc CreateComment (CreateCommentRequest) returns (CreateCommentReply);
  
  // 删除评论
  rpc DeleteComment (DeleteCommentRequest) returns (DeleteCommentReply);
  
  // 点赞/取消点赞
  rpc LikeComment (LikeCommentRequest) returns (LikeCommentReply);
  
  // 举报评论
  rpc ReportComment (ReportCommentRequest) returns (ReportCommentReply);
}
```

### 1. 评论列表查询接口

#### 接口信息
- **路径**: `GET /intelligence/api/comment/v1/comments`
- **功能**: 获取指定资源的评论列表，支持分页和树形结构
- **业务场景**: 用户浏览教育资源时查看其他用户的评论和讨论

#### 请求参数
```json
{
  "resource_id": "worksheet_123",
  "resource_type": "worksheet",
  "page": 1,
  "page_size": 20,
  "sort_type": "latest",
  "parent_id": 0
}
```

#### 参数说明
- `resource_id`: 资源ID，关联的教育资源标识
- `resource_type`: 资源类型 (worksheet, video, coloring_page等)
- `page`: 页码，从1开始
- `page_size`: 每页数量，默认20，最大100
- `sort_type`: 排序方式
  - `latest`: 最新发布
  - `oldest`: 最早发布
  - `most_liked`: 最多点赞
- `parent_id`: 父评论ID，0表示查询顶级评论

#### 响应示例
```json
{
  "total": 156,
  "page": 1,
  "page_size": 20,
  "has_more": true,
  "comments": [
    {
      "id": 1001,
      "content": "This worksheet is really helpful for my daughter's math practice!",
      "user_id": "user_12345",
      "user_name": "Sarah Johnson",
      "user_avatar": "https://example.com/avatars/sarah.jpg",
      "parent_id": 0,
      "level": 1,
      "like_count": 15,
      "reply_count": 3,
      "is_liked": false,
      "status": "published",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "replies": [
        {
          "id": 1002,
          "content": "I agree! My son loves these addition problems.",
          "user_id": "user_67890",
          "user_name": "Mike Chen",
          "user_avatar": "https://example.com/avatars/mike.jpg",
          "parent_id": 1001,
          "level": 2,
          "like_count": 8,
          "reply_count": 1,
          "is_liked": true,
          "status": "published",
          "created_at": "2024-01-15T11:15:00Z",
          "updated_at": "2024-01-15T11:15:00Z",
          "replies": [
            {
              "id": 1003,
              "content": "@Mike Chen How old is your son? Mine is 8 and finds these perfect.",
              "user_id": "user_12345",
              "user_name": "Sarah Johnson",
              "user_avatar": "https://example.com/avatars/sarah.jpg",
              "parent_id": 1002,
              "level": 3,
              "like_count": 2,
              "reply_count": 0,
              "is_liked": false,
              "status": "published",
              "created_at": "2024-01-15T12:00:00Z",
              "updated_at": "2024-01-15T12:00:00Z",
              "replies": []
            }
          ]
        }
      ]
    }
  ]
}
```

### 2. 发布评论接口

#### 接口信息
- **路径**: `POST /intelligence/api/comment/v1/comments`
- **功能**: 发布新评论或回复已有评论
- **认证**: 需要用户登录

#### 请求参数
```json
{
  "resource_id": "worksheet_123",
  "resource_type": "worksheet",
  "content": "This is a great worksheet for practicing addition!",
  "parent_id": 0,
  "mentioned_users": ["user_67890"]
}
```

#### 参数验证
- `content`: 长度1-1000字符，不能为空
- `parent_id`: 如果是回复，必须是有效的评论ID
- `resource_id`: 必须是有效的资源ID

#### 响应示例
```json
{
  "success": true,
  "comment": {
    "id": 1004,
    "content": "This is a great worksheet for practicing addition!",
    "user_id": "user_11111",
    "user_name": "Emma Wilson",
    "user_avatar": "https://example.com/avatars/emma.jpg",
    "parent_id": 0,
    "level": 1,
    "like_count": 0,
    "reply_count": 0,
    "is_liked": false,
    "status": "published",
    "created_at": "2024-01-15T14:30:00Z",
    "updated_at": "2024-01-15T14:30:00Z"
  },
  "message": "Comment published successfully"
}
```

### 3. 点赞接口

#### 接口信息
- **路径**: `POST /intelligence/api/comment/v1/comments/{comment_id}/like`
- **功能**: 对评论进行点赞或取消点赞
- **认证**: 需要用户登录

#### 请求参数
```json
{
  "action": "like"
}
```

#### 参数说明
- `action`: 操作类型
  - `like`: 点赞
  - `unlike`: 取消点赞

#### 响应示例
```json
{
  "success": true,
  "like_count": 16,
  "is_liked": true,
  "message": "Comment liked successfully"
}
```

## 数据模型设计

### 1. 评论表 (comments)

```sql
CREATE TABLE comments (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '评论ID',
  resource_id VARCHAR(100) NOT NULL COMMENT '资源ID',
  resource_type VARCHAR(50) NOT NULL COMMENT '资源类型',
  user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
  user_name VARCHAR(100) NOT NULL COMMENT '用户名',
  user_avatar VARCHAR(500) DEFAULT '' COMMENT '用户头像',
  parent_id BIGINT UNSIGNED DEFAULT 0 COMMENT '父评论ID，0表示顶级评论',
  level TINYINT UNSIGNED DEFAULT 1 COMMENT '评论层级，1为顶级',
  content TEXT NOT NULL COMMENT '评论内容',
  like_count INT UNSIGNED DEFAULT 0 COMMENT '点赞数',
  reply_count INT UNSIGNED DEFAULT 0 COMMENT '回复数',
  status ENUM('published', 'hidden', 'deleted', 'pending') DEFAULT 'published' COMMENT '状态',
  ip_address VARCHAR(45) DEFAULT '' COMMENT 'IP地址',
  user_agent TEXT DEFAULT '' COMMENT '用户代理',
  mentioned_users JSON DEFAULT NULL COMMENT '提及的用户列表',
  extra_data JSON DEFAULT NULL COMMENT '扩展数据',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_resource (resource_id, resource_type),
  INDEX idx_user (user_id),
  INDEX idx_parent (parent_id),
  INDEX idx_status_created (status, created_at),
  INDEX idx_like_count (like_count),
  FULLTEXT INDEX ft_content (content)
);
```

### 2. 评论点赞表 (comment_likes)

```sql
CREATE TABLE comment_likes (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  comment_id BIGINT UNSIGNED NOT NULL COMMENT '评论ID',
  user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
  
  UNIQUE INDEX idx_comment_user (comment_id, user_id),
  INDEX idx_user (user_id),
  INDEX idx_created (created_at),
  
  FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE
);
```

### 3. 评论举报表 (comment_reports)

```sql
CREATE TABLE comment_reports (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  comment_id BIGINT UNSIGNED NOT NULL COMMENT '评论ID',
  reporter_user_id VARCHAR(50) NOT NULL COMMENT '举报用户ID',
  reason ENUM('spam', 'inappropriate', 'harassment', 'false_info', 'other') NOT NULL COMMENT '举报原因',
  description TEXT DEFAULT '' COMMENT '举报描述',
  status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending' COMMENT '处理状态',
  admin_user_id VARCHAR(50) DEFAULT '' COMMENT '处理管理员ID',
  admin_note TEXT DEFAULT '' COMMENT '管理员备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_comment (comment_id),
  INDEX idx_reporter (reporter_user_id),
  INDEX idx_status (status),
  INDEX idx_created (created_at)
);
```

## 业务逻辑实现

### Service层实现
```go
type CommentService struct {
    pb.UnimplementedCommentServer
    uc  *biz.CommentUseCase
    log *log.Helper
}

func (s *CommentService) GetComments(ctx context.Context, req *pb.GetCommentsRequest) (*pb.GetCommentsReply, error) {
    return s.uc.GetComments(ctx, req)
}

func (s *CommentService) CreateComment(ctx context.Context, req *pb.CreateCommentRequest) (*pb.CreateCommentReply, error) {
    return s.uc.CreateComment(ctx, req)
}

func (s *CommentService) LikeComment(ctx context.Context, req *pb.LikeCommentRequest) (*pb.LikeCommentReply, error) {
    return s.uc.LikeComment(ctx, req)
}
```

### Business层核心逻辑

#### 评论列表查询
```go
func (uc *CommentUseCase) GetComments(ctx context.Context, req *pb.GetCommentsRequest) (*pb.GetCommentsReply, error) {
    // 1. 参数验证
    if err := uc.validateGetCommentsRequest(req); err != nil {
        return nil, err
    }
    
    // 2. 获取当前用户ID（如果已登录）
    currentUserID := uc.getCurrentUserID(ctx)
    
    // 3. 构建查询条件
    queryReq := &model.CommentQueryReq{
        ResourceID:   req.ResourceId,
        ResourceType: req.ResourceType,
        ParentID:     req.ParentId,
        Page:         int(req.Page),
        PageSize:     int(req.PageSize),
        SortType:     req.SortType,
        Status:       "published",
    }
    
    // 4. 查询评论列表
    comments, total, err := uc.repo.GetComments(ctx, queryReq)
    if err != nil {
        return nil, fmt.Errorf("failed to get comments: %w", err)
    }
    
    // 5. 构建树形结构
    commentTree := uc.buildCommentTree(comments, req.ParentId)
    
    // 6. 获取用户点赞状态
    if currentUserID != "" {
        err = uc.fillUserLikeStatus(ctx, commentTree, currentUserID)
        if err != nil {
            uc.log.WithContext(ctx).Errorf("Failed to fill like status: %v", err)
        }
    }
    
    // 7. 数据转换
    pbComments := make([]*pb.Comment, 0, len(commentTree))
    for _, comment := range commentTree {
        pbComment := uc.convertToProtoComment(comment)
        pbComments = append(pbComments, pbComment)
    }
    
    // 8. 构建响应
    reply := &pb.GetCommentsReply{
        Total:    int32(total),
        Page:     req.Page,
        PageSize: req.PageSize,
        HasMore:  uc.hasMoreComments(int(req.Page), int(req.PageSize), total),
        Comments: pbComments,
    }
    
    return reply, nil
}
```

#### 发布评论
```go
func (uc *CommentUseCase) CreateComment(ctx context.Context, req *pb.CreateCommentRequest) (*pb.CreateCommentReply, error) {
    // 1. 用户认证
    userInfo, err := uc.getCurrentUser(ctx)
    if err != nil {
        return nil, fmt.Errorf("authentication required: %w", err)
    }
    
    // 2. 参数验证
    if err := uc.validateCreateCommentRequest(req); err != nil {
        return nil, err
    }
    
    // 3. 内容安全检查
    if uc.confBiz.ContentSafetyEnabled {
        isSafe, err := uc.contentSafety.CheckText(ctx, req.Content)
        if err != nil || !isSafe {
            return nil, fmt.Errorf("content safety check failed")
        }
    }
    
    // 4. 验证父评论（如果是回复）
    var parentComment *model.Comment
    var level int = 1
    
    if req.ParentId > 0 {
        parentComment, err = uc.repo.GetCommentByID(ctx, req.ParentId)
        if err != nil {
            return nil, fmt.Errorf("parent comment not found: %w", err)
        }
        
        if parentComment.Status != "published" {
            return nil, fmt.Errorf("cannot reply to unpublished comment")
        }
        
        level = parentComment.Level + 1
        
        // 限制评论层级深度
        if level > uc.confBiz.MaxCommentLevel {
            return nil, fmt.Errorf("comment level too deep")
        }
    }
    
    // 5. 创建评论对象
    comment := &model.Comment{
        ResourceID:     req.ResourceId,
        ResourceType:   req.ResourceType,
        UserID:         userInfo.ID,
        UserName:       userInfo.Name,
        UserAvatar:     userInfo.Avatar,
        ParentID:       req.ParentId,
        Level:          level,
        Content:        req.Content,
        Status:         "published",
        IPAddress:      uc.getClientIP(ctx),
        UserAgent:      uc.getUserAgent(ctx),
        MentionedUsers: req.MentionedUsers,
    }
    
    // 6. 保存评论
    err = uc.repo.CreateComment(ctx, comment)
    if err != nil {
        return nil, fmt.Errorf("failed to create comment: %w", err)
    }
    
    // 7. 更新父评论回复数
    if parentComment != nil {
        err = uc.repo.IncrementReplyCount(ctx, parentComment.ID)
        if err != nil {
            uc.log.WithContext(ctx).Errorf("Failed to update reply count: %v", err)
        }
    }
    
    // 8. 发送通知（异步）
    go uc.sendCommentNotifications(ctx, comment, parentComment)
    
    // 9. 构建响应
    pbComment := uc.convertToProtoComment(comment)
    
    return &pb.CreateCommentReply{
        Success: true,
        Comment: pbComment,
        Message: "Comment published successfully",
    }, nil
}
```

#### 点赞功能
```go
func (uc *CommentUseCase) LikeComment(ctx context.Context, req *pb.LikeCommentRequest) (*pb.LikeCommentReply, error) {
    // 1. 用户认证
    userInfo, err := uc.getCurrentUser(ctx)
    if err != nil {
        return nil, fmt.Errorf("authentication required: %w", err)
    }
    
    // 2. 验证评论存在
    comment, err := uc.repo.GetCommentByID(ctx, req.CommentId)
    if err != nil {
        return nil, fmt.Errorf("comment not found: %w", err)
    }
    
    if comment.Status != "published" {
        return nil, fmt.Errorf("cannot like unpublished comment")
    }
    
    // 3. 检查当前点赞状态
    isLiked, err := uc.repo.IsCommentLiked(ctx, req.CommentId, userInfo.ID)
    if err != nil {
        return nil, fmt.Errorf("failed to check like status: %w", err)
    }
    
    var newLikeCount int
    var newIsLiked bool
    var message string
    
    // 4. 执行点赞/取消点赞操作
    if req.Action == "like" && !isLiked {
        // 添加点赞
        err = uc.repo.AddCommentLike(ctx, req.CommentId, userInfo.ID)
        if err != nil {
            return nil, fmt.Errorf("failed to add like: %w", err)
        }
        
        newLikeCount = comment.LikeCount + 1
        newIsLiked = true
        message = "Comment liked successfully"
        
        // 更新评论点赞数
        err = uc.repo.UpdateCommentLikeCount(ctx, req.CommentId, newLikeCount)
        if err != nil {
            uc.log.WithContext(ctx).Errorf("Failed to update like count: %v", err)
        }
        
    } else if req.Action == "unlike" && isLiked {
        // 取消点赞
        err = uc.repo.RemoveCommentLike(ctx, req.CommentId, userInfo.ID)
        if err != nil {
            return nil, fmt.Errorf("failed to remove like: %w", err)
        }
        
        newLikeCount = comment.LikeCount - 1
        if newLikeCount < 0 {
            newLikeCount = 0
        }
        newIsLiked = false
        message = "Comment unliked successfully"
        
        // 更新评论点赞数
        err = uc.repo.UpdateCommentLikeCount(ctx, req.CommentId, newLikeCount)
        if err != nil {
            uc.log.WithContext(ctx).Errorf("Failed to update like count: %v", err)
        }
        
    } else {
        // 无效操作
        newLikeCount = comment.LikeCount
        newIsLiked = isLiked
        message = "No action performed"
    }
    
    return &pb.LikeCommentReply{
        Success:   true,
        LikeCount: int32(newLikeCount),
        IsLiked:   newIsLiked,
        Message:   message,
    }, nil
}
```

### 树形结构构建
```go
func (uc *CommentUseCase) buildCommentTree(comments []*model.Comment, parentID int64) []*model.Comment {
    // 1. 按父ID分组
    childrenMap := make(map[int64][]*model.Comment)
    var rootComments []*model.Comment
    
    for _, comment := range comments {
        if comment.ParentID == parentID {
            rootComments = append(rootComments, comment)
        } else {
            childrenMap[comment.ParentID] = append(childrenMap[comment.ParentID], comment)
        }
    }
    
    // 2. 递归构建树形结构
    var buildTree func([]*model.Comment)
    buildTree = func(nodes []*model.Comment) {
        for _, node := range nodes {
            if children, exists := childrenMap[node.ID]; exists {
                node.Replies = children
                buildTree(children)
            }
        }
    }
    
    buildTree(rootComments)
    return rootComments
}
```

### 通知系统集成
```go
func (uc *CommentUseCase) sendCommentNotifications(ctx context.Context, comment *model.Comment, parentComment *model.Comment) {
    // 1. 回复通知
    if parentComment != nil && parentComment.UserID != comment.UserID {
        notification := &model.Notification{
            UserID:      parentComment.UserID,
            Type:        "comment_reply",
            Title:       "New Reply to Your Comment",
            Content:     fmt.Sprintf("%s replied to your comment", comment.UserName),
            ResourceID:  comment.ResourceID,
            ResourceType: comment.ResourceType,
            RelatedID:   comment.ID,
        }
        
        err := uc.notificationRepo.Create(ctx, notification)
        if err != nil {
            uc.log.WithContext(ctx).Errorf("Failed to create reply notification: %v", err)
        }
    }
    
    // 2. 提及通知
    for _, mentionedUserID := range comment.MentionedUsers {
        if mentionedUserID != comment.UserID {
            notification := &model.Notification{
                UserID:      mentionedUserID,
                Type:        "comment_mention",
                Title:       "You Were Mentioned",
                Content:     fmt.Sprintf("%s mentioned you in a comment", comment.UserName),
                ResourceID:  comment.ResourceID,
                ResourceType: comment.ResourceType,
                RelatedID:   comment.ID,
            }
            
            err := uc.notificationRepo.Create(ctx, notification)
            if err != nil {
                uc.log.WithContext(ctx).Errorf("Failed to create mention notification: %v", err)
            }
        }
    }
}
```

## 性能优化策略

### 1. 缓存策略
```go
const (
    CommentListCacheTTL = 5 * time.Minute   // 评论列表缓存5分钟
    CommentCountCacheTTL = 1 * time.Hour    // 评论数量缓存1小时
    UserLikeCacheTTL = 30 * time.Minute     // 用户点赞状态缓存30分钟
)

func (uc *CommentUseCase) getCachedComments(ctx context.Context, cacheKey string) ([]*model.Comment, error) {
    data, err := uc.rdb.Get(ctx, cacheKey).Result()
    if err != nil {
        return nil, err
    }
    
    var comments []*model.Comment
    err = json.Unmarshal([]byte(data), &comments)
    return comments, err
}

func (uc *CommentUseCase) cacheComments(ctx context.Context, cacheKey string, comments []*model.Comment) {
    data, err := json.Marshal(comments)
    if err != nil {
        return
    }
    
    uc.rdb.Set(ctx, cacheKey, data, CommentListCacheTTL)
}
```

### 2. 数据库查询优化
```go
func (dao *CommentDao) GetCommentsOptimized(ctx context.Context, req *model.CommentQueryReq) ([]*model.Comment, int, error) {
    query := dao.db.WithContext(ctx).Model(&model.Comment{})
    
    // 1. 基础过滤条件
    query = query.Where("resource_id = ? AND resource_type = ? AND status = ?", 
        req.ResourceID, req.ResourceType, req.Status)
    
    // 2. 父评论过滤
    if req.ParentID > 0 {
        query = query.Where("parent_id = ?", req.ParentID)
    } else {
        query = query.Where("parent_id = 0")
    }
    
    // 3. 排序
    switch req.SortType {
    case "latest":
        query = query.Order("created_at DESC")
    case "oldest":
        query = query.Order("created_at ASC")
    case "most_liked":
        query = query.Order("like_count DESC, created_at DESC")
    default:
        query = query.Order("created_at DESC")
    }
    
    // 4. 计算总数
    var total int64
    countQuery := query
    err := countQuery.Count(&total).Error
    if err != nil {
        return nil, 0, err
    }
    
    // 5. 分页查询
    offset := (req.Page - 1) * req.PageSize
    query = query.Offset(offset).Limit(req.PageSize)
    
    var comments []*model.Comment
    err = query.Find(&comments).Error
    return comments, int(total), err
}
```

### 3. 批量操作优化
```go
func (uc *CommentUseCase) fillUserLikeStatus(ctx context.Context, comments []*model.Comment, userID string) error {
    if len(comments) == 0 {
        return nil
    }
    
    // 1. 收集所有评论ID
    commentIDs := make([]int64, 0)
    var collectIDs func([]*model.Comment)
    collectIDs = func(nodes []*model.Comment) {
        for _, comment := range nodes {
            commentIDs = append(commentIDs, comment.ID)
            if len(comment.Replies) > 0 {
                collectIDs(comment.Replies)
            }
        }
    }
    collectIDs(comments)
    
    // 2. 批量查询点赞状态
    likedComments, err := uc.repo.GetUserLikedComments(ctx, commentIDs, userID)
    if err != nil {
        return err
    }
    
    // 3. 构建点赞状态映射
    likedMap := make(map[int64]bool)
    for _, commentID := range likedComments {
        likedMap[commentID] = true
    }
    
    // 4. 填充点赞状态
    var fillStatus func([]*model.Comment)
    fillStatus = func(nodes []*model.Comment) {
        for _, comment := range nodes {
            comment.IsLiked = likedMap[comment.ID]
            if len(comment.Replies) > 0 {
                fillStatus(comment.Replies)
            }
        }
    }
    fillStatus(comments)
    
    return nil
}
```

## 监控和日志

### 1. 关键指标监控
- 评论发布成功率
- 评论查询响应时间
- 点赞操作成功率
- 评论举报处理时间
- 用户活跃度统计

### 2. 业务日志记录
```go
func (uc *CommentUseCase) logCommentActivity(ctx context.Context, action string, commentID int64, userID string, extra map[string]interface{}) {
    logData := map[string]interface{}{
        "action":     action,
        "comment_id": commentID,
        "user_id":    userID,
        "timestamp":  time.Now().Unix(),
    }
    
    for k, v := range extra {
        logData[k] = v
    }
    
    logJson, _ := json.Marshal(logData)
    uc.log.WithContext(ctx).Infof("CommentActivity: %s", string(logJson))
}
```

评论系统模块通过完善的树形结构设计、高效的查询机制、丰富的交互功能，为海外教育平台提供了活跃的社区交流环境，有效促进了用户参与和内容互动。
