# 指尖查词模块详细文档

## 模块概述

指尖查词模块是hw-paas-service项目的核心功能之一，通过OCR技术识别用户手指指向的英文单词，并提供详细的词典信息。该模块结合了图像识别、文字定位、词典查询等多项技术，为用户提供便捷的单词学习体验。

## 技术架构

### 核心技术栈
- **OCR识别**: 混合OCR服务，支持文字识别和定位
- **词典数据**: Elasticsearch存储的英文单词词典
- **AI增强**: GPT-4o提供增强的单词解释
- **安全审核**: 内容安全服务过滤不当内容
- **缓存优化**: Redis缓存热门单词查询结果

### 业务流程
```mermaid
graph TD
    A[用户上传图片+手指坐标] --> B[图片预处理]
    B --> C[OCR文字识别]
    C --> D[文字定位分析]
    D --> E[手指指向单词匹配]
    E --> F[单词标准化处理]
    F --> G[词典数据查询]
    G --> H[AI增强解释]
    H --> I[内容安全检查]
    I --> J[结果缓存]
    J --> K[返回查词结果]
```

## API接口详细说明

### 服务定义
```protobuf
service FingerWords {
  // 指尖查词入口
  rpc FingerWordsEntry (FingerWordsEntryRequest) returns (FingerWordsEntryReply);
  
  // 单词查询
  rpc FingerWordsQuery (FingerWordsQueryRequest) returns (FingerWordsQueryReply);
}
```

### 1. 指尖查词入口接口

#### 接口信息
- **路径**: `POST /intelligence/v1/finger_words/entry`
- **功能**: 处理用户上传的图片和手指坐标，识别指向的单词
- **业务场景**: 用户在阅读英文材料时，用手指指向不认识的单词并拍照

#### 请求参数
```json
{
  "client_trace_id": "trace_123456789",
  "retry_status": 0,
  "text_bmp": "base64编码的图片数据",
  "finger_pos_2ma": [100, 200],
  "text_pos_2ma": [50, 150, 300, 250],
  "version": "1.0.0",
  "app_version": "2.1.0",
  "local_model_time": "2024-01-15T10:00:00Z"
}
```

#### 参数说明
- `client_trace_id`: 客户端追踪ID，用于问题排查
- `retry_status`: 重试状态，0为首次请求
- `text_bmp`: Base64编码的图片数据
- `finger_pos_2ma`: 手指坐标 [x, y]
- `text_pos_2ma`: 文本区域坐标 [x1, y1, x2, y2]
- `version`: 算法版本号
- `app_version`: 应用版本号
- `local_model_time`: 本地模型时间

#### 响应示例
```json
{
  "near_word": "beautiful",
  "line_words": ["The", "beautiful", "flower", "blooms", "in", "spring"],
  "near_index": 1,
  "ocr": "The beautiful flower blooms in spring",
  "retry_status": 0,
  "result": {
    "word": "beautiful",
    "confidence": 0.95,
    "position": {
      "x": 120,
      "y": 180,
      "width": 80,
      "height": 20
    }
  },
  "dispatch": {
    "algorithm_version": "v2.1",
    "processing_time": 1200,
    "ocr_service": "hybrid_ocr_v3"
  }
}
```

#### 响应字段说明
- `near_word`: 识别到的最近单词
- `line_words`: 该行的所有单词列表
- `near_index`: 目标单词在行中的索引
- `ocr`: OCR识别的完整文本
- `retry_status`: 处理状态
- `result`: 详细识别结果
- `dispatch`: 调度和处理信息

### 2. 单词查询接口

#### 接口信息
- **路径**: `POST /intelligence/v1/finger_words/query`
- **功能**: 根据单词获取详细的词典信息
- **业务场景**: 获取单词的释义、音标、例句等详细信息

#### 请求参数
```json
{
  "word": "beautiful",
  "app_version": "2.1.0",
  "skill_name": "reading_comprehension"
}
```

#### 响应示例
```json
{
  "detail": {
    "id": 12345,
    "word": "beautiful",
    "british": "/ˈbjuːtɪfl/",
    "american": "/ˈbjuːtɪfl/",
    "meanings": [
      {
        "part_of_speech": "adjective",
        "definition": "pleasing the senses or mind aesthetically"
      }
    ],
    "synonyms": ["lovely", "attractive", "gorgeous"],
    "antonyms": ["ugly", "hideous"],
    "sentences": [
      "She has beautiful eyes.",
      "The sunset was beautiful tonight."
    ],
    "frequency": 8500,
    "video_url": "encrypted_video_url",
    "speech": "encrypted_audio_url"
  },
  "dispatch": {
    "source": "elasticsearch",
    "cache_hit": true,
    "processing_time": 50
  }
}
```

## 数据模型设计

### 1. 英文单词表 (hw_en_word)

```sql
CREATE TABLE hw_en_word (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  word VARCHAR(50) NOT NULL COMMENT '单词',
  british VARCHAR(10) NOT NULL DEFAULT '' COMMENT '英式音标',
  american VARCHAR(10) NOT NULL DEFAULT '' COMMENT '美式音标',
  meanings VARCHAR(2048) NOT NULL DEFAULT '' COMMENT '释义(JSON格式)',
  synonyms VARCHAR(256) NOT NULL DEFAULT '' COMMENT '同义词',
  antonyms VARCHAR(256) NOT NULL DEFAULT '' COMMENT '反义词',
  sentences VARCHAR(2048) NOT NULL DEFAULT '' COMMENT '例句',
  inflections VARCHAR(256) NOT NULL DEFAULT '' COMMENT '变形词',
  prefix VARCHAR(20) NOT NULL DEFAULT '' COMMENT '前缀',
  suffix VARCHAR(20) NOT NULL DEFAULT '' COMMENT '后缀',
  phrases VARCHAR(512) NOT NULL DEFAULT '' COMMENT '短语',
  frequency BIGINT NOT NULL COMMENT '词频',
  status TINYINT NOT NULL COMMENT '状态:1待审核;2待上架;3已上架;4已下架;5违禁词',
  opt_uid VARCHAR(20) NOT NULL DEFAULT '' COMMENT '操作人工号',
  opt_name VARCHAR(20) NOT NULL DEFAULT '' COMMENT '操作人姓名',
  rel_word VARCHAR(50) NOT NULL DEFAULT '' COMMENT '关联原形词',
  video_url VARCHAR(512) NOT NULL DEFAULT '' COMMENT '视频讲解URL',
  video_img VARCHAR(512) NOT NULL DEFAULT '' COMMENT '视频封面URL',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE INDEX idx_word (word),
  INDEX idx_status (status),
  INDEX idx_frequency (frequency),
  INDEX idx_rel_word (rel_word),
  FULLTEXT INDEX ft_meanings (meanings)
);
```

### 2. GPT-4o增强单词表 (hw_en_word_gpt4o)

```sql
CREATE TABLE hw_en_word_gpt4o (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  word VARCHAR(50) NOT NULL COMMENT '单词',
  content TEXT NOT NULL COMMENT 'GPT-4o生成的详细解释',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE INDEX idx_word (word),
  INDEX idx_created (created_at)
);
```

### 3. 异步任务表 (apx_async_task)

```sql
CREATE TABLE apx_async_task (
  id INT PRIMARY KEY AUTO_INCREMENT,
  word VARCHAR(50) NOT NULL COMMENT '处理的单词',
  task_id VARCHAR(100) NOT NULL COMMENT '任务ID',
  status TINYINT NOT NULL COMMENT '任务状态:3成功;4失败',
  response TEXT NOT NULL COMMENT '模型响应数据',
  rel_word VARCHAR(50) NOT NULL DEFAULT '' COMMENT '关联的原形词',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_word (word),
  INDEX idx_task_id (task_id),
  INDEX idx_status (status)
);
```

## 业务逻辑实现

### Service层实现
```go
type FingerWordsService struct {
    pb.UnimplementedFingerWordsServer
    uc  *biz.FingerWordsUseCase
    log *log.Helper
}

func (s *FingerWordsService) FingerWordsEntry(ctx context.Context, req *pb.FingerWordsEntryRequest) (*pb.FingerWordsEntryReply, error) {
    return s.uc.FingerWordsEntry(ctx, req)
}

func (s *FingerWordsService) FingerWordsQuery(ctx context.Context, req *pb.FingerWordsQueryRequest) (*pb.FingerWordsQueryReply, error) {
    return s.uc.FingerWordsQuery(ctx, req)
}
```

### Business层核心逻辑

#### 指尖查词入口逻辑
```go
func (uc *FingerWordsUseCase) FingerWordsEntry(ctx context.Context, req *pb.FingerWordsEntryRequest) (*pb.FingerWordsEntryReply, error) {
    traceId := req.ClientTraceId
    
    // 1. 参数验证
    if err := uc.validateRequest(req); err != nil {
        return nil, err
    }
    
    // 2. 图片解码
    imageData, err := base64.StdEncoding.DecodeString(req.TextBmp)
    if err != nil {
        return nil, errors.New("invalid image data")
    }
    
    // 3. OCR识别
    ocrResult, err := uc.services.HOSvc.RecognizeText(ctx, imageData)
    if err != nil {
        return nil, err
    }
    
    // 4. 文字定位和单词匹配
    nearWord, lineWords, nearIndex := uc.findNearestWord(
        ocrResult.Words, 
        req.FingerPos_2Ma,
    )
    
    // 5. 内容安全检查
    if uc.confBiz.SafetyEnabled {
        isSafe, err := uc.services.SafetySvc.CheckContent(ctx, nearWord)
        if err != nil || !isSafe {
            return nil, errors.New("content safety check failed")
        }
    }
    
    // 6. 构建响应
    reply := &pb.FingerWordsEntryReply{
        NearWord:    nearWord,
        LineWords:   lineWords,
        NearIndex:   int32(nearIndex),
        Ocr:         ocrResult.Text,
        RetryStatus: 0,
    }
    
    // 7. 设置结果和调度信息
    result := map[string]interface{}{
        "word":       nearWord,
        "confidence": ocrResult.Confidence,
        "position":   ocrResult.Position,
    }
    reply.Result, _ = structpb.NewStruct(result)
    
    dispatch := map[string]interface{}{
        "algorithm_version": uc.confBiz.AlgorithmVersion,
        "processing_time":   time.Since(startTime).Milliseconds(),
        "ocr_service":       "hybrid_ocr_v3",
    }
    reply.Dispatch, _ = structpb.NewStruct(dispatch)
    
    return reply, nil
}
```

#### 单词查询逻辑
```go
func (uc *FingerWordsUseCase) FingerWordsQuery(ctx context.Context, req *pb.FingerWordsQueryRequest) (*pb.FingerWordsQueryReply, error) {
    word := strings.ToLower(strings.TrimSpace(req.Word))
    
    // 1. 缓存查询
    if cached := uc.getFromCache(ctx, word); cached != nil {
        return cached, nil
    }
    
    // 2. 数据库查询
    wordInfo, err := uc.fingerWordsDao.GetByWord(ctx, word)
    if err != nil {
        return nil, err
    }
    
    // 3. 状态检查
    if wordInfo.Status == model.EnWordStatusCurse {
        return nil, errors.New("word is blocked")
    }
    
    // 4. 数据转换
    detail := uc.convertToWordDetail(wordInfo)
    
    // 5. AI增强(如果需要)
    if uc.shouldEnhanceWithAI(word) {
        enhanced, err := uc.enhanceWithGPT4o(ctx, word)
        if err == nil {
            detail = uc.mergeEnhancedContent(detail, enhanced)
        }
    }
    
    // 6. 视频URL加密
    if detail.VideoUrl != "" {
        detail.Speech = util.AesEncrypt(detail.VideoUrl, uc.confBiz.AesKey)
        detail.VideoUrl = ""
    }
    
    // 7. 构建响应
    detailStruct, _ := structpb.NewStruct(detail)
    reply := &pb.FingerWordsQueryReply{
        Detail: detailStruct,
    }
    
    // 8. 设置调度信息
    dispatch := map[string]interface{}{
        "source":         "database",
        "cache_hit":      false,
        "processing_time": time.Since(startTime).Milliseconds(),
    }
    reply.Dispatch, _ = structpb.NewStruct(dispatch)
    
    // 9. 缓存结果
    uc.cacheResult(ctx, word, reply)
    
    return reply, nil
}
```

### 核心算法实现

#### 手指指向单词匹配算法
```go
func (uc *FingerWordsUseCase) findNearestWord(words []OCRWord, fingerPos []int32) (string, []string, int) {
    if len(fingerPos) < 2 || len(words) == 0 {
        return "", nil, -1
    }
    
    fingerX, fingerY := int(fingerPos[0]), int(fingerPos[1])
    minDistance := math.MaxFloat64
    nearestIndex := -1
    var lineWords []string
    
    // 1. 找到手指所在的文本行
    targetLine := uc.findTargetLine(words, fingerY)
    
    // 2. 提取该行的所有单词
    for _, word := range words {
        if uc.isInSameLine(word, targetLine) {
            lineWords = append(lineWords, word.Text)
        }
    }
    
    // 3. 计算距离，找到最近的单词
    for i, word := range lineWords {
        wordCenter := uc.getWordCenter(word)
        distance := uc.calculateDistance(fingerX, fingerY, wordCenter.X, wordCenter.Y)
        
        if distance < minDistance {
            minDistance = distance
            nearestIndex = i
        }
    }
    
    if nearestIndex >= 0 && nearestIndex < len(lineWords) {
        return lineWords[nearestIndex], lineWords, nearestIndex
    }
    
    return "", lineWords, -1
}
```

#### 单词标准化处理
```go
func (uc *FingerWordsUseCase) normalizeWord(word string) string {
    // 1. 转小写
    word = strings.ToLower(word)
    
    // 2. 去除标点符号
    word = regexp.MustCompile(`[^\p{L}]`).ReplaceAllString(word, "")
    
    // 3. 处理复数形式
    if strings.HasSuffix(word, "s") && len(word) > 3 {
        singular := word[:len(word)-1]
        if uc.isValidWord(singular) {
            return singular
        }
    }
    
    // 4. 处理过去式
    if strings.HasSuffix(word, "ed") && len(word) > 4 {
        base := word[:len(word)-2]
        if uc.isValidWord(base) {
            return base
        }
    }
    
    // 5. 处理进行时
    if strings.HasSuffix(word, "ing") && len(word) > 5 {
        base := word[:len(word)-3]
        if uc.isValidWord(base) {
            return base
        }
    }
    
    return word
}
```

## 外部服务集成

### 1. 混合OCR服务
```go
type HybridOCRService struct {
    host    string
    timeout time.Duration
    client  *resty.Client
}

func (s *HybridOCRService) RecognizeText(ctx context.Context, imageData []byte) (*OCRResult, error) {
    resp, err := s.client.R().
        SetContext(ctx).
        SetFileReader("image", "image.jpg", bytes.NewReader(imageData)).
        Post("/det")
    
    if err != nil {
        return nil, err
    }
    
    var result OCRResult
    err = json.Unmarshal(resp.Body(), &result)
    return &result, err
}
```

### 2. GPT-4o增强服务
```go
func (uc *FingerWordsUseCase) enhanceWithGPT4o(ctx context.Context, word string) (*EnhancedContent, error) {
    // 1. 检查是否已有缓存
    if cached := uc.gpt4oDao.GetByWord(ctx, word); cached != nil {
        return cached, nil
    }
    
    // 2. 调用GPT-4o API
    prompt := fmt.Sprintf("Please provide a detailed explanation of the English word '%s' including pronunciation, meanings, usage examples, and etymology.", word)
    
    response, err := uc.services.LlmSvc.GenerateContent(ctx, prompt)
    if err != nil {
        return nil, err
    }
    
    // 3. 保存结果
    enhanced := &model.HwEnWordGpt4o{
        Word:    word,
        Content: response.Content,
    }
    
    err = uc.gpt4oDao.Create(ctx, enhanced)
    if err != nil {
        uc.log.WithContext(ctx).Errorf("Failed to save GPT-4o result: %v", err)
    }
    
    return &EnhancedContent{
        Word:        word,
        Content:     response.Content,
        Explanation: response.DetailedExplanation,
    }, nil
}
```

## 性能优化策略

### 1. 缓存策略
```go
// Redis缓存配置
const (
    WordCacheTTL     = 24 * time.Hour  // 单词缓存24小时
    OCRCacheTTL      = 1 * time.Hour   // OCR结果缓存1小时
    SafetyCacheTTL   = 7 * 24 * time.Hour // 安全检查缓存7天
)

func (uc *FingerWordsUseCase) cacheWordResult(ctx context.Context, word string, result *pb.FingerWordsQueryReply) {
    key := fmt.Sprintf("finger_words:query:%s", word)
    data, _ := json.Marshal(result)
    uc.rdb.Set(ctx, key, data, WordCacheTTL)
}
```

### 2. 并发优化
```go
func (uc *FingerWordsUseCase) batchProcessWords(ctx context.Context, words []string) map[string]*WordResult {
    results := make(map[string]*WordResult)
    var mu sync.Mutex
    var wg sync.WaitGroup
    
    // 使用协程池控制并发数
    semaphore := make(chan struct{}, 10)
    
    for _, word := range words {
        wg.Add(1)
        go func(w string) {
            defer wg.Done()
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            result := uc.processWord(ctx, w)
            
            mu.Lock()
            results[w] = result
            mu.Unlock()
        }(word)
    }
    
    wg.Wait()
    return results
}
```

### 3. 数据库优化
- **索引优化**: 为word字段建立唯一索引，为status和frequency建立复合索引
- **分区策略**: 按照单词首字母进行分区，提高查询效率
- **读写分离**: 查询操作使用只读副本，减少主库压力

## 监控和日志

### 1. 关键指标监控
- OCR识别成功率
- 单词匹配准确率
- 查询响应时间
- 缓存命中率
- 外部服务调用成功率

### 2. 错误处理和重试机制
```go
func (uc *FingerWordsUseCase) queryWithRetry(ctx context.Context, word string, maxRetries int) (*WordResult, error) {
    var lastErr error
    
    for i := 0; i < maxRetries; i++ {
        result, err := uc.queryWord(ctx, word)
        if err == nil {
            return result, nil
        }
        
        lastErr = err
        
        // 指数退避
        backoff := time.Duration(math.Pow(2, float64(i))) * time.Second
        time.Sleep(backoff)
        
        uc.log.WithContext(ctx).Warnf("Query retry %d/%d for word %s: %v", i+1, maxRetries, word, err)
    }
    
    return nil, fmt.Errorf("query failed after %d retries: %w", maxRetries, lastErr)
}
```

指尖查词模块通过先进的OCR技术、智能的单词匹配算法、丰富的词典数据，为用户提供了便捷高效的英文学习体验，是海外教育平台的重要组成部分。
