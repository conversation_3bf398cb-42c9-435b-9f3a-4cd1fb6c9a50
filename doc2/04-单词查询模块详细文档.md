# 单词查询模块详细文档

## 模块概述

单词查询模块基于Elasticsearch搜索引擎，提供强大的英文单词检索功能。支持多维度查询条件，包括单词本身、音标、释义、同义词、反义词等，为用户提供精准的单词搜索和学习体验。

## 技术架构

### 核心技术栈
- **搜索引擎**: Elasticsearch v7.0.32
- **数据源**: MySQL英文单词词典
- **缓存**: Redis缓存热门查询结果
- **安全**: 违禁词过滤和内容安全检查
- **加密**: AES加密敏感资源URL

### 数据流架构
```mermaid
graph TD
    A[用户查询请求] --> B[参数验证和预处理]
    B --> C[构建ES查询条件]
    C --> D[Elasticsearch搜索]
    D --> E[结果过滤和排序]
    E --> F[违禁词检查]
    F --> G[数据格式化]
    G --> H[URL加密处理]
    H --> I[缓存结果]
    I --> J[返回查询结果]
```

## API接口详细说明

### 服务定义
```protobuf
service QueryWords {
  rpc QueryWordsList (QueryWordsListRequest) returns (QueryWordsListReply);
}
```

### 单词列表查询接口

#### 接口信息
- **路径**: `POST /intelligence/v1/query_words/list`
- **功能**: 根据多种条件查询英文单词列表
- **业务场景**: 用户搜索单词、学习同义词反义词、查找相关词汇

#### 请求参数
```json
{
  "word": "beautiful",
  "query": {
    "word": "",
    "british": "/ˈbjuːtɪfl/",
    "american": "/ˈbjuːtɪfl/",
    "synonym": "lovely",
    "antonym": "ugly",
    "inflection": "beautifully",
    "prefix": "be",
    "suffix": "ful",
    "phrase": "beautiful day",
    "meaning": "pleasing to look at",
    "sentence": "beautiful sunset"
  }
}
```

#### 参数说明
- `word`: 直接查询的单词（优先级最高）
- `query`: 复合查询条件对象
  - `word`: 单词匹配
  - `british`: 英式音标匹配
  - `american`: 美式音标匹配
  - `synonym`: 同义词匹配
  - `antonym`: 反义词匹配
  - `inflection`: 变形词匹配
  - `prefix`: 前缀匹配
  - `suffix`: 后缀匹配
  - `phrase`: 短语匹配
  - `meaning`: 释义匹配
  - `sentence`: 例句匹配

#### 响应示例
```json
{
  "total": 5,
  "curse": false,
  "list": [
    {
      "score": 1.0,
      "detail": {
        "id": 12345,
        "word": "beautiful",
        "british": "/ˈbjuːtɪfl/",
        "american": "/ˈbjuːtɪfl/",
        "inflections": ["beautifully", "more beautiful", "most beautiful"],
        "prefix": "be",
        "suffix": "ful",
        "phrases": ["beautiful day", "beautiful weather"],
        "meanings": [
          {
            "part_of_speech": "adjective",
            "definition": "pleasing the senses or mind aesthetically"
          }
        ],
        "synonyms": ["lovely", "attractive", "gorgeous", "stunning"],
        "antonyms": ["ugly", "hideous", "unattractive"],
        "sentences": [
          "She has beautiful eyes.",
          "The sunset was beautiful tonight.",
          "What a beautiful garden!"
        ],
        "frequency": 8500,
        "status": 3,
        "speech": "encrypted_audio_url",
        "msg": ""
      }
    }
  ]
}
```

#### 响应字段说明
- `total`: 匹配的总数量
- `curse`: 是否包含违禁词
- `list`: 查询结果列表
  - `score`: 相关性评分
  - `detail`: 单词详细信息
    - `id`: 单词ID
    - `word`: 单词
    - `british/american`: 英式/美式音标
    - `inflections`: 变形词列表
    - `meanings`: 释义列表（包含词性和定义）
    - `synonyms/antonyms`: 同义词/反义词列表
    - `sentences`: 例句列表
    - `frequency`: 词频
    - `speech`: 加密的语音URL

## 数据模型设计

### Elasticsearch索引结构
```json
{
  "mappings": {
    "properties": {
      "id": {"type": "long"},
      "word": {
        "type": "text",
        "analyzer": "standard",
        "fields": {
          "keyword": {"type": "keyword"},
          "suggest": {"type": "completion"}
        }
      },
      "british": {"type": "keyword"},
      "american": {"type": "keyword"},
      "meanings": {
        "type": "nested",
        "properties": {
          "part_of_speech": {"type": "keyword"},
          "definition": {"type": "text", "analyzer": "english"}
        }
      },
      "synonyms": {"type": "keyword"},
      "antonyms": {"type": "keyword"},
      "sentences": {"type": "text", "analyzer": "english"},
      "inflections": {"type": "keyword"},
      "prefix": {"type": "keyword"},
      "suffix": {"type": "keyword"},
      "phrases": {"type": "text", "analyzer": "english"},
      "frequency": {"type": "long"},
      "status": {"type": "integer"}
    }
  },
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "english": {
          "type": "english"
        }
      }
    }
  }
}
```

### 数据同步策略
```go
// 从MySQL同步到Elasticsearch的数据结构
type ESWordDocument struct {
    ID          int64                `json:"id"`
    Word        string              `json:"word"`
    British     string              `json:"british"`
    American    string              `json:"american"`
    Meanings    []EnWordMeaning     `json:"meanings"`
    Synonyms    []string            `json:"synonyms"`
    Antonyms    []string            `json:"antonyms"`
    Sentences   []string            `json:"sentences"`
    Inflections []string            `json:"inflections"`
    Prefix      string              `json:"prefix"`
    Suffix      string              `json:"suffix"`
    Phrases     []string            `json:"phrases"`
    Frequency   int64               `json:"frequency"`
    Status      int                 `json:"status"`
    VideoUrl    string              `json:"video_url,omitempty"`
}
```

## 业务逻辑实现

### Service层实现
```go
type QueryWordsService struct {
    pb.UnimplementedQueryWordsServer
    uc  *biz.QueryWordsUseCase
    log *log.Helper
}

func (s *QueryWordsService) QueryWordsList(ctx context.Context, req *pb.QueryWordsListRequest) (*pb.QueryWordsListReply, error) {
    return s.uc.List(ctx, req)
}
```

### Business层核心逻辑
```go
func (uc *QueryWordsUseCase) List(ctx context.Context, req *pb.QueryWordsListRequest) (*pb.QueryWordsListReply, error) {
    var queryWhere *SearchWhereTypes
    
    // 1. 处理直接单词查询
    if req.Word != "" {
        word := strings.ToLower(req.Word)
        doc, err := uc.getDetail(ctx, word)
        if err != nil {
            return nil, err
        }
        
        // 基于单词详情构建查询条件
        queryWhere = &SearchWhereTypes{
            Query: &pb.QueryWordsListQuery{
                British:  doc.British,
                American: doc.American,
            },
        }
    }
    
    // 2. 处理复合查询条件
    if req.Query != nil {
        queryWhere = &SearchWhereTypes{
            Query: req.Query,
        }
    }
    
    // 3. 执行Elasticsearch查询
    total, esRes, err := uc.esDao.Search(
        ctx, 
        uc.confBiz.EnWordIndex, 
        uc.getListSearchWhere(queryWhere), 
        1, 
        5,
    )
    
    if err != nil {
        return nil, ai_pb.ErrorHwPaasUnexceptError("es查询错误")
    }
    
    if len(esRes) == 0 {
        return nil, ai_pb.ErrorHwPaasNotFoundError("未找到单词")
    }
    
    // 4. 构建响应
    reply := &pb.QueryWordsListReply{
        Total: int32(total),
        List:  make([]*pb.QueryWordsListItem, 0),
    }
    
    // 5. 处理查询结果
    for _, re := range esRes {
        doc := &model.EnWordItem{}
        err = jsoniter.Unmarshal(re.Source, doc)
        if err != nil || doc.Word == "" {
            continue
        }
        
        // 6. 违禁词检查
        if doc.Status == model.EnWordStatusCurse {
            reply.Total = 0
            reply.List = []*pb.QueryWordsListItem{}
            reply.Curse = true
            return reply, nil
        }
        
        // 7. URL加密处理
        if doc.VideoUrl != "" {
            doc.Speech = util.AesEncrypt(doc.VideoUrl, uc.confBiz.AesKey)
            doc.VideoUrl = ""
        }
        
        // 8. 构建结果项
        item := &pb.QueryWordsListItem{
            Score: float32(re.Score),
        }
        
        docByte, err := jsoniter.Marshal(doc)
        if err != nil {
            continue
        }
        
        st := &structpb.Struct{}
        _ = protojson.Unmarshal(docByte, st)
        item.Detail = st
        
        reply.List = append(reply.List, item)
    }
    
    return reply, nil
}
```

### 搜索查询构建
```go
func (uc *QueryWordsUseCase) getListSearchWhere(where *SearchWhereTypes) map[string]interface{} {
    if where == nil || where.Query == nil {
        return map[string]interface{}{
            "match_all": map[string]interface{}{},
        }
    }
    
    query := where.Query
    mustQueries := []map[string]interface{}{}
    
    // 1. 单词匹配
    if query.Word != "" {
        mustQueries = append(mustQueries, map[string]interface{}{
            "multi_match": map[string]interface{}{
                "query":  query.Word,
                "fields": []string{"word^3", "word.keyword^2"},
                "type":   "best_fields",
            },
        })
    }
    
    // 2. 音标匹配
    if query.British != "" {
        mustQueries = append(mustQueries, map[string]interface{}{
            "term": map[string]interface{}{
                "british": query.British,
            },
        })
    }
    
    if query.American != "" {
        mustQueries = append(mustQueries, map[string]interface{}{
            "term": map[string]interface{}{
                "american": query.American,
            },
        })
    }
    
    // 3. 同义词/反义词匹配
    if query.Synonym != "" {
        mustQueries = append(mustQueries, map[string]interface{}{
            "term": map[string]interface{}{
                "synonyms": query.Synonym,
            },
        })
    }
    
    if query.Antonym != "" {
        mustQueries = append(mustQueries, map[string]interface{}{
            "term": map[string]interface{}{
                "antonyms": query.Antonym,
            },
        })
    }
    
    // 4. 释义匹配
    if query.Meaning != "" {
        mustQueries = append(mustQueries, map[string]interface{}{
            "nested": map[string]interface{}{
                "path": "meanings",
                "query": map[string]interface{}{
                    "match": map[string]interface{}{
                        "meanings.definition": query.Meaning,
                    },
                },
            },
        })
    }
    
    // 5. 例句匹配
    if query.Sentence != "" {
        mustQueries = append(mustQueries, map[string]interface{}{
            "match": map[string]interface{}{
                "sentences": query.Sentence,
            },
        })
    }
    
    // 6. 构建最终查询
    if len(mustQueries) == 0 {
        return map[string]interface{}{
            "match_all": map[string]interface{}{},
        }
    }
    
    return map[string]interface{}{
        "bool": map[string]interface{}{
            "must": mustQueries,
            "filter": []map[string]interface{}{
                {
                    "range": map[string]interface{}{
                        "status": map[string]interface{}{
                            "gte": model.EnWordStatusWaitShelf,
                            "lte": model.EnWordStatusShelf,
                        },
                    },
                },
            },
        },
    }
}
```

### 单词详情查询
```go
func (uc *QueryWordsUseCase) getDetail(ctx context.Context, word string) (*model.EnWordItem, error) {
    // 1. 构建精确匹配查询
    searchWhere := map[string]interface{}{
        "bool": map[string]interface{}{
            "must": []map[string]interface{}{
                {
                    "term": map[string]interface{}{
                        "word.keyword": word,
                    },
                },
            },
            "filter": []map[string]interface{}{
                {
                    "range": map[string]interface{}{
                        "status": map[string]interface{}{
                            "gte": model.EnWordStatusWaitShelf,
                        },
                    },
                },
            },
        },
    }
    
    // 2. 执行查询
    _, esRes, err := uc.esDao.Search(ctx, uc.confBiz.EnWordIndex, searchWhere, 1, 1)
    if err != nil {
        return nil, ai_pb.ErrorHwPaasUnexceptError("es查询错误")
    }
    
    if len(esRes) == 0 {
        return nil, ai_pb.ErrorHwPaasNotFoundError("未找到单词")
    }
    
    // 3. 解析结果
    doc := &model.EnWordItem{}
    err = jsoniter.Unmarshal(esRes[0].Source, doc)
    if err != nil || doc.Word == "" {
        return nil, ai_pb.ErrorHwPaasNotFoundError("未找到单词")
    }
    
    return doc, nil
}
```

## 性能优化策略

### 1. Elasticsearch优化
```json
{
  "settings": {
    "index": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "refresh_interval": "30s",
      "max_result_window": 10000
    },
    "analysis": {
      "analyzer": {
        "english_analyzer": {
          "type": "custom",
          "tokenizer": "standard",
          "filter": ["lowercase", "english_stemmer", "english_stop"]
        }
      },
      "filter": {
        "english_stemmer": {
          "type": "stemmer",
          "language": "english"
        },
        "english_stop": {
          "type": "stop",
          "stopwords": "_english_"
        }
      }
    }
  }
}
```

### 2. 缓存策略
```go
const (
    QueryCacheTTL    = 1 * time.Hour    // 查询结果缓存1小时
    DetailCacheTTL   = 24 * time.Hour   // 单词详情缓存24小时
    HotWordCacheTTL  = 7 * 24 * time.Hour // 热门单词缓存7天
)

func (uc *QueryWordsUseCase) cacheQueryResult(ctx context.Context, cacheKey string, result *pb.QueryWordsListReply) {
    data, err := json.Marshal(result)
    if err != nil {
        return
    }
    
    uc.rdb.Set(ctx, cacheKey, data, QueryCacheTTL)
}

func (uc *QueryWordsUseCase) getCachedResult(ctx context.Context, cacheKey string) (*pb.QueryWordsListReply, error) {
    data, err := uc.rdb.Get(ctx, cacheKey).Result()
    if err != nil {
        return nil, err
    }
    
    var result pb.QueryWordsListReply
    err = json.Unmarshal([]byte(data), &result)
    return &result, err
}
```

### 3. 查询优化
```go
// 查询性能监控
func (uc *QueryWordsUseCase) monitorQuery(ctx context.Context, queryType string, duration time.Duration) {
    // 记录查询耗时
    uc.metrics.QueryDuration.WithLabelValues(queryType).Observe(duration.Seconds())
    
    // 慢查询告警
    if duration > 2*time.Second {
        uc.log.WithContext(ctx).Warnf("Slow query detected: type=%s, duration=%v", queryType, duration)
    }
}

// 批量查询优化
func (uc *QueryWordsUseCase) batchQuery(ctx context.Context, words []string) (map[string]*model.EnWordItem, error) {
    if len(words) == 0 {
        return nil, nil
    }
    
    // 构建批量查询
    searchWhere := map[string]interface{}{
        "bool": map[string]interface{}{
            "must": []map[string]interface{}{
                {
                    "terms": map[string]interface{}{
                        "word.keyword": words,
                    },
                },
            },
        },
    }
    
    _, esRes, err := uc.esDao.Search(ctx, uc.confBiz.EnWordIndex, searchWhere, 1, len(words))
    if err != nil {
        return nil, err
    }
    
    results := make(map[string]*model.EnWordItem)
    for _, res := range esRes {
        doc := &model.EnWordItem{}
        if err := jsoniter.Unmarshal(res.Source, doc); err == nil {
            results[doc.Word] = doc
        }
    }
    
    return results, nil
}
```

## 监控和日志

### 1. 关键指标监控
- 查询QPS和响应时间
- Elasticsearch集群健康状态
- 缓存命中率
- 违禁词拦截率
- 查询错误率

### 2. 日志记录
```go
func (uc *QueryWordsUseCase) logQuery(ctx context.Context, req *pb.QueryWordsListRequest, result *pb.QueryWordsListReply, duration time.Duration) {
    logData := map[string]interface{}{
        "query_type":    "word_list",
        "word":          req.Word,
        "has_query":     req.Query != nil,
        "result_count":  len(result.List),
        "total":         result.Total,
        "duration_ms":   duration.Milliseconds(),
        "curse_blocked": result.Curse,
    }
    
    logJson, _ := json.Marshal(logData)
    uc.log.WithContext(ctx).Infof("QueryWords: %s", string(logJson))
}
```

### 3. 错误处理
```go
func (uc *QueryWordsUseCase) handleQueryError(ctx context.Context, err error, queryType string) error {
    switch {
    case errors.Is(err, elasticsearch.ErrTimeout):
        uc.metrics.QueryTimeouts.WithLabelValues(queryType).Inc()
        return ai_pb.ErrorHwPaasTimeoutError("查询超时")
    case errors.Is(err, elasticsearch.ErrConnectionFailed):
        uc.metrics.QueryConnectionErrors.WithLabelValues(queryType).Inc()
        return ai_pb.ErrorHwPaasUnexceptError("搜索服务不可用")
    default:
        uc.metrics.QueryErrors.WithLabelValues(queryType).Inc()
        return ai_pb.ErrorHwPaasUnexceptError("查询失败")
    }
}
```

单词查询模块通过强大的Elasticsearch搜索引擎、灵活的查询条件组合、完善的缓存策略，为用户提供了快速准确的英文单词检索服务，是英语学习平台的重要基础设施。
