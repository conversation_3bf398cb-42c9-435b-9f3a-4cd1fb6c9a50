# 博客接口详细文档

## 接口概述

博客模块为海外教育平台提供完整的博客文章管理功能，包括文章列表查询、详情获取、分类管理、反馈收集和文章创建/更新等功能。该模块支持SEO优化、分类筛选、分页查询等特性。

## API接口列表

### 1. 博客列表接口

#### 接口信息
- **路径**: `GET /intelligence/api/blog/list`
- **方法**: BlogList
- **功能**: 获取博客文章列表，支持分页和分类筛选

#### 请求参数 (BlogListReq)
```json
{
  "page": 1,
  "page_size": 10,
  "category": "Math Instruction"
}
```

**参数说明**:
- `page` (int32): 页码，从1开始，默认为1
- `page_size` (int32): 每页数量，默认为10，最大100
- `category` (string): 文章分类，可选参数，为空时查询所有分类

#### 响应参数 (BlogListResp)
```json
{
  "list": [
    {
      "id": 1,
      "path": "/blog/math-tips-for-kids",
      "category": "Math Instruction",
      "article_title": "10 Math Tips for Kids",
      "short_content": "Discover effective math learning strategies that make numbers fun and engaging for children...",
      "cover_img": "https://example.com/images/math-tips-cover.jpg",
      "author_avatar": "https://example.com/avatars/sarah-johnson.jpg",
      "author_name": "Sarah Johnson",
      "article_content": "",
      "page_title": "10 Math Tips for Kids | Educational Blog",
      "meta_keywords": "math tips, kids education, learning strategies",
      "meta_description": "Learn 10 proven math tips that help kids develop strong mathematical skills while having fun.",
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T10:00:00Z"
    }
  ],
  "total": 50
}
```

**响应字段说明**:
- `list`: 文章列表数组
- `total`: 符合条件的文章总数
- `article_content`: 在列表接口中为空，节省带宽

#### 实现代码

**Service层实现**:
```go
// internal/service/ai.go
func (s *AiService) BlogList(ctx context.Context, req *pb.BlogListReq) (*pb.BlogListResp, error) {
    return s.edu.ListBlogWithPage(ctx, req)
}
```

**Business层实现**:
```go
// internal/biz/biz.edu.go
func (b *EduUseCase) ListBlogWithPage(ctx context.Context, req *pb.BlogListReq) (res *pb.BlogListResp, err error) {
    res = &pb.BlogListResp{}
    
    // 调用DAO层查询数据
    articles, total, err := b.eduDao.ListBlogWithPage(ctx, int64(req.Page), int64(req.PageSize), req.Category)
    if err != nil {
        return nil, pb.ErrorHwPaasUnexceptError("Failed to list blog articles")
    }
    
    // 转换数据格式
    res.List = make([]*pb.BlogArticle, 0)
    for _, article := range articles {
        res.List = append(res.List, &pb.BlogArticle{
            Id:              int32(article.ID),
            Path:            article.Path,
            Category:        article.Category,
            ArticleTitle:    article.ArticleTitle,
            ShortContent:    article.ShortContent,
            CoverImg:        article.CoverImg,
            AuthorAvatar:    article.AuthorAvatar,
            AuthorName:      article.AuthorName,
            PageTitle:       article.PageTitle,
            MetaKeywords:    article.MetaKeywords,
            MetaDescription: article.MetaDescription,
            CreatedAt:       article.CreatedAt.Format("2006-01-02T15:04:05Z"),
            UpdatedAt:       article.UpdatedAt.Format("2006-01-02T15:04:05Z"),
        })
    }
    res.Total = int32(total)
    
    return res, nil
}
```

**DAO层实现**:
```go
// internal/data/dao/edu.go
func (d *EduDao) ListBlogWithPage(ctx context.Context, page, pageSize int64, category string) ([]*model.BlogArticle, int64, error) {
    var list []*model.BlogArticle
    var total int64

    db := d.data.DB.WithContext(ctx).Model(&model.BlogArticle{})
    
    // 分类筛选
    if category != "" {
        db = db.Where("category = ?", category)
    }

    // 只查询当前时间之前的数据（已发布的文章）
    db = db.Where("created_at < ?", time.Now().Format("2006-01-02 15:04:05"))
    
    // 统计总数
    err := db.Count(&total).Error
    if err != nil {
        return nil, 0, err
    }
    
    // 参数验证和默认值设置
    if page <= 0 {
        page = 1
    }
    if pageSize <= 0 || pageSize > 100 {
        pageSize = 10
    }
    
    // 分页查询，按更新时间倒序
    err = db.Order("updated_at desc").
        Offset(int((page - 1) * pageSize)).
        Limit(int(pageSize)).
        Find(&list).Error
    if err != nil {
        return nil, 0, err
    }
    
    return list, total, nil
}
```

### 2. 博客详情接口

#### 接口信息
- **路径**: `GET /intelligence/api/blog/detail`
- **方法**: BlogDetail
- **功能**: 根据路径或ID获取博客文章详细内容

#### 请求参数 (BlogDetailReq)
```json
{
  "path": "/blog/math-tips-for-kids",
  "id": 0
}
```

**参数说明**:
- `path` (string): 文章路径，优先使用
- `id` (int32): 文章ID，当path为空时使用

#### 响应参数 (BlogDetailResp)
```json
{
  "article": {
    "id": 1,
    "path": "/blog/math-tips-for-kids",
    "category": "Math Instruction",
    "article_title": "10 Math Tips for Kids",
    "short_content": "Discover effective math learning strategies...",
    "cover_img": "https://example.com/images/math-tips-cover.jpg",
    "author_avatar": "https://example.com/avatars/sarah-johnson.jpg",
    "author_name": "Sarah Johnson",
    "article_content": "<h1>10 Math Tips for Kids</h1><p>Mathematics can be challenging for children...</p>",
    "page_title": "10 Math Tips for Kids | Educational Blog",
    "meta_keywords": "math tips, kids education, learning strategies",
    "meta_description": "Learn 10 proven math tips that help kids develop strong mathematical skills while having fun.",
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T10:00:00Z"
  }
}
```

#### 实现代码

**Service层实现**:
```go
func (s *AiService) BlogDetail(ctx context.Context, req *pb.BlogDetailReq) (*pb.BlogDetailResp, error) {
    return s.edu.FirstBlogWithPath(ctx, req)
}
```

**Business层实现**:
```go
func (b *EduUseCase) FirstBlogWithPath(ctx context.Context, req *pb.BlogDetailReq) (res *pb.BlogDetailResp, err error) {
    res = &pb.BlogDetailResp{}
    
    var article *model.BlogArticle
    
    // 优先使用path查询，否则使用id
    if req.Path != "" {
        article, err = b.eduDao.FirstBlogWithPath(ctx, req.Path)
    } else if req.Id > 0 {
        article, err = b.eduDao.GetBlogById(ctx, req.Id)
    } else {
        return nil, pb.ErrorHwPaasUnexceptError("Path or ID is required")
    }
    
    if err != nil {
        return nil, pb.ErrorHwPaasUnexceptError("Blog article not found")
    }
    
    // 转换数据格式
    res.Article = &pb.BlogArticle{
        Id:              int32(article.ID),
        Path:            article.Path,
        Category:        article.Category,
        ArticleTitle:    article.ArticleTitle,
        ShortContent:    article.ShortContent,
        CoverImg:        article.CoverImg,
        AuthorAvatar:    article.AuthorAvatar,
        AuthorName:      article.AuthorName,
        ArticleContent:  article.ArticleContent, // 详情接口返回完整内容
        PageTitle:       article.PageTitle,
        MetaKeywords:    article.MetaKeywords,
        MetaDescription: article.MetaDescription,
        CreatedAt:       article.CreatedAt.Format("2006-01-02T15:04:05Z"),
        UpdatedAt:       article.UpdatedAt.Format("2006-01-02T15:04:05Z"),
    }
    
    return res, nil
}
```

**DAO层实现**:
```go
// 根据路径查询
func (d *EduDao) FirstBlogWithPath(ctx context.Context, path string) (*model.BlogArticle, error) {
    var article model.BlogArticle
    err := d.data.DB.WithContext(ctx).Model(&model.BlogArticle{}).
        Where("path = ?", path).First(&article).Error
    if err != nil {
        return nil, err
    }
    return &article, nil
}

// 根据ID查询
func (d *EduDao) GetBlogById(ctx context.Context, id int32) (*model.BlogArticle, error) {
    var article model.BlogArticle
    err := d.data.DB.WithContext(ctx).Model(&model.BlogArticle{}).
        Where("id = ?", id).First(&article).Error
    if err != nil {
        return nil, err
    }
    return &article, nil
}
```

### 3. 博客分类接口

#### 接口信息
- **路径**: `GET /intelligence/api/blog/category`
- **方法**: BlogCategory
- **功能**: 获取所有博客分类列表

#### 请求参数 (BlogCategoryReq)
```json
{}
```

#### 响应参数 (BlogCategoryResp)
```json
{
  "list": [
    "Math Instruction",
    "English Learning",
    "Science Education",
    "Art & Creativity",
    "Parenting Tips"
  ]
}
```

#### 实现代码

**Service层实现**:
```go
func (s *AiService) BlogCategory(ctx context.Context, req *pb.BlogCategoryReq) (*pb.BlogCategoryResp, error) {
    return s.edu.BlogCategory(ctx, req)
}
```

**Business层实现**:
```go
func (b *EduUseCase) BlogCategory(ctx context.Context, req *pb.BlogCategoryReq) (res *pb.BlogCategoryResp, err error) {
    res = &pb.BlogCategoryResp{}
    // 从配置中获取博客分类列表
    res.List = b.confBiz.BlogCategories
    return res, nil
}
```

### 4. 博客反馈接口

#### 接口信息
- **路径**: `POST /intelligence/api/blog/feedback`
- **方法**: BlogFeedback
- **功能**: 收集用户对博客文章的反馈

#### 请求参数 (BlogFeedbackReq)
```json
{
  "id": 1,
  "feedback_type": "helpful"
}
```

**参数说明**:
- `id` (int32): 文章ID
- `feedback_type` (string): 反馈类型，如 "helpful", "not_helpful", "report"

#### 响应参数
```json
{
  "success": true,
  "message": "Feedback recorded successfully"
}
```

#### 实现代码

**Service层实现**:
```go
func (s *AiService) BlogFeedback(ctx context.Context, req *pb.BlogFeedbackReq) (*structpb.Struct, error) {
    return s.edu.BlogFeedback(ctx, req)
}
```

**Business层实现**:
```go
func (b *EduUseCase) BlogFeedback(ctx context.Context, req *pb.BlogFeedbackReq) (res *structpb.Struct, err error) {
    traceId := custom_context.GetTraceId(ctx)

    // 记录反馈信息到日志
    feedback, _ := json.Marshal(req)
    b.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(traceId, "blog", "blog_feedback", string(feedback)))
    
    // 可以在这里添加数据库存储逻辑
    // err = b.eduDao.SaveBlogFeedback(ctx, req.Id, req.FeedbackType)
    
    return nil, nil
}
```

### 5. 博客设置接口

#### 接口信息
- **路径**: `POST /intelligence/api/blog/set`
- **方法**: SetBlog
- **功能**: 创建或更新博客文章（仅测试和灰度环境）

#### 请求参数 (BlogArticle)
```json
{
  "id": 0,
  "path": "/blog/new-math-strategies",
  "category": "Math Instruction",
  "article_title": "New Math Learning Strategies",
  "short_content": "Explore innovative approaches to teaching mathematics...",
  "cover_img": "https://example.com/images/new-strategies.jpg",
  "author_avatar": "https://example.com/avatars/john-doe.jpg",
  "author_name": "John Doe",
  "article_content": "<h1>New Math Learning Strategies</h1><p>Content here...</p>",
  "page_title": "New Math Learning Strategies | Educational Blog",
  "meta_keywords": "math strategies, innovative teaching, education",
  "meta_description": "Discover new and innovative math learning strategies for modern education.",
  "created_at": "2024-01-15T10:00:00",
  "updated_at": "2024-01-15T10:00:00"
}
```

#### 响应参数
```json
{
  "success": true,
  "message": "Blog article saved successfully"
}
```

#### 实现代码

**Service层实现**:
```go
func (s *AiService) SetBlog(ctx context.Context, req *pb.BlogArticle) (*structpb.Struct, error) {
    return s.edu.SetBlog(ctx, req)
}
```

**Business层实现**:
```go
func (b *EduUseCase) SetBlog(ctx context.Context, req *pb.BlogArticle) (res *structpb.Struct, err error) {
    res = &structpb.Struct{}

    // 只有测试环境和灰度环境开启
    if !b.confBiz.BlogSetEnable {
        return nil, pb.ErrorHwPaasUnexceptError("Blog set is not enabled")
    }

    // 转换数据模型
    article := &model.BlogArticle{
        ID:              int64(req.Id),
        Path:            req.Path,
        Category:        req.Category,
        ArticleTitle:    req.ArticleTitle,
        ShortContent:    req.ShortContent,
        CoverImg:        req.CoverImg,
        AuthorAvatar:    req.AuthorAvatar,
        AuthorName:      req.AuthorName,
        ArticleContent:  req.ArticleContent,
        PageTitle:       req.PageTitle,
        MetaKeywords:    req.MetaKeywords,
        MetaDescription: req.MetaDescription,
    }

    // 时间处理
    loc, _ := time.LoadLocation("Local")
    if req.CreatedAt != "" {
        article.CreatedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", req.CreatedAt, loc)
    }
    if req.UpdatedAt != "" {
        article.UpdatedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", req.UpdatedAt, loc)
    }

    // 保存到数据库
    err = b.eduDao.SaveBlog(ctx, article)
    if err != nil {
        return nil, pb.ErrorHwPaasUnexceptError("Failed to set blog article")
    }

    return res, nil
}
```

**DAO层实现**:
```go
func (d *EduDao) SaveBlog(ctx context.Context, article *model.BlogArticle) error {
    // 使用GORM的Save方法，如果ID存在则更新，否则创建
    err := d.data.DB.WithContext(ctx).Save(article).Error
    if err != nil {
        return err
    }
    return nil
}
```

## 数据模型

### BlogArticle 数据模型
```go
type BlogArticle struct {
    ID              int64     `gorm:"column:id" json:"id"`
    Path            string    `gorm:"column:path" json:"path"`
    Category        string    `gorm:"column:category" json:"category"`
    ArticleTitle    string    `gorm:"column:article_title" json:"article_title"`
    ShortContent    string    `gorm:"column:short_content" json:"short_content"`
    CoverImg        string    `gorm:"column:cover_img" json:"cover_img"`
    AuthorAvatar    string    `gorm:"column:author_avatar" json:"author_avatar"`
    AuthorName      string    `gorm:"column:author_name" json:"author_name"`
    ArticleContent  string    `gorm:"column:article_content" json:"article_content"`
    PageTitle       string    `gorm:"column:page_title" json:"page_title"`
    MetaKeywords    string    `gorm:"column:meta_keywords" json:"meta_keywords"`
    MetaDescription string    `gorm:"column:meta_description" json:"meta_description"`
    CreatedAt       time.Time `gorm:"column:created_at;autoCreateTime:false" json:"created_at"`
    UpdatedAt       time.Time `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
}

func (BlogArticle) TableName() string {
    return "blog_articles"
}
```

## 业务特性

### 1. SEO优化
- 支持自定义页面标题、关键词和描述
- 文章路径SEO友好
- 结构化数据支持

### 2. 分类管理
- 支持多级分类
- 分类配置化管理
- 分类筛选查询

### 3. 时间控制
- 只显示当前时间之前的文章
- 支持定时发布功能
- 按更新时间排序

### 4. 安全控制
- 博客设置接口仅在特定环境开启
- 参数验证和错误处理
- 日志记录和追踪

博客接口模块通过完善的CRUD操作、SEO优化支持、分类管理功能，为海外教育平台提供了专业的博客内容管理系统，有效支撑了内容营销和用户教育需求。
