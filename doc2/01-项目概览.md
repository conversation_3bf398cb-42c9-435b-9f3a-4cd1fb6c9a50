# hw-paas-service 项目概览文档

## 项目基本信息

### 项目简介
hw-paas-service 是一个基于 Go 语言和 Kratos 微服务框架构建的海外教育智能服务平台，主要为海外学习机提供AI辅助教学功能。项目采用现代化的微服务架构，提供多种教育工具和服务。

### 技术栈概览
- **编程语言**: Go 1.23.0
- **微服务框架**: Kratos v2.7.3
- **依赖注入**: Google Wire v0.5.0
- **API定义**: Protocol Buffers + gRPC + HTTP
- **数据库**: MySQL (GORM v1.25.8)
- **缓存**: Redis v9.5.1
- **搜索引擎**: Elasticsearch v7.0.32
- **配置中心**: Nacos
- **监控**: Prometheus + OpenTelemetry
- **存储**: Azure Blob Storage
- **日志**: Zap + Lumberjack

### 项目结构
```
hw-paas-service/
├── api/                    # API定义（protobuf）
│   ├── ai/v1/             # AI服务接口
│   ├── comment/v1/        # 评论服务接口
│   ├── eduadmin/v1/       # 管理后台接口
│   ├── finger_words/v1/   # 指尖查词接口
│   ├── query_words/v1/    # 单词查询接口
│   ├── reading_book/v1/   # 阅读绘本接口
│   ├── resource/v1/       # 资源管理接口
│   └── skill/v1/          # 技能管理接口
├── cmd/hw-paas-service/   # 应用入口
├── configs/               # 配置文件
├── internal/              # 内部代码
│   ├── biz/              # 业务逻辑层
│   ├── data/             # 数据访问层
│   ├── service/          # 服务层
│   ├── server/           # 服务器配置
│   ├── conf/             # 配置结构定义
│   ├── common/           # 公共常量
│   └── pkg/              # 内部工具包
├── pkg/                   # 公共工具包
├── third_party/          # 第三方proto文件
├── doc/                  # 原有文档
├── doc2/                 # 新生成文档
├── Dockerfile            # Docker构建文件
├── Makefile             # 构建脚本
├── go.mod               # Go模块定义
└── README.md            # 项目说明
```

## 架构设计

### 分层架构
项目采用经典的四层架构设计：

1. **API层 (api/)**
   - 使用 Protocol Buffers 定义服务接口
   - 支持 HTTP 和 gRPC 双协议
   - 自动生成客户端和服务端代码

2. **Service层 (internal/service/)**
   - 处理 HTTP/gRPC 请求
   - 参数验证和转换
   - 调用业务逻辑层

3. **Business层 (internal/biz/)**
   - 核心业务逻辑处理
   - 业务规则实现
   - 跨模块业务协调

4. **Data层 (internal/data/)**
   - 数据访问抽象
   - 数据库操作
   - 外部服务调用
   - 缓存管理

### 依赖注入
使用 Google Wire 进行依赖注入：
- 通过 ProviderSet 定义依赖关系
- 自动生成依赖注入代码
- 确保各层之间的解耦
- 支持接口和实现的分离

### 服务发现与配置
- **本地配置**: config.yaml 基础配置
- **Nacos配置中心**: 动态配置管理
- **环境变量**: 敏感信息配置
- **配置热更新**: 支持运行时配置更新

## 核心功能模块

### 1. AI服务模块 (ai)
**主要功能**:
- 口算批改：数学题图片识别和自动批改
- 英文纠错：英文文本错误检测和修正
- 题目管理：AI题库的增删改查和导入导出
- 博客管理：教育博客内容管理和SEO优化
- 用户认证：基于UCenter的用户登录和权限管理

**核心接口**:
- `POST /intelligence/api/ai/v1/query_correct` - 口算批改
- `POST /intelligence/api/ai/v2/feedback/trace` - 反馈追踪
- `GET /intelligence/api/ai/v1/questions` - 题目列表
- `GET /intelligence/api/blog/list` - 博客列表

### 2. 指尖查词模块 (finger_words)
**主要功能**:
- OCR文字识别：识别图片中的英文文字
- 单词定位：精确定位用户手指指向的单词
- 词典查询：提供详细的单词释义和例句

**核心接口**:
- `POST /intelligence/v1/finger_words/entry` - 指尖查词入口
- `POST /intelligence/v1/finger_words/query` - 单词查询

### 3. 单词查询模块 (query_words)
**主要功能**:
- 英文单词检索：基于Elasticsearch的全文搜索
- 同义词/反义词查询：语义相关词汇推荐
- 词频统计：单词使用频率分析

**核心接口**:
- `POST /intelligence/v1/query_words/list` - 单词列表查询

### 4. 阅读绘本模块 (reading_book)
**主要功能**:
- 句子识别：识别绘本中的英文句子
- 语音评测：英文发音评测和打分

**核心接口**:
- `POST /intelligence/v1/sentence_identify` - 句子识别

### 5. 资源管理模块 (resource)
**主要功能**:
- 教育资源管理：工作表、视频、涂色页等资源
- 分类筛选：按年级、学科、主题分类
- 元数据管理：SEO优化和内容标签

**核心接口**:
- `GET /intelligence/api/v1/resource/list` - 资源列表
- `GET /intelligence/api/v1/resource/detail` - 资源详情
- `GET /intelligence/api/v1/resource/grades` - 年级列表

### 6. 评论系统模块 (comment)
**主要功能**:
- 多级评论：支持评论和回复的树形结构
- 评论管理：评论的增删改查

**核心接口**:
- `GET /intelligence/api/comment/v1/comments` - 评论列表

### 7. 技能管理模块 (skill)
**主要功能**:
- RN版本控制：React Native应用版本管理
- 技能包管理：技能相关资源包管理

**核心接口**:
- `GET /intelligence/v1/skill/rns` - RN列表

### 8. 管理后台模块 (eduadmin)
**主要功能**:
- 数据导入：Excel文件数据导入
- 系统管理：后台管理功能
- SQL执行：数据库脚本执行

**核心接口**:
- `GET /intelligence/v1/api/edu-admin/import-term` - 导入教育术语
- `GET /intelligence/v1/api/edu-admin/execute-sql-file` - 执行SQL文件

## 外部服务集成

### AI服务
- **口算批改服务**: 数学题目识别和批改
- **OCR服务**: 图片文字识别
- **英文纠错服务**: 英文语法和拼写检查
- **语音评测服务**: 英文发音评测

### 基础服务
- **文件上传服务**: Base64图片上传
- **内容安全服务**: 内容审核和过滤
- **用户中心**: UCenter用户管理集成
- **任务驱动服务**: 新手任务引导

### 第三方平台
- **Azure Blob Storage**: 文件存储
- **八斗平台**: 教育内容平台集成
- **Nacos**: 配置中心和服务发现

## 数据库设计概览

### 核心数据表
- `hw_en_word`: 英文单词词典表
- `hw_en_word_gpt4o`: GPT-4o增强单词表
- `ai_jzx_question`: AI题目库表
- `blog_articles`: 博客文章表
- `edu_resources`: 教育资源表
- `edu_term`: 教育术语表
- `comments`: 评论系统表
- `feedback_trace`: 反馈追踪表
- `account`: 用户账户表
- `apx_async_task`: 异步任务表

### 数据存储策略
- **MySQL**: 主要业务数据存储
- **Redis**: 缓存和会话管理
- **Elasticsearch**: 英文单词全文搜索
- **Azure Blob**: 文件和媒体资源存储

## 部署和运维

### 容器化部署
- **Docker**: 使用多阶段构建优化镜像大小
- **端口配置**: HTTP(8002) + gRPC(9002)
- **配置挂载**: 支持外部配置文件挂载

### 配置管理
- **本地开发**: 使用 config.yaml 本地配置
- **生产环境**: 使用 Nacos 配置中心
- **环境隔离**: 支持多环境配置管理

### 监控和日志
- **Prometheus**: 业务指标监控
- **OpenTelemetry**: 分布式链路追踪
- **结构化日志**: 统一日志格式和分析
- **健康检查**: 服务健康状态监控

### 构建和部署流程
1. **代码生成**: `make all` 生成proto代码
2. **依赖注入**: `wire` 生成依赖注入代码
3. **构建**: `make build` 编译二进制文件
4. **容器化**: `docker build` 构建镜像
5. **部署**: 容器编排部署到目标环境

## 性能优化

### 缓存策略
- **多级缓存**: 本地缓存 + Redis缓存
- **缓存预热**: 系统启动时预加载热点数据
- **缓存更新**: 支持缓存刷新和失效策略

### 数据库优化
- **索引优化**: 针对查询模式优化索引
- **查询优化**: SQL查询性能优化
- **连接池**: 数据库连接池管理

### 并发处理
- **协程池**: 控制并发协程数量
- **限流**: 接口访问频率限制
- **熔断**: 服务故障自动熔断保护

这个项目展现了现代Go微服务架构的最佳实践，通过清晰的分层设计、完善的依赖注入、丰富的外部服务集成，为海外教育场景提供了全面的AI辅助教学解决方案。
