# 部署运维文档

## 部署架构概览

hw-paas-service项目采用容器化部署方案，支持本地开发、测试环境和生产环境的多环境部署。通过Docker容器化、配置中心管理、服务发现等技术，实现了高可用、可扩展的部署架构。

## 部署架构图

```mermaid
graph TD
    A[负载均衡器] --> B[hw-paas-service实例1]
    A --> C[hw-paas-service实例2]
    A --> D[hw-paas-service实例N]
    
    B --> E[MySQL主库]
    C --> E
    D --> E
    
    B --> F[MySQL从库]
    C --> F
    D --> F
    
    B --> G[Redis集群]
    C --> G
    D --> G
    
    B --> H[Elasticsearch集群]
    C --> H
    D --> H
    
    I[Nacos配置中心] --> B
    I --> C
    I --> D
    
    J[Prometheus监控] --> B
    J --> C
    J --> D
    
    K[Azure Blob Storage] --> B
    K --> C
    K --> D
```

## 环境要求

### 1. 基础环境
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Go**: 1.23.0+ (开发环境)
- **Git**: 2.30+

### 2. 硬件要求

#### 开发环境
- **CPU**: 2核心
- **内存**: 4GB
- **存储**: 20GB SSD
- **网络**: 100Mbps

#### 测试环境
- **CPU**: 4核心
- **内存**: 8GB
- **存储**: 50GB SSD
- **网络**: 1Gbps

#### 生产环境
- **CPU**: 8核心+
- **内存**: 16GB+
- **存储**: 100GB+ SSD
- **网络**: 10Gbps

### 3. 依赖服务
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Elasticsearch**: 7.0+
- **Nacos**: 2.0+

## 构建和打包

### 1. 代码生成
```bash
# 生成protobuf代码
make api

# 生成依赖注入代码
make wire

# 生成所有代码
make all
```

### 2. 本地构建
```bash
# 构建二进制文件
make build

# 构建并运行
make run

# 运行测试
make test
```

### 3. Docker构建

#### Dockerfile解析

```dockerfile
# 多阶段构建，优化镜像大小
FROM golang:1.23-alpine AS builder

# 设置工作目录
WORKDIR /src

# 复制依赖文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY .. .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o app ./cmd/hw-paas-service

# 运行阶段
FROM alpine:latest

# 安装必要工具
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建应用用户
RUN addgroup -g 1001 app && adduser -D -s /bin/sh -u 1001 -G app app

# 设置工作目录
WORKDIR /app

# 复制二进制文件
COPY --from=builder /src/app .
COPY --from=builder /src/configs ./configs

# 设置文件权限
RUN chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8002 9002

# 启动命令
CMD ["./app", "-conf", "./configs"]
```

#### 构建镜像
```bash
# 构建镜像
docker build -t hw-paas-service:latest .

# 构建指定版本
docker build -t hw-paas-service:v1.0.0 .

# 多平台构建
docker buildx build --platform linux/amd64,linux/arm64 -t hw-paas-service:latest .
```

## 配置管理

### 1. 配置文件结构
```yaml
# configs/config.yaml
server:
  http:
    addr: 0.0.0.0:8002
    timeout: 30s
  grpc:
    addr: 0.0.0.0:9002
    timeout: 30s

data:
  database:
    driver: mysql
    source: ${DB_SOURCE}
  redis:
    addr: ${REDIS_ADDR}
    password: ${REDIS_PASSWORD}
    db: 0

nacos:
  server_configs:
    - ip_addr: ${NACOS_HOST}
      port: ${NACOS_PORT}
  client_config:
    namespace_id: ${NACOS_NAMESPACE}
    group: ${NACOS_GROUP}
    data_id: ${NACOS_DATA_ID}
```

### 2. 环境变量配置
```bash
# 数据库配置
export DB_SOURCE="user:password@tcp(mysql:3306)/hw_paas?charset=utf8mb4&parseTime=True&loc=Local"

# Redis配置
export REDIS_ADDR="redis:6379"
export REDIS_PASSWORD="your_redis_password"

# Nacos配置
export NACOS_HOST="nacos"
export NACOS_PORT="8848"
export NACOS_NAMESPACE="hw-paas"
export NACOS_GROUP="DEFAULT_GROUP"
export NACOS_DATA_ID="hw-paas-service.yaml"

# 外部服务配置
export AI_SERVICE_HOST="https://ai-service.example.com"
export OCR_SERVICE_HOST="https://ocr-service.example.com"
export AZURE_STORAGE_ACCOUNT="your_storage_account"
export AZURE_STORAGE_KEY="your_storage_key"
```

### 3. Nacos配置中心
```yaml
# Nacos中的配置内容
ai_service:
  host: "https://ai-service.example.com"
  timeout: 30s
  retry_count: 3

ocr_service:
  host: "https://ocr-service.example.com"
  timeout: 15s

content_safety:
  enabled: true
  host: "https://content-safety.example.com"

cache:
  ttl:
    word_query: 3600
    resource_list: 7200
    comment_list: 300

rate_limit:
  ocr_requests_per_second: 10
  ai_requests_per_minute: 100
```

## Docker Compose部署

### 1. 开发环境
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  hw-paas-service:
    build: .
    ports:
      - "8002:8002"
      - "9002:9002"
    environment:
      - DB_SOURCE=root:password@tcp(mysql:3306)/hw_paas?charset=utf8mb4&parseTime=True&loc=Local
      - REDIS_ADDR=redis:6379
      - NACOS_HOST=nacos
      - NACOS_PORT=8848
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - ./configs:/app/configs
      - ./logs:/app/logs

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: hw_paas
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nacos:
    image: nacos/nacos-server:v2.2.0
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: password
    ports:
      - "8848:8848"
    depends_on:
      - mysql

  elasticsearch:
    image: elasticsearch:7.17.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data

volumes:
  mysql_data:
  redis_data:
  es_data:
```

### 2. 生产环境
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  hw-paas-service:
    image: hw-paas-service:v1.0.0
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '1'
          memory: 2G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    ports:
      - "8002:8002"
      - "9002:9002"
    environment:
      - DB_SOURCE=${DB_SOURCE}
      - REDIS_ADDR=${REDIS_ADDR}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - NACOS_HOST=${NACOS_HOST}
    networks:
      - hw-paas-network
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - hw-paas-service
    networks:
      - hw-paas-network

networks:
  hw-paas-network:
    driver: overlay
```

## Kubernetes部署

### 1. Deployment配置
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hw-paas-service
  namespace: hw-paas
spec:
  replicas: 3
  selector:
    matchLabels:
      app: hw-paas-service
  template:
    metadata:
      labels:
        app: hw-paas-service
    spec:
      containers:
      - name: hw-paas-service
        image: hw-paas-service:v1.0.0
        ports:
        - containerPort: 8002
          name: http
        - containerPort: 9002
          name: grpc
        env:
        - name: DB_SOURCE
          valueFrom:
            secretKeyRef:
              name: hw-paas-secret
              key: db-source
        - name: REDIS_ADDR
          value: "redis-service:6379"
        - name: NACOS_HOST
          value: "nacos-service"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8002
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 2. Service配置
```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: hw-paas-service
  namespace: hw-paas
spec:
  selector:
    app: hw-paas-service
  ports:
  - name: http
    port: 80
    targetPort: 8002
  - name: grpc
    port: 9002
    targetPort: 9002
  type: ClusterIP
```

### 3. ConfigMap配置
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: hw-paas-config
  namespace: hw-paas
data:
  config.yaml: |
    server:
      http:
        addr: 0.0.0.0:8002
        timeout: 30s
      grpc:
        addr: 0.0.0.0:9002
        timeout: 30s
    data:
      redis:
        addr: redis-service:6379
        db: 0
```

## 监控和日志

### 1. Prometheus监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'hw-paas-service'
    static_configs:
      - targets: ['hw-paas-service:8002']
    metrics_path: /metrics
    scrape_interval: 10s

  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

### 2. Grafana仪表板
```json
{
  "dashboard": {
    "title": "hw-paas-service监控",
    "panels": [
      {
        "title": "QPS",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{path}}"
          }
        ]
      },
      {
        "title": "响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "错误率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      }
    ]
  }
}
```

### 3. 日志收集
```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /app/logs/*.log
  fields:
    service: hw-paas-service
  fields_under_root: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "hw-paas-service-%{+yyyy.MM.dd}"

logging.level: info
```

## 运维脚本

### 1. 启动脚本
```bash
#!/bin/bash
# start.sh

set -e

echo "Starting hw-paas-service..."

# 检查环境变量
if [ -z "$DB_SOURCE" ]; then
    echo "Error: DB_SOURCE environment variable is not set"
    exit 1
fi

# 等待依赖服务
echo "Waiting for dependencies..."
./wait-for-it.sh mysql:3306 -t 60
./wait-for-it.sh redis:6379 -t 60
./wait-for-it.sh nacos:8848 -t 60

# 启动应用
echo "Starting application..."
exec ./app -conf ./configs
```

### 2. 健康检查脚本
```bash
#!/bin/bash
# health-check.sh

HTTP_PORT=${HTTP_PORT:-8002}
GRPC_PORT=${GRPC_PORT:-9002}

# HTTP健康检查
http_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:${HTTP_PORT}/health)
if [ "$http_status" != "200" ]; then
    echo "HTTP health check failed: $http_status"
    exit 1
fi

# gRPC健康检查
grpc_status=$(grpc_health_probe -addr=localhost:${GRPC_PORT})
if [ $? -ne 0 ]; then
    echo "gRPC health check failed"
    exit 1
fi

echo "Health check passed"
exit 0
```

### 3. 部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

VERSION=${1:-latest}
ENVIRONMENT=${2:-dev}

echo "Deploying hw-paas-service:$VERSION to $ENVIRONMENT environment..."

# 拉取最新镜像
docker pull hw-paas-service:$VERSION

# 停止旧容器
docker-compose -f docker-compose.$ENVIRONMENT.yml down

# 启动新容器
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d

# 等待服务启动
sleep 30

# 健康检查
./health-check.sh

echo "Deployment completed successfully"
```

## 故障排查

### 1. 常见问题

#### 服务启动失败
```bash
# 检查日志
docker logs hw-paas-service

# 检查配置
docker exec hw-paas-service cat /app/configs/config.yaml

# 检查环境变量
docker exec hw-paas-service env | grep -E "(DB_|REDIS_|NACOS_)"
```

#### 数据库连接失败
```bash
# 检查数据库连接
docker exec hw-paas-service nc -zv mysql 3306

# 检查数据库状态
docker exec mysql mysql -u root -p -e "SHOW PROCESSLIST;"
```

#### Redis连接失败
```bash
# 检查Redis连接
docker exec hw-paas-service nc -zv redis 6379

# 检查Redis状态
docker exec redis redis-cli ping
```

### 2. 性能调优

#### JVM参数优化
```bash
# 设置合适的堆内存
export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC"
```

#### 数据库连接池优化
```yaml
data:
  database:
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600s
```

#### Redis连接池优化
```yaml
data:
  redis:
    pool_size: 100
    min_idle_conns: 10
    max_retries: 3
```

通过完善的部署方案、监控体系和运维脚本，hw-paas-service项目能够在各种环境中稳定运行，并提供高可用的服务保障。
