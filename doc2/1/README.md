# hw-paas-service 项目文档

## 文档概述

本文档集为hw-paas-service海外教育智能服务平台提供了全面的技术文档，涵盖了项目架构、功能模块、API接口、数据库设计、部署运维等各个方面。

## 项目简介

hw-paas-service是一个基于Go语言和Kratos微服务框架构建的海外教育智能服务平台，主要为海外学习机提供AI辅助教学功能。项目采用现代化的微服务架构，提供多种教育工具和服务，包括：

- **AI口算批改**: 智能识别和批改数学题目
- **指尖查词**: OCR识别用户指向的英文单词并提供详细释义
- **单词查询**: 基于Elasticsearch的强大英文单词检索
- **阅读绘本**: 英文绘本句子识别和语音评测
- **资源管理**: 教育资源的分类管理和智能推荐
- **评论系统**: 多级评论和社交互动功能
- **管理后台**: 数据导入和系统管理功能

## 技术栈

- **编程语言**: Go 1.23.0
- **微服务框架**: Kratos v2.7.3
- **API定义**: Protocol Buffers + gRPC + HTTP
- **数据库**: MySQL 8.0 + Redis 6.0 + Elasticsearch 7.0
- **配置中心**: Nacos
- **监控**: Prometheus + Grafana + OpenTelemetry
- **部署**: Docker + Kubernetes
- **存储**: Azure Blob Storage

## 文档目录

### 1. [项目概览](01-项目概览.md)
- 项目基本信息和技术栈介绍
- 系统架构设计和分层结构
- 核心功能模块概述
- 外部服务集成说明
- 性能优化和监控策略

### 2. [AI服务模块详细文档](02-AI服务模块详细文档.md)
- 口算批改功能实现
- 英文纠错服务集成
- 题目管理系统
- 博客管理功能
- 用户认证和权限控制
- 反馈追踪机制

### 3. [指尖查词模块详细文档](03-指尖查词模块详细文档.md)
- OCR文字识别技术
- 手指指向单词匹配算法
- 词典数据查询和缓存
- AI增强解释功能
- 内容安全检查机制

### 4. [单词查询模块详细文档](04-单词查询模块详细文档.md)
- Elasticsearch搜索引擎配置
- 多维度查询条件支持
- 搜索结果排序和过滤
- 违禁词检查和处理
- 查询性能优化策略

### 5. [阅读绘本模块详细文档](05-阅读绘本模块详细文档.md)
- 绘本OCR识别服务
- 句子定位和提取算法
- 语音评测功能集成
- WebSocket实时通信
- 评分等级映射机制

### 6. [资源管理模块详细文档](06-资源管理模块详细文档.md)
- 教育资源分类体系
- 多维度筛选和查询
- SEO优化和元数据管理
- 随机推荐算法
- 缓存策略和性能优化

### 7. [评论系统模块详细文档](07-评论系统模块详细文档.md)
- 多级评论树形结构
- 点赞和回复功能
- 用户提及和通知
- 内容审核和管理
- 实时交互体验

### 8. [管理后台模块详细文档](08-管理后台模块详细文档.md)
- Excel数据批量导入
- SQL脚本安全执行
- 系统配置管理
- 操作审计和日志
- 权限控制机制

### 9. [数据库设计文档](09-数据库设计文档.md)
- 完整的表结构设计
- 索引策略和性能优化
- 数据关系和约束
- 分区和备份策略
- 查询优化建议

### 10. [部署运维文档](10-部署运维文档.md)
- Docker容器化部署
- Kubernetes集群部署
- 配置管理和环境变量
- 监控和日志收集
- 故障排查和性能调优

## 快速开始

### 1. 环境准备
```bash
# 安装Go 1.23.0+
go version

# 安装Docker和Docker Compose
docker --version
docker-compose --version

# 克隆项目
git clone <repository-url>
cd hw-paas-service
```

### 2. 本地开发
```bash
# 生成代码
make all

# 启动依赖服务
docker-compose -f docker-compose.dev.yml up -d mysql redis nacos elasticsearch

# 配置环境变量
export DB_SOURCE="root:password@tcp(localhost:3306)/hw_paas?charset=utf8mb4&parseTime=True&loc=Local"
export REDIS_ADDR="localhost:6379"
export NACOS_HOST="localhost"

# 运行服务
make run
```

### 3. 接口测试
```bash
# 健康检查
curl http://localhost:8002/health

# 测试AI服务
curl -X POST http://localhost:8002/intelligence/api/ai/v1/query_correct \
  -H "Content-Type: application/json" \
  -d '{"image_url": "https://example.com/math.jpg"}'

# 测试指尖查词
curl -X POST http://localhost:8002/intelligence/v1/finger_words/entry \
  -H "Content-Type: application/json" \
  -d '{"text_bmp": "base64_image_data", "finger_pos_2ma": [100, 200]}'
```

## API文档

### 主要接口端点

#### AI服务
- `POST /intelligence/api/ai/v1/query_correct` - 口算批改
- `POST /intelligence/api/ai/v2/feedback/trace` - 反馈追踪
- `GET /intelligence/api/ai/v1/questions` - 题目列表
- `GET /intelligence/api/blog/list` - 博客列表

#### 指尖查词
- `POST /intelligence/v1/finger_words/entry` - 指尖查词入口
- `POST /intelligence/v1/finger_words/query` - 单词查询

#### 单词查询
- `POST /intelligence/v1/query_words/list` - 单词列表查询

#### 阅读绘本
- `POST /intelligence/v1/sentence_identify` - 句子识别

#### 资源管理
- `GET /intelligence/api/v1/resource/list` - 资源列表
- `GET /intelligence/api/v1/resource/detail` - 资源详情
- `GET /intelligence/api/v1/resource/grades` - 年级列表

#### 评论系统
- `GET /intelligence/api/comment/v1/comments` - 评论列表
- `POST /intelligence/api/comment/v1/comments` - 发布评论

### 接口认证
部分接口需要用户认证，通过以下方式进行：
```bash
# 在请求头中添加认证信息
Authorization: Bearer <token>
```

## 配置说明

### 环境变量
```bash
# 数据库配置
DB_SOURCE="user:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local"

# Redis配置
REDIS_ADDR="host:port"
REDIS_PASSWORD="password"

# Nacos配置
NACOS_HOST="nacos-host"
NACOS_PORT="8848"
NACOS_NAMESPACE="namespace"

# 外部服务
AI_SERVICE_HOST="https://ai-service.example.com"
OCR_SERVICE_HOST="https://ocr-service.example.com"
```

### 配置文件
主要配置文件位于 `configs/config.yaml`，包含：
- 服务端口配置
- 数据库连接配置
- 外部服务配置
- 缓存配置
- 日志配置

## 监控和运维

### 监控指标
- **业务指标**: QPS、响应时间、错误率
- **系统指标**: CPU、内存、磁盘、网络
- **数据库指标**: 连接数、查询性能、慢查询
- **缓存指标**: 命中率、内存使用、连接数

### 日志管理
- **结构化日志**: 使用JSON格式统一日志结构
- **日志级别**: DEBUG、INFO、WARN、ERROR
- **日志轮转**: 按大小和时间自动轮转
- **日志收集**: 通过Filebeat收集到Elasticsearch

### 告警配置
- **服务可用性**: 服务下线、健康检查失败
- **性能指标**: 响应时间过长、错误率过高
- **资源使用**: CPU、内存、磁盘使用率过高
- **业务指标**: 关键业务流程异常

## 开发规范

### 代码规范
- 遵循Go官方代码规范
- 使用gofmt格式化代码
- 添加必要的注释和文档
- 编写单元测试和集成测试

### Git工作流
- 使用Git Flow分支模型
- 提交信息遵循Conventional Commits规范
- 代码审查通过后才能合并
- 保持提交历史清晰

### 版本管理
- 使用语义化版本号(Semantic Versioning)
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

## 贡献指南

1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证，详情请参阅 [LICENSE](../../LICENSE) 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: [GitHub Issues](https://github.com/your-org/hw-paas-service/issues)
- 邮箱: <EMAIL>
- 文档更新: 请提交PR到doc2目录

---

*最后更新时间: 2024-01-15*
*文档版本: v1.0.0*
