# 管理后台模块详细文档

## 模块概述

管理后台模块(EduAdmin)为海外教育平台提供强大的后台管理功能，包括数据导入、SQL脚本执行、系统配置管理等核心管理功能。该模块主要面向系统管理员和运维人员，提供高效的数据管理和系统维护工具。

## 技术架构

### 核心功能
- **数据导入管理**: Excel文件批量导入教育术语和资源数据
- **SQL脚本执行**: 安全的数据库脚本执行和管理
- **系统配置**: 平台参数配置和功能开关管理
- **数据同步**: 与外部系统的数据同步和更新
- **操作审计**: 管理员操作记录和审计日志

### 安全机制
```mermaid
graph TD
    A[管理员请求] --> B[身份认证]
    B --> C[权限验证]
    C --> D[操作授权]
    D --> E[参数验证]
    E --> F[安全检查]
    F --> G[执行操作]
    G --> H[操作日志]
    H --> I[结果返回]
```

## API接口详细说明

### 服务定义
```protobuf
service EduAdmin {
  // 数据导入接口
  rpc ImportTerm (google.protobuf.Empty) returns (google.protobuf.Struct);
  
  // SQL脚本执行
  rpc ExecuteSqlFile (google.protobuf.Empty) returns (google.protobuf.Struct);
  
  // 系统配置管理
  rpc UpdateSystemConfig (UpdateSystemConfigRequest) returns (UpdateSystemConfigReply);
  
  // 数据同步
  rpc SyncExternalData (SyncExternalDataRequest) returns (SyncExternalDataReply);
  
  // 操作日志查询
  rpc GetOperationLogs (GetOperationLogsRequest) returns (GetOperationLogsReply);
}
```

### 1. 教育术语导入接口

#### 接口信息
- **路径**: `GET /intelligence/v1/api/edu-admin/import-term`
- **功能**: 从预定义的Excel文件导入教育术语数据
- **权限**: 需要管理员权限
- **业务场景**: 批量导入新的教育术语和知识点分类

#### 请求参数
无需参数，使用预配置的文件路径

#### 响应示例
```json
{
  "success": true,
  "message": "Import completed successfully",
  "data": {
    "total_processed": 1250,
    "successful_imports": 1200,
    "failed_imports": 50,
    "duplicate_skipped": 25,
    "processing_time": "45.2s",
    "import_summary": {
      "math_terms": 600,
      "english_terms": 400,
      "science_terms": 200,
      "other_terms": 50
    },
    "failed_records": [
      {
        "row": 15,
        "term": "invalid_term",
        "error": "Missing required field: knowledge_1"
      },
      {
        "row": 23,
        "term": "duplicate_term",
        "error": "Term already exists"
      }
    ]
  }
}
```

#### 导入数据格式
Excel文件应包含以下列：
- `path`: 术语路径
- `subject`: 学科
- `term`: 术语名称
- `knowledge_1`: 一级知识点
- `knowledge_2`: 二级知识点
- `meta_description`: SEO描述
- `title`: 标题
- `tag`: 标签(逗号分隔)
- `module_type`: 模块类型
- `module_index`: 模块索引
- `block_type`: 块类型
- `block_order`: 块顺序
- `block_content`: 块内容

### 2. SQL脚本执行接口

#### 接口信息
- **路径**: `GET /intelligence/v1/api/edu-admin/execute-sql-file`
- **功能**: 执行预定义的SQL脚本文件
- **权限**: 需要超级管理员权限
- **安全限制**: 只能执行白名单中的SQL文件

#### 请求参数
```json
{
  "script_name": "update_resource_metadata.sql",
  "dry_run": false,
  "confirm_token": "admin_confirm_12345"
}
```

#### 参数说明
- `script_name`: SQL脚本文件名（必须在白名单中）
- `dry_run`: 是否为试运行模式，不实际执行
- `confirm_token`: 确认令牌，防止误操作

#### 响应示例
```json
{
  "success": true,
  "message": "SQL script executed successfully",
  "data": {
    "script_name": "update_resource_metadata.sql",
    "execution_time": "12.5s",
    "affected_rows": 3456,
    "statements_executed": 15,
    "warnings": [],
    "execution_log": [
      {
        "statement": "UPDATE edu_resources SET meta_title = CONCAT(title, ' | Free Educational Resource') WHERE meta_title = ''",
        "affected_rows": 1200,
        "execution_time": "2.3s"
      },
      {
        "statement": "UPDATE edu_resources SET meta_keywords = CONCAT(subject, ',', grade, ',', resource_type) WHERE meta_keywords = ''",
        "affected_rows": 2256,
        "execution_time": "4.1s"
      }
    ]
  }
}
```

#### 安全机制
```go
// SQL脚本白名单
var allowedSQLScripts = map[string]bool{
    "update_resource_metadata.sql":     true,
    "cleanup_expired_sessions.sql":     true,
    "optimize_database_indexes.sql":    true,
    "migrate_legacy_data.sql":          true,
}

// 危险SQL关键词检查
var dangerousKeywords = []string{
    "DROP", "DELETE", "TRUNCATE", "ALTER", 
    "CREATE USER", "GRANT", "REVOKE",
}
```

### 3. 系统配置管理接口

#### 接口信息
- **路径**: `POST /intelligence/v1/api/edu-admin/system-config`
- **功能**: 更新系统配置参数
- **权限**: 需要管理员权限

#### 请求参数
```json
{
  "config_updates": {
    "ai_service_enabled": true,
    "max_comment_level": 5,
    "content_safety_enabled": true,
    "cache_ttl_hours": 24,
    "rate_limit_per_minute": 100,
    "maintenance_mode": false
  }
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "System configuration updated successfully",
  "updated_configs": [
    "ai_service_enabled",
    "max_comment_level",
    "content_safety_enabled"
  ],
  "restart_required": false
}
```

## 业务逻辑实现

### Service层实现
```go
type EduAdminService struct {
    pb.UnimplementedEduAdminServer
    uc  *biz.EduAdminUseCase
    log *log.Helper
}

func (s *EduAdminService) ImportTerm(ctx context.Context, req *emptypb.Empty) (*structpb.Struct, error) {
    return s.uc.ImportTerm(ctx)
}

func (s *EduAdminService) ExecuteSqlFile(ctx context.Context, req *emptypb.Empty) (*structpb.Struct, error) {
    return s.uc.ExecuteSqlFile(ctx)
}
```

### Business层核心逻辑

#### 教育术语导入
```go
func (uc *EduAdminUseCase) ImportTerm(ctx context.Context) (*structpb.Struct, error) {
    startTime := time.Now()
    
    // 1. 权限检查
    if err := uc.checkAdminPermission(ctx); err != nil {
        return nil, fmt.Errorf("permission denied: %w", err)
    }
    
    // 2. 获取Excel文件路径
    filePath := uc.confBiz.EduAdmin.TermImportFile
    if filePath == "" {
        return nil, errors.New("import file path not configured")
    }
    
    // 3. 验证文件存在
    if _, err := os.Stat(filePath); os.IsNotExist(err) {
        return nil, fmt.Errorf("import file not found: %s", filePath)
    }
    
    // 4. 读取Excel文件
    records, err := uc.readExcelFile(filePath)
    if err != nil {
        return nil, fmt.Errorf("failed to read excel file: %w", err)
    }
    
    // 5. 数据验证和转换
    validRecords, invalidRecords := uc.validateTermRecords(records)
    
    // 6. 批量导入数据
    importResult := &ImportResult{
        TotalProcessed:    len(records),
        SuccessfulImports: 0,
        FailedImports:     len(invalidRecords),
        DuplicateSkipped:  0,
        FailedRecords:     invalidRecords,
        ImportSummary:     make(map[string]int),
    }
    
    // 7. 分批处理数据
    batchSize := 100
    for i := 0; i < len(validRecords); i += batchSize {
        end := i + batchSize
        if end > len(validRecords) {
            end = len(validRecords)
        }
        
        batch := validRecords[i:end]
        err := uc.processBatch(ctx, batch, importResult)
        if err != nil {
            uc.log.WithContext(ctx).Errorf("Batch processing failed: %v", err)
        }
    }
    
    // 8. 记录操作日志
    uc.logAdminOperation(ctx, "import_term", map[string]interface{}{
        "total_processed":     importResult.TotalProcessed,
        "successful_imports":  importResult.SuccessfulImports,
        "failed_imports":      importResult.FailedImports,
        "processing_time":     time.Since(startTime).String(),
    })
    
    // 9. 构建响应
    response := map[string]interface{}{
        "success":             true,
        "message":             "Import completed successfully",
        "total_processed":     importResult.TotalProcessed,
        "successful_imports":  importResult.SuccessfulImports,
        "failed_imports":      importResult.FailedImports,
        "duplicate_skipped":   importResult.DuplicateSkipped,
        "processing_time":     time.Since(startTime).String(),
        "import_summary":      importResult.ImportSummary,
        "failed_records":      importResult.FailedRecords,
    }
    
    return structpb.NewStruct(response)
}
```

#### Excel文件读取
```go
func (uc *EduAdminUseCase) readExcelFile(filePath string) ([]map[string]string, error) {
    file, err := excelize.OpenFile(filePath)
    if err != nil {
        return nil, fmt.Errorf("failed to open excel file: %w", err)
    }
    defer file.Close()
    
    // 获取第一个工作表
    sheetName := file.GetSheetName(0)
    rows, err := file.GetRows(sheetName)
    if err != nil {
        return nil, fmt.Errorf("failed to get rows: %w", err)
    }
    
    if len(rows) < 2 {
        return nil, errors.New("excel file must have at least 2 rows (header + data)")
    }
    
    // 解析表头
    headers := rows[0]
    var records []map[string]string
    
    // 解析数据行
    for i := 1; i < len(rows); i++ {
        row := rows[i]
        record := make(map[string]string)
        
        for j, header := range headers {
            if j < len(row) {
                record[header] = strings.TrimSpace(row[j])
            } else {
                record[header] = ""
            }
        }
        
        records = append(records, record)
    }
    
    return records, nil
}
```

#### 数据验证
```go
func (uc *EduAdminUseCase) validateTermRecords(records []map[string]string) ([]*model.EduTerm, []FailedRecord) {
    var validRecords []*model.EduTerm
    var invalidRecords []FailedRecord
    
    requiredFields := []string{"subject", "term", "knowledge_1", "title"}
    
    for i, record := range records {
        // 1. 检查必填字段
        var missingFields []string
        for _, field := range requiredFields {
            if record[field] == "" {
                missingFields = append(missingFields, field)
            }
        }
        
        if len(missingFields) > 0 {
            invalidRecords = append(invalidRecords, FailedRecord{
                Row:   i + 2, // Excel行号从2开始（跳过表头）
                Term:  record["term"],
                Error: fmt.Sprintf("Missing required fields: %s", strings.Join(missingFields, ", ")),
            })
            continue
        }
        
        // 2. 数据格式验证
        if err := uc.validateTermData(record); err != nil {
            invalidRecords = append(invalidRecords, FailedRecord{
                Row:   i + 2,
                Term:  record["term"],
                Error: err.Error(),
            })
            continue
        }
        
        // 3. 转换为数据模型
        term := &model.EduTerm{
            Path:            record["path"],
            Subject:         record["subject"],
            Term:            record["term"],
            Knowledge1:      record["knowledge_1"],
            Knowledge2:      record["knowledge_2"],
            MetaDescription: record["meta_description"],
            Title:           record["title"],
            Tag:             record["tag"],
            ModuleType:      record["module_type"],
            BlockType:       record["block_type"],
            BlockContent:    record["block_content"],
        }
        
        // 4. 解析数值字段
        if moduleIndex, err := strconv.Atoi(record["module_index"]); err == nil {
            term.ModuleIndex = uint8(moduleIndex)
        }
        
        if blockOrder, err := strconv.Atoi(record["block_order"]); err == nil {
            term.BlockOrder = uint8(blockOrder)
        }
        
        validRecords = append(validRecords, term)
    }
    
    return validRecords, invalidRecords
}
```

#### SQL脚本执行
```go
func (uc *EduAdminUseCase) ExecuteSqlFile(ctx context.Context) (*structpb.Struct, error) {
    startTime := time.Now()
    
    // 1. 超级管理员权限检查
    if err := uc.checkSuperAdminPermission(ctx); err != nil {
        return nil, fmt.Errorf("super admin permission required: %w", err)
    }
    
    // 2. 获取SQL脚本配置
    scriptConfig := uc.confBiz.EduAdmin.SqlScript
    if scriptConfig.Name == "" {
        return nil, errors.New("no SQL script configured")
    }
    
    // 3. 验证脚本在白名单中
    if !uc.isScriptAllowed(scriptConfig.Name) {
        return nil, fmt.Errorf("script not in whitelist: %s", scriptConfig.Name)
    }
    
    // 4. 读取SQL文件
    scriptPath := filepath.Join(uc.confBiz.EduAdmin.SqlScriptDir, scriptConfig.Name)
    sqlContent, err := ioutil.ReadFile(scriptPath)
    if err != nil {
        return nil, fmt.Errorf("failed to read SQL file: %w", err)
    }
    
    // 5. 安全检查
    if err := uc.validateSQLContent(string(sqlContent)); err != nil {
        return nil, fmt.Errorf("SQL security check failed: %w", err)
    }
    
    // 6. 解析SQL语句
    statements := uc.parseSQLStatements(string(sqlContent))
    
    // 7. 执行SQL语句
    executionResult := &SQLExecutionResult{
        ScriptName:         scriptConfig.Name,
        StatementsExecuted: 0,
        AffectedRows:       0,
        ExecutionLog:       make([]StatementResult, 0),
        Warnings:           make([]string, 0),
    }
    
    // 8. 开启事务
    tx := uc.db.Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
            panic(r)
        }
    }()
    
    // 9. 逐条执行SQL语句
    for _, stmt := range statements {
        if strings.TrimSpace(stmt) == "" {
            continue
        }
        
        stmtStartTime := time.Now()
        result := tx.Exec(stmt)
        
        if result.Error != nil {
            tx.Rollback()
            return nil, fmt.Errorf("SQL execution failed: %w", result.Error)
        }
        
        stmtResult := StatementResult{
            Statement:     stmt,
            AffectedRows:  result.RowsAffected,
            ExecutionTime: time.Since(stmtStartTime).String(),
        }
        
        executionResult.ExecutionLog = append(executionResult.ExecutionLog, stmtResult)
        executionResult.StatementsExecuted++
        executionResult.AffectedRows += result.RowsAffected
    }
    
    // 10. 提交事务
    if err := tx.Commit().Error; err != nil {
        return nil, fmt.Errorf("failed to commit transaction: %w", err)
    }
    
    // 11. 记录操作日志
    uc.logAdminOperation(ctx, "execute_sql", map[string]interface{}{
        "script_name":         executionResult.ScriptName,
        "statements_executed": executionResult.StatementsExecuted,
        "affected_rows":       executionResult.AffectedRows,
        "execution_time":      time.Since(startTime).String(),
    })
    
    // 12. 构建响应
    response := map[string]interface{}{
        "success":             true,
        "message":             "SQL script executed successfully",
        "script_name":         executionResult.ScriptName,
        "execution_time":      time.Since(startTime).String(),
        "affected_rows":       executionResult.AffectedRows,
        "statements_executed": executionResult.StatementsExecuted,
        "warnings":            executionResult.Warnings,
        "execution_log":       executionResult.ExecutionLog,
    }
    
    return structpb.NewStruct(response)
}
```

### 安全验证
```go
func (uc *EduAdminUseCase) validateSQLContent(sqlContent string) error {
    // 1. 检查危险关键词
    upperSQL := strings.ToUpper(sqlContent)
    for _, keyword := range dangerousKeywords {
        if strings.Contains(upperSQL, keyword) {
            return fmt.Errorf("dangerous SQL keyword detected: %s", keyword)
        }
    }
    
    // 2. 检查SQL注入模式
    injectionPatterns := []string{
        `--`,           // SQL注释
        `/*`,           // 多行注释
        `;.*DROP`,      // 分号后跟DROP
        `;.*DELETE`,    // 分号后跟DELETE
        `UNION.*SELECT`, // UNION注入
    }
    
    for _, pattern := range injectionPatterns {
        if matched, _ := regexp.MatchString(pattern, upperSQL); matched {
            return fmt.Errorf("potential SQL injection pattern detected: %s", pattern)
        }
    }
    
    // 3. 检查表名白名单
    allowedTables := []string{
        "edu_resources", "edu_term", "comments", 
        "blog_articles", "hw_en_word",
    }
    
    tablePattern := regexp.MustCompile(`(?i)\b(?:FROM|JOIN|UPDATE|INTO)\s+(\w+)`)
    matches := tablePattern.FindAllStringSubmatch(sqlContent, -1)
    
    for _, match := range matches {
        if len(match) > 1 {
            tableName := strings.ToLower(match[1])
            allowed := false
            for _, allowedTable := range allowedTables {
                if tableName == allowedTable {
                    allowed = true
                    break
                }
            }
            if !allowed {
                return fmt.Errorf("table not in whitelist: %s", tableName)
            }
        }
    }
    
    return nil
}
```

## 监控和审计

### 1. 操作日志记录
```go
type AdminOperationLog struct {
    ID          int64     `json:"id"`
    AdminUserID string    `json:"admin_user_id"`
    AdminName   string    `json:"admin_name"`
    Operation   string    `json:"operation"`
    Details     string    `json:"details"`
    IPAddress   string    `json:"ip_address"`
    UserAgent   string    `json:"user_agent"`
    Success     bool      `json:"success"`
    ErrorMsg    string    `json:"error_msg,omitempty"`
    CreatedAt   time.Time `json:"created_at"`
}

func (uc *EduAdminUseCase) logAdminOperation(ctx context.Context, operation string, details map[string]interface{}) {
    adminInfo := uc.getCurrentAdmin(ctx)
    
    detailsJSON, _ := json.Marshal(details)
    
    log := &AdminOperationLog{
        AdminUserID: adminInfo.ID,
        AdminName:   adminInfo.Name,
        Operation:   operation,
        Details:     string(detailsJSON),
        IPAddress:   uc.getClientIP(ctx),
        UserAgent:   uc.getUserAgent(ctx),
        Success:     true,
    }
    
    err := uc.adminLogRepo.Create(ctx, log)
    if err != nil {
        uc.log.WithContext(ctx).Errorf("Failed to log admin operation: %v", err)
    }
}
```

### 2. 性能监控
```go
func (uc *EduAdminUseCase) monitorOperation(ctx context.Context, operation string, fn func() error) error {
    startTime := time.Now()
    
    err := fn()
    
    duration := time.Since(startTime)
    
    // 记录性能指标
    uc.metrics.AdminOperationDuration.WithLabelValues(operation).Observe(duration.Seconds())
    
    if err != nil {
        uc.metrics.AdminOperationErrors.WithLabelValues(operation).Inc()
    } else {
        uc.metrics.AdminOperationSuccess.WithLabelValues(operation).Inc()
    }
    
    // 慢操作告警
    if duration > 30*time.Second {
        uc.log.WithContext(ctx).Warnf("Slow admin operation: %s took %v", operation, duration)
    }
    
    return err
}
```

管理后台模块通过严格的权限控制、完善的安全机制、详细的操作审计，为海外教育平台提供了安全可靠的后台管理功能，确保了系统的稳定运行和数据安全。
