# AI服务模块详细文档

## 模块概述

AI服务模块是hw-paas-service项目的核心模块，提供多种AI辅助教学功能，包括口算批改、英文纠错、题目管理、博客管理、用户认证等服务。该模块集成了多个外部AI服务，为海外学习机提供智能化的教育解决方案。

## API接口详细说明

### 服务定义
```protobuf
service Ai {
  // 口算批改
  rpc QueryCorrect (QueryCorrectRequest) returns (google.protobuf.Struct);
  
  // 反馈追踪
  rpc FeedbackTrace (FeedbackTraceRequest) returns (FeedbackTraceReply);
  
  // 题目管理
  rpc ListQuestions (ListQuestionsRequest) returns (ListQuestionsReply);
  rpc GetQuestion (GetQuestionRequest) returns (Question);
  rpc UpsertQuestion (UpsertQuestionRequest) returns (google.protobuf.Struct);
  
  // 博客管理
  rpc BlogList (BlogListReq) returns (BlogListResp);
  rpc BlogDetail (BlogDetailReq) returns (BlogDetailResp);
  rpc BlogCategory (BlogCategoryReq) returns (BlogCategoryResp);
  
  // 用户认证
  rpc CheckLogin (CheckLoginReq) returns (CheckLoginResp);
  rpc CodeLogin (CodeLoginReq) returns (CodeLoginResp);
  rpc LoginOut (LoginOutReq) returns (LoginOutResp);
  
  // 通用配置
  rpc CommonConfig (CommonConfigReq) returns (CommonConfigResp);
  rpc TaskDrive (TaskDriveReq) returns (TaskDriveResp);
}
```

### 1. 口算批改接口

#### 接口信息
- **路径**: `POST /intelligence/api/ai/v1/query_correct`
- **功能**: 对上传的口算题图片进行AI批改
- **业务场景**: 学生完成口算练习后，通过拍照上传，系统自动识别并批改

#### 请求参数
```json
{
  "image_url": "https://example.com/math_problem.jpg"
}
```

#### 响应示例
```json
{
  "result": {
    "correct_count": 8,
    "total_count": 10,
    "accuracy": 80.0,
    "details": [
      {
        "question": "3 + 5 = 8",
        "is_correct": true,
        "user_answer": "8",
        "correct_answer": "8"
      },
      {
        "question": "7 - 2 = 6",
        "is_correct": false,
        "user_answer": "6",
        "correct_answer": "5"
      }
    ]
  }
}
```

#### 业务流程
```mermaid
graph TD
    A[用户上传图片] --> B[图片预处理]
    B --> C[OCR文字识别]
    C --> D[题目解析]
    D --> E[答案计算]
    E --> F[结果对比]
    F --> G[生成批改报告]
    G --> H[返回结果]
```

### 2. 反馈追踪接口

#### 接口信息
- **路径**: `POST /intelligence/api/ai/v2/feedback/trace`
- **功能**: 收集用户对AI服务的反馈，用于服务质量改进

#### 请求参数
```json
{
  "trace_id": "trace_123456",
  "feedback": "批改结果很准确",
  "biz": 1
}
```

#### 业务类型说明
- `1`: 口算批改
- `2`: 指尖查词
- `3`: 作业批改
- `4`: 指尖查词
- `5`: 语音查词
- `6`: 绘本指读

### 3. 题目管理接口

#### 题目列表查询
- **路径**: `GET /intelligence/api/ai/v1/questions`
- **功能**: 分页查询题目列表，支持多种筛选条件

#### 请求参数
```json
{
  "page": 1,
  "page_size": 20,
  "grade": "Grade 3",
  "subject": "Math",
  "difficulty": 2,
  "q_type": 1
}
```

#### 题目详情查询
- **路径**: `GET /intelligence/api/ai/v1/questions/detail`
- **功能**: 根据题目ID获取详细信息

#### 题目批量查询
- **路径**: `GET /intelligence/api/ai/v1/questions/detail_batch`
- **功能**: 批量获取多个题目的详细信息

### 4. 博客管理接口

#### 博客列表
- **路径**: `GET /intelligence/api/blog/list`
- **功能**: 获取博客文章列表，支持分页和分类筛选

#### 请求参数
```json
{
  "page": 1,
  "page_size": 10,
  "category": "Math Instruction"
}
```

#### 响应示例
```json
{
  "list": [
    {
      "id": 1,
      "path": "/blog/math-tips-for-kids",
      "category": "Math Instruction",
      "article_title": "10 Math Tips for Kids",
      "short_content": "Discover effective math learning strategies...",
      "cover_img": "https://example.com/cover.jpg",
      "author_name": "Sarah Johnson",
      "created_at": "2024-01-15T10:00:00Z"
    }
  ],
  "total": 50
}
```

#### 博客详情
- **路径**: `GET /intelligence/api/blog/detail`
- **功能**: 根据路径获取博客详细内容

#### 博客分类
- **路径**: `GET /intelligence/api/blog/category`
- **功能**: 获取所有博客分类列表

### 5. 用户认证接口

#### 登录检查
- **路径**: `GET /intelligence/api/v1/check_login`
- **功能**: 验证用户登录状态

#### 代码登录
- **路径**: `POST /intelligence/api/v1/code_login`
- **功能**: 通过授权码进行用户登录

#### 登出
- **路径**: `POST /intelligence/api/v1/login_out`
- **功能**: 用户登出操作

## 数据模型设计

### 1. AI题目表 (ai_jzx_question)

```sql
CREATE TABLE ai_jzx_question (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  q_type TINYINT NOT NULL DEFAULT 0 COMMENT '题型:1选择题单选 2选择题无序多选 3选择题有序多选 4连线题一对一 5连线题一对多 6填空题单空 7填空题多空 8拖拽题排序 9拖拽题填空 10阅读理解',
  grade VARCHAR(32) NOT NULL DEFAULT '' COMMENT '年级',
  subject VARCHAR(32) NOT NULL DEFAULT '' COMMENT '学科',
  difficulty TINYINT NOT NULL DEFAULT 0 COMMENT '难度值',
  question MEDIUMTEXT NULL COMMENT '题干',
  answer MEDIUMTEXT NULL COMMENT '答案',
  solution MEDIUMTEXT NULL COMMENT '题目解析',
  knowledge VARCHAR(128) NOT NULL DEFAULT '' COMMENT '知识点',
  knowledge_no VARCHAR(128) NOT NULL DEFAULT '' COMMENT '知识点序号',
  note TEXT NULL COMMENT '备注',
  status TINYINT DEFAULT 0 NULL COMMENT '题目状态: 1待审核 2审核不通过 3审核通过 4已下架 5已上架',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_grade_subject (grade, subject),
  INDEX idx_difficulty (difficulty),
  INDEX idx_status (status),
  INDEX idx_knowledge (knowledge)
);
```

### 2. 博客文章表 (blog_articles)

```sql
CREATE TABLE blog_articles (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  path VARCHAR(500) NOT NULL COMMENT '文章路径',
  category VARCHAR(100) NOT NULL COMMENT '文章分类',
  article_title VARCHAR(500) NOT NULL COMMENT '文章标题',
  short_content TEXT COMMENT '文章摘要',
  cover_img VARCHAR(500) COMMENT '封面图片',
  author_avatar VARCHAR(500) COMMENT '作者头像',
  author_name VARCHAR(100) COMMENT '作者姓名',
  article_content LONGTEXT COMMENT '文章内容',
  page_title VARCHAR(500) COMMENT '页面标题',
  meta_keywords VARCHAR(1000) COMMENT 'SEO关键词',
  meta_description VARCHAR(1000) COMMENT 'SEO描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE INDEX idx_path (path),
  INDEX idx_category (category),
  INDEX idx_created (created_at)
);
```

### 3. 反馈追踪表 (feedback_trace)

```sql
CREATE TABLE feedback_trace (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  biz TINYINT NOT NULL COMMENT '业务类型',
  trace_id VARCHAR(100) NOT NULL COMMENT '追踪ID',
  content TEXT NOT NULL COMMENT '反馈内容',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_biz_trace (biz, trace_id),
  INDEX idx_created (created_at)
);
```

## 业务逻辑实现

### Service层实现
```go
type AiService struct {
    pb.UnimplementedAiServer
    correct  *biz.CorrectUseCase
    log      *log.Helper
    question *biz.JzxQuestionUseCase
    edu      *biz.EduUseCase
    login    *biz.CodeLoginUseCase
}

// 口算批改
func (s *AiService) QueryCorrect(ctx context.Context, req *pb.QueryCorrectRequest) (*structpb.Struct, error) {
    return s.correct.QueryCorrect(ctx, req)
}

// 博客列表
func (s *AiService) BlogList(ctx context.Context, req *pb.BlogListReq) (*pb.BlogListResp, error) {
    return s.edu.ListBlogWithPage(ctx, req)
}
```

### Business层核心逻辑

#### 口算批改业务逻辑
```go
func (uc *CorrectUseCase) QueryCorrect(ctx context.Context, req *pb.QueryCorrectRequest) (*structpb.Struct, error) {
    // 1. 参数验证
    if req.ImageUrl == "" {
        return nil, errors.New("image_url is required")
    }
    
    // 2. 调用外部口算服务
    result, err := uc.kousuanClient.Correct(ctx, req.ImageUrl)
    if err != nil {
        return nil, err
    }
    
    // 3. 结果处理和格式化
    response := map[string]interface{}{
        "correct_count": result.CorrectCount,
        "total_count":   result.TotalCount,
        "accuracy":      result.Accuracy,
        "details":       result.Details,
    }
    
    // 4. 转换为protobuf结构
    return structpb.NewStruct(response)
}
```

#### 博客管理业务逻辑
```go
func (uc *EduUseCase) ListBlogWithPage(ctx context.Context, req *pb.BlogListReq) (*pb.BlogListResp, error) {
    // 1. 参数处理
    page := req.Page
    if page <= 0 {
        page = 1
    }
    pageSize := req.PageSize
    if pageSize <= 0 {
        pageSize = 10
    }
    
    // 2. 数据库查询
    articles, total, err := uc.eduDao.ListBlogWithPage(ctx, int64(page), int64(pageSize), req.Category)
    if err != nil {
        return nil, err
    }
    
    // 3. 数据转换
    resp := &pb.BlogListResp{
        Total: int32(total),
        List:  make([]*pb.BlogArticle, 0, len(articles)),
    }
    
    for _, article := range articles {
        resp.List = append(resp.List, &pb.BlogArticle{
            Id:           int32(article.ID),
            Path:         article.Path,
            Category:     article.Category,
            ArticleTitle: article.ArticleTitle,
            ShortContent: article.ShortContent,
            CoverImg:     article.CoverImg,
            AuthorName:   article.AuthorName,
            CreatedAt:    article.CreatedAt.Format(time.RFC3339),
        })
    }
    
    return resp, nil
}
```

## 外部服务集成

### 1. 口算服务集成
```go
type KousuanClient struct {
    host    string
    timeout time.Duration
    client  *resty.Client
}

func (c *KousuanClient) Correct(ctx context.Context, imageUrl string) (*CorrectResult, error) {
    resp, err := c.client.R().
        SetContext(ctx).
        SetBody(map[string]interface{}{
            "image_url": imageUrl,
        }).
        Post("/api/correct")
    
    if err != nil {
        return nil, err
    }
    
    var result CorrectResult
    err = json.Unmarshal(resp.Body(), &result)
    return &result, err
}
```

### 2. 用户中心集成
```go
func (uc *CodeLoginUseCase) CodeLogin(ctx context.Context, code string) (*pb.CodeLoginResp, error) {
    // 1. 调用UCenter验证授权码
    userInfo, err := uc.ucenterRepo.VerifyCode(ctx, code)
    if err != nil {
        return nil, err
    }
    
    // 2. 生成用户token
    token, err := uc.generateToken(userInfo)
    if err != nil {
        return nil, err
    }
    
    // 3. 同步用户信息到本地
    err = uc.syncUserInfo(ctx, userInfo)
    if err != nil {
        return nil, err
    }
    
    return &pb.CodeLoginResp{
        TalId:    userInfo.TalId,
        TalToken: token,
        Expired:  time.Now().Add(24 * time.Hour).Unix(),
        Life:     24 * 3600,
    }, nil
}
```

## 性能优化策略

### 1. 缓存策略
- **博客列表缓存**: Redis缓存热门博客列表，TTL 1小时
- **题目缓存**: 本地缓存常用题目，减少数据库查询
- **用户信息缓存**: Redis缓存用户登录信息，TTL 24小时

### 2. 数据库优化
- **索引优化**: 针对查询条件建立复合索引
- **分页优化**: 使用游标分页替代OFFSET分页
- **查询优化**: 避免N+1查询，使用JOIN优化关联查询

### 3. 并发控制
- **限流**: 对口算批改接口进行限流保护
- **熔断**: 外部服务调用失败时自动熔断
- **超时控制**: 设置合理的请求超时时间

## 监控和日志

### 1. 业务指标监控
- 口算批改成功率
- 博客访问量统计
- 用户登录成功率
- 接口响应时间

### 2. 错误处理和日志
```go
func (s *AiService) QueryCorrect(ctx context.Context, req *pb.QueryCorrectRequest) (*structpb.Struct, error) {
    traceId := custom_context.GetTraceId(ctx)
    
    s.log.WithContext(ctx).Infof("QueryCorrect start, traceId: %s, imageUrl: %s", traceId, req.ImageUrl)
    
    result, err := s.correct.QueryCorrect(ctx, req)
    if err != nil {
        s.log.WithContext(ctx).Errorf("QueryCorrect failed, traceId: %s, error: %v", traceId, err)
        return nil, err
    }
    
    s.log.WithContext(ctx).Infof("QueryCorrect success, traceId: %s", traceId)
    return result, nil
}
```

AI服务模块作为项目的核心，通过完善的接口设计、robust的业务逻辑实现、高效的外部服务集成，为海外教育场景提供了全面的AI辅助教学解决方案。
