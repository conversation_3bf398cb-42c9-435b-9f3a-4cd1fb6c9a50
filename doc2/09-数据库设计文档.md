# 数据库设计文档

## 数据库概览

hw-paas-service项目采用MySQL作为主要数据存储，结合Redis缓存和Elasticsearch搜索引擎，构建了完整的数据存储架构。数据库设计遵循第三范式，合理划分业务模块，确保数据一致性和查询性能。

## 数据库架构

### 存储架构
```mermaid
graph TD
    A[应用层] --> B[MySQL主库]
    A --> C[MySQL从库]
    A --> D[Redis缓存]
    A --> E[Elasticsearch]
    
    B --> F[数据同步]
    F --> C
    F --> E
    
    G[定时任务] --> H[数据备份]
    B --> H
```

### 数据分类
- **核心业务数据**: 存储在MySQL中，包括用户数据、教育资源、评论等
- **搜索数据**: 同步到Elasticsearch，支持全文搜索
- **缓存数据**: 存储在Redis中，提高查询性能
- **文件数据**: 存储在Azure Blob Storage中

## 核心数据表设计

### 1. 英文单词表 (hw_en_word)

**表用途**: 存储英文单词词典数据，支持指尖查词和单词查询功能

```sql
CREATE TABLE hw_en_word (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  word VARCHAR(50) NOT NULL COMMENT '单词',
  british VARCHAR(10) NOT NULL DEFAULT '' COMMENT '英式音标',
  american VARCHAR(10) NOT NULL DEFAULT '' COMMENT '美式音标',
  meanings VARCHAR(2048) NOT NULL DEFAULT '' COMMENT '释义(JSON格式)',
  synonyms VARCHAR(256) NOT NULL DEFAULT '' COMMENT '同义词',
  antonyms VARCHAR(256) NOT NULL DEFAULT '' COMMENT '反义词',
  sentences VARCHAR(2048) NOT NULL DEFAULT '' COMMENT '例句',
  inflections VARCHAR(256) NOT NULL DEFAULT '' COMMENT '变形词',
  prefix VARCHAR(20) NOT NULL DEFAULT '' COMMENT '前缀',
  suffix VARCHAR(20) NOT NULL DEFAULT '' COMMENT '后缀',
  phrases VARCHAR(512) NOT NULL DEFAULT '' COMMENT '短语',
  frequency BIGINT NOT NULL COMMENT '词频',
  status TINYINT NOT NULL COMMENT '状态:1待审核;2待上架;3已上架;4已下架;5违禁词',
  opt_uid VARCHAR(20) NOT NULL DEFAULT '' COMMENT '操作人工号',
  opt_name VARCHAR(20) NOT NULL DEFAULT '' COMMENT '操作人姓名',
  rel_word VARCHAR(50) NOT NULL DEFAULT '' COMMENT '关联原形词',
  video_url VARCHAR(512) NOT NULL DEFAULT '' COMMENT '视频讲解URL',
  video_img VARCHAR(512) NOT NULL DEFAULT '' COMMENT '视频封面URL',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE INDEX idx_word (word),
  INDEX idx_status (status),
  INDEX idx_frequency (frequency),
  INDEX idx_rel_word (rel_word),
  INDEX idx_created (created_at),
  FULLTEXT INDEX ft_meanings (meanings)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='英文单词表';
```

**字段说明**:
- `meanings`: JSON格式存储多个释义，包含词性和定义
- `frequency`: 单词使用频率，用于排序和推荐
- `status`: 单词状态管理，支持审核流程
- `rel_word`: 关联原形词，处理单词变形

### 2. GPT-4o增强单词表 (hw_en_word_gpt4o)

**表用途**: 存储GPT-4o生成的增强单词解释

```sql
CREATE TABLE hw_en_word_gpt4o (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  word VARCHAR(50) NOT NULL COMMENT '单词',
  content TEXT NOT NULL COMMENT 'GPT-4o生成的详细解释',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  UNIQUE INDEX idx_word (word),
  INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='GPT-4o增强单词表';
```

### 3. AI题目表 (ai_jzx_question)

**表用途**: 存储AI生成和管理的教育题目

```sql
CREATE TABLE ai_jzx_question (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  q_type TINYINT NOT NULL DEFAULT 0 COMMENT '题型:1选择题单选 2选择题无序多选 3选择题有序多选 4连线题一对一 5连线题一对多 6填空题单空 7填空题多空 8拖拽题排序 9拖拽题填空 10阅读理解',
  grade VARCHAR(32) NOT NULL DEFAULT '' COMMENT '年级',
  subject VARCHAR(32) NOT NULL DEFAULT '' COMMENT '学科',
  difficulty TINYINT NOT NULL DEFAULT 0 COMMENT '难度值',
  question MEDIUMTEXT NULL COMMENT '题干',
  answer MEDIUMTEXT NULL COMMENT '答案',
  solution MEDIUMTEXT NULL COMMENT '题目解析',
  knowledge VARCHAR(128) NOT NULL DEFAULT '' COMMENT '知识点',
  knowledge_no VARCHAR(128) NOT NULL DEFAULT '' COMMENT '知识点序号',
  note TEXT NULL COMMENT '备注',
  status TINYINT DEFAULT 0 NULL COMMENT '题目状态: 1待审核 2审核不通过 3审核通过 4已下架 5已上架',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_grade_subject (grade, subject),
  INDEX idx_difficulty (difficulty),
  INDEX idx_status (status),
  INDEX idx_knowledge (knowledge),
  INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI题目表';
```

### 4. 教育资源表 (edu_resources)

**表用途**: 存储各类教育资源的元数据信息

```sql
CREATE TABLE edu_resources (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  resource_type VARCHAR(20) NOT NULL DEFAULT '' COMMENT '资源类型: worksheet, video, coloringpage',
  resource_code VARCHAR(50) NOT NULL DEFAULT '' COMMENT '资源编码',
  subject VARCHAR(20) NOT NULL DEFAULT '' COMMENT '学科',
  grade VARCHAR(10) NOT NULL DEFAULT '' COMMENT '年级',
  title VARCHAR(500) NOT NULL DEFAULT '' COMMENT '标题',
  topic VARCHAR(50) NOT NULL DEFAULT '' COMMENT '二级知识点',
  learning_module VARCHAR(50) NOT NULL DEFAULT '' COMMENT '学习模块',
  resource VARCHAR(50) NOT NULL DEFAULT '' COMMENT '三级知识点',
  resource_detail VARCHAR(500) NOT NULL DEFAULT '' COMMENT '四级知识点',
  standards VARCHAR(100) NOT NULL DEFAULT '' COMMENT '最细粒度知识点',
  resource_description VARCHAR(1024) NOT NULL DEFAULT '' COMMENT '简介',
  extra_data TEXT NOT NULL DEFAULT '' COMMENT '扩展数据(JSON格式)',
  status INT NOT NULL DEFAULT 1 COMMENT '状态: 1上线, 2删除',
  url VARCHAR(500) NOT NULL DEFAULT '' COMMENT '资源URL',
  meta_title VARCHAR(500) NOT NULL DEFAULT '' COMMENT 'SEO标题',
  meta_description VARCHAR(1000) NOT NULL DEFAULT '' COMMENT 'SEO描述',
  meta_keywords VARCHAR(500) NOT NULL DEFAULT '' COMMENT 'SEO关键词',
  schemas TEXT NOT NULL DEFAULT '' COMMENT '结构化数据',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE INDEX idx_url (url),
  INDEX idx_type_subject_grade (resource_type, subject, grade),
  INDEX idx_topic_module (topic, learning_module),
  INDEX idx_resource_detail (resource, resource_detail),
  INDEX idx_status (status),
  INDEX idx_updated (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教育资源表';
```

### 5. 教育术语表 (edu_term)

**表用途**: 存储教育领域的专业术语和知识点

```sql
CREATE TABLE edu_term (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  path VARCHAR(255) NOT NULL DEFAULT '' COMMENT '术语路径',
  subject VARCHAR(64) NOT NULL DEFAULT '' COMMENT '学科',
  term VARCHAR(255) NOT NULL DEFAULT '' COMMENT '术语名称',
  knowledge_1 VARCHAR(64) NOT NULL DEFAULT '' COMMENT '一级知识点',
  knowledge_2 VARCHAR(64) NOT NULL DEFAULT '' COMMENT '二级知识点',
  meta_description VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'SEO描述',
  title VARCHAR(255) NOT NULL DEFAULT '' COMMENT '标题',
  tag VARCHAR(255) NOT NULL DEFAULT '' COMMENT '标签(逗号分隔)',
  module_type VARCHAR(64) NOT NULL DEFAULT '' COMMENT '模块类型',
  module_index TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '模块索引',
  block_type VARCHAR(64) NOT NULL DEFAULT '' COMMENT '块类型',
  block_order TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '块顺序',
  block_content TEXT NOT NULL COMMENT '块内容',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_subject (subject),
  INDEX idx_term (term),
  INDEX idx_knowledge (knowledge_1, knowledge_2),
  INDEX idx_path (path)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教育术语表';
```

### 6. 博客文章表 (blog_articles)

**表用途**: 存储教育博客文章内容和SEO信息

```sql
CREATE TABLE blog_articles (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  path VARCHAR(500) NOT NULL COMMENT '文章路径',
  category VARCHAR(100) NOT NULL COMMENT '文章分类',
  article_title VARCHAR(500) NOT NULL COMMENT '文章标题',
  short_content TEXT COMMENT '文章摘要',
  cover_img VARCHAR(500) COMMENT '封面图片',
  author_avatar VARCHAR(500) COMMENT '作者头像',
  author_name VARCHAR(100) COMMENT '作者姓名',
  article_content LONGTEXT COMMENT '文章内容',
  page_title VARCHAR(500) COMMENT '页面标题',
  meta_keywords VARCHAR(1000) COMMENT 'SEO关键词',
  meta_description VARCHAR(1000) COMMENT 'SEO描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE INDEX idx_path (path),
  INDEX idx_category (category),
  INDEX idx_created (created_at),
  FULLTEXT INDEX ft_content (article_title, short_content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='博客文章表';
```

### 7. 评论表 (comments)

**表用途**: 存储用户评论和回复，支持多级评论结构

```sql
CREATE TABLE comments (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '评论ID',
  resource_id VARCHAR(100) NOT NULL COMMENT '资源ID',
  resource_type VARCHAR(50) NOT NULL COMMENT '资源类型',
  user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
  user_name VARCHAR(100) NOT NULL COMMENT '用户名',
  user_avatar VARCHAR(500) DEFAULT '' COMMENT '用户头像',
  parent_id BIGINT UNSIGNED DEFAULT 0 COMMENT '父评论ID，0表示顶级评论',
  level TINYINT UNSIGNED DEFAULT 1 COMMENT '评论层级，1为顶级',
  content TEXT NOT NULL COMMENT '评论内容',
  like_count INT UNSIGNED DEFAULT 0 COMMENT '点赞数',
  reply_count INT UNSIGNED DEFAULT 0 COMMENT '回复数',
  status ENUM('published', 'hidden', 'deleted', 'pending') DEFAULT 'published' COMMENT '状态',
  ip_address VARCHAR(45) DEFAULT '' COMMENT 'IP地址',
  user_agent TEXT DEFAULT '' COMMENT '用户代理',
  mentioned_users JSON DEFAULT NULL COMMENT '提及的用户列表',
  extra_data JSON DEFAULT NULL COMMENT '扩展数据',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_resource (resource_id, resource_type),
  INDEX idx_user (user_id),
  INDEX idx_parent (parent_id),
  INDEX idx_status_created (status, created_at),
  INDEX idx_like_count (like_count),
  FULLTEXT INDEX ft_content (content)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';
```

### 8. 评论点赞表 (comment_likes)

**表用途**: 记录用户对评论的点赞关系

```sql
CREATE TABLE comment_likes (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  comment_id BIGINT UNSIGNED NOT NULL COMMENT '评论ID',
  user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
  
  UNIQUE INDEX idx_comment_user (comment_id, user_id),
  INDEX idx_user (user_id),
  INDEX idx_created (created_at),
  
  FOREIGN KEY (comment_id) REFERENCES comments(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞表';
```

### 9. 反馈追踪表 (feedback_trace)

**表用途**: 记录用户对AI服务的反馈信息

```sql
CREATE TABLE feedback_trace (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  biz TINYINT NOT NULL COMMENT '业务类型',
  trace_id VARCHAR(100) NOT NULL COMMENT '追踪ID',
  content TEXT NOT NULL COMMENT '反馈内容',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_biz_trace (biz, trace_id),
  INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='反馈追踪表';
```

### 10. 用户账户表 (account)

**表用途**: 存储用户基本信息和认证数据

```sql
CREATE TABLE account (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  tal_id VARCHAR(50) NOT NULL COMMENT '用户ID',
  tal_token VARCHAR(500) NOT NULL COMMENT '用户令牌',
  expired BIGINT NOT NULL COMMENT '过期时间戳',
  life INT NOT NULL COMMENT '生命周期(秒)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE INDEX idx_tal_id (tal_id),
  INDEX idx_expired (expired),
  INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户账户表';
```

### 11. 异步任务表 (apx_async_task)

**表用途**: 记录异步任务的执行状态和结果

```sql
CREATE TABLE apx_async_task (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  word VARCHAR(50) NOT NULL COMMENT '处理的单词',
  task_id VARCHAR(100) NOT NULL COMMENT '任务ID',
  status TINYINT NOT NULL COMMENT '任务状态:3成功;4失败',
  response TEXT NOT NULL COMMENT '模型响应数据',
  rel_word VARCHAR(50) NOT NULL DEFAULT '' COMMENT '关联的原形词',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_word (word),
  INDEX idx_task_id (task_id),
  INDEX idx_status (status),
  INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异步任务表';
```

## 数据关系图

```mermaid
erDiagram
    hw_en_word ||--o{ hw_en_word_gpt4o : "word"
    edu_resources ||--o{ comments : "resource_id"
    comments ||--o{ comment_likes : "comment_id"
    comments ||--o{ comments : "parent_id"
    account ||--o{ comments : "user_id"
    account ||--o{ comment_likes : "user_id"
    edu_term ||--|| edu_resources : "knowledge"
    blog_articles ||--o{ comments : "resource_id"
    apx_async_task ||--|| hw_en_word : "word"
    feedback_trace ||--|| account : "trace_id"
```

## 索引设计策略

### 1. 主键索引
所有表都使用自增主键，确保插入性能和唯一性

### 2. 唯一索引
- `hw_en_word.word`: 确保单词唯一性
- `edu_resources.url`: 确保资源URL唯一性
- `blog_articles.path`: 确保文章路径唯一性
- `comment_likes(comment_id, user_id)`: 防止重复点赞

### 3. 复合索引
- `edu_resources(resource_type, subject, grade)`: 支持资源筛选查询
- `comments(resource_id, resource_type)`: 支持评论列表查询
- `comments(status, created_at)`: 支持状态和时间排序

### 4. 全文索引
- `hw_en_word.meanings`: 支持释义全文搜索
- `blog_articles(article_title, short_content)`: 支持文章内容搜索
- `comments.content`: 支持评论内容搜索

## 数据分区策略

### 1. 时间分区
对于大数据量的日志表，采用按月分区：
```sql
-- 评论表按月分区示例
ALTER TABLE comments PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404)
);
```

### 2. 哈希分区
对于用户相关数据，可考虑按用户ID哈希分区：
```sql
-- 用户账户表哈希分区示例
ALTER TABLE account PARTITION BY HASH(CRC32(tal_id)) PARTITIONS 8;
```

## 数据备份和恢复

### 1. 备份策略
- **全量备份**: 每日凌晨进行全量备份
- **增量备份**: 每小时进行增量备份
- **binlog备份**: 实时备份binlog文件

### 2. 恢复策略
- **时间点恢复**: 基于binlog进行精确时间点恢复
- **表级恢复**: 针对特定表的数据恢复
- **跨地域备份**: 异地备份确保数据安全

## 性能优化建议

### 1. 查询优化
- 合理使用索引，避免全表扫描
- 优化复杂查询，使用EXPLAIN分析执行计划
- 避免SELECT *，只查询需要的字段

### 2. 写入优化
- 批量插入替代单条插入
- 合理设置事务大小
- 使用INSERT ... ON DUPLICATE KEY UPDATE处理冲突

### 3. 存储优化
- 选择合适的数据类型，节省存储空间
- 定期清理历史数据
- 使用压缩存储引擎

### 4. 连接池优化
- 合理配置连接池大小
- 设置合适的连接超时时间
- 监控连接池使用情况

数据库设计充分考虑了业务需求、性能要求和扩展性，通过合理的表结构设计、索引策略和分区方案，为hw-paas-service项目提供了稳定高效的数据存储基础。
