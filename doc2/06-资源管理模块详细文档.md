# 资源管理模块详细文档

## 模块概述

资源管理模块负责管理海外教育平台的各类教育资源，包括工作表(worksheets)、视频(videos)、涂色页(coloring pages)等。该模块提供完整的资源分类、检索、元数据管理和SEO优化功能，支持按年级、学科、主题等多维度组织和查询教育资源。

## 技术架构

### 核心功能
- **资源分类管理**: 按类型、年级、学科、主题分类
- **元数据管理**: SEO标题、描述、关键词、结构化数据
- **多维度查询**: 支持复杂的筛选和搜索条件
- **缓存优化**: Redis缓存热门资源和元数据
- **随机推荐**: 智能推荐相关资源

### 数据模型关系
```mermaid
graph TD
    A[EduResource 教育资源] --> B[ResourceType 资源类型]
    A --> C[Grade 年级]
    A --> D[Subject 学科]
    A --> E[Topic 主题]
    A --> F[LearningModule 学习模块]
    A --> G[ResourceDetail 资源详情]
    A --> H[Standards 标准]
    
    I[EduTerm 教育术语] --> D
    I --> J[Knowledge 知识点]
```

## API接口详细说明

### 服务定义
```protobuf
service Resource {
  // 基础元数据接口
  rpc GetGrades(google.protobuf.Empty) returns (GradesResp);
  rpc GetSubjects(google.protobuf.Empty) returns (SubjectsResp);
  rpc GetResourceType(google.protobuf.Empty) returns (ResourceTypeResp);
  rpc GetResourceMeta(google.protobuf.Empty) returns (ResourceMetaResp);
  
  // 资源查询接口
  rpc GetResourceGroups(GetResourceGroupsReq) returns (GetResourceGroupsResp);
  rpc GetResourceList(GetResourceListReq) returns (GetResourceListResp);
  rpc GetResourceDetail(GetResourceDetailReq) returns (ResourceDetail);
  rpc ListRandResource(ListRandResourceReq) returns (ListRandResourceResp);
  
  // 缓存管理
  rpc RefreshAllCache(google.protobuf.Empty) returns (google.protobuf.Empty);
}
```

### 1. 年级列表接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/grades`
- **功能**: 获取所有支持的年级列表
- **缓存**: 长期缓存，很少变更

#### 响应示例
```json
{
  "grades": [
    {"name": "Pre-Kindergarten", "value": "Pre-K"},
    {"name": "Kindergarten", "value": "K"},
    {"name": "Grade 1", "value": "1"},
    {"name": "Grade 2", "value": "2"},
    {"name": "Grade 3", "value": "3"},
    {"name": "Grade 4", "value": "4"},
    {"name": "Grade 5", "value": "5"},
    {"name": "Grade 6", "value": "6"}
  ]
}
```

### 2. 学科列表接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/subjects`
- **功能**: 获取学科分类树形结构

#### 响应示例
```json
{
  "subjects": [
    {
      "name": "Math",
      "nodes": [
        {
          "name": "Number Sense",
          "nodes": [
            {"name": "Counting", "nodes": []},
            {"name": "Addition", "nodes": []},
            {"name": "Subtraction", "nodes": []}
          ]
        },
        {
          "name": "Geometry",
          "nodes": [
            {"name": "Shapes", "nodes": []},
            {"name": "Measurement", "nodes": []}
          ]
        }
      ]
    },
    {
      "name": "English Language Arts",
      "nodes": [
        {
          "name": "Reading",
          "nodes": [
            {"name": "Phonics", "nodes": []},
            {"name": "Comprehension", "nodes": []}
          ]
        }
      ]
    }
  ]
}
```

### 3. 资源分组查询接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/groups`
- **功能**: 按条件分组查询资源，用于分类展示

#### 请求参数
```json
{
  "resource_type": "worksheets",
  "subject": "math",
  "grade": "3",
  "page": 1,
  "page_size": 10,
  "path": "/math/grade-3/addition"
}
```

#### 响应示例
```json
{
  "groups": [
    {
      "title": "Basic Addition",
      "total": 15,
      "resources": [
        {
          "id": 1001,
          "resource_type": "worksheets",
          "subject": "math",
          "grade": "3",
          "title": "Single Digit Addition",
          "learning_topic": "Number Sense",
          "resource": "Addition",
          "resource_detail": "Basic Addition Facts",
          "standards": "3.NBT.A.2",
          "resource_description": "Practice single digit addition problems",
          "url": "/worksheets/math/grade-3/single-digit-addition",
          "meta_title": "Single Digit Addition Worksheets for Grade 3",
          "meta_description": "Free printable single digit addition worksheets for 3rd grade students",
          "meta_keywords": "addition, math, grade 3, worksheets",
          "updated_at": "2024-01-15T10:00:00Z"
        }
      ]
    }
  ],
  "title": {
    "url": "/math/grade-3/addition",
    "h1_title": "Grade 3 Math Addition Worksheets",
    "description": "Comprehensive collection of addition worksheets for 3rd grade",
    "meta_title": "Grade 3 Addition Worksheets | Free Math Resources",
    "meta_description": "Download free Grade 3 addition worksheets. Perfect for classroom and home learning.",
    "meta_keywords": "grade 3, addition, worksheets, math, free, printable",
    "schemas": "{\"@type\": \"EducationalResource\", \"educationalLevel\": \"Grade 3\"}",
    "filter": "grade=3&subject=math&topic=addition"
  }
}
```

### 4. 资源详情接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/detail`
- **功能**: 获取单个资源的详细信息，包括上一个/下一个资源

#### 请求参数
```json
{
  "url": "/worksheets/math/grade-3/single-digit-addition",
  "resource_type": "worksheets",
  "subject": "math",
  "grade": "3"
}
```

#### 响应示例
```json
{
  "resource": {
    "id": 1001,
    "title": "Single Digit Addition",
    "resource_description": "Practice single digit addition problems with visual aids",
    "extra_data": {
      "difficulty_level": "beginner",
      "estimated_time": "15 minutes",
      "skills_practiced": ["addition", "number recognition"],
      "answer_key": true
    },
    "url": "/worksheets/math/grade-3/single-digit-addition",
    "schemas": "{\"@type\": \"EducationalResource\", \"name\": \"Single Digit Addition\"}"
  },
  "pre_url": "/worksheets/math/grade-3/counting-review",
  "next_url": "/worksheets/math/grade-3/double-digit-addition"
}
```

### 5. 随机资源推荐接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/rand`
- **功能**: 根据条件随机推荐相关资源

#### 请求参数
```json
{
  "subject": "math",
  "grade": "3",
  "activity_topic": "Number Sense",
  "exclude_urls": "/worksheets/math/grade-3/current-worksheet",
  "page_size": 6
}
```

#### 响应示例
```json
{
  "worksheets": [
    {
      "id": 1002,
      "title": "Addition with Pictures",
      "url": "/worksheets/math/grade-3/addition-with-pictures",
      "resource_description": "Visual addition problems using pictures and objects"
    },
    {
      "id": 1003,
      "title": "Number Line Addition",
      "url": "/worksheets/math/grade-3/number-line-addition",
      "resource_description": "Learn addition using number lines"
    }
  ],
  "coloring_pages": [
    {
      "id": 2001,
      "title": "Math Monster Addition",
      "url": "/coloring-pages/math/grade-3/math-monster-addition",
      "resource_description": "Fun coloring page with addition problems"
    }
  ],
  "terms": [
    {
      "id": 3001,
      "term": "Addition",
      "knowledge_1": "Number Sense",
      "knowledge_2": "Basic Operations",
      "title": "Understanding Addition",
      "tag": ["math", "arithmetic", "basic"],
      "path": "/glossary/math/addition"
    }
  ]
}
```

## 数据模型设计

### 1. 教育资源表 (edu_resources)

```sql
CREATE TABLE edu_resources (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  resource_type VARCHAR(20) NOT NULL DEFAULT '' COMMENT '资源类型: worksheet, video, coloringpage',
  resource_code VARCHAR(50) NOT NULL DEFAULT '' COMMENT '资源编码',
  subject VARCHAR(20) NOT NULL DEFAULT '' COMMENT '学科',
  grade VARCHAR(10) NOT NULL DEFAULT '' COMMENT '年级',
  title VARCHAR(500) NOT NULL DEFAULT '' COMMENT '标题',
  topic VARCHAR(50) NOT NULL DEFAULT '' COMMENT '二级知识点',
  learning_module VARCHAR(50) NOT NULL DEFAULT '' COMMENT '学习模块',
  resource VARCHAR(50) NOT NULL DEFAULT '' COMMENT '三级知识点',
  resource_detail VARCHAR(500) NOT NULL DEFAULT '' COMMENT '四级知识点',
  standards VARCHAR(100) NOT NULL DEFAULT '' COMMENT '最细粒度知识点',
  resource_description VARCHAR(1024) NOT NULL DEFAULT '' COMMENT '简介',
  extra_data TEXT NOT NULL DEFAULT '' COMMENT '扩展数据(JSON格式)',
  status INT NOT NULL DEFAULT 1 COMMENT '状态: 1上线, 2删除',
  url VARCHAR(500) NOT NULL DEFAULT '' COMMENT '资源URL',
  meta_title VARCHAR(500) NOT NULL DEFAULT '' COMMENT 'SEO标题',
  meta_description VARCHAR(1000) NOT NULL DEFAULT '' COMMENT 'SEO描述',
  meta_keywords VARCHAR(500) NOT NULL DEFAULT '' COMMENT 'SEO关键词',
  schemas TEXT NOT NULL DEFAULT '' COMMENT '结构化数据',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE INDEX idx_url (url),
  INDEX idx_type_subject_grade (resource_type, subject, grade),
  INDEX idx_topic_module (topic, learning_module),
  INDEX idx_resource_detail (resource, resource_detail),
  INDEX idx_status (status),
  INDEX idx_updated (updated_at)
);
```

### 2. 教育术语表 (edu_term)

```sql
CREATE TABLE edu_term (
  id INT AUTO_INCREMENT PRIMARY KEY,
  path VARCHAR(255) NOT NULL DEFAULT '' COMMENT '术语路径',
  subject VARCHAR(64) NOT NULL DEFAULT '' COMMENT '学科',
  term VARCHAR(255) NOT NULL DEFAULT '' COMMENT '术语名称',
  knowledge_1 VARCHAR(64) NOT NULL DEFAULT '' COMMENT '一级知识点',
  knowledge_2 VARCHAR(64) NOT NULL DEFAULT '' COMMENT '二级知识点',
  meta_description VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'SEO描述',
  title VARCHAR(255) NOT NULL DEFAULT '' COMMENT '标题',
  tag VARCHAR(255) NOT NULL DEFAULT '' COMMENT '标签(逗号分隔)',
  module_type VARCHAR(64) NOT NULL DEFAULT '' COMMENT '模块类型',
  module_index TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '模块索引',
  block_type VARCHAR(64) NOT NULL DEFAULT '' COMMENT '块类型',
  block_order TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '块顺序',
  block_content TEXT NOT NULL COMMENT '块内容',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_subject (subject),
  INDEX idx_term (term),
  INDEX idx_knowledge (knowledge_1, knowledge_2),
  INDEX idx_path (path)
);
```

## 业务逻辑实现

### Service层实现
```go
type ResourceService struct {
    pb.UnimplementedResourceServer
    uc  *biz.ResourceUseCase
    log *log.Helper
}

func (s *ResourceService) GetResourceList(ctx context.Context, req *pb.GetResourceListReq) (*pb.GetResourceListResp, error) {
    return s.uc.GetResourceList(ctx, req)
}

func (s *ResourceService) GetResourceDetail(ctx context.Context, req *pb.GetResourceDetailReq) (*pb.ResourceDetail, error) {
    return s.uc.GetResourceDetail(ctx, req)
}
```

### Business层核心逻辑

#### 资源列表查询
```go
func (uc *ResourceUseCase) GetResourceList(ctx context.Context, req *pb.GetResourceListReq) (*pb.GetResourceListResp, error) {
    // 1. 参数解析和验证
    rp := &requestParser{}
    resourceReq := rp.buildResourceReq(req)
    
    if err := uc.validateResourceRequest(resourceReq); err != nil {
        return nil, fmt.Errorf("invalid request: %w", err)
    }
    
    // 2. 缓存查询
    cacheKey := uc.buildCacheKey("resource_list", resourceReq)
    if cached := uc.getCachedResourceList(ctx, cacheKey); cached != nil {
        return cached, nil
    }
    
    // 3. 数据库查询
    resourceList, err := uc.repo.GetResourceList(ctx, resourceReq)
    if err != nil {
        return nil, fmt.Errorf("failed to get resource list: %w", err)
    }
    
    // 4. 数据转换
    respResources := make([]*pb.EduResource, 0, len(resourceList))
    for _, r := range resourceList {
        dto, err := uc.DataToDTO(ctx, r)
        if err != nil {
            uc.log.WithContext(ctx).Errorf("DataToDTO error: %v", err)
            continue
        }
        respResources = append(respResources, dto)
    }
    
    // 5. 构建页面元数据
    title, err := uc.buildPageTitle(ctx, resourceReq)
    if err != nil {
        uc.log.WithContext(ctx).Errorf("buildPageTitle error: %v", err)
    }
    
    // 6. 构建响应
    resp := &pb.GetResourceListResp{
        Resources: respResources,
        Title:     title,
        Total:     int32(len(respResources)),
    }
    
    // 7. 缓存结果
    uc.cacheResourceList(ctx, cacheKey, resp)
    
    return resp, nil
}
```

#### 资源详情查询
```go
func (uc *ResourceUseCase) GetResourceDetail(ctx context.Context, req *pb.GetResourceDetailReq) (*pb.ResourceDetail, error) {
    // 1. 参数验证
    if req.Url == "" {
        return nil, errors.New("url is required")
    }
    
    // 2. 解析URL路径
    gc := &gradeConverter{}
    rp := &requestParser{}
    
    subject, topic, learningModule, resourceDetail := rp.parseSubjectLevels(req.Subject)
    grade := gc.getGradeName(req.Grade)
    resourceType := req.ResourceType
    
    // 3. 查询当前资源
    detail, err := uc.repo.GetResourceByURL(ctx, req.Url)
    if err != nil {
        return nil, fmt.Errorf("resource not found: %w", err)
    }
    
    // 4. 数据转换
    resourceDTO, err := uc.DataToDTO(ctx, detail)
    if err != nil {
        return nil, fmt.Errorf("failed to convert resource: %w", err)
    }
    
    // 5. 构建响应
    result := &pb.ResourceDetail{
        Resource: resourceDTO,
    }
    
    // 6. 查询上一个资源
    resourcePrev, err := uc.repo.GetResourcePrev(ctx, &model.ResourceReq{
        ResourceType:   resourceType,
        Grade:          grade,
        Subject:        subject,
        Topic:          topic,
        LearningModule: learningModule,
    }, detail.ID)
    
    if err == nil && resourcePrev != nil {
        result.PreUrl = resourcePrev.Url
    }
    
    // 7. 查询下一个资源
    resourceNext, err := uc.repo.GetResourceNext(ctx, &model.ResourceReq{
        ResourceType:   resourceType,
        Grade:          grade,
        Subject:        subject,
        Topic:          topic,
        LearningModule: learningModule,
    }, detail.ID)
    
    if err == nil && resourceNext != nil {
        result.NextUrl = resourceNext.Url
    }
    
    return result, nil
}
```

#### 随机资源推荐
```go
func (uc *ResourceUseCase) ListRandResource(ctx context.Context, req *pb.ListRandResourceReq) (*pb.ListRandResourceResp, error) {
    rp := &requestParser{}
    gc := &gradeConverter{}
    
    subject, topic, learningModule, resourceDetail := rp.parseSubjectLevels(req.Subject)
    grade := gc.getGradeName(req.Grade)
    
    resp := &pb.ListRandResourceResp{}
    
    // 1. 处理全学科查询
    if req.Subject == "all" {
        // 获取数学工作表
        mathWorksheets, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
            ResourceType:   "worksheets",
            Grade:          grade,
            Subject:        "math",
            Topic:          topic,
            LearningModule: learningModule,
            ResourceDetail: resourceDetail,
            PageSize:       int(req.PageSize / 2),
        }, req.ExcludeUrls)
        
        if err == nil {
            for _, r := range mathWorksheets {
                dto, _ := uc.DataToDTO(ctx, r)
                if dto != nil {
                    resp.Worksheets = append(resp.Worksheets, dto)
                }
            }
        }
        
        // 获取英语工作表
        elaWorksheets, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
            ResourceType:   "worksheets",
            Grade:          grade,
            Subject:        "english-language-arts",
            Topic:          topic,
            LearningModule: learningModule,
            ResourceDetail: resourceDetail,
            PageSize:       int(req.PageSize / 2),
        }, req.ExcludeUrls)
        
        if err == nil {
            for _, r := range elaWorksheets {
                dto, _ := uc.DataToDTO(ctx, r)
                if dto != nil {
                    resp.Worksheets = append(resp.Worksheets, dto)
                }
            }
        }
    } else {
        // 2. 单学科查询
        worksheets, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
            ResourceType:   "worksheets",
            Grade:          grade,
            Subject:        subject,
            Topic:          topic,
            LearningModule: learningModule,
            ResourceDetail: resourceDetail,
            PageSize:       int(req.PageSize),
        }, req.ExcludeUrls)
        
        if err == nil {
            for _, r := range worksheets {
                dto, _ := uc.DataToDTO(ctx, r)
                if dto != nil {
                    resp.Worksheets = append(resp.Worksheets, dto)
                }
            }
        }
    }
    
    // 3. 获取涂色页
    coloringPages, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
        ResourceType: "coloring-pages",
        Grade:        grade,
        Subject:      subject,
        PageSize:     2,
    }, req.ExcludeUrls)
    
    if err == nil {
        for _, r := range coloringPages {
            dto, _ := uc.DataToDTO(ctx, r)
            if dto != nil {
                resp.ColoringPages = append(resp.ColoringPages, dto)
            }
        }
    }
    
    // 4. 获取相关术语
    terms, err := uc.eduRepo.ListTermBySubject(ctx, subject)
    if err == nil && len(terms) > 0 {
        // 随机选择3个术语
        selectedTerms := uc.selectRandomTerms(terms, 3)
        for _, term := range selectedTerms {
            resp.Terms = append(resp.Terms, &pb.EduTermInfo{
                Id:          int32(term.ID),
                Term:        term.Term,
                Knowledge_1: term.Knowledge1,
                Knowledge_2: term.Knowledge2,
                Title:       term.Title,
                Tag:         strings.Split(term.Tag, ","),
                Path:        term.Path,
            })
        }
    }
    
    return resp, nil
}
```

### 数据转换和URL生成
```go
func (uc *ResourceUseCase) DataToDTO(ctx context.Context, data *model.EduResources) (*pb.EduResource, error) {
    dto := &pb.EduResource{}
    
    // 1. 基础字段复制
    err := copier.Copy(dto, data)
    if err != nil {
        return nil, fmt.Errorf("failed to copy data: %w", err)
    }
    
    // 2. 处理扩展数据
    if data.ExtraData != "" {
        var extraData map[string]interface{}
        if err := json.Unmarshal([]byte(data.ExtraData), &extraData); err == nil {
            dto.ExtraData, _ = structpb.NewStruct(extraData)
        }
    }
    
    // 3. 生成SEO友好的URL
    if data.Url == "" {
        dto.Url = uc.urlGenerator.GenerateResourceURL(data)
    } else {
        dto.Url = data.Url
    }
    
    // 4. 处理时间格式
    dto.UpdatedAt = data.UpdatedAt.Format("2006-01-02T15:04:05Z")
    
    return dto, nil
}

type URLGenerator struct {
    baseURL string
}

func (g *URLGenerator) GenerateResourceURL(resource *model.EduResources) string {
    // 生成SEO友好的URL: /worksheets/math/grade-3/addition/single-digit-addition
    parts := []string{
        g.slugify(resource.ResourceType),
        g.slugify(resource.Subject),
        g.slugify("grade-" + resource.Grade),
        g.slugify(resource.Topic),
        g.slugify(resource.Title),
    }
    
    return "/" + strings.Join(parts, "/")
}

func (g *URLGenerator) slugify(text string) string {
    // 转换为URL友好的slug格式
    text = strings.ToLower(text)
    text = regexp.MustCompile(`[^a-z0-9]+`).ReplaceAllString(text, "-")
    text = strings.Trim(text, "-")
    return text
}
```

## 缓存策略

### 1. 多级缓存设计
```go
const (
    ResourceListCacheTTL  = 2 * time.Hour    // 资源列表缓存2小时
    ResourceDetailCacheTTL = 24 * time.Hour  // 资源详情缓存24小时
    MetadataCacheTTL      = 7 * 24 * time.Hour // 元数据缓存7天
    RandomResourceCacheTTL = 30 * time.Minute // 随机推荐缓存30分钟
)

func (uc *ResourceUseCase) buildCacheKey(prefix string, req *model.ResourceReq) string {
    parts := []string{
        prefix,
        req.ResourceType,
        req.Subject,
        req.Grade,
        req.Topic,
        req.LearningModule,
        strconv.Itoa(req.Page),
        strconv.Itoa(req.PageSize),
    }
    return strings.Join(parts, ":")
}
```

### 2. 缓存刷新机制
```go
func (uc *ResourceUseCase) RefreshAllCache(ctx context.Context) error {
    // 1. 清除所有资源相关缓存
    pattern := "resource:*"
    keys, err := uc.rdb.Keys(ctx, pattern).Result()
    if err != nil {
        return err
    }
    
    if len(keys) > 0 {
        err = uc.rdb.Del(ctx, keys...).Err()
        if err != nil {
            return err
        }
    }
    
    // 2. 预热热门资源缓存
    go uc.preloadHotResources(ctx)
    
    uc.log.WithContext(ctx).Infof("Refreshed %d cache keys", len(keys))
    return nil
}

func (uc *ResourceUseCase) preloadHotResources(ctx context.Context) {
    // 预加载热门年级和学科的资源
    hotGrades := []string{"K", "1", "2", "3"}
    hotSubjects := []string{"math", "english-language-arts"}
    
    for _, grade := range hotGrades {
        for _, subject := range hotSubjects {
            req := &pb.GetResourceListReq{
                ResourceType: "worksheets",
                Subject:      subject,
                Grade:        grade,
                Page:         1,
                PageSize:     20,
            }
            
            _, err := uc.GetResourceList(ctx, req)
            if err != nil {
                uc.log.WithContext(ctx).Errorf("Failed to preload cache for %s-%s: %v", subject, grade, err)
            }
        }
    }
}
```

## 性能优化策略

### 1. 数据库查询优化
```go
func (dao *EduResourceDao) GetResourceListOptimized(ctx context.Context, req *model.ResourceReq) ([]*model.EduResources, error) {
    query := dao.db.WithContext(ctx).Model(&model.EduResources{})
    
    // 1. 基础过滤条件
    query = query.Where("status = ?", 1)
    
    // 2. 动态添加查询条件
    if req.ResourceType != "" {
        query = query.Where("resource_type = ?", req.ResourceType)
    }
    if req.Subject != "" {
        query = query.Where("subject = ?", req.Subject)
    }
    if req.Grade != "" {
        query = query.Where("grade = ?", req.Grade)
    }
    
    // 3. 使用索引优化排序
    query = query.Order("updated_at DESC, id DESC")
    
    // 4. 分页查询
    if req.Page > 0 && req.PageSize > 0 {
        offset := (req.Page - 1) * req.PageSize
        query = query.Offset(offset).Limit(req.PageSize)
    }
    
    var resources []*model.EduResources
    err := query.Find(&resources).Error
    return resources, err
}
```

### 2. 并发查询优化
```go
func (uc *ResourceUseCase) GetResourceGroupsConcurrent(ctx context.Context, req *pb.GetResourceGroupsReq) (*pb.GetResourceGroupsResp, error) {
    var wg sync.WaitGroup
    var mu sync.Mutex
    
    groups := make([]*pb.EduResourceGroup, 0)
    errChan := make(chan error, 1)
    
    // 并发查询不同的资源组
    topics := []string{"Number Sense", "Geometry", "Algebra"}
    
    for _, topic := range topics {
        wg.Add(1)
        go func(t string) {
            defer wg.Done()
            
            groupReq := &model.ResourceReq{
                ResourceType: req.ResourceType,
                Subject:      req.Subject,
                Grade:        req.Grade,
                Topic:        t,
                PageSize:     5,
            }
            
            resources, err := uc.repo.GetResourceList(ctx, groupReq)
            if err != nil {
                select {
                case errChan <- err:
                default:
                }
                return
            }
            
            if len(resources) > 0 {
                group := &pb.EduResourceGroup{
                    Title:     t,
                    Total:     int32(len(resources)),
                    Resources: make([]*pb.EduResource, 0),
                }
                
                for _, r := range resources {
                    dto, _ := uc.DataToDTO(ctx, r)
                    if dto != nil {
                        group.Resources = append(group.Resources, dto)
                    }
                }
                
                mu.Lock()
                groups = append(groups, group)
                mu.Unlock()
            }
        }(topic)
    }
    
    wg.Wait()
    
    // 检查是否有错误
    select {
    case err := <-errChan:
        return nil, err
    default:
    }
    
    return &pb.GetResourceGroupsResp{
        Groups: groups,
    }, nil
}
```

## 监控和日志

### 1. 关键指标监控
- 资源查询QPS和响应时间
- 缓存命中率
- 数据库查询性能
- 随机推荐点击率
- SEO页面访问量

### 2. 业务日志记录
```go
func (uc *ResourceUseCase) logResourceAccess(ctx context.Context, resourceType, subject, grade string, resultCount int, duration time.Duration) {
    logData := map[string]interface{}{
        "action":        "resource_query",
        "resource_type": resourceType,
        "subject":       subject,
        "grade":         grade,
        "result_count":  resultCount,
        "duration_ms":   duration.Milliseconds(),
        "cache_hit":     uc.isCacheHit(ctx),
    }
    
    logJson, _ := json.Marshal(logData)
    uc.log.WithContext(ctx).Infof("ResourceAccess: %s", string(logJson))
}
```

资源管理模块通过完善的分类体系、高效的查询机制、智能的推荐算法，为海外教育平台提供了丰富的教育资源管理和检索功能，有效支撑了个性化学习和教学需求。
