// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.3
// source: internal/conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Server   *Server              `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Data     *Data                `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Nacos    *Nacos               `protobuf:"bytes,3,opt,name=nacos,proto3" json:"nacos,omitempty"`
	Error    *ErrorHandle         `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	Log      *Log                 `protobuf:"bytes,5,opt,name=log,proto3" json:"log,omitempty"`
	Sign     map[string]*SignConf `protobuf:"bytes,6,rep,name=sign,proto3" json:"sign,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Biz      *Biz                 `protobuf:"bytes,7,opt,name=biz,proto3" json:"biz,omitempty"`
	Services *Services            `protobuf:"bytes,8,opt,name=services,proto3" json:"services,omitempty"`
	Auth     *Auth                `protobuf:"bytes,9,opt,name=auth,proto3" json:"auth,omitempty"` // 鉴权配置
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Bootstrap) GetNacos() *Nacos {
	if x != nil {
		return x.Nacos
	}
	return nil
}

func (x *Bootstrap) GetError() *ErrorHandle {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *Bootstrap) GetLog() *Log {
	if x != nil {
		return x.Log
	}
	return nil
}

func (x *Bootstrap) GetSign() map[string]*SignConf {
	if x != nil {
		return x.Sign
	}
	return nil
}

func (x *Bootstrap) GetBiz() *Biz {
	if x != nil {
		return x.Biz
	}
	return nil
}

func (x *Bootstrap) GetServices() *Services {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *Bootstrap) GetAuth() *Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

type ServiceBaseConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host    string               `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
	Key     string               `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	Secret  string               `protobuf:"bytes,4,opt,name=secret,proto3" json:"secret,omitempty"`
	Data    map[string]string    `protobuf:"bytes,5,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ServiceBaseConfig) Reset() {
	*x = ServiceBaseConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceBaseConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceBaseConfig) ProtoMessage() {}

func (x *ServiceBaseConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceBaseConfig.ProtoReflect.Descriptor instead.
func (*ServiceBaseConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceBaseConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *ServiceBaseConfig) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

func (x *ServiceBaseConfig) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ServiceBaseConfig) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *ServiceBaseConfig) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

type Services struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HybridOcr        *ServiceBaseConfig `protobuf:"bytes,1,opt,name=hybrid_ocr,json=hybridOcr,proto3" json:"hybrid_ocr,omitempty"`                        //指尖查词依赖的ocr服务
	EnCorrect        *ServiceBaseConfig `protobuf:"bytes,2,opt,name=en_correct,json=enCorrect,proto3" json:"en_correct,omitempty"`                        //英文纠错服务
	RnVersionControl *ServiceBaseConfig `protobuf:"bytes,3,opt,name=rn_version_control,json=rnVersionControl,proto3" json:"rn_version_control,omitempty"` //rn版本控制服务
	Base64Upload     *ServiceBaseConfig `protobuf:"bytes,4,opt,name=base64_upload,json=base64Upload,proto3" json:"base64_upload,omitempty"`               //base64上传服务
	Safety           *ServiceBaseConfig `protobuf:"bytes,5,opt,name=safety,proto3" json:"safety,omitempty"`                                               //安全服务
	Oss              *ServiceBaseConfig `protobuf:"bytes,6,opt,name=oss,proto3" json:"oss,omitempty"`                                                     //海外上传服务
	Badou            *ServiceBaseConfig `protobuf:"bytes,7,opt,name=badou,proto3" json:"badou,omitempty"`                                                 //八斗平台服务
	TaskDrive        *ServiceBaseConfig `protobuf:"bytes,8,opt,name=task_drive,json=taskDrive,proto3" json:"task_drive,omitempty"`                        //新手任务驱动
	Llm              *Llm               `protobuf:"bytes,9,opt,name=llm,proto3" json:"llm,omitempty"`
	Ucenter          *UCenter           `protobuf:"bytes,10,opt,name=ucenter,proto3" json:"ucenter,omitempty"` // UDC中台用户管理
}

func (x *Services) Reset() {
	*x = Services{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Services) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Services) ProtoMessage() {}

func (x *Services) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Services.ProtoReflect.Descriptor instead.
func (*Services) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Services) GetHybridOcr() *ServiceBaseConfig {
	if x != nil {
		return x.HybridOcr
	}
	return nil
}

func (x *Services) GetEnCorrect() *ServiceBaseConfig {
	if x != nil {
		return x.EnCorrect
	}
	return nil
}

func (x *Services) GetRnVersionControl() *ServiceBaseConfig {
	if x != nil {
		return x.RnVersionControl
	}
	return nil
}

func (x *Services) GetBase64Upload() *ServiceBaseConfig {
	if x != nil {
		return x.Base64Upload
	}
	return nil
}

func (x *Services) GetSafety() *ServiceBaseConfig {
	if x != nil {
		return x.Safety
	}
	return nil
}

func (x *Services) GetOss() *ServiceBaseConfig {
	if x != nil {
		return x.Oss
	}
	return nil
}

func (x *Services) GetBadou() *ServiceBaseConfig {
	if x != nil {
		return x.Badou
	}
	return nil
}

func (x *Services) GetTaskDrive() *ServiceBaseConfig {
	if x != nil {
		return x.TaskDrive
	}
	return nil
}

func (x *Services) GetLlm() *Llm {
	if x != nil {
		return x.Llm
	}
	return nil
}

func (x *Services) GetUcenter() *UCenter {
	if x != nil {
		return x.Ucenter
	}
	return nil
}

type Biz struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kousuan            *SendService        `protobuf:"bytes,1,opt,name=kousuan,proto3" json:"kousuan,omitempty"`
	EnCorrectSwitch    int32               `protobuf:"varint,2,opt,name=en_correct_switch,json=enCorrectSwitch,proto3" json:"en_correct_switch,omitempty"`
	Base64UploadSwitch int32               `protobuf:"varint,3,opt,name=base64_upload_switch,json=base64UploadSwitch,proto3" json:"base64_upload_switch,omitempty"`
	EnWordIndex        string              `protobuf:"bytes,4,opt,name=en_word_index,json=enWordIndex,proto3" json:"en_word_index,omitempty"`
	ReadingBookOcr     *SendService        `protobuf:"bytes,5,opt,name=reading_book_ocr,json=readingBookOcr,proto3" json:"reading_book_ocr,omitempty"`
	TalEnStandard      *TalEnStandard      `protobuf:"bytes,6,opt,name=tal_en_standard,json=talEnStandard,proto3" json:"tal_en_standard,omitempty"`
	EnglishScale       int32               `protobuf:"varint,7,opt,name=english_scale,json=englishScale,proto3" json:"english_scale,omitempty"`
	ReadingOcrSwitch   int32               `protobuf:"varint,8,opt,name=reading_ocr_switch,json=readingOcrSwitch,proto3" json:"reading_ocr_switch,omitempty"`
	EnStandard         *TalEnStandardParam `protobuf:"bytes,9,opt,name=en_standard,json=enStandard,proto3" json:"en_standard,omitempty"`
	AesKey             string              `protobuf:"bytes,10,opt,name=aes_key,json=aesKey,proto3" json:"aes_key,omitempty"`
	CommonConfig       *CommonConfig       `protobuf:"bytes,11,opt,name=common_config,json=commonConfig,proto3" json:"common_config,omitempty"`
	BlogCategories     []string            `protobuf:"bytes,12,rep,name=blog_categories,json=blogCategories,proto3" json:"blog_categories,omitempty"`
	BlogSetEnable      bool                `protobuf:"varint,13,opt,name=blog_set_enable,json=blogSetEnable,proto3" json:"blog_set_enable,omitempty"`
	Grades             []*Grade            `protobuf:"bytes,14,rep,name=grades,proto3" json:"grades,omitempty"` // 年级列表
}

func (x *Biz) Reset() {
	*x = Biz{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Biz) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Biz) ProtoMessage() {}

func (x *Biz) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Biz.ProtoReflect.Descriptor instead.
func (*Biz) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{3}
}

func (x *Biz) GetKousuan() *SendService {
	if x != nil {
		return x.Kousuan
	}
	return nil
}

func (x *Biz) GetEnCorrectSwitch() int32 {
	if x != nil {
		return x.EnCorrectSwitch
	}
	return 0
}

func (x *Biz) GetBase64UploadSwitch() int32 {
	if x != nil {
		return x.Base64UploadSwitch
	}
	return 0
}

func (x *Biz) GetEnWordIndex() string {
	if x != nil {
		return x.EnWordIndex
	}
	return ""
}

func (x *Biz) GetReadingBookOcr() *SendService {
	if x != nil {
		return x.ReadingBookOcr
	}
	return nil
}

func (x *Biz) GetTalEnStandard() *TalEnStandard {
	if x != nil {
		return x.TalEnStandard
	}
	return nil
}

func (x *Biz) GetEnglishScale() int32 {
	if x != nil {
		return x.EnglishScale
	}
	return 0
}

func (x *Biz) GetReadingOcrSwitch() int32 {
	if x != nil {
		return x.ReadingOcrSwitch
	}
	return 0
}

func (x *Biz) GetEnStandard() *TalEnStandardParam {
	if x != nil {
		return x.EnStandard
	}
	return nil
}

func (x *Biz) GetAesKey() string {
	if x != nil {
		return x.AesKey
	}
	return ""
}

func (x *Biz) GetCommonConfig() *CommonConfig {
	if x != nil {
		return x.CommonConfig
	}
	return nil
}

func (x *Biz) GetBlogCategories() []string {
	if x != nil {
		return x.BlogCategories
	}
	return nil
}

func (x *Biz) GetBlogSetEnable() bool {
	if x != nil {
		return x.BlogSetEnable
	}
	return false
}

func (x *Biz) GetGrades() []*Grade {
	if x != nil {
		return x.Grades
	}
	return nil
}

type Grade struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`   // 年级名称
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"` // 年级值
}

func (x *Grade) Reset() {
	*x = Grade{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Grade) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Grade) ProtoMessage() {}

func (x *Grade) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Grade.ProtoReflect.Descriptor instead.
func (*Grade) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{4}
}

func (x *Grade) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Grade) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type CommonConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ranges    []*Range `protobuf:"bytes,1,rep,name=ranges,proto3" json:"ranges,omitempty"`
	RightLine int32    `protobuf:"varint,2,opt,name=right_line,json=rightLine,proto3" json:"right_line,omitempty"`
}

func (x *CommonConfig) Reset() {
	*x = CommonConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonConfig) ProtoMessage() {}

func (x *CommonConfig) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonConfig.ProtoReflect.Descriptor instead.
func (*CommonConfig) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{5}
}

func (x *CommonConfig) GetRanges() []*Range {
	if x != nil {
		return x.Ranges
	}
	return nil
}

func (x *CommonConfig) GetRightLine() int32 {
	if x != nil {
		return x.RightLine
	}
	return 0
}

type Range struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Word       string `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"`
	Min        int32  `protobuf:"varint,2,opt,name=min,proto3" json:"min,omitempty"`
	Max        int32  `protobuf:"varint,3,opt,name=max,proto3" json:"max,omitempty"`
	IncludeMin bool   `protobuf:"varint,4,opt,name=include_min,json=includeMin,proto3" json:"include_min,omitempty"`
	IncludeMax bool   `protobuf:"varint,5,opt,name=include_max,json=includeMax,proto3" json:"include_max,omitempty"`
}

func (x *Range) Reset() {
	*x = Range{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Range) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Range) ProtoMessage() {}

func (x *Range) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Range.ProtoReflect.Descriptor instead.
func (*Range) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{6}
}

func (x *Range) GetWord() string {
	if x != nil {
		return x.Word
	}
	return ""
}

func (x *Range) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *Range) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *Range) GetIncludeMin() bool {
	if x != nil {
		return x.IncludeMin
	}
	return false
}

func (x *Range) GetIncludeMax() bool {
	if x != nil {
		return x.IncludeMax
	}
	return false
}

type TalEnStandardParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PronWeight           float32 `protobuf:"fixed32,1,opt,name=pron_weight,json=pronWeight,proto3" json:"pron_weight,omitempty"`
	FluencyWeight        float32 `protobuf:"fixed32,2,opt,name=fluency_weight,json=fluencyWeight,proto3" json:"fluency_weight,omitempty"`
	IntegrityWeight      float32 `protobuf:"fixed32,3,opt,name=integrity_weight,json=integrityWeight,proto3" json:"integrity_weight,omitempty"`
	VolumeWeight         float32 `protobuf:"fixed32,4,opt,name=volume_weight,json=volumeWeight,proto3" json:"volume_weight,omitempty"`
	NeedUrl              bool    `protobuf:"varint,5,opt,name=need_url,json=needUrl,proto3" json:"need_url,omitempty"`
	VadMaxSec            float32 `protobuf:"fixed32,6,opt,name=vad_max_sec,json=vadMaxSec,proto3" json:"vad_max_sec,omitempty"`
	VadPauseSec          float32 `protobuf:"fixed32,7,opt,name=vad_pause_sec,json=vadPauseSec,proto3" json:"vad_pause_sec,omitempty"`
	VadStSilSec          float32 `protobuf:"fixed32,8,opt,name=vad_st_sil_sec,json=vadStSilSec,proto3" json:"vad_st_sil_sec,omitempty"`
	SuffixPenalQuick     float32 `protobuf:"fixed32,9,opt,name=suffix_penal_quick,json=suffixPenalQuick,proto3" json:"suffix_penal_quick,omitempty"`
	HighScoreThreshold   float32 `protobuf:"fixed32,10,opt,name=high_score_threshold,json=highScoreThreshold,proto3" json:"high_score_threshold,omitempty"`
	HighStopLowThreshold float32 `protobuf:"fixed32,11,opt,name=high_stop_low_threshold,json=highStopLowThreshold,proto3" json:"high_stop_low_threshold,omitempty"`
}

func (x *TalEnStandardParam) Reset() {
	*x = TalEnStandardParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TalEnStandardParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TalEnStandardParam) ProtoMessage() {}

func (x *TalEnStandardParam) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TalEnStandardParam.ProtoReflect.Descriptor instead.
func (*TalEnStandardParam) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{7}
}

func (x *TalEnStandardParam) GetPronWeight() float32 {
	if x != nil {
		return x.PronWeight
	}
	return 0
}

func (x *TalEnStandardParam) GetFluencyWeight() float32 {
	if x != nil {
		return x.FluencyWeight
	}
	return 0
}

func (x *TalEnStandardParam) GetIntegrityWeight() float32 {
	if x != nil {
		return x.IntegrityWeight
	}
	return 0
}

func (x *TalEnStandardParam) GetVolumeWeight() float32 {
	if x != nil {
		return x.VolumeWeight
	}
	return 0
}

func (x *TalEnStandardParam) GetNeedUrl() bool {
	if x != nil {
		return x.NeedUrl
	}
	return false
}

func (x *TalEnStandardParam) GetVadMaxSec() float32 {
	if x != nil {
		return x.VadMaxSec
	}
	return 0
}

func (x *TalEnStandardParam) GetVadPauseSec() float32 {
	if x != nil {
		return x.VadPauseSec
	}
	return 0
}

func (x *TalEnStandardParam) GetVadStSilSec() float32 {
	if x != nil {
		return x.VadStSilSec
	}
	return 0
}

func (x *TalEnStandardParam) GetSuffixPenalQuick() float32 {
	if x != nil {
		return x.SuffixPenalQuick
	}
	return 0
}

func (x *TalEnStandardParam) GetHighScoreThreshold() float32 {
	if x != nil {
		return x.HighScoreThreshold
	}
	return 0
}

func (x *TalEnStandardParam) GetHighStopLowThreshold() float32 {
	if x != nil {
		return x.HighStopLowThreshold
	}
	return 0
}

type TalEnStandard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Appkey string `protobuf:"bytes,2,opt,name=appkey,proto3" json:"appkey,omitempty"`
	Secret string `protobuf:"bytes,3,opt,name=secret,proto3" json:"secret,omitempty"`
}

func (x *TalEnStandard) Reset() {
	*x = TalEnStandard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TalEnStandard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TalEnStandard) ProtoMessage() {}

func (x *TalEnStandard) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TalEnStandard.ProtoReflect.Descriptor instead.
func (*TalEnStandard) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{8}
}

func (x *TalEnStandard) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *TalEnStandard) GetAppkey() string {
	if x != nil {
		return x.Appkey
	}
	return ""
}

func (x *TalEnStandard) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

type SendService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host    string               `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *SendService) Reset() {
	*x = SendService{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendService) ProtoMessage() {}

func (x *SendService) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendService.ProtoReflect.Descriptor instead.
func (*SendService) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{9}
}

func (x *SendService) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *SendService) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Http *Server_HTTP `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc *Server_GRPC `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{10}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

type Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database *Data_Database `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Redis    *Data_Redis    `protobuf:"bytes,2,opt,name=redis,proto3" json:"redis,omitempty"`
	Es       *Data_Es       `protobuf:"bytes,3,opt,name=es,proto3" json:"es,omitempty"`
}

func (x *Data) Reset() {
	*x = Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{11}
}

func (x *Data) GetDatabase() *Data_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedis() *Data_Redis {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Data) GetEs() *Data_Es {
	if x != nil {
		return x.Es
	}
	return nil
}

type Nacos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address     string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Port        uint64 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	NamespaceId string `protobuf:"bytes,3,opt,name=namespaceId,proto3" json:"namespaceId,omitempty"`
	ServerGroup string `protobuf:"bytes,4,opt,name=serverGroup,proto3" json:"serverGroup,omitempty"`
}

func (x *Nacos) Reset() {
	*x = Nacos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nacos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nacos) ProtoMessage() {}

func (x *Nacos) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nacos.ProtoReflect.Descriptor instead.
func (*Nacos) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{12}
}

func (x *Nacos) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Nacos) GetPort() uint64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Nacos) GetNamespaceId() string {
	if x != nil {
		return x.NamespaceId
	}
	return ""
}

func (x *Nacos) GetServerGroup() string {
	if x != nil {
		return x.ServerGroup
	}
	return ""
}

type ErrorHandle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Handle  map[string]*ErrorHandle_ErrorMessages `protobuf:"bytes,1,rep,name=handle,proto3" json:"handle,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Default string                                `protobuf:"bytes,2,opt,name=default,proto3" json:"default,omitempty"`
}

func (x *ErrorHandle) Reset() {
	*x = ErrorHandle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorHandle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorHandle) ProtoMessage() {}

func (x *ErrorHandle) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorHandle.ProtoReflect.Descriptor instead.
func (*ErrorHandle) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{13}
}

func (x *ErrorHandle) GetHandle() map[string]*ErrorHandle_ErrorMessages {
	if x != nil {
		return x.Handle
	}
	return nil
}

func (x *ErrorHandle) GetDefault() string {
	if x != nil {
		return x.Default
	}
	return ""
}

type Log struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename  string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
	MaxSize   int32  `protobuf:"varint,2,opt,name=max_size,json=maxSize,proto3" json:"max_size,omitempty"`
	MaxBackup int32  `protobuf:"varint,3,opt,name=max_backup,json=maxBackup,proto3" json:"max_backup,omitempty"`
	MaxAge    int32  `protobuf:"varint,4,opt,name=max_age,json=maxAge,proto3" json:"max_age,omitempty"`
	Compress  bool   `protobuf:"varint,5,opt,name=compress,proto3" json:"compress,omitempty"`
}

func (x *Log) Reset() {
	*x = Log{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Log) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Log) ProtoMessage() {}

func (x *Log) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Log.ProtoReflect.Descriptor instead.
func (*Log) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{14}
}

func (x *Log) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *Log) GetMaxSize() int32 {
	if x != nil {
		return x.MaxSize
	}
	return 0
}

func (x *Log) GetMaxBackup() int32 {
	if x != nil {
		return x.MaxBackup
	}
	return 0
}

func (x *Log) GetMaxAge() int32 {
	if x != nil {
		return x.MaxAge
	}
	return 0
}

func (x *Log) GetCompress() bool {
	if x != nil {
		return x.Compress
	}
	return false
}

type SignConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignKey string `protobuf:"bytes,1,opt,name=sign_key,json=signKey,proto3" json:"sign_key,omitempty"`
}

func (x *SignConf) Reset() {
	*x = SignConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignConf) ProtoMessage() {}

func (x *SignConf) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignConf.ProtoReflect.Descriptor instead.
func (*SignConf) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{15}
}

func (x *SignConf) GetSignKey() string {
	if x != nil {
		return x.SignKey
	}
	return ""
}

type Llm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Key  string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *Llm) Reset() {
	*x = Llm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Llm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Llm) ProtoMessage() {}

func (x *Llm) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Llm.ProtoReflect.Descriptor instead.
func (*Llm) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{16}
}

func (x *Llm) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Llm) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`             // 应用ID
	AppSecret string `protobuf:"bytes,2,opt,name=app_secret,json=appSecret,proto3" json:"app_secret,omitempty"` // 应用名称
}

func (x *AppInfo) Reset() {
	*x = AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppInfo) ProtoMessage() {}

func (x *AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppInfo.ProtoReflect.Descriptor instead.
func (*AppInfo) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{17}
}

func (x *AppInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AppInfo) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

// UDC中台用户管理
type UCenter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PublicKey string   `protobuf:"bytes,1,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"` // UCenter公钥
	Domain    string   `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`                        // UCenter域名
	Env       string   `protobuf:"bytes,3,opt,name=env,proto3" json:"env,omitempty"`                              // 环境标识，sandbox、sandbox_internal、prod_internal, prod
	Suffix    string   `protobuf:"bytes,4,opt,name=suffix,proto3" json:"suffix,omitempty"`                        // UCenter后缀
	TicketUrl string   `protobuf:"bytes,5,opt,name=ticket_url,json=ticketUrl,proto3" json:"ticket_url,omitempty"` // UCenter ticket获取地址
	AppInfo   *AppInfo `protobuf:"bytes,6,opt,name=app_info,json=appInfo,proto3" json:"app_info,omitempty"`       // 应用信息
	ClientId  string   `protobuf:"bytes,7,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`    // 客户端ID
	Host      string   `protobuf:"bytes,8,opt,name=host,proto3" json:"host,omitempty"`                            // UCenter服务地址
}

func (x *UCenter) Reset() {
	*x = UCenter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UCenter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UCenter) ProtoMessage() {}

func (x *UCenter) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UCenter.ProtoReflect.Descriptor instead.
func (*UCenter) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{18}
}

func (x *UCenter) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

func (x *UCenter) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *UCenter) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *UCenter) GetSuffix() string {
	if x != nil {
		return x.Suffix
	}
	return ""
}

func (x *UCenter) GetTicketUrl() string {
	if x != nil {
		return x.TicketUrl
	}
	return ""
}

func (x *UCenter) GetAppInfo() *AppInfo {
	if x != nil {
		return x.AppInfo
	}
	return nil
}

func (x *UCenter) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *UCenter) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

type Auth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProtectedUrls []string `protobuf:"bytes,1,rep,name=protected_urls,json=protectedUrls,proto3" json:"protected_urls,omitempty"` // 需要鉴权的URL
}

func (x *Auth) Reset() {
	*x = Auth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Auth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Auth) ProtoMessage() {}

func (x *Auth) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Auth.ProtoReflect.Descriptor instead.
func (*Auth) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{19}
}

func (x *Auth) GetProtectedUrls() []string {
	if x != nil {
		return x.ProtectedUrls
	}
	return nil
}

type Server_HTTP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{10, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{10, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Data_Database struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Driver string `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	Source string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *Data_Database) Reset() {
	*x = Data_Database{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Database) ProtoMessage() {}

func (x *Data_Database) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Database.ProtoReflect.Descriptor instead.
func (*Data_Database) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{11, 0}
}

func (x *Data_Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *Data_Database) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

type Data_Redis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network      string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr         string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Password     string               `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	Db           int32                `protobuf:"varint,4,opt,name=db,proto3" json:"db,omitempty"`
	DialTimeout  *durationpb.Duration `protobuf:"bytes,5,opt,name=dial_timeout,json=dialTimeout,proto3" json:"dial_timeout,omitempty"`
	ReadTimeout  *durationpb.Duration `protobuf:"bytes,6,opt,name=read_timeout,json=readTimeout,proto3" json:"read_timeout,omitempty"`
	WriteTimeout *durationpb.Duration `protobuf:"bytes,7,opt,name=write_timeout,json=writeTimeout,proto3" json:"write_timeout,omitempty"`
	PoolSize     int32                `protobuf:"varint,8,opt,name=pool_size,json=poolSize,proto3" json:"pool_size,omitempty"`
}

func (x *Data_Redis) Reset() {
	*x = Data_Redis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Redis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Redis) ProtoMessage() {}

func (x *Data_Redis) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Redis.ProtoReflect.Descriptor instead.
func (*Data_Redis) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{11, 1}
}

func (x *Data_Redis) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Data_Redis) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Data_Redis) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Data_Redis) GetDb() int32 {
	if x != nil {
		return x.Db
	}
	return 0
}

func (x *Data_Redis) GetDialTimeout() *durationpb.Duration {
	if x != nil {
		return x.DialTimeout
	}
	return nil
}

func (x *Data_Redis) GetReadTimeout() *durationpb.Duration {
	if x != nil {
		return x.ReadTimeout
	}
	return nil
}

func (x *Data_Redis) GetWriteTimeout() *durationpb.Duration {
	if x != nil {
		return x.WriteTimeout
	}
	return nil
}

func (x *Data_Redis) GetPoolSize() int32 {
	if x != nil {
		return x.PoolSize
	}
	return 0
}

type Data_Es struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host     string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *Data_Es) Reset() {
	*x = Data_Es{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Es) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Es) ProtoMessage() {}

func (x *Data_Es) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Es.ProtoReflect.Descriptor instead.
func (*Data_Es) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{11, 2}
}

func (x *Data_Es) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Data_Es) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Data_Es) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type ErrorHandle_ErrorMessages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorMessages []*ErrorHandle_ErrorMessage `protobuf:"bytes,1,rep,name=error_messages,json=errorMessages,proto3" json:"error_messages,omitempty"`
}

func (x *ErrorHandle_ErrorMessages) Reset() {
	*x = ErrorHandle_ErrorMessages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorHandle_ErrorMessages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorHandle_ErrorMessages) ProtoMessage() {}

func (x *ErrorHandle_ErrorMessages) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorHandle_ErrorMessages.ProtoReflect.Descriptor instead.
func (*ErrorHandle_ErrorMessages) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ErrorHandle_ErrorMessages) GetErrorMessages() []*ErrorHandle_ErrorMessage {
	if x != nil {
		return x.ErrorMessages
	}
	return nil
}

type ErrorHandle_ErrorMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorReason string `protobuf:"bytes,1,opt,name=error_reason,json=errorReason,proto3" json:"error_reason,omitempty"`
	Message     string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ErrorHandle_ErrorMessage) Reset() {
	*x = ErrorHandle_ErrorMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_conf_conf_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorHandle_ErrorMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorHandle_ErrorMessage) ProtoMessage() {}

func (x *ErrorHandle_ErrorMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorHandle_ErrorMessage.ProtoReflect.Descriptor instead.
func (*ErrorHandle_ErrorMessage) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{13, 1}
}

func (x *ErrorHandle_ErrorMessage) GetErrorReason() string {
	if x != nil {
		return x.ErrorReason
	}
	return ""
}

func (x *ErrorHandle_ErrorMessage) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_internal_conf_conf_proto protoreflect.FileDescriptor

var file_internal_conf_conf_proto_rawDesc = []byte{
	0x0a, 0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd7, 0x03, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x74, 0x73,
	0x74, 0x72, 0x61, 0x70, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x05, 0x6e, 0x61, 0x63, 0x6f, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x4e, 0x61, 0x63, 0x6f, 0x73, 0x52, 0x05, 0x6e, 0x61, 0x63, 0x6f, 0x73, 0x12,
	0x2d, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x21,
	0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x6f, 0x67, 0x52, 0x03, 0x6c, 0x6f,
	0x67, 0x12, 0x33, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x6f, 0x6f,
	0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x21, 0x0a, 0x03, 0x62, 0x69, 0x7a, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x42, 0x69, 0x7a, 0x52, 0x03, 0x62, 0x69, 0x7a, 0x12, 0x30, 0x0a, 0x08, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x04, 0x61,
	0x75, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x04, 0x61, 0x75, 0x74,
	0x68, 0x1a, 0x4d, 0x0a, 0x09, 0x53, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xfc, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xc4, 0x04, 0x0a, 0x08, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0a,
	0x68, 0x79, 0x62, 0x72, 0x69, 0x64, 0x5f, 0x6f, 0x63, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x09, 0x68, 0x79, 0x62, 0x72, 0x69, 0x64, 0x4f, 0x63, 0x72, 0x12, 0x3c, 0x0a, 0x0a, 0x65, 0x6e,
	0x5f, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x65,
	0x6e, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x12, 0x4b, 0x0a, 0x12, 0x72, 0x6e, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x10, 0x72, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x42, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f,
	0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x62, 0x61, 0x73,
	0x65, 0x36, 0x34, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79,
	0x12, 0x2f, 0x0a, 0x03, 0x6f, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x03, 0x6f, 0x73,
	0x73, 0x12, 0x33, 0x0a, 0x05, 0x62, 0x61, 0x64, 0x6f, 0x75, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x05, 0x62, 0x61, 0x64, 0x6f, 0x75, 0x12, 0x3c, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x12, 0x21, 0x0a, 0x03, 0x6c, 0x6c, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4c,
	0x6c, 0x6d, 0x52, 0x03, 0x6c, 0x6c, 0x6d, 0x12, 0x2d, 0x0a, 0x07, 0x75, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x52, 0x07, 0x75,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x22, 0xa8, 0x05, 0x0a, 0x03, 0x42, 0x69, 0x7a, 0x12, 0x31,
	0x0a, 0x07, 0x6b, 0x6f, 0x75, 0x73, 0x75, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x6b, 0x6f, 0x75, 0x73, 0x75, 0x61,
	0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x5f,
	0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x65, 0x6e,
	0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x30, 0x0a,
	0x14, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x62, 0x61, 0x73,
	0x65, 0x36, 0x34, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12,
	0x22, 0x0a, 0x0d, 0x65, 0x6e, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e, 0x57, 0x6f, 0x72, 0x64, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x41, 0x0a, 0x10, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x63, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x42,
	0x6f, 0x6f, 0x6b, 0x4f, 0x63, 0x72, 0x12, 0x41, 0x0a, 0x0f, 0x74, 0x61, 0x6c, 0x5f, 0x65, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x61, 0x6c,
	0x45, 0x6e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x52, 0x0d, 0x74, 0x61, 0x6c, 0x45,
	0x6e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x67,
	0x6c, 0x69, 0x73, 0x68, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x65, 0x6e, 0x67, 0x6c, 0x69, 0x73, 0x68, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x63, 0x72, 0x5f, 0x73, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x72, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x4f, 0x63, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x3f, 0x0a, 0x0b,
	0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54,
	0x61, 0x6c, 0x45, 0x6e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x0a, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x61, 0x65, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x65, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x6c, 0x6f, 0x67, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e,
	0x62, 0x6c, 0x6f, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x62, 0x6c, 0x6f, 0x67, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x62, 0x6c, 0x6f, 0x67, 0x53, 0x65, 0x74,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x67, 0x72, 0x61, 0x64, 0x65, 0x73,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x47, 0x72, 0x61, 0x64, 0x65, 0x52, 0x06, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x73, 0x22, 0x31, 0x0a, 0x05, 0x47, 0x72, 0x61, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x58, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x29, 0x0a, 0x06, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x06, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x22, 0x81,
	0x01, 0x0a, 0x05, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6d, 0x61, 0x78,
	0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4d, 0x69,
	0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6d, 0x61, 0x78,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4d,
	0x61, 0x78, 0x22, 0xc7, 0x03, 0x0a, 0x12, 0x54, 0x61, 0x6c, 0x45, 0x6e, 0x53, 0x74, 0x61, 0x6e,
	0x64, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f,
	0x6e, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a,
	0x70, 0x72, 0x6f, 0x6e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x6c,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0d, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x69, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0c, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x57, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x6e, 0x65, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0b,
	0x76, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x76, 0x61, 0x64, 0x4d, 0x61, 0x78, 0x53, 0x65, 0x63, 0x12, 0x22, 0x0a, 0x0d,
	0x76, 0x61, 0x64, 0x5f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0b, 0x76, 0x61, 0x64, 0x50, 0x61, 0x75, 0x73, 0x65, 0x53, 0x65, 0x63,
	0x12, 0x23, 0x0a, 0x0e, 0x76, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x5f, 0x73, 0x69, 0x6c, 0x5f, 0x73,
	0x65, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x76, 0x61, 0x64, 0x53, 0x74, 0x53,
	0x69, 0x6c, 0x53, 0x65, 0x63, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x5f,
	0x70, 0x65, 0x6e, 0x61, 0x6c, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x10, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x50, 0x65, 0x6e, 0x61, 0x6c, 0x51, 0x75,
	0x69, 0x63, 0x6b, 0x12, 0x30, 0x0a, 0x14, 0x68, 0x69, 0x67, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x12, 0x68, 0x69, 0x67, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x68, 0x69, 0x67, 0x68, 0x5f, 0x73, 0x74,
	0x6f, 0x70, 0x5f, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x68, 0x69, 0x67, 0x68, 0x53, 0x74, 0x6f, 0x70,
	0x4c, 0x6f, 0x77, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0x51, 0x0a, 0x0d,
	0x54, 0x61, 0x6c, 0x45, 0x6e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x70, 0x70, 0x6b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22,
	0x56, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f,
	0x73, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0xb8, 0x02, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x12, 0x2b, 0x0a, 0x04, 0x68, 0x74, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x04, 0x68, 0x74, 0x74, 0x70, 0x12,
	0x2b, 0x0a, 0x04, 0x67, 0x72, 0x70, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x47, 0x52, 0x50, 0x43, 0x52, 0x04, 0x67, 0x72, 0x70, 0x63, 0x1a, 0x69, 0x0a, 0x04,
	0x48, 0x54, 0x54, 0x50, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12,
	0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64,
	0x64, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0x69, 0x0a, 0x04, 0x47, 0x52, 0x50, 0x43, 0x12,
	0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x33, 0x0a,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x22, 0xdb, 0x04, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61,
	0x73, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73,
	0x12, 0x23, 0x0a, 0x02, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x45,
	0x73, 0x52, 0x02, 0x65, 0x73, 0x1a, 0x3a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x1a, 0xba, 0x02, 0x0a, 0x05, 0x52, 0x65, 0x64, 0x69, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x64, 0x62, 0x12, 0x3c, 0x0a, 0x0c, 0x64, 0x69, 0x61, 0x6c, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x64, 0x69, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x12, 0x3c, 0x0a, 0x0c, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x12, 0x3e, 0x0a, 0x0d, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x6f, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x1a, 0x50,
	0x0a, 0x02, 0x45, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x22, 0x79, 0x0a, 0x05, 0x4e, 0x61, 0x63, 0x6f, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xf1, 0x02, 0x0a, 0x0b,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x06, 0x68,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x1a, 0x5c, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x0e, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x1a, 0x4b, 0x0a, 0x0c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x60, 0x0a,
	0x0b, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x90, 0x01, 0x0a, 0x03, 0x4c, 0x6f, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x17, 0x0a,
	0x07, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x22, 0x25, 0x0a, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x69, 0x67, 0x6e, 0x4b, 0x65, 0x79, 0x22, 0x2b, 0x0a, 0x03, 0x4c, 0x6c, 0x6d,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x3f, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70,
	0x70, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0xea, 0x01, 0x0a, 0x07, 0x55, 0x43, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b,
	0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e,
	0x76, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x75,
	0x66, 0x66, 0x69, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x70, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x22, 0x2d, 0x0a, 0x04, 0x41, 0x75, 0x74, 0x68, 0x12, 0x25, 0x0a, 0x0e,
	0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x55,
	0x72, 0x6c, 0x73, 0x42, 0x24, 0x5a, 0x22, 0x68, 0x77, 0x2d, 0x70, 0x61, 0x61, 0x73, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x3b, 0x63, 0x6f, 0x6e, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_internal_conf_conf_proto_rawDescOnce sync.Once
	file_internal_conf_conf_proto_rawDescData = file_internal_conf_conf_proto_rawDesc
)

func file_internal_conf_conf_proto_rawDescGZIP() []byte {
	file_internal_conf_conf_proto_rawDescOnce.Do(func() {
		file_internal_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_conf_conf_proto_rawDescData)
	})
	return file_internal_conf_conf_proto_rawDescData
}

var file_internal_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_internal_conf_conf_proto_goTypes = []interface{}{
	(*Bootstrap)(nil),                 // 0: kratos.api.Bootstrap
	(*ServiceBaseConfig)(nil),         // 1: kratos.api.ServiceBaseConfig
	(*Services)(nil),                  // 2: kratos.api.Services
	(*Biz)(nil),                       // 3: kratos.api.Biz
	(*Grade)(nil),                     // 4: kratos.api.Grade
	(*CommonConfig)(nil),              // 5: kratos.api.CommonConfig
	(*Range)(nil),                     // 6: kratos.api.Range
	(*TalEnStandardParam)(nil),        // 7: kratos.api.TalEnStandardParam
	(*TalEnStandard)(nil),             // 8: kratos.api.TalEnStandard
	(*SendService)(nil),               // 9: kratos.api.SendService
	(*Server)(nil),                    // 10: kratos.api.Server
	(*Data)(nil),                      // 11: kratos.api.Data
	(*Nacos)(nil),                     // 12: kratos.api.Nacos
	(*ErrorHandle)(nil),               // 13: kratos.api.ErrorHandle
	(*Log)(nil),                       // 14: kratos.api.Log
	(*SignConf)(nil),                  // 15: kratos.api.SignConf
	(*Llm)(nil),                       // 16: kratos.api.Llm
	(*AppInfo)(nil),                   // 17: kratos.api.AppInfo
	(*UCenter)(nil),                   // 18: kratos.api.UCenter
	(*Auth)(nil),                      // 19: kratos.api.Auth
	nil,                               // 20: kratos.api.Bootstrap.SignEntry
	nil,                               // 21: kratos.api.ServiceBaseConfig.DataEntry
	(*Server_HTTP)(nil),               // 22: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),               // 23: kratos.api.Server.GRPC
	(*Data_Database)(nil),             // 24: kratos.api.Data.Database
	(*Data_Redis)(nil),                // 25: kratos.api.Data.Redis
	(*Data_Es)(nil),                   // 26: kratos.api.Data.Es
	(*ErrorHandle_ErrorMessages)(nil), // 27: kratos.api.ErrorHandle.ErrorMessages
	(*ErrorHandle_ErrorMessage)(nil),  // 28: kratos.api.ErrorHandle.ErrorMessage
	nil,                               // 29: kratos.api.ErrorHandle.HandleEntry
	(*durationpb.Duration)(nil),       // 30: google.protobuf.Duration
}
var file_internal_conf_conf_proto_depIdxs = []int32{
	10, // 0: kratos.api.Bootstrap.server:type_name -> kratos.api.Server
	11, // 1: kratos.api.Bootstrap.data:type_name -> kratos.api.Data
	12, // 2: kratos.api.Bootstrap.nacos:type_name -> kratos.api.Nacos
	13, // 3: kratos.api.Bootstrap.error:type_name -> kratos.api.ErrorHandle
	14, // 4: kratos.api.Bootstrap.log:type_name -> kratos.api.Log
	20, // 5: kratos.api.Bootstrap.sign:type_name -> kratos.api.Bootstrap.SignEntry
	3,  // 6: kratos.api.Bootstrap.biz:type_name -> kratos.api.Biz
	2,  // 7: kratos.api.Bootstrap.services:type_name -> kratos.api.Services
	19, // 8: kratos.api.Bootstrap.auth:type_name -> kratos.api.Auth
	30, // 9: kratos.api.ServiceBaseConfig.timeout:type_name -> google.protobuf.Duration
	21, // 10: kratos.api.ServiceBaseConfig.data:type_name -> kratos.api.ServiceBaseConfig.DataEntry
	1,  // 11: kratos.api.Services.hybrid_ocr:type_name -> kratos.api.ServiceBaseConfig
	1,  // 12: kratos.api.Services.en_correct:type_name -> kratos.api.ServiceBaseConfig
	1,  // 13: kratos.api.Services.rn_version_control:type_name -> kratos.api.ServiceBaseConfig
	1,  // 14: kratos.api.Services.base64_upload:type_name -> kratos.api.ServiceBaseConfig
	1,  // 15: kratos.api.Services.safety:type_name -> kratos.api.ServiceBaseConfig
	1,  // 16: kratos.api.Services.oss:type_name -> kratos.api.ServiceBaseConfig
	1,  // 17: kratos.api.Services.badou:type_name -> kratos.api.ServiceBaseConfig
	1,  // 18: kratos.api.Services.task_drive:type_name -> kratos.api.ServiceBaseConfig
	16, // 19: kratos.api.Services.llm:type_name -> kratos.api.Llm
	18, // 20: kratos.api.Services.ucenter:type_name -> kratos.api.UCenter
	9,  // 21: kratos.api.Biz.kousuan:type_name -> kratos.api.SendService
	9,  // 22: kratos.api.Biz.reading_book_ocr:type_name -> kratos.api.SendService
	8,  // 23: kratos.api.Biz.tal_en_standard:type_name -> kratos.api.TalEnStandard
	7,  // 24: kratos.api.Biz.en_standard:type_name -> kratos.api.TalEnStandardParam
	5,  // 25: kratos.api.Biz.common_config:type_name -> kratos.api.CommonConfig
	4,  // 26: kratos.api.Biz.grades:type_name -> kratos.api.Grade
	6,  // 27: kratos.api.CommonConfig.ranges:type_name -> kratos.api.Range
	30, // 28: kratos.api.SendService.timeout:type_name -> google.protobuf.Duration
	22, // 29: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	23, // 30: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	24, // 31: kratos.api.Data.database:type_name -> kratos.api.Data.Database
	25, // 32: kratos.api.Data.redis:type_name -> kratos.api.Data.Redis
	26, // 33: kratos.api.Data.es:type_name -> kratos.api.Data.Es
	29, // 34: kratos.api.ErrorHandle.handle:type_name -> kratos.api.ErrorHandle.HandleEntry
	17, // 35: kratos.api.UCenter.app_info:type_name -> kratos.api.AppInfo
	15, // 36: kratos.api.Bootstrap.SignEntry.value:type_name -> kratos.api.SignConf
	30, // 37: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	30, // 38: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	30, // 39: kratos.api.Data.Redis.dial_timeout:type_name -> google.protobuf.Duration
	30, // 40: kratos.api.Data.Redis.read_timeout:type_name -> google.protobuf.Duration
	30, // 41: kratos.api.Data.Redis.write_timeout:type_name -> google.protobuf.Duration
	28, // 42: kratos.api.ErrorHandle.ErrorMessages.error_messages:type_name -> kratos.api.ErrorHandle.ErrorMessage
	27, // 43: kratos.api.ErrorHandle.HandleEntry.value:type_name -> kratos.api.ErrorHandle.ErrorMessages
	44, // [44:44] is the sub-list for method output_type
	44, // [44:44] is the sub-list for method input_type
	44, // [44:44] is the sub-list for extension type_name
	44, // [44:44] is the sub-list for extension extendee
	0,  // [0:44] is the sub-list for field type_name
}

func init() { file_internal_conf_conf_proto_init() }
func file_internal_conf_conf_proto_init() {
	if File_internal_conf_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_conf_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bootstrap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceBaseConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Services); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Biz); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Grade); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Range); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TalEnStandardParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TalEnStandard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendService); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nacos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorHandle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Log); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Llm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UCenter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Auth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_HTTP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_GRPC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Database); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Redis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Es); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorHandle_ErrorMessages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_conf_conf_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorHandle_ErrorMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_conf_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_conf_conf_proto_goTypes,
		DependencyIndexes: file_internal_conf_conf_proto_depIdxs,
		MessageInfos:      file_internal_conf_conf_proto_msgTypes,
	}.Build()
	File_internal_conf_conf_proto = out.File
	file_internal_conf_conf_proto_rawDesc = nil
	file_internal_conf_conf_proto_goTypes = nil
	file_internal_conf_conf_proto_depIdxs = nil
}
