syntax = "proto3";
package kratos.api;

option go_package = "hw-paas-service/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  Nacos nacos = 3;
  ErrorHandle error = 4;
  Log log = 5;
  map<string, SignConf> sign = 6;
  Biz biz = 7;
  Services services = 8;
  Auth auth = 9; // 鉴权配置
}

message ServiceBaseConfig {
  string host = 1;
  google.protobuf.Duration timeout = 2;
  string key = 3;
  string secret = 4;
  map<string, string> data = 5;
}

message Services {
  ServiceBaseConfig hybrid_ocr = 1; //指尖查词依赖的ocr服务
  ServiceBaseConfig en_correct = 2; //英文纠错服务
  ServiceBaseConfig rn_version_control = 3; //rn版本控制服务
  ServiceBaseConfig base64_upload = 4; //base64上传服务
  ServiceBaseConfig safety = 5; //安全服务
  ServiceBaseConfig oss = 6; //海外上传服务
  ServiceBaseConfig badou = 7; //八斗平台服务
  ServiceBaseConfig task_drive = 8; //新手任务驱动
  Llm llm = 9;
  UCenter ucenter = 10; // UDC中台用户管理
}

message Biz{
  SendService kousuan = 1;
  int32 en_correct_switch = 2;
  int32 base64_upload_switch = 3;
  string en_word_index = 4;
  SendService reading_book_ocr = 5;
  TalEnStandard tal_en_standard = 6;
  int32 english_scale = 7;
  int32 reading_ocr_switch = 8;
  TalEnStandardParam en_standard = 9;
  string aes_key = 10;
  CommonConfig common_config = 11;
  repeated string blog_categories = 12;
  bool blog_set_enable = 13;
  repeated Grade grades = 14; // 年级列表
}

message Grade {
  string name = 1; // 年级名称
  string value = 2; // 年级值
}
message CommonConfig{
  repeated Range ranges = 1;
  int32 right_line = 2;
}
message Range {
  string word = 1;
  int32 min = 2;
  int32 max = 3;
  bool include_min = 4;
  bool include_max = 5;
}
message TalEnStandardParam {
  float pron_weight = 1;
  float fluency_weight = 2;
  float integrity_weight = 3;
  float volume_weight = 4;
  bool need_url = 5;
  float vad_max_sec = 6;
  float vad_pause_sec = 7;
  float vad_st_sil_sec = 8;
  float suffix_penal_quick = 9;
  float high_score_threshold = 10;
  float high_stop_low_threshold = 11;
}

message TalEnStandard {
  string url = 1;
  string appkey = 2;
  string secret = 3;
}

message SendService {
  string host = 1;
  google.protobuf.Duration timeout = 2;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    string password = 3;
    int32 db = 4;
    google.protobuf.Duration dial_timeout = 5;
    google.protobuf.Duration read_timeout = 6;
    google.protobuf.Duration write_timeout = 7;
    int32 pool_size = 8;
  }
  message Es {
    string host = 1;
    string username = 2;
    string password = 3;
  }
  Database database = 1;
  Redis redis = 2;
  Es es = 3;
}

message Nacos {
  string address = 1;
  uint64 port = 2;
  string namespaceId = 3;
  string serverGroup = 4;
}

message ErrorHandle {
  message ErrorMessages {
    repeated ErrorMessage error_messages = 1;
  }
  message ErrorMessage {
    string error_reason = 1;
    string message = 2;
  }
  map<string, ErrorMessages> handle = 1;
  string default = 2;
}

message Log {
  string filename = 1;
  int32 max_size = 2;
  int32 max_backup = 3;
  int32 max_age = 4;
  bool compress = 5;
}

message SignConf {
  string sign_key = 1;
}

message Llm{
  string host = 1;
  string key = 2;
}

message AppInfo {
  string app_id = 1; // 应用ID
  string app_secret = 2; // 应用名称
}

// UDC中台用户管理
message UCenter {
  string public_key = 1; // UCenter公钥
  string domain = 2;  // UCenter域名
  string env = 3; // 环境标识，sandbox、sandbox_internal、prod_internal, prod
  string suffix = 4; // UCenter后缀
  string ticket_url = 5; // UCenter ticket获取地址
  AppInfo app_info = 6; // 应用信息
  string client_id = 7; // 客户端ID
  string host = 8; // UCenter服务地址

}

message Auth {
  repeated string protected_urls = 1; // 需要鉴权的URL
}