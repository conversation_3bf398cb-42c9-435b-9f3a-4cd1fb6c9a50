package service

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	errorV1 "hw-paas-service/api/ai/v1"
	pb "hw-paas-service/api/reading_book/v1"
	"hw-paas-service/internal/biz"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/pkg/custom_context"
)

type ReadingBookService struct {
	pb.UnimplementedReadingBookServer
	rb  *biz.ReadingBookUseCase
	log *log.Helper
}

func NewReadingBookService(rb *biz.ReadingBookUseCase, logger log.Logger) *ReadingBookService {
	return &ReadingBookService{
		rb:  rb,
		log: log.NewHelper(logger),
	}
}

func (s *ReadingBookService) SentenceIdentify(ctx context.Context, req *pb.SentenceIdentifyRequest) (*pb.SentenceIdentifyReply, error) {

	traceId := custom_context.GetTraceId(ctx)
	sn := custom_context.GetDeviceId(ctx)
	// 打印基础日志信息
	base := map[string]interface{}{
		"tal_id":         traceId,
		"device_sn":      sn,
		"channel_source": "绘本跟读OCR",
	}
	baseJson, _ := jsoniter.Marshal(base)
	s.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(traceId, "reading_book", "base_info", string(baseJson)))
	sentence, resCode, err := s.rb.SentenceIdentify(ctx, req)
	if err != nil {
		return nil, errorV1.ErrorHwPaasThirdPartError(err.Error())
	}
	return &pb.SentenceIdentifyReply{
		Sentence:   sentence,
		ResultCode: int32(resCode),
	}, nil
}
