package service

import (
	"context"

	pb "hw-paas-service/api/skill/v1"
	"hw-paas-service/internal/biz"
)

type SkillService struct {
	biz *biz.SkillUsecase
}

func NewSkillService(biz *biz.SkillUsecase) *SkillService {
	return &SkillService{
		biz: biz,
	}
}

func (s *SkillService) GetRnList(ctx context.Context, req *pb.GetRnListRequest) (*pb.GetRnListReply, error) {
	return s.biz.GetRnList(ctx, req.AppVersion)
}
