package service

import (
	"context"

	pb "hw-paas-service/api/comment/v1"
	"hw-paas-service/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
)

type CommentService struct {
	pb.UnimplementedCommentServer
	commentUseCase *biz.CommentUseCase
	log            *log.Helper
}

func NewCommentService(commentUseCase *biz.CommentUseCase, logger log.Logger) *CommentService {
	return &CommentService{
		commentUseCase: commentUseCase,
		log:            log.NewHelper(logger),
	}
}

func (s *CommentService) ListComment(ctx context.Context, req *pb.ListCommentRequest) (*pb.ListCommentReply, error) {
	subjectType := int(req.SubjectType)
	return s.commentUseCase.ListComments(ctx, subjectType, req.SubjectId, uint64(req.ParentId),
		int(req.Page), int(req.PageSize))
}
