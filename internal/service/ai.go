package service

import (
	"context"
	pb "hw-paas-service/api/ai/v1"
	"hw-paas-service/internal/biz"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/pkg/custom_context"
	"hw-paas-service/internal/pkg/utils"

	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/types/known/structpb"
)

type AiService struct {
	pb.UnimplementedAiServer
	correct  *biz.CorrectUseCase
	log      *log.Helper
	question *biz.JzxQuestionUseCase
	edu      *biz.EduUseCase
	login    *biz.CodeLoginUseCase
}

func NewAiService(
	correct *biz.CorrectUseCase,
	question *biz.JzxQuestionUseCase,
	edu *biz.EduUseCase,
	logger log.Logger,
	login *biz.CodeLoginUseCase,
) *AiService {
	return &AiService{
		correct:  correct,
		question: question,
		edu:      edu,
		log:      log.<PERSON>(logger),
		login:    login,
	}
}

func (s *AiService) QueryCorrect(ctx context.Context, req *pb.QueryCorrectRequest) (*structpb.Struct, error) {
	traceId := custom_context.GetTraceId(ctx)
	sn := custom_context.GetDeviceId(ctx)
	// 打印基础日志信息
	base := map[string]interface{}{
		"tal_id":         traceId,
		"device_sn":      sn,
		"channel_source": "口算批改",
	}
	baseJson, _ := jsoniter.Marshal(base)
	s.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(sn, "kousuan_query_correct", "base_info", string(baseJson)))
	data, err := s.correct.QueryCorrect(ctx, req.ImageUrl)
	return utils.ReplyAny(data, err)
}

// FeedbackTrace 通用ai工具反馈接口
func (s *AiService) FeedbackTrace(ctx context.Context, req *pb.FeedbackTraceRequest) (*pb.FeedbackTraceReply, error) {
	return s.correct.FeedbackTrace(ctx, req)
}

func (s *AiService) ListQuestions(ctx context.Context, req *pb.ListQuestionsRequest) (*pb.ListQuestionsReply, error) {
	return s.question.ListWithPage(ctx, req)
}

func (s *AiService) GetQuestion(ctx context.Context, req *pb.GetQuestionRequest) (*pb.Question, error) {
	return s.question.GetQuestion(ctx, req)
}

func (s *AiService) GetQuestionBatch(ctx context.Context, req *pb.GetQuestionBatchRequest) (*pb.GetQuestionBatchReply, error) {
	return s.question.GetQuestionBatch(ctx, req)
}

func (s *AiService) UpsertQuestion(ctx context.Context, req *pb.UpsertQuestionRequest) (*structpb.Struct, error) {
	return s.question.UpsertQuestion(ctx, req)
}

func (s *AiService) UpdateQuestionWithKey(ctx context.Context, req *pb.UpdateQuestionWithKeyRequest) (*structpb.Struct, error) {
	return s.question.UpdateQuestionWithKey(ctx, req)
}

func (s *AiService) GetQuestionStatus(ctx context.Context, req *pb.GetQuestionStatusRequest) (*pb.GetQuestionStatusReply, error) {
	return s.question.GetQuestionStatus(ctx, req)
}

func (s *AiService) CreateQuestionImport(ctx context.Context, req *pb.CreateQuestionImportRequest) (*structpb.Struct, error) {
	return s.question.CreateQuestionImport(ctx, req)
}

func (s *AiService) ListQuestionImport(ctx context.Context, req *pb.ListQuestionImportRequest) (*pb.ListQuestionImportReply, error) {
	return s.question.ListQuestionImport(ctx, req)
}

func (s *AiService) QuestionImportAction(ctx context.Context, req *pb.QuestionImportActionRequest) (*structpb.Struct, error) {
	return s.question.QuestionImportAction(ctx, req)
}

func (s *AiService) GetQuestionImportDetail(ctx context.Context, req *pb.GetQuestionImportDetailRequest) (*pb.GetQuestionImportDetailReply, error) {
	return s.question.GetQuestionImportDetail(ctx, req)
}

func (s *AiService) UploadFile(ctx context.Context, req *pb.UploadFileRequest) (*pb.UploadFileReply, error) {
	return s.question.UploadFile(ctx, req)
}

// 通用配置接口
func (s *AiService) CommonConfig(ctx context.Context, req *pb.CommonConfigReq) (*pb.CommonConfigResp, error) {
	return s.correct.CommonConfig()
}

// 新手任务驱动
func (s *AiService) TaskDrive(ctx context.Context, req *pb.TaskDriveReq) (*pb.TaskDriveResp, error) {
	return s.correct.TaskDrive(ctx, req.App)
}

// 博客列表
func (s *AiService) BlogList(ctx context.Context, req *pb.BlogListReq) (*pb.BlogListResp, error) {
	return s.edu.ListBlogWithPage(ctx, req)
}

func (s *AiService) SetBlog(ctx context.Context, req *pb.BlogArticle) (*structpb.Struct, error) {
	return s.edu.SetBlog(ctx, req)
}

// 博客详情
func (s *AiService) BlogDetail(ctx context.Context, req *pb.BlogDetailReq) (*pb.BlogDetailResp, error) {
	return s.edu.FirstBlogWithPath(ctx, req)
}

// 博客分类
func (s *AiService) BlogCategory(ctx context.Context, req *pb.BlogCategoryReq) (*pb.BlogCategoryResp, error) {
	return s.edu.BlogCategory(ctx, req)
}

// 博客反馈
func (s *AiService) BlogFeedback(ctx context.Context, req *pb.BlogFeedbackReq) (*structpb.Struct, error) {
	return s.edu.BlogFeedback(ctx, req)
}

// 词汇术语列表
func (s *AiService) EduTermList(ctx context.Context, req *pb.EduTermListReq) (*pb.EduTermListResp, error) {
	return s.edu.EduTermList(ctx, req)
}

// 词汇术语详情
func (s *AiService) EduTermDetail(ctx context.Context, req *pb.EduTermDetailReq) (*pb.EduTermDetailResp, error) {
	return s.edu.EduTermDetail(ctx, req.Path)
}

func (s *AiService) CodeLogin(ctx context.Context, req *pb.CodeLoginReq) (*pb.CodeLoginResp, error) {
	resp, err := s.login.CodeLogin(ctx, &biz.CodeLoginReq{
		Code: req.Code,
	})
	if err != nil {
		return nil, err
	}
	return &pb.CodeLoginResp{
		TalId:    resp.TalID,
		TalToken: resp.TalToken,
		Expired:  resp.Expire,
		Life:     resp.Life,
	}, nil
}

func (s *AiService) LoginOut(ctx context.Context, req *pb.LoginOutReq) (*pb.LoginOutResp, error) {

	rlt, err := s.login.LoginOut(ctx, req.TalToken)
	if err != nil {
		return nil, err
	}
	return &pb.LoginOutResp{
		Msg: rlt,
	}, nil
}

func (s *AiService) CheckLogin(ctx context.Context, req *pb.CheckLoginReq) (*pb.CheckLoginResp, error) {
	res, err := s.login.CheckLogin(ctx, req.TalToken)
	if err != nil {
		return nil, pb.ErrorHwPaasUnauthorized(err.Error())
	}

	return &pb.CheckLoginResp{
		TalId:    res.TalID,
		TalToken: res.TalToken,
		Expired:  res.Expire,
		Life:     res.Life,
	}, nil
}

func (s *AiService) CheckEmailExists(ctx context.Context, req *pb.CheckEmailExistsReq) (*pb.CheckEmailExistsResp, error) {
	isExists, err := s.login.CheckEmailExists(ctx, req.Email)
	if err != nil {
		return &pb.CheckEmailExistsResp{Exists: false}, err
	}
	return &pb.CheckEmailExistsResp{Exists: isExists}, nil
}
