package service

import (
	"context"
	pb "hw-paas-service/api/query_words/v1"
	"hw-paas-service/internal/biz"
)

type QueryWordsService struct {
	pb.UnimplementedQueryWordsServer
	fw *biz.QueryWordsUseCase
}

func NewQueryWordsService(fw *biz.QueryWordsUseCase) *QueryWordsService {
	return &QueryWordsService{
		fw: fw,
	}
}

func (s *QueryWordsService) QueryWordsList(ctx context.Context, req *pb.QueryWordsListRequest) (*pb.QueryWordsListReply, error) {
	return s.fw.List(ctx, req)
}
