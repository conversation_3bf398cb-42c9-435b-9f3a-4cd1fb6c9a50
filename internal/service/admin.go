package service

import (
	"context"

	pb "hw-paas-service/api/eduadmin/v1"
	"hw-paas-service/internal/biz"
)

type AdminService struct {
	pb.UnimplementedAdminServer
	admin *biz.AdminUseCase
}

func NewAdminService(useCase *biz.AdminUseCase) *AdminService {
	return &AdminService{
		admin: useCase,
	}
}

func (s *AdminService) ImportEduTerms(ctx context.Context, req *pb.ImportEduTermsRequest) (*pb.ImportEduTermsReply, error) {
	err := s.admin.ImportExcelToTerms(ctx, req.File)
	if err != nil {
		return &pb.ImportEduTermsReply{
			Message: err.Error(),
		}, err
	}
	return &pb.ImportEduTermsReply{
		Message: "Import success",
	}, nil
}

func (s *AdminService) ExecuteSqlFile(ctx context.Context, req *pb.ExecuteSqlFileRequest) (*pb.ExecuteSqlFileReply, error) {
	err := s.admin.ExcuteSQLFile(ctx, req.File)
	if err != nil {
		return &pb.ExecuteSqlFileReply{
			Message: err.Error(),
		}, err
	}
	return &pb.ExecuteSqlFileReply{
		Message: "SQL file executed successfully",
	}, nil
}
