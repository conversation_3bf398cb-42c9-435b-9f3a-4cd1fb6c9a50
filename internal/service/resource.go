package service

import (
	"context"

	pb "hw-paas-service/api/resource/v1"
	"hw-paas-service/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/emptypb"
)

type ResourceService struct {
	pb.UnimplementedResourceServer
	uc  *biz.ResourceUseCase
	log *log.Helper
}

func NewResourceService(logger log.Logger, uc *biz.ResourceUseCase) *ResourceService {
	return &ResourceService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

func (s *ResourceService) GetGrades(ctx context.Context, req *emptypb.Empty) (*pb.GradesResp, error) {
	grades, err := s.uc.GetGrades(ctx)
	if err != nil {
		return nil, err
	}
	return grades, nil
}
func (s *ResourceService) GetSubjects(ctx context.Context, req *emptypb.Empty) (*pb.SubjectsResp, error) {
	subjects, err := s.uc.GetSubjects(ctx)
	if err != nil {
		return nil, err
	}
	return subjects, nil
}

func (s *ResourceService) GetResourceType(ctx context.Context, req *emptypb.Empty) (*pb.ResourceTypeResp, error) {
	resourceTypes, err := s.uc.GetResourceType(ctx)
	if err != nil {
		return nil, err
	}
	return resourceTypes, nil
}

func (s *ResourceService) GetResourceGroups(ctx context.Context, req *pb.GetResourceGroupsReq) (*pb.GetResourceGroupsResp, error) {
	s.log.WithContext(ctx).Infof("GetResourceGroups Request: %+v", req)
	resourceGroups, err := s.uc.GetResourceGroups(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetResourceGroups error: %v", err)
		return nil, err
	}
	return resourceGroups, nil
}

func (s *ResourceService) GetResourceList(ctx context.Context, req *pb.GetResourceListReq) (*pb.GetResourceListResp, error) {
	s.log.WithContext(ctx).Infof("GetResourceList Request: %+v", req)
	resourceList, err := s.uc.GetResourceList(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetResourceList error: %v", err)
		return nil, err
	}
	return resourceList, nil
}

func (s *ResourceService) GetResourceMeta(ctx context.Context, req *emptypb.Empty) (*pb.ResourceMetaResp, error) {
	meta, err := s.uc.GetResourceMeta(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetResourceMeta error: %v", err)
		return nil, err
	}
	return meta, nil
}

func (s *ResourceService) GetResourceDetail(ctx context.Context, req *pb.GetResourceDetailReq) (*pb.ResourceDetail, error) {
	s.log.WithContext(ctx).Infof("GetResourceDetail Request: %+v", req)
	detail, err := s.uc.GetResourceDetail(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetResourceDetail error: %v", err)
		return nil, err
	}
	return detail, nil
}

func (s *ResourceService) ListRandResource(ctx context.Context, req *pb.ListRandResourceReq) (*pb.ListRandResourceResp, error) {
	s.log.WithContext(ctx).Infof("ListRandResource Request: %+v", req)
	resourceList, err := s.uc.ListRandResource(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("ListRandResource error: %v", err)
		return nil, err
	}
	return resourceList, nil
}

func (s *ResourceService) RefreshAllCache(ctx context.Context, req *emptypb.Empty) (*emptypb.Empty, error) {
	err := s.uc.RefreshAllCache(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("RefreshAllCache error: %v", err)
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
