package service

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/types/known/structpb"
	pb "hw-paas-service/api/finger_words/v1"
	"hw-paas-service/internal/biz"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/pkg/custom_context"
	"hw-paas-service/internal/pkg/utils"
)

var (
	fingerSkill = "oversea_finger"
)

type FingerWordsService struct {
	pb.UnimplementedFingerWordsServer
	fw       *biz.FingerWordsUseCase
	skillBiz *biz.SkillUsecase
	log      *log.Helper
}

func NewFingerWordsService(fw *biz.FingerWordsUseCase, skillBiz *biz.SkillUsecase, logger log.Logger) *FingerWordsService {
	return &FingerWordsService{
		fw:       fw,
		skillBiz: skillBiz,
		log:      log.<PERSON><PERSON>elper(logger),
	}
}

func (s *FingerWordsService) FingerWordsEntry(ctx context.Context, req *pb.FingerWordsEntryRequest) (*pb.FingerWordsEntryReply, error) {
	traceId := custom_context.GetTraceId(ctx)
	sn := custom_context.GetDeviceId(ctx)
	// 打印基础日志信息
	base := map[string]interface{}{
		"tal_id":         traceId,
		"device_sn":      sn,
		"channel_source": "指尖查词",
		//"grade":          "",
		//"subject":        "",
		//"app_id":         "",
		//"plat_version":   "",
		//"rom_version":    "",
		//"app_version":    "",
	}
	baseJson, _ := jsoniter.Marshal(base)
	s.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(traceId, "finger_words", "base_info", string(baseJson)))

	reply, err := s.fw.Entry(ctx, req)
	if err != nil {
		return nil, err
	}

	//rn dispatch
	dispatch := &structpb.Struct{}
	rnReply, _ := s.skillBiz.GetRnList(ctx, req.AppVersion)
	if rnReply != nil && len(rnReply.RnList) > 0 {
		for _, bundle := range rnReply.RnList {
			if bundle.SkillName == fingerSkill {
				dispatch, _ = utils.FormatStructPb(bundle)
				break
			}
		}
	}
	reply.Dispatch = dispatch

	dispatchJson, _ := jsoniter.Marshal(dispatch)
	s.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(traceId, "finger_words", "dw_entry_dispatch", string(dispatchJson)))

	return reply, err
}

func (s *FingerWordsService) FingerWordsQuery(ctx context.Context, req *pb.FingerWordsQueryRequest) (*pb.FingerWordsQueryReply, error) {
	reply, err := s.fw.Query(ctx, req)
	if err != nil {
		return nil, err
	}

	//rn dispatch
	dispatch := &structpb.Struct{}
	rnReply, _ := s.skillBiz.GetRnList(ctx, req.AppVersion)
	if rnReply != nil && len(rnReply.RnList) > 0 {
		for _, bundle := range rnReply.RnList {
			if bundle.SkillName == req.SkillName {
				dispatch, _ = utils.FormatStructPb(bundle)
				break
			}
		}
	}
	reply.Dispatch = dispatch

	return reply, err
}
