package evaluate

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/gorilla/websocket"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/pkg/ailab/util"
	"io"
	"net/http"
	"time"
)

const (
	talStart  = "started"
	talResult = "result"
	talFinish = "finish"
)

var (
	soundErr      = errors.New("en evaluate return err")
	noSupportEval = errors.New("noSupport eval type ")
)

// Ssound 下游先声评测服务
type TalStandard struct {
	downstream *Downstream
	data       *conf.Biz
}

func NewTalStandard(downstream *Downstream, data *conf.Biz) *TalStandard {
	return &TalStandard{downstream: downstream, data: data}
}

type TalReq struct {
	MimeType     string       `json:"mime_type"`
	AssessRef    AssessRef    `json:"assess_ref"`
	NeedUrl      bool         `json:"need_url"`
	ControlParam ControlParam `json:"control_param"`
}

type AssessRef struct {
	Text            string  `json:"text"`
	CoreType        string  `json:"core_type"`
	PronWeight      float64 `json:"pron_weight"`
	FluencyWeight   float64 `json:"fluency_weight"`
	IntegrityWeight float64 `json:"integrity_weight"`
	VolumeWeight    float64 `json:"volume_weight"`
}
type ControlParam struct {
	VadMaxSec            float64 `json:"vad_max_sec"`
	VadPauseSec          float64 `json:"vad_pause_sec"`
	VadStSilSec          float64 `json:"vad_st_sil_sec"`
	SuffixPenalQuick     float64 `json:"suffix_penal_quick"`
	HighScoreThreshold   float64 `json:"high_score_threshold"`
	HighStopLowThreshold float64 `json:"high_stop_low_threshold"`
}

type TalResponse struct {
	Action    string `json:"action"`
	Code      int    `json:"code"`
	Data      Data   `json:"data"`
	Msg       string `json:"msg"`
	RequestId string `json:"requestId"`
}

type Data struct {
	CoreType       string     `json:"core_type"`
	Fluency        TalFluency `json:"fluency"`
	GradeTight     int        `json:"grade_tight"`
	Integrity      int        `json:"integrity"`
	ModelVersion   string     `json:"model_version"`
	PronScore      int        `json:"pron_score"`
	Rank           int        `json:"rank"`
	ReciteScore    []TalScore `json:"recite_score"`
	SpeechDuration int        `json:"speech_duration"`
	TotalScore     int        `json:"total_score"`
	Volume         float64    `json:"volume"`
	Words          []TalWord  `json:"words"`
	VoiceUrl       string     `json:"url"`
}

type TalFluency struct {
	Overall int `json:"overall"`
}

type TalScore struct {
	RefCharacter string  `json:"ref_character"`
	Score        float64 `json:"score"`
	Word         string  `json:"word"`
}

type TalWord struct {
	End    int        `json:"end"`
	IsOov  int        `json:"is_oov"`
	Phones []TalPhone `json:"phones"`
	Score  int        `json:"score"`
	Start  int        `json:"start"`
	Word   string     `json:"word"`
}

type TalPhone struct {
	End   int    `json:"end"`
	Phone string `json:"phone"`
	Start int    `json:"start"`
}

func (s *TalStandard) Write(data []byte) error {
	messageType := websocket.BinaryMessage

	root := jsoniter.Get(data)
	action := root.Get("cmd").ToString()
	if action == "start" {
		messageType = websocket.TextMessage
		m := TalReq{
			MimeType: "wav",
			AssessRef: AssessRef{
				Text:            root.Get("assess_ref").Get("text").ToString(),
				CoreType:        "en.snt.score",
				PronWeight:      float64(s.data.EnStandard.PronWeight),
				FluencyWeight:   float64(s.data.EnStandard.FluencyWeight),
				IntegrityWeight: float64(s.data.EnStandard.IntegrityWeight),
				VolumeWeight:    float64(s.data.EnStandard.VolumeWeight),
			},
			NeedUrl: s.data.EnStandard.NeedUrl,
			ControlParam: ControlParam{
				VadMaxSec:            float64(s.data.EnStandard.VadMaxSec),
				VadPauseSec:          float64(s.data.EnStandard.VadPauseSec),
				VadStSilSec:          float64(s.data.EnStandard.VadStSilSec),
				SuffixPenalQuick:     float64(s.data.EnStandard.SuffixPenalQuick),
				HighScoreThreshold:   float64(s.data.EnStandard.HighScoreThreshold),
				HighStopLowThreshold: float64(s.data.EnStandard.HighStopLowThreshold),
			},
		}
		data, _ = json.Marshal(m)
	} else if action == "stop" {
		messageType = websocket.TextMessage
		data = []byte("end")
	}
	//fmt.Println("tal standard write data is ", string(data))
	return s.downstream.conn.WriteMessage(messageType, data)
}

func (s *TalStandard) Conn(cf *conf.Biz, header http.Header) (*websocket.Conn, error) {
	var urlParams = make(map[string]string)
	urlParams["mod"] = "px-1.0"
	timestamp := util.GetCurrentDate()
	requestUrl, err := util.GetWsSign(
		cf.TalEnStandard.Appkey,
		cf.TalEnStandard.Secret,
		timestamp,
		cf.TalEnStandard.Url,
		urlParams)

	if err != nil {
		s.downstream.log().Errorf("websocket GetWsSign tal servie err is %v", err)
	}
	start := time.Now()
	conn, dialRes, err := websocket.DefaultDialer.Dial(requestUrl, header)
	s.downstream.log().Infof("conn cost %d", time.Since(start).Milliseconds())
	if err != nil {
		//读取响应体dialRes.Body中的值
		var res []byte
		if dialRes != nil {
			res, _ = io.ReadAll(dialRes.Body)
		}
		s.downstream.log().Errorf("websocket conn tal servie err is %v, dialRes:%s", err, string(res))
		return nil, err
	}
	return conn, err
}

func (s *TalStandard) Name() string {
	return "tal_standard"
}

// FormatOutputData 根据不同的阶段，调用不同的格式化输出函数
func (s *TalStandard) FormatOutputData(data []byte) (TransFerWriteMsg, error) {
	root := TalResponse{}
	// 反序列数据到TalResponse中
	err := json.Unmarshal(data, &root)
	if err != nil {
		return TransFerWriteMsg{}, err
	}
	resp := TransFerWriteMsg{}
	if s.haveErr(root) {
		return s.FormatOutputClose(root)
	}
	// 中间过程和评测完成的数据结构不同
	action := root.Action
	if action == talStart {
		return s.FormatOutputStart(root)
	} else if action == talResult {
		return s.FormatOutputResult(root)
	} else if action == talFinish {
		return s.FormatOutputClose(root)
	}
	return resp, noSupportEval
}

func (s *TalStandard) haveErr(root TalResponse) bool {
	// 判断错误码
	if root.Code == 20000 || root.Code == 10000 {
		return false
	}
	s.downstream.log().Warnf("tal评测服务返回结果异常,errId :  %d , errmsg : %s", root.Code, root.Msg)

	return true
}

func (s *TalStandard) FormatOutputStart(root TalResponse) (TransFerWriteMsg, error) {
	msg := TransFerWriteMsg{}

	genericResp := GenericResp{}
	genericResp.Action = root.Action
	genericResp.Code = root.Code
	genericResp.RequestId = root.RequestId
	marshal, err := jsoniter.Marshal(genericResp)
	if err != nil {
		return msg, err
	}
	msg.cmd = Start
	msg.data = marshal
	s.downstream.log().Infof("dw_ws_started_trace %+v", string(marshal))
	return msg, nil
}
func (s *TalStandard) FormatOutputClose(root TalResponse) (TransFerWriteMsg, error) {
	// 打印基础日志信息
	base := map[string]interface{}{
		"tal_id":         s.downstream.talId,
		"req_id":         s.downstream.reqId,
		"device_sn":      s.downstream.deviceId,
		"channel_source": "绘本跟读评测",
	}
	baseJson, _ := jsoniter.Marshal(base)
	s.downstream.log().Infof(common.MakeLogBackFlowMsgInfo(s.downstream.reqId, "reading_book_finish", "base_info", string(baseJson)))

	msg := TransFerWriteMsg{}
	genericResp := GenericResp{}
	genericResp.Action = root.Action
	genericResp.Code = root.Code
	genericResp.Data.CoreType = root.Data.CoreType
	genericResp.Data.Fluency.Overall = root.Data.Fluency.Overall
	genericResp.Data.GradeTight = root.Data.GradeTight
	genericResp.Data.Integrity = root.Data.Integrity
	genericResp.Data.ModelVersion = root.Data.ModelVersion
	genericResp.Data.PronScore = root.Data.PronScore
	genericResp.Data.Rank = root.Data.Rank
	genericResp.Data.SpeechDuration = root.Data.SpeechDuration
	genericResp.Data.TotalScore = root.Data.TotalScore
	genericResp.Data.Volume = root.Data.Volume
	genericResp.Data.VoiceUrl = root.Data.VoiceUrl
	var originText []Word
	for _, v := range root.Data.Words {
		originText = append(originText, Word{
			Word:  v.Word,
			Score: cast.ToInt(v.Score),
		})
	}
	genericResp.Data.OriginText = originText
	genericResp.RequestId = root.RequestId

	name := "tools/ssound-voice/" + s.downstream.reqId + ".wav"
	wav := util.PcmToWav(string(s.downstream.voiceBuf.Bytes()), 1, 16, 16000)

	myWav, err := s.downstream.services.OssSvc.Upload2Oss(context.Background(), wav, name)
	if err != nil {
		s.downstream.log().Errorf("upload myWav to oss err:%+v", err)
	}
	genericResp.Data.MyWav = myWav

	if genericResp.Code == ConTimeout {
		genericResp.Data = s.downstream.resultCache.Data
	}
	marshal, err := jsoniter.Marshal(genericResp)
	if err != nil {
		return msg, err
	}
	msg.cmd = Finish
	msg.data = marshal
	// 打印基础日志信息
	sendMsg := map[string]interface{}{
		"tal_id":    s.downstream.talId,
		"req_id":    s.downstream.reqId,
		"device_sn": s.downstream.deviceId,
		"msg":       string(marshal),
	}
	msgJson, _ := jsoniter.Marshal(sendMsg)
	s.downstream.log().Infof(common.MakeLogBackFlowMsgInfo(s.downstream.reqId, "reading_book_finish", "dw_reading_book_resp", string(msgJson)))

	return msg, nil
}

// FormatOutputResult 格式化中间的音频返回结果
func (s *TalStandard) FormatOutputResult(root TalResponse) (TransFerWriteMsg, error) {
	//fmt.Println(fmt.Sprintf("tal standard result data is %+v", root))
	//if len(root.Data.ReciteScore) == 0 {
	//	return TransFerWriteMsg{}, nil
	//}
	//if root.Data.TotalScore == 0 {
	//	return TransFerWriteMsg{}, nil
	//}
	msg := TransFerWriteMsg{}
	genericResp := GenericResp{}
	genericResp.Action = root.Action
	genericResp.Code = root.Code
	genericResp.Data.CoreType = root.Data.CoreType
	genericResp.Data.Fluency.Overall = root.Data.Fluency.Overall
	genericResp.Data.GradeTight = root.Data.GradeTight
	genericResp.Data.Integrity = root.Data.Integrity
	genericResp.Data.ModelVersion = root.Data.ModelVersion
	genericResp.Data.PronScore = root.Data.PronScore
	genericResp.Data.Rank = root.Data.Rank
	genericResp.Data.SpeechDuration = root.Data.SpeechDuration
	genericResp.Data.TotalScore = root.Data.TotalScore
	genericResp.Data.Volume = root.Data.Volume

	var originText []Word
	for _, v := range root.Data.Words {
		originText = append(originText, Word{
			Word:  v.Word,
			Score: cast.ToInt(v.Score),
		})
	}
	genericResp.Data.OriginText = originText
	genericResp.RequestId = root.RequestId
	marshal, err := jsoniter.Marshal(genericResp)
	if err != nil {
		s.downstream.log().Warnf("tal_standard_result err:%+v", err)
		return msg, err
	}
	msg.cmd = Result
	msg.data = marshal
	s.downstream.resultCache = genericResp
	s.downstream.log().Infof("dw_ws_result_trace %+v", string(marshal))
	return msg, nil
}
