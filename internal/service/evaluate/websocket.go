package evaluate

import (
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/pkg/zlog"
	"net/http"
	"runtime/debug"
	"strings"
	"time"
)

const (
	// buffer
	bufferSize = 65535
)

// close reason
const (
	soundErrClose      = "sound_err_close"       //  先声返回结果异常关闭
	clientClose        = "client_close"          //  client 主动关闭
	goServerErrClose   = "go_server_err_close"   //  go server err close
	javaServerErrClose = "java_server_err_close" //  java server err close
	normallyClose      = "normally_close"        //  正常一次评测业务结束关闭
)

type EvalService struct {
	conf     *conf.Biz
	services *services.Services
}

func NewEvaluateService(cf *conf.Biz, services *services.Services) *EvalService {
	return &EvalService{
		conf:     cf,
		services: services,
	}
}

func (eval *EvalService) Upgrade(w http.ResponseWriter, r *http.Request, header http.Header) (*websocket.Conn, error) {
	upgrader := &websocket.Upgrader{
		ReadBufferSize:  bufferSize,
		WriteBufferSize: bufferSize,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		eval.log(header).Errorf("websocket upgrader conn fail, req url : %s ip : %s, err is %v", r.URL.Path, r.RemoteAddr, err)
		return nil, err
	}
	return conn, nil
}

// WsHandler 建立websocket连接，并和中台进行连接
func (eval *EvalService) WsHandler(w http.ResponseWriter, r *http.Request) {
	now := time.Now()
	// 捕获可能得panic，防止程序异常
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("go routine panic错误：", err, "stack:", debug.Stack())
			return
		}
	}()

	var (
		err  error
		conn *websocket.Conn
	)

	// 解析请求头
	header := eval.parseHeader(r)
	//todo 临时验签

	// 升级连接为websocket
	conn, err = eval.Upgrade(w, r, header)

	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(http.StatusText(http.StatusBadRequest)))
		return
	}
	// 客户端的连接
	// 创建一个新的transfer对象负责读写和下游服务的通信
	/**
	return &Transfer{
		conn:        conn,
		close:       make(chan struct{}, 1), // 通知关闭连接
		writeQueue:  make(chan TransFerWriteMsg, 1000), // 缓存待发送的消息
		lastPkgTime: time.Now().Unix(), // 记录最后一次接收消息的时间戳，用于心跳检测
	}
	*/
	transfer := NewWsTransfer(conn)
	// 组装下游连接，根据请求头和请求url请求中台
	err = transfer.AssembleDownstream(eval.conf, eval.services, header)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(http.StatusText(http.StatusBadRequest)))
		return
	}

	transfer.deviceId = header.Get("deviceId")
	transfer.reqId = header.Get("req_id")
	//transfer.pReqId = header.Get("p_req_id")
	transfer.From = header.Get("from")
	transfer.talId = header.Get("talId")
	eval.log(header).Info("websocket new conn create...")
	// run
	transfer.run()
	eval.log(header).Infof("websocket conn cost %d", time.Since(now).Milliseconds())
}

func (eval *EvalService) WsSignHandler(w http.ResponseWriter, r *http.Request) {
	// 捕获可能得panic，防止程序异常
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("WsSignHandler panic错误：", err, "stack:", debug.Stack())
			return
		}
	}()

}

func (eval *EvalService) GetCountdown(w http.ResponseWriter, r *http.Request) {

	defer func() {
		if err := recover(); err != nil {
			fmt.Println("GetCountdown go routine panic错误：", err, "stack:", debug.Stack())
			return
		}
	}()

	countdown := 290
	m := map[string]int{
		"time": countdown,
	}
	s, _ := json.Marshal(m)
	w.Write(s)
}
func (eval *EvalService) parseHeader(r *http.Request) http.Header {
	_ = r.ParseForm()
	header := http.Header{}

	deviceId := r.Form.Get("X-Genie-DeviceId")
	//pReqId := r.Form.Get("p_req_id")
	reqId := r.Form.Get("req_id")

	if deviceId == "" {
		deviceId = r.Header.Get("X-Tal-Sn")
	}
	//if pReqId == "" {
	//	pReqId = strings.Replace(uuid.NewString(), "-", "", -1)
	//}
	if reqId == "" {
		reqId = strings.Replace(uuid.NewString(), "-", "", -1)
	}

	talId := r.Header.Get("talId")

	header.Set("deviceId", deviceId)
	//header.Set("p_req_id", pReqId)
	header.Set("req_id", reqId)
	header.Set("from", r.Form.Get("from"))
	header.Set("talId", talId)
	return header
}

func (eval *EvalService) log(header http.Header) *zap.SugaredLogger {
	instance := zlog.SugaredInstance()
	return instance.With("deviceId", header.Get("deviceId")).With("reqId", header.Get("req_id")).
		With("talId", header.Get("talId"))
}

func (eval *EvalService) logInfo() *zap.SugaredLogger {
	instance := zlog.SugaredInstance()
	return instance
}
