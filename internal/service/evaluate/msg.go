package evaluate

const (
	Start  = "start"  // 开始包
	Result = "result" // 中间结果包
	Finish = "finish" // 完成包
)
const (
	ConTimeout = 300701701 //超过30秒自动结束
)

// GenericResp 通用返回结构对象
type GenericResp struct {
	Action string `json:"action"`
	Code   int    `json:"code"`
	Data   struct {
		CoreType string `json:"core_type"`
		Fluency  struct {
			Overall int `json:"overall"`
		} `json:"fluency"`
		GradeTight     int     `json:"grade_tight"`
		Integrity      int     `json:"integrity"`
		ModelVersion   string  `json:"model_version"`
		OriginText     []Word  `json:"origin_text"`
		PronScore      int     `json:"pron_score"`
		Rank           int     `json:"rank"`
		SpeechDuration int     `json:"speech_duration"`
		TotalScore     int     `json:"total_score"`
		Volume         float64 `json:"volume"`
		Words          []Word  `json:"words"`
		VoiceUrl       string  `json:"voice_url"`
		MyWav          string  `json:"my_wav"`
	} `json:"data"`
	RequestId string `json:"request_id"`
}

type Word struct {
	End   int    `json:"end"`
	IsOov int    `json:"is_oov"`
	Score int    `json:"score"`
	Start int    `json:"start"`
	Word  string `json:"word"`
}
