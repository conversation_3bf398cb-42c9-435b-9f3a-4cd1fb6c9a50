package evaluate

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/gorilla/websocket"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/internal/pkg/ailab/util"
	"hw-paas-service/pkg/zlog"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"
)

// DownStreamCase  具体下游websocket服务对象需要实现的接口
type DownStreamCase interface {
	Conn(data *conf.Biz, header http.Header) (*websocket.Conn, error)
	Write(data []byte) error
	Name() string
	FormatOutputData(data []byte) (TransFerWriteMsg, error)
}

// Downstream 下游对象超抽象,比如ssound java服务等
type Downstream struct {
	conn        *websocket.Conn
	close       chan struct{}
	writeQueue  chan []byte
	upstream    *Transfer
	dsc         DownStreamCase
	isClose     atomic.Bool
	deviceId    string
	reqId       string
	writeLock   sync.Mutex
	closeReason string
	voiceBuf    *bytes.Buffer
	talId       string
	services    *services.Services
	resultCache GenericResp
}

func NewWsDownstream(transfer *Transfer, cf *conf.Biz, services *services.Services) *Downstream {
	return &Downstream{
		writeQueue: make(chan []byte, 1000),
		close:      make(chan struct{}, 1),
		upstream:   transfer,
		voiceBuf:   &bytes.Buffer{},
		services:   services,
	}
}

func (downstream *Downstream) Conn(cf *conf.Biz, header http.Header) error {
	downStreamCase := NewTalStandard(downstream, cf)

	conn, err := downStreamCase.Conn(cf, header)
	if err != nil {
		return err
	}
	downstream.deviceId = header.Get("deviceId")
	downstream.reqId = header.Get("req_id")
	downstream.talId = header.Get("talId")
	downstream.conn = conn
	downstream.dsc = downStreamCase
	return nil
}

func (downstream *Downstream) run() {
	go downstream.read()
	go downstream.write()
}

func (downstream *Downstream) read() {
	var talResponses []TalResponse
	var totalScore int

	defer func() {
		if err := recover(); err != nil {
			downstream.log().Warnf("downstream %s readMsg panic err is %v", downstream.dsc.Name(), err)
		}
		downstream.CloseMessageHandle(javaServerErrClose)
		for i, talResponse := range talResponses {
			if talResponse.Action == talFinish {
				name := "tools/ssound-voice/" + talResponse.RequestId + ".wav"
				wav := util.PcmToWav(string(downstream.voiceBuf.Bytes()), 1, 16, 16000)
				myWav, err := downstream.services.OssSvc.Upload2Oss(context.Background(), wav, name)
				if err != nil {
					downstream.log().Errorf("upload wav to oss err:%+v", err)
				} else {
					downstream.log().Infof("upload wav to oss success, url:%s", myWav)
					talResponses[i].Data.VoiceUrl = myWav
				}
			}
		}

		talResponseStr, _ := json.Marshal(talResponses)
		downstream.log().Infof(common.MakeLogBackFlowMsgInfo(downstream.reqId, "reading_book", "tal_standard_data", string(talResponseStr)))
		downstream.log().Infof(common.MakeLogBackFlowMsgInfo(downstream.reqId, "reading_book", "base_info_score", "{\"score\":"+cast.ToString(totalScore)+"}"))
	}()

	for {
		messageType, data, err := downstream.conn.ReadMessage()
		if err == nil {
			downstream.log().Infof("downstream_read, name:%s, msg:%+v", downstream.dsc.Name(), string(data))
			downstream.handleMsg(messageType, data)
			var talResponse TalResponse
			_ = json.Unmarshal(data, &talResponse)
			if talResponse.Action == talFinish {
				talResponses = []TalResponse{talResponse}
			} else {
				talResponses = append(talResponses, talResponse)
			}
			totalScore = talResponse.Data.TotalScore
			continue
		}
		if err == io.EOF {
			continue
		}
		downstream.log().Warnf("audioLen:%d", len(downstream.voiceBuf.Bytes()))
		downstream.log().Warnf("downstream %s readMsg err is %v", downstream.dsc.Name(), err)
		return
	}
}

func (downstream *Downstream) write() {
	defer func() {
		if err := recover(); err != nil {
			downstream.log().Warnf("downstream %s write panic err is %v", downstream.dsc.Name(), err)
		}
		downstream.CloseMessageHandle(javaServerErrClose)
	}()
	ticker := time.NewTicker(time.Second * 3)
	for {
		select {
		// 如果从写队列中取到数据
		case w := <-downstream.writeQueue:
			// 调用方法，写入下游服务
			downstream.writeData(w)
			// 定时发送心跳，防止连接断开
		case <-ticker.C:
			downstream.ping()
			// 收到关闭信号，
		case <-downstream.close:
			// 关闭定时器
			ticker.Stop()
			// 关闭下游连接
			downstream.Close()
			return
		}
	}
}

// 根据不同的消息类型，对消息进行处理
func (downstream *Downstream) writeData(data []byte) {

	downstream.writeLock.Lock()
	defer downstream.writeLock.Unlock()
	// 解析传入的数据
	root := jsoniter.Get(data)
	// 获取消息中的cmd字段，来获取消息的类型
	action := root.Get("cmd").ToString()
	if action == "start" {
		// 获取传入内容的text字段，判断内容是否为空，为空直接断开连接
		if strings.TrimSpace(root.Get("assess_ref").Get("text").ToString()) == "" {
			downstream.CloseMessageHandle(goServerErrClose)
			return
		}
		custom := root.Get("custom")
		if custom != nil {
			downstream.reqId = custom.Get("req_id").ToString()
		}
		downstream.log().Infof("downstream %s writeData start: %s, custom: %+v", downstream.dsc.Name(), string(data), custom)
	} else if action == "stop" {
		downstream.log().Infof("downstream %s writeData stop: %s", downstream.dsc.Name(), string(data))
	} else {
		downstream.log().Infof("downstream %s writeData audio: %+v", downstream.dsc.Name(), len(data))
		// 如果是音频数据，加入voice缓存中
		downstream.voiceBuf.Write(data) //存储语音数据
	}
	// 将数据写入到下游服务
	err := downstream.dsc.Write(data)
	if err != nil {
		downstream.log().Warnf("downstream %s write msg err : %v", downstream.dsc.Name(), err)
		downstream.CloseMessageHandle(javaServerErrClose)
	}
}

func (downstream *Downstream) Close() {
	close(downstream.writeQueue)
	close(downstream.close)
	downstream.log().With("closeReason", downstream.closeReason).Info("downstream conn close")
	_ = downstream.conn.Close()
	downstream.upstream.CloseMessageHandle(downstream.closeReason)
}

func (downstream *Downstream) handleMsg(messageType int, data []byte) {
	switch messageType {
	case websocket.PingMessage:
		downstream.PingMessageHandle()
	case websocket.CloseMessage:
		downstream.CloseMessageHandle(javaServerErrClose)
	case websocket.TextMessage, websocket.BinaryMessage:
		downstream.TextOrBinaryMessageHandle(data)
	}
}

func (downstream *Downstream) TextOrBinaryMessageHandle(data []byte) {
	outputData, err := downstream.dsc.FormatOutputData(data)
	if err != nil {
		downstream.CloseMessageHandle(soundErrClose)
		return
	}
	if downstream.upstream.isClose.Load() == false {
		// 将数据写入到和客户端的连接
		downstream.upstream.writeQueue <- outputData
	}
}

func (downstream *Downstream) PingMessageHandle() {
	downstream.writeLock.Lock()
	defer downstream.writeLock.Unlock()
	err := downstream.conn.WriteMessage(websocket.PongMessage, []byte("pong"))
	if err != nil {
		downstream.log().Warnf("downstream %s reply to pong err is %v", downstream.dsc.Name(), err)
		downstream.CloseMessageHandle(javaServerErrClose)
	}
}

func (downstream *Downstream) CloseMessageHandle(closeReason string) {
	if downstream.isClose.CAS(false, true) {
		downstream.closeReason = closeReason
		downstream.close <- struct{}{}
	}
}

func (downstream *Downstream) ping() {
	downstream.writeLock.Lock()
	defer downstream.writeLock.Unlock()
	err := downstream.conn.WriteMessage(websocket.PingMessage, []byte("ping"))
	if err != nil {
		downstream.log().Warnf("downstream %s reply to pong err is %v", downstream.dsc.Name(), err)
		downstream.CloseMessageHandle(javaServerErrClose)
	}
}

func (downstream *Downstream) log() *zap.SugaredLogger {
	instance := zlog.SugaredInstance()
	return instance.With("deviceId", downstream.deviceId).With("reqId", downstream.reqId)
}
