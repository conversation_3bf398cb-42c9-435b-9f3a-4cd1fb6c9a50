package evaluate

import (
	"github.com/gorilla/websocket"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/pkg/zlog"
	"io"
	"net/http"
	"sync"
	"time"
)

const (
	// Disconnect 多长时间无收到数据包断开连接
	disconnectTime = 86400
	// 业务正常结束延迟关闭，一个心跳时间
	delayClose = 3
)

// read 时候出错 上次ping，和数据包和断开连接时的间时间
const (
	lastPingInterval = "lastPingInterval"
	lastDataInterval = "lastDataInterval"
)

const (
	successFinishCallback = "successFinishCallback"
	failFinishCallback    = "failFinishCallback"
)

// Transfer 对客户端的连接对象
type Transfer struct {
	conn       *websocket.Conn
	close      chan struct{}
	writeQueue chan TransFerWriteMsg
	downstream *Downstream
	isClose    atomic.Bool
	deviceId   string
	//pReqId            string
	reqId             string
	lastPkgTime       int64
	lastPingTime      int64
	writeLock         sync.Mutex
	closeReason       string
	finishCallbackRes string
	From              string
	talId             string
}

// TransFerWriteMsg write to client
type TransFerWriteMsg struct {
	cmd  string
	data []byte
}

var (
	ping = []byte("ping")
	pong = []byte("pong")
)

func NewWsTransfer(conn *websocket.Conn) *Transfer {
	return &Transfer{
		conn:        conn,
		close:       make(chan struct{}, 1),
		writeQueue:  make(chan TransFerWriteMsg, 1000),
		lastPkgTime: time.Now().Unix(),
	}
}

// AssembleDownstream 组装下游服务
func (transfer *Transfer) AssembleDownstream(cf *conf.Biz, services *services.Services, header http.Header) error {
	downstream := NewWsDownstream(transfer, cf, services)
	err := downstream.Conn(cf, header)
	if err != nil {
		return err
	}
	transfer.downstream = downstream
	return nil
}

func (transfer *Transfer) run() {
	transfer.downstream.run()
	go transfer.read()
	go transfer.write()
}

// 对websocket连接进行持续的监听，如果读取到消息就调用handMsg函数进行处理，
func (transfer *Transfer) read() {
	defer func() {
		if err := recover(); err != nil {
			nowSec := time.Now().Unix()
			transfer.log().With(lastPingInterval, nowSec-transfer.lastPingTime).With(lastDataInterval, nowSec-transfer.lastPkgTime).Warnf("transfer readMsg panic err is %v", err)
		}
		transfer.log().Info("transfer read exit")
		transfer.CloseMessageHandle(clientClose)
	}()
	for {
		messageType, data, err := transfer.conn.ReadMessage()
		if err == nil {
			// 如果读取成功进行处理
			transfer.handleMsg(messageType, data)
			continue
		}
		if err == io.EOF {
			continue
		}
		nowSec := time.Now().Unix()
		transfer.log().With(lastPingInterval, nowSec-transfer.lastPingTime).With(lastDataInterval, nowSec-transfer.lastPkgTime).
			Warnf("transfer readMsg err is %v", err)
		return
	}
}

func (transfer *Transfer) write() {
	defer func() {
		if err := recover(); err != nil {
			nowSec := time.Now().Unix()
			transfer.log().With(lastPingInterval, nowSec-transfer.lastPingTime).With(lastDataInterval, nowSec-transfer.lastPkgTime).Warnf("transfer write panic err is %v", err)
		}
		transfer.log().Info("transfer write exit")
		transfer.CloseMessageHandle(clientClose)
	}()
	ticker := time.NewTicker(time.Second * 3)
	for {
		select {
		case w := <-transfer.writeQueue:
			if w.data == nil {
				continue
			}
			err := transfer.writeOutput(w)
			// finish 阶段关闭连接
			if err == nil && w.cmd == Finish {
				transfer.finishCallbackRes = successFinishCallback
				time.Sleep(time.Second * delayClose)
				transfer.log().Info("transfer write finish msg success")
				transfer.CloseMessageHandle(normallyClose)
			}
		case <-transfer.close:
			ticker.Stop()
			transfer.Close()
			return
		case <-ticker.C:
			transfer.ping()
		}
	}
}

// 写入到和客户端建立的websocket连接中
func (transfer *Transfer) writeOutput(w TransFerWriteMsg) error {
	transfer.writeLock.Lock()
	defer transfer.writeLock.Unlock()
	err := transfer.conn.WriteMessage(websocket.TextMessage, w.data)
	transfer.log().Infof("transfer write msg, cmd: %s, data: %s", w.cmd, string(w.data))
	if err != nil {
		transfer.log().Infof("transfer write msg err is %v", err)
		transfer.CloseMessageHandle(goServerErrClose)
	}
	return err
}

// 根据客户端不同的消息类型进行处理
func (transfer *Transfer) handleMsg(messageType int, data []byte) {

	switch messageType {
	case websocket.PingMessage:
		// 维持心跳，保持连接的有效性
		transfer.PingMessageHandle()
	case websocket.CloseMessage:
		// 安全关闭连接
		transfer.log().Info("CloseMessage")
		transfer.CloseMessageHandle(clientClose)
	case websocket.TextMessage, websocket.BinaryMessage:
		transfer.TextOrBinaryMessageHandle(data)
	}
}

func (transfer *Transfer) TextOrBinaryMessageHandle(data []byte) {
	// 前端无websocket帧类型，通过四个字节来判断
	if isPong(data) {
		return
	}
	// 保持连接的有效性
	if isPing(data) {
		transfer.PingMessageHandle()
		return
	}
	// 更新最新收包时间
	transfer.lastPkgTime = time.Now().Unix()
	// 如果和中台的连接未关闭，就将消息写入下游的写队列
	if transfer.downstream.isClose.Load() == false {
		transfer.downstream.writeQueue <- data
	}
}

func (transfer *Transfer) PingMessageHandle() {
	transfer.log().Info("receive ping ")
	nowSex := time.Now().Unix()
	if nowSex-transfer.lastPkgTime >= disconnectTime {
		transfer.log().Warnf("disconnect without packets for a long time ")
		transfer.CloseMessageHandle(goServerErrClose)
		return
	}
	transfer.writeLock.Lock()
	defer transfer.writeLock.Unlock()
	transfer.lastPingTime = nowSex
	_ = transfer.conn.WriteMessage(websocket.PongMessage, []byte("pong"))
}

func (transfer *Transfer) ping() {
	transfer.writeLock.Lock()
	defer transfer.writeLock.Unlock()
	_ = transfer.conn.WriteMessage(websocket.PingMessage, []byte("ping"))
}

func (transfer *Transfer) CloseMessageHandle(closeReason string) {
	if transfer.isClose.CAS(false, true) {
		transfer.log().With("closeReason", closeReason).Info("CloseMessageHandle ")
		transfer.closeReason = closeReason
		transfer.close <- struct{}{}
	}
}

func (transfer *Transfer) Close() {
	close(transfer.writeQueue)
	close(transfer.close)
	if transfer.finishCallbackRes != successFinishCallback {
		transfer.finishCallbackRes = failFinishCallback
	}
	transfer.log().With("finishCallback", transfer.finishCallbackRes).
		With("closeReason", transfer.closeReason).Info("transfer conn close ")
	_ = transfer.conn.Close()
	transfer.downstream.CloseMessageHandle(transfer.closeReason)
}

func (transfer *Transfer) log() *zap.SugaredLogger {
	instance := zlog.SugaredInstance()
	return instance.With("deviceId", transfer.deviceId).With("reqId", transfer.reqId)
}

func isPing(bts []byte) bool {
	if len(bts) != 4 {
		return false
	}
	if ping[0] == bts[0] && ping[1] == bts[1] &&
		ping[2] == bts[2] && ping[3] == bts[3] {
		return true
	}
	return false
}

func isPong(bts []byte) bool {
	if len(bts) != 4 {
		return false
	}
	if pong[0] == bts[0] && pong[1] == bts[1] &&
		pong[2] == bts[2] && pong[3] == bts[3] {
		return true
	}
	return false
}
