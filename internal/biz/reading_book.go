package biz

import (
	"context"
	"encoding/base64"
	"git.100tal.com/znxx_xpp/go-libs/util"
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	pb "hw-paas-service/api/reading_book/v1"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/internal/data/services/sf_ocr"
	"hw-paas-service/internal/pkg/custom_context"
	"hw-paas-service/internal/pkg/logger"
	"hw-paas-service/internal/pkg/sync"
	"unicode"
)

type ReadingBookUseCase struct {
	log         *log.Helper
	biz         *conf.Biz
	transClient *sf_ocr.Client
	services    *services.Services
}

var (
	show = 1
	hide = 2
)

// 打印指查链路日志
type traceOcrResult struct {
	TraceID     string             `json:"trace_id"`      //traceID
	UserId      string             `json:"user_id"`       //用户ID
	DeviceId    string             `json:"device_id"`     //设备ID
	OcrImageUrl string             `json:"ocr_image_url"` //ocr图片
	OcrReq      *sf_ocr.OCRRequest `json:"ocr_req"`       //ocr请求
	OcrResp     string             `json:"ocr_resp"`      //ocr响应结果
}

func NewReadingBookUseCase(conf *conf.Biz, services *services.Services, logger log.Logger) *ReadingBookUseCase {
	return &ReadingBookUseCase{
		log:         log.NewHelper(logger),
		biz:         conf,
		services:    services,
		transClient: sf_ocr.NewClient(conf, logger),
	}
}

// SentenceIdentify 处理客户端请求，调用 OCR 服务并返回识别结果
func (r *ReadingBookUseCase) SentenceIdentify(ctx context.Context, req *pb.SentenceIdentifyRequest) (string, int, error) {
	traceId := custom_context.GetTraceId(ctx)
	r.log.WithContext(ctx).Infof("SentenceIdentify: %v", req)
	//打印链路日志
	traceLog := &traceOcrResult{
		TraceID:  custom_context.GetTraceId(ctx),
		UserId:   custom_context.GetJwtUserId(ctx),
		DeviceId: custom_context.GetDeviceId(ctx),
		OcrReq: &sf_ocr.OCRRequest{
			FingerPos: [2]int32{req.FingerPos[0], req.FingerPos[1]},
			TraceID:   traceId,
		},
	}
	// 异步打印链路日志-评测
	defer func() {
		gCtx := util.NewTraceContext(nil, traceId)
		sync.Go(gCtx, r.log, func() {
			//去掉base64图片数据,新增ocr图片url
			if r.biz.ReadingOcrSwitch == 1 {
				base64B, _ := base64.StdEncoding.DecodeString(req.ImageBase64)
				base64Url, _ := r.services.OssSvc.Upload2Oss(gCtx, base64B, traceLog.TraceID+".jpg")
				if base64Url != "" {
					traceLog.OcrImageUrl = base64Url
				}
			}
			traceB, _ := jsoniter.Marshal(traceLog)
			r.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(traceId, "reading_book", "dw_ocr_trace", string(traceB)))
			return
		})

	}()
	// 创建 OCR 请求
	ocrReq := sf_ocr.OCRRequest{
		ImageBase64: req.ImageBase64,
		FingerPos:   [2]int32{req.FingerPos[0], req.FingerPos[1]},
		TraceID:     traceId,
	}
	sentence, err := r.transClient.SfFingerTranslate(ctx, ocrReq)
	if err != nil {
		r.log.WithContext(ctx).Errorf("SfFingerTranslate error: %v", err)
		return "", 0, err
	}
	traceLog.OcrResp = sentence
	// 过滤非英文字符
	sentence = filterNonEnglishString(ctx, sentence)
	return sentence, show, nil
}

// filterNonEnglishString 检查字符串是否为纯非英文字符，若是则返回空字符串
func filterNonEnglishString(ctx context.Context, str string) string {
	hasEnglish := false
	for _, char := range str {
		if unicode.IsLetter(char) && unicode.Is(unicode.Latin, char) {
			hasEnglish = true
			break
		}
	}
	if !hasEnglish {
		logger.Infof(ctx, "filterNonEnglishString: %s", str)
		return ""
	}
	return str
}
