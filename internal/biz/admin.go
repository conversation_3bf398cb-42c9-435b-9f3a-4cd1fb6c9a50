package biz

import (
	"context"
	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/xuri/excelize/v2"

	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
)

type AdminUseCase struct {
	log          *log.Helper
	confBiz      *conf.Biz
	eduDao       *dao.EduDao
	eduadminRepo *dao.AdminRepo
}

func NewAdminUseCase(confBiz *conf.Biz, logger log.Logger, eduDao *dao.EduDao, eduadminRepo *dao.AdminRepo) *AdminUseCase {
	return &AdminUseCase{
		confBiz:      confBiz,
		log:          log.<PERSON>elper(logger),
		eduDao:       eduDao,
		eduadminRepo: eduadminRepo,
	}
}

func (b *AdminUseCase) ImportExcelToTerms(ctx context.Context, filePath string) error {
	columnMapping := map[string]string{
		"subject":          "subject",
		"term":             "term",
		"knowledge_1":      "knowledge_1",
		"knowledge_2":      "knowledge_2",
		"meta_description": "meta_description",
		"title":            "title",
		"tag":              "tag",
		"module_type":      "module_type",
		"module_index":     "module_index",
		"block_type":       "block_type",
		"block_order":      "block_order",
		"block_content":    "block_content",
	}

	type TempTerm struct {
		subject       string
		term          string
		title         string
		maxBlockOrder int
	}

	validMap := map[string]*TempTerm{}
	filePath = strings.Join([]string{"./uploads", filePath}, "/")
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return fmt.Errorf("无法打开 Excel 文件: %v", err)
	}
	defer f.Close()

	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return fmt.Errorf("无法读取 Excel 行数据: %v", err)
	}
	if len(rows) < 2 {
		return fmt.Errorf("Excel 文件中没有数据行")
	}
	dataRows := rows[1:]

	eduTerms := make([]*model.EduTerm, 0, len(dataRows))

	for _, row := range dataRows {
		if len(row) == 0 || strings.TrimSpace(row[0]) == "" {
			continue
		}

		rowData := make(map[string]string)
		for colIdx, cellValue := range row {
			colName, err := f.GetCellValue(sheetName, fmt.Sprintf("%c1", 'A'+colIdx))
			if err != nil {
				continue
			}
			if mappedField, ok := columnMapping[strings.ToLower(colName)]; ok {
				if mappedField == "title" {
					v := strings.Trim(cellValue, " ")
					v = strings.ReplaceAll(v, " ", "-")
					v = strings.ReplaceAll(v, ",", "-")
					v = strings.ReplaceAll(v, ":", "-")
					v = strings.ReplaceAll(v, "&", "-")
					v = strings.ReplaceAll(v, "\"", "-")
					v = strings.ReplaceAll(v, "----", "-")
					v = strings.ReplaceAll(v, "-–-", "-")
					v = strings.ReplaceAll(v, "--", "-")
					v = strings.ReplaceAll(v, "：", "-")
					v = strings.ReplaceAll(v, "，", "-")
					v = strings.ReplaceAll(v, "”", "-")
					v = strings.ReplaceAll(v, "“", "-")
					v = strings.ReplaceAll(v, "‘", "-")
					v = strings.ReplaceAll(v, "’", "-")
					v = strings.ReplaceAll(v, "'", "-")
					rowData["path"] = v
				}
				if mappedField == "term" {
					cellValue = strings.Trim(cellValue, " ")
					cellValue = strings.ReplaceAll(cellValue, "\n", "")
				}
				rowData[mappedField] = cellValue
			}
		}

		tempTerm, ok := validMap[rowData["term"]]
		if !ok {
			tempTerm = &TempTerm{
				maxBlockOrder: 0,
				title:         "",
				term:          rowData["term"],
			}
			validMap["term"] = tempTerm
		}

		if tempTerm.title == "" {
			tempTerm.title = rowData["title"]
		} else {
			if tempTerm.title != rowData["title"] {
				return fmt.Errorf("term:%s, title:%s, title:%s, 两个标题不一致", rowData["term"], tempTerm.title, rowData["title"])
			}
		}

		if rowData["block_order"] != "" {
			blockOrder := parseUint(rowData["block_order"])
			if blockOrder > tempTerm.maxBlockOrder {
				tempTerm.maxBlockOrder = blockOrder
			} else {
				return fmt.Errorf("term:%s, boloc_order: %s, block_order必须递增", rowData["term"], rowData["block_order"])
			}
		}

		if rowData["subject"] == "" {
			return fmt.Errorf("term:%s, boloc_order: %s, subject字段不能为空", rowData["term"], rowData["block_order"])
		}

		if rowData["subject"] != "" {
			if tempTerm.subject == "" {
				tempTerm.subject = rowData["subject"]
			} else if tempTerm.subject != rowData["subject"] {
				return fmt.Errorf("term:%s, boloc_order: %s, subject字段不一致", rowData["term"], rowData["block_order"])
			}
		}

		requiredFields := []string{"subject", "term", "title", "block_content"}
		missing := false
		fileNames := []string{}
		for _, field := range requiredFields {
			if rowData[field] == "" {
				fileNames = append(fileNames, field)
				missing = true
			}
		}
		if missing {
			return fmt.Errorf("term:%s, boloc_order: %s, %s字段不能为空", rowData["term"], rowData["block_order"], strings.Join(fileNames, ","))
		}

		blockOrder := parseUint(rowData["block_order"])

		eduTerm := &model.EduTerm{
			Path:            rowData["path"],
			Subject:         rowData["subject"],
			Term:            rowData["term"],
			Knowledge1:      rowData["knowledge_1"],
			Knowledge2:      rowData["knowledge_2"],
			MetaDescription: rowData["meta_description"],
			Title:           rowData["title"],
			Tag:             rowData["tag"],
			ModuleType:      rowData["module_type"],
			ModuleIndex:     parseUint(rowData["module_index"]),
			BlockType:       rowData["block_type"],
			BlockOrder:      blockOrder,
			BlockContent:    rowData["block_content"],
		}
		eduTerms = append(eduTerms, eduTerm)
	}

	return b.eduDao.BatchInsertTerm(ctx, eduTerms)
}

func parseUint(s string) int {
	var value int
	fmt.Sscanf(s, "%d", &value)
	if value > 255 {
		value = 255 // 防止溢出
	}
	return value
}

func (b *AdminUseCase) ExcuteSQLFile(ctx context.Context, filePath string) error {
	filePath = strings.Join([]string{"./uploads", filePath}, "/")
	return b.eduadminRepo.ExecuteSQLFile(ctx, filePath)
}
