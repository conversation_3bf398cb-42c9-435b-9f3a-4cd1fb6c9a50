package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"hw-paas-service/internal/pkg/custom_context"
	"slices"
	"strings"

	"github.com/jinzhu/copier"

	pb "hw-paas-service/api/resource/v1"

	"google.golang.org/protobuf/types/known/structpb"

	"github.com/go-kratos/kratos/v2/log"
)

// ResourceUseCase 教育资源业务逻辑
type ResourceUseCase struct {
	repo         *dao.EduResourceDao
	eduRepo      *dao.EduDao
	log          *log.Helper
	urlGenerator *URLGenerator
}

// NewResourceUseCase 创建教育资源业务逻辑实例
func NewResourceUseCase(repo *dao.EduResourceDao, eduRepo *dao.EduDao, logger log.Logger) *ResourceUseCase {
	return &ResourceUseCase{
		log:          log.NewHelper(logger),
		repo:         repo,
		eduRepo:      eduRepo,
		urlGenerator: NewURLGenerator(),
	}
}

// requestParser 请求参数解析器
type requestParser struct{}

// parseSubjectLevels 解析学科层级
func (rp *requestParser) parseSubjectLevels(subject string) (string, string, string, string) {
	subjectLevels := strings.Split(subject, "/")

	subjectName := ""
	topic := ""
	learningModule := ""
	resourceDetail := ""

	if len(subjectLevels) > 0 {
		subjectName = subjectLevels[0]
	}
	if len(subjectLevels) > 1 {
		topic = subjectLevels[1]
	}
	if len(subjectLevels) > 2 {
		learningModule = subjectLevels[2]
	}
	if len(subjectLevels) > 3 {
		resourceDetail = subjectLevels[3]
	}

	return subjectName, topic, learningModule, resourceDetail
}

// parseResourceType 解析资源类型
func (rp *requestParser) parseResourceType(resourceType string) (string, string) {
	resources := strings.Split(resourceType, "/")
	resourceTypeName := resources[0]
	topic := ""

	if len(resources) > 1 {
		topic = resources[1]
	}

	return resourceTypeName, topic
}

// buildResourceReq 构建资源请求参数
func (rp *requestParser) buildResourceReq(req interface{}) *model.ResourceReq {
	switch v := req.(type) {
	case *pb.GetResourceGroupsReq:
		subject, topic, learningModule, resourceDetail := rp.parseSubjectLevels(v.Subject)
		resourceType, _ := rp.parseResourceType(v.ResourceType)

		return &model.ResourceReq{
			ResourceType:   resourceType,
			Grade:          v.Grade,
			Subject:        subject,
			Topic:          topic,
			LearningModule: learningModule,
			ResourceDetail: resourceDetail,
			Page:           int(v.Page),
			PageSize:       int(v.PageSize),
		}
	case *pb.GetResourceListReq:
		subject, topic, learningModule, resourceDetail := rp.parseSubjectLevels(v.Subject)
		resourceType, _ := rp.parseResourceType(v.ResourceType)

		return &model.ResourceReq{
			ResourceType:   resourceType,
			Grade:          v.Grade,
			Subject:        subject,
			Topic:          topic,
			LearningModule: learningModule,
			ResourceDetail: resourceDetail,
			Page:           int(v.Page),
			PageSize:       int(v.PageSize),
		}
	default:
		return &model.ResourceReq{}
	}
}

// gradeConverter 年级转换器
type gradeConverter struct{}

// getGradeName 获取年级显示名称
func (gc *gradeConverter) getGradeName(grade string) string {
	gradeMap := map[string]string{
		"pre-Kindergarten": "Grade Pre-K",
		"Kindergarten":     "Grade K",
		"cc-first-grade":   "Grade 1",
		"cc-second-grade":  "Grade 2",
		"cc-third-grade":   "Grade 3",
		"cc-fourth-grade":  "Grade 4",
		"cc-fifth-grade":   "Grade 5",
		"cc-sixth-grade":   "Grade 6",
	}

	if displayName, exists := gradeMap[grade]; exists {
		return displayName
	}
	return grade
}

// responseBuilder 响应构建器
type responseBuilder struct{}

// buildGradesResponse 构建年级响应
func (rb *responseBuilder) buildGradesResponse(grades []*model.Grade) *pb.GradesResp {
	respGrades := make([]*pb.EduGrade, 0, len(grades))
	for _, g := range grades {
		respGrades = append(respGrades, &pb.EduGrade{
			Name:  g.Name,
			Value: g.Value,
		})
	}
	return &pb.GradesResp{
		Grades: respGrades,
	}
}

// buildSubjectsResponse 构建学科响应
func (rb *responseBuilder) buildSubjectsResponse(subjects []*model.SubjectNode) *pb.SubjectsResp {
	respSubjects := rb.recuerseSubject(subjects)
	return &pb.SubjectsResp{
		Subjects: respSubjects,
	}
}

// recuerseSubject 递归构建学科节点
func (rb *responseBuilder) recuerseSubject(subjects []*model.SubjectNode) []*pb.EduSubjectNode {
	respSubjects := make([]*pb.EduSubjectNode, 0, len(subjects))
	for _, s := range subjects {
		if s.Name == "" {
			continue // Skip empty subjects
		}
		nodes := rb.recuerseSubject(s.Nodes)
		respSubjects = append(respSubjects, &pb.EduSubjectNode{
			Name:  s.Name,
			Nodes: nodes,
		})
	}
	return respSubjects
}

// buildResourceTypeResponse 构建资源类型响应
func (rb *responseBuilder) buildResourceTypeResponse(resourceTypes []*model.ResourceType) *pb.ResourceTypeResp {
	respResourceTypes := make([]*pb.ResourceType, 0, len(resourceTypes))
	typesMap := make(map[string]*pb.ResourceType)

	for _, r := range resourceTypes {
		ty, ok := typesMap[r.Name]
		if !ok {
			ty = &pb.ResourceType{
				Name:  r.Name,
				Nodes: make([]*pb.ResourceType, 0),
			}
			respResourceTypes = append(respResourceTypes, ty)
			typesMap[r.Name] = ty
		}
		if r.Topic != "" {
			ty.Nodes = append(ty.Nodes, &pb.ResourceType{
				Name: r.Topic,
			})
		}
	}

	return &pb.ResourceTypeResp{
		Types: respResourceTypes,
	}
}

// buildResourceTitleResponse 构建资源标题响应
func (rb *responseBuilder) buildResourceTitleResponse(title *model.EduResourcesTitle) *pb.EduResourceTitle {
	return &pb.EduResourceTitle{
		Url:             title.URL,
		H1Title:         title.H1Title,
		Description:     title.Description,
		MetaTitle:       title.MetaTitle,
		MetaDescription: title.MetaDescription,
		MetaKeywords:    title.MetaKeywords,
		Schemas:         title.Schemas,
		Filter:          title.Filter,
	}
}

// GetGrades 获取年级列表
func (uc *ResourceUseCase) GetGrades(ctx context.Context) (*pb.GradesResp, error) {
	grades, err := uc.repo.GetGrade(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get grades: %w", err)
	}

	rb := &responseBuilder{}
	return rb.buildGradesResponse(grades), nil
}

// GetSubjects 获取学科列表
func (uc *ResourceUseCase) GetSubjects(ctx context.Context) (*pb.SubjectsResp, error) {
	subjects, err := uc.repo.GetSubject(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get subjects: %w", err)
	}

	rb := &responseBuilder{}
	return rb.buildSubjectsResponse(subjects), nil
}

// GetResourceType 获取资源类型列表
func (uc *ResourceUseCase) GetResourceType(ctx context.Context) (*pb.ResourceTypeResp, error) {
	resourceTypes, err := uc.repo.GetResourceType(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource types: %w", err)
	}

	rb := &responseBuilder{}
	return rb.buildResourceTypeResponse(resourceTypes), nil
}

type ResourceFilter struct {
	Grade          string `json:"grade"`
	Topic          string `json:"topic"`
	Subject        string `json:"subject"`
	Resource       string `json:"resource"`
	ResourceType   string `json:"resource_type"`
	LearningModule string `json:"learning_module"`
	ResourceDetail string `json:"resource_detail"`
}

// GetResourceGroups 获取资源分组
func (uc *ResourceUseCase) GetResourceGroups(ctx context.Context, req *pb.GetResourceGroupsReq) (*pb.GetResourceGroupsResp, error) {
	rp := &requestParser{}
	resourceReq := rp.buildResourceReq(req)

	titleData, err := uc.repo.GetResourceTitle(ctx, req.Path)

	if err != nil {
		return nil, fmt.Errorf("failed to get resource title: %w", err)
	}

	filter := titleData.Filter

	rf := &ResourceFilter{}
	err = json.Unmarshal([]byte(filter), rf)

	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal filter: %w", err)
	}

	resourceReq.ResourceType = rf.ResourceType
	resourceReq.Subject = rf.Subject
	resourceReq.Grade = rf.Grade
	resourceReq.Topic = rf.Topic
	resourceReq.LearningModule = rf.LearningModule
	resourceReq.Resource = rf.Resource
	resourceReq.ResourceDetail = rf.ResourceDetail

	resourceGroups, err := uc.repo.GetResourceGroup(ctx, resourceReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource groups: %w", err)
	}

	rb := &responseBuilder{}
	respResourceGroups := make([]*pb.EduResourceGroup, 0, len(resourceGroups))

	for _, r := range resourceGroups {
		resources := make([]*pb.EduResource, 0, len(r.Resources))
		for _, r1 := range r.Resources {
			dto, err := uc.DataToDTO(ctx, r1)
			if err != nil {
				uc.log.WithContext(ctx).Errorf("DataToDTO error: %v", err)
				continue
			}
			resources = append(resources, dto)
		}
		respResourceGroups = append(respResourceGroups, &pb.EduResourceGroup{
			Title:     r.Title,
			Resources: resources,
			Total:     int32(r.Count),
		})
	}

	return &pb.GetResourceGroupsResp{
		Groups: respResourceGroups,
		Title:  rb.buildResourceTitleResponse(titleData),
	}, nil
}

// DataToDTO 数据转换为DTO
func (uc *ResourceUseCase) DataToDTO(ctx context.Context, data *model.EduResources) (*pb.EduResource, error) {
	if data == nil {
		return nil, fmt.Errorf("data is nil")
	}
	extraData := &structpb.Struct{}
	if data.ExtraData != "" {
		err := json.Unmarshal([]byte(data.ExtraData), extraData)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal extra data: %v", err)
		}

		talToken := custom_context.GetTalToken(ctx)
		if talToken == "" {
			extraData.Fields["pdf_path"] = &structpb.Value{
				Kind: &structpb.Value_StringValue{
					StringValue: "",
				},
			}
		}
	}

	return &pb.EduResource{
		Id:                  uint32(data.ID),
		Title:               data.Title,
		ResourceType:        data.ResourceType,
		Subject:             data.Subject,
		Grade:               data.Grade,
		LearningTopic:       data.Topic,
		LearningModule:      data.LearningModule,
		Resource:            data.Resource,
		ResourceDetail:      data.ResourceDetail,
		Standards:           data.Standards,
		ResourceDescription: data.ResourceDescription,
		ExtraData:           extraData,
		MetaDescription:     data.MetaDescription,
		Url:                 data.Url,
		MetaTitle:           data.MetaTitle,
		MetaKeywords:        data.MetaKeywords,
		Schemas:             data.Schemas,
		UpdatedAt:           data.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// GetResourceList 获取资源列表
func (uc *ResourceUseCase) GetResourceList(ctx context.Context, req *pb.GetResourceListReq) (*pb.GetResourceListResp, error) {
	rp := &requestParser{}
	resourceReq := rp.buildResourceReq(req)

	resourceList, err := uc.repo.GetResourceList(ctx, resourceReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource list: %w", err)
	}

	respResources := make([]*pb.EduResource, 0, len(resourceList))
	for _, r := range resourceList {
		dto, err := uc.DataToDTO(ctx, r)
		if err != nil {
			uc.log.WithContext(ctx).Errorf("DataToDTO error: %v", err)
			continue
		}
		respResources = append(respResources, dto)
	}
	urlAndBreadcrumb, _ := uc.urlGenerator.generate(resourceReq.ResourceType, resourceReq.Subject, resourceReq.Grade, resourceReq.Topic, resourceReq.LearningModule,
		resourceReq.Resource, resourceReq.ResourceDetail)
	title, err := uc.repo.GetResourceTitle(ctx, urlAndBreadcrumb.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource title: %w", err)
	}

	rb := &responseBuilder{}
	return &pb.GetResourceListResp{
		Resources: respResources,
		Total:     int32(len(resourceList)),
		Title:     rb.buildResourceTitleResponse(title),
	}, nil
}

// GetResourceMeta 获取资源元信息
func (uc *ResourceUseCase) GetResourceMeta(ctx context.Context) (*pb.ResourceMetaResp, error) {
	grades, err := uc.repo.GetGrade(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get grades: %w", err)
	}

	subjects, err := uc.repo.GetSubject(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get subjects: %w", err)
	}

	typeData, err := uc.GetResourceType(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource types: %w", err)
	}

	rb := &responseBuilder{}
	respGrades := rb.buildGradesResponse(grades).Grades
	respSubjects := rb.buildSubjectsResponse(subjects).Subjects

	meta := &pb.ResourceMeta{
		Name: "Resources",
		Children: &pb.ResourceMeta_Children{
			Grades:   respGrades,
			Subjects: respSubjects,
			Types:    typeData.Types,
		},
	}

	metas := []*pb.ResourceMeta{meta}
	return &pb.ResourceMetaResp{
		Metas: metas,
	}, nil
}

// GetResourceDetail 获取资源详情
func (uc *ResourceUseCase) GetResourceDetail(ctx context.Context, req *pb.GetResourceDetailReq) (*pb.ResourceDetail, error) {
	gc := &gradeConverter{}
	rp := &requestParser{}

	grade := gc.getGradeName(req.Grade)
	subject, topic, learningModule, resourceDetail := rp.parseSubjectLevels(req.Subject)
	resourceType, _ := rp.parseResourceType(req.ResourceType)

	// 处理URL中的连字符
	topic = strings.ReplaceAll(topic, "-", " ")
	learningModule = strings.ReplaceAll(learningModule, "-", " ")
	resourceDetail = strings.ReplaceAll(resourceDetail, "-", " ")

	detail, err := uc.repo.GetResourceByUrl(ctx, req.Url)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource by url: %w", err)
	}

	if detail == nil {
		return &pb.ResourceDetail{
			PreUrl:   "",
			Resource: nil,
			NextUrl:  "",
		}, nil
	}

	dto, err := uc.DataToDTO(ctx, detail)
	if err != nil {
		return nil, fmt.Errorf("failed to convert data to DTO: %w", err)
	}

	result := &pb.ResourceDetail{
		PreUrl:   "",
		Resource: dto,
		NextUrl:  "",
	}

	// 获取上一个资源
	resourcePre, err := uc.repo.GetResourcePre(ctx, &model.ResourceReq{
		ResourceType:   resourceType,
		Grade:          grade,
		Subject:        subject,
		Topic:          topic,
		LearningModule: learningModule,
		ResourceDetail: "",
		Resource:       "",
		Page:           0,
		PageSize:       0,
	}, detail.ID)

	if err == nil && resourcePre != nil {
		result.PreUrl = resourcePre.Url
	}

	// 获取下一个资源
	resourceNext, err := uc.repo.GetResourceNext(ctx, &model.ResourceReq{
		ResourceType:   resourceType,
		Grade:          grade,
		Subject:        subject,
		Topic:          topic,
		LearningModule: learningModule,
		ResourceDetail: "",
		Resource:       "",
		Page:           0,
		PageSize:       0,
	}, detail.ID)

	if err == nil && resourceNext != nil {
		result.NextUrl = resourceNext.Url
	}

	return result, nil
}

// ListRandResource 获取随机资源列表
func (uc *ResourceUseCase) ListRandResource(ctx context.Context, req *pb.ListRandResourceReq) (*pb.ListRandResourceResp, error) {
	rp := &requestParser{}
	gc := &gradeConverter{}

	subject, topic, learningModule, resourceDetail := rp.parseSubjectLevels(req.Subject)
	grade := gc.getGradeName(req.Grade)

	worksheets := make([]*model.EduResources, 0)
	if req.Subject == "all" {
		// 获取工作表资源
		mathWorksheets, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
			ResourceType:   "worksheets",
			Grade:          grade,
			Subject:        "math",
			Topic:          topic,
			LearningModule: learningModule,
			ResourceDetail: resourceDetail,
			PageSize:       int(req.PageSize / 2),
		})

		if err != nil {
			uc.log.WithContext(ctx).Warnf("failed to get worksheets: %v", err)
		}

		elaWorksheets, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
			ResourceType:   "worksheets",
			Grade:          grade,
			Subject:        "english",
			Topic:          topic,
			LearningModule: learningModule,
			ResourceDetail: resourceDetail,
			PageSize:       int(req.PageSize / 2),
		})
		if err != nil {
			uc.log.WithContext(ctx).Warnf("failed to get worksheets: %v", err)
		}

		pageSize := int(req.PageSize)
		if pageSize == 0 {
			pageSize = 6
		}
		worksheets = append(worksheets, mathWorksheets[:int(pageSize/2)]...)
		worksheets = append(worksheets, elaWorksheets[:int(pageSize/2)]...)
	} else {
		// 获取工作表资源
		worksheets1, err1 := uc.repo.ListRandResources(ctx, &model.ResourceReq{
			ResourceType:   "worksheets",
			Grade:          grade,
			Subject:        subject,
			Topic:          topic,
			LearningModule: learningModule,
			ResourceDetail: resourceDetail,
			PageSize:       int(req.PageSize),
		})
		if err1 != nil {
			uc.log.WithContext(ctx).Errorf("failed to get worksheets: %v", err1)
		}
		worksheets = append(worksheets, worksheets1...)
	}

	// 获取涂色页资源
	coloringPages, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
		ResourceType: "coloring Pages",
		Topic:        req.ActivityTopic,
		PageSize:     int(req.PageSize),
	})
	if err != nil {
		uc.log.WithContext(ctx).Errorf("failed to get coloring pages: %v", err)
	}

	// 获取词汇表
	if subject == "all" {
		subject = ""
	}
	glossarys, err := uc.eduRepo.ListRandTerm(ctx, subject, int(req.PageSize))
	if err != nil {
		uc.log.WithContext(ctx).Errorf("failed to get glossarys: %v", err)
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}
	// 构建响应
	pbWorksheets := uc.buildResourceList(ctx, worksheets, req.ExcludeUrls, int(req.PageSize))
	slices.SortFunc(pbWorksheets, func(a, b *pb.EduResource) int {
		gradeA, errA := MinGradeNumber(a.Grade)
		gradeB, errB := MinGradeNumber(b.Grade)
		if errA != nil && errB != nil {
			return 0
		}
		if errA != nil {
			return 1 // 把非法年级排后面
		}
		if errB != nil {
			return -1
		}
		return gradeA - gradeB
	})
	pbColoringPages := uc.buildResourceList(ctx, coloringPages, req.ExcludeUrls, int(req.PageSize))
	slices.SortFunc(pbColoringPages, func(a, b *pb.EduResource) int {
		gradeA, errA := MinGradeNumber(a.Grade)
		gradeB, errB := MinGradeNumber(b.Grade)
		if errA != nil && errB != nil {
			return 0
		}
		if errA != nil {
			return 1 // 把非法年级排后面
		}
		if errB != nil {
			return -1
		}
		return gradeA - gradeB
	})
	pbGlossarys := uc.buildTermList(glossarys)

	return &pb.ListRandResourceResp{
		Worksheets:    pbWorksheets,
		ColoringPages: pbColoringPages,
		Terms:         pbGlossarys,
	}, nil
}

func MinGradeNumber(grades string) (int, error) {
	gradeMap := map[string]int{
		"grade pre-k": 0,
		"grade k":     1,
		"grade 1":     2,
		"grade 2":     3,
		"grade 3":     4,
		"grade 4":     5,
		"grade 5":     6,
		"grade 6":     7,
	}
	minGrade := -1
	for _, g := range strings.Split(grades, ",") {
		grade := strings.ToLower(strings.TrimSpace(g))
		if val, ok := gradeMap[grade]; ok {
			if minGrade == -1 || val < minGrade {
				minGrade = val
			}
		}
	}
	if minGrade == -1 {
		return 0, fmt.Errorf("invalid grade: %s", grades)
	}
	return minGrade, nil
}

// buildResourceList 构建资源列表
func (uc *ResourceUseCase) buildResourceList(ctx context.Context, resources []*model.EduResources, excludeUrls string, maxSize int) []*pb.EduResource {
	result := make([]*pb.EduResource, 0)
	urls := strings.Split(excludeUrls, ",")

	for _, v := range resources {
		if len(result) >= maxSize {
			break
		}

		// 检查是否在排除列表中
		excluded := false
		for _, url := range urls {
			if v.Url == url {
				excluded = true
				break
			}
		}

		if excluded {
			continue
		}

		dto, err := uc.DataToDTO(ctx, v)
		if err != nil {
			continue
		}
		result = append(result, dto)
	}

	return result
}

// buildTermList 构建词汇表列表
func (uc *ResourceUseCase) buildTermList(terms []*model.EduTerm) []*pb.EduTermInfo {
	result := make([]*pb.EduTermInfo, 0, len(terms))

	for _, v := range terms {
		termInfo := &pb.EduTermInfo{}
		err := copier.Copy(termInfo, v)
		if err != nil {
			continue
		}
		result = append(result, termInfo)
	}

	return result
}

// RefreshAllCache 刷新所有教育资源缓存
func (uc *ResourceUseCase) RefreshAllCache(ctx context.Context) error {
	uc.log.WithContext(ctx).Info("开始刷新所有教育资源缓存")

	if err := uc.repo.RefreshCache(ctx); err != nil {
		uc.log.WithContext(ctx).Errorf("刷新缓存失败: %v", err)
		return fmt.Errorf("failed to refresh all cache: %w", err)
	}

	uc.log.WithContext(ctx).Info("所有教育资源缓存刷新完成")
	return nil
}
