package biz

import (
	"fmt"
	"regexp"
	"strings"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

// BreadcrumbItem 面包屑项
type BreadcrumbItem struct {
	Title string `json:"title"`
	URL   string `json:"url"`
}

// URLResult URL和面包屑结果
type URLResult struct {
	URL string `json:"url"`
}

// ResourceInfo 资源信息结构体
type ResourceInfo struct {
	ResourceType   string
	Subject        string
	Grade          string
	LearningTopic  string
	LearningModule string
	Resource       string
	ResourceDetail string
}

// URLGenerator URL生成器
type URLGenerator struct {
	// 可以添加配置选项
}

// NewURLGenerator 创建新的URL生成器
func NewURLGenerator() *URLGenerator {
	return &URLGenerator{}
}

// GenerateURLAndBreadcrumb 根据资源信息生成URL和面包屑
func GenerateURLAndBreadcrumb(resourceType, subject, grade, learningTopic, learningModule, resource, resourceDetail string) (URLResult, int) {
	generator := NewURLGenerator()
	return generator.generate(resourceType, subject, grade, learningTopic, learningModule, resource, resourceDetail)
}

// generate 内部生成方法
func (g *URLGenerator) generate(resourceType, subject, grade, learningTopic, learningModule, resource, resourceDetail string) (URLResult, int) {
	// 标准化输入参数
	normalized := g.normalizeInputs(resourceType, subject, grade, learningTopic, learningModule, resource, resourceDetail)

	// 根据资源类型生成URL和面包屑
	if g.isActivityResource(normalized.ResourceType) {
		return g.generateActivityResourceURL(normalized)
	}

	return g.generateLearningResourceURL(normalized)
}

// normalizeInputs 标准化输入参数
func (g *URLGenerator) normalizeInputs(resourceType, subject, grade, learningTopic, learningModule, resource, resourceDetail string) ResourceInfo {
	return ResourceInfo{
		ResourceType:   normalizeResourceType(resourceType),
		Subject:        normalizeSubject(subject),
		Grade:          normalizeGrade(grade),
		LearningTopic:  normalizeTopic(learningTopic),
		LearningModule: normalizeTopic(learningModule),
		Resource:       SanitizeString(resource),
		ResourceDetail: SanitizeString(resourceDetail),
	}
}

// generateActivityResourceURL 生成活动类资源的URL和面包屑
func (g *URLGenerator) generateActivityResourceURL(info ResourceInfo) (URLResult, int) {
	// 活动类资源：按内容类型(主题分类)聚合
	level := 0
	url := ""

	///resource_type/｛activity_topic｝-for-｛grade｝
	if info.ResourceType != "" && info.LearningTopic != "" && info.Grade != "" {
		url = fmt.Sprintf("/%s/%s-for-%s", info.ResourceType, info.LearningTopic, info.Grade)
		level = 1
	} else if info.ResourceType != "" && info.LearningTopic != "" {
		url = fmt.Sprintf("/%s/%s", info.ResourceType, info.LearningTopic)
		level = 1
	} else {
		url = g.generateBasicURL(info)
	}

	return URLResult{
		URL: url,
	}, level
}

// generateLearningResourceURL 生成学习类资源的URL和面包屑
func (g *URLGenerator) generateLearningResourceURL(info ResourceInfo) (URLResult, int) {
	var url string

	level := 0

	// 根据知识点层级选择生成策略
	if info.Subject != "" && info.LearningTopic != "" && info.LearningModule != "" {
		url = g.generateURLWithLearningModule(info)
		level = 2
	} else if info.Subject != "" && info.LearningTopic != "" {
		url = g.generateURLWithLearningTopic(info)
		level = 1
	} else {
		url = g.generateBasicURL(info)
	}

	return URLResult{
		URL: url,
	}, level
}

// generateURLWithLearningModule 生成包含二级知识点的URL和面包屑
func (g *URLGenerator) generateURLWithLearningModule(info ResourceInfo) string {
	// 生成URL
	url := g.buildURLWithModule(info)

	return url
}

// generateURLWithLearningTopic 生成包含一级知识点的URL和面包屑
func (g *URLGenerator) generateURLWithLearningTopic(info ResourceInfo) string {

	// 生成URL
	url := g.buildURLWithTopic(info)

	return url
}

// generateBasicURL 生成基础维度的URL和面包屑
func (g *URLGenerator) generateBasicURL(info ResourceInfo) string {
	// 生成URL
	url := g.buildBasicURL(info)

	return url
}

// addBreadcrumbs 添加面包屑项
func (g *URLGenerator) addBreadcrumbs(info ResourceInfo, breadcrumb []BreadcrumbItem) []BreadcrumbItem {
	if info.Subject != "" {
		breadcrumb = append(breadcrumb, BreadcrumbItem{
			Title: formatSubjectTitle(info.Subject),
			URL:   fmt.Sprintf("/%s", info.Subject),
		})
	}

	if info.Grade != "" {
		breadcrumb = append(breadcrumb, BreadcrumbItem{
			Title: formatGradeTitle(info.Grade),
			URL:   fmt.Sprintf("/%s", info.Grade),
		})
	}

	if info.ResourceType != "" {
		breadcrumb = append(breadcrumb, BreadcrumbItem{
			Title: formatResourceTypeTitle(info.ResourceType),
			URL:   fmt.Sprintf("/%s", info.ResourceType),
		})
	}

	return breadcrumb
}

// buildURLWithModule 构建包含二级知识点的URL
func (g *URLGenerator) buildURLWithModule(info ResourceInfo) string {
	if info.Subject == "" || info.LearningTopic == "" || info.LearningModule == "" {
		return "/resources"
	}

	url := fmt.Sprintf("/%s/%s", info.Subject, info.LearningModule)
	return g.appendURLComponents(url, info)
}

// buildURLWithTopic 构建包含一级知识点的URL
func (g *URLGenerator) buildURLWithTopic(info ResourceInfo) string {
	if info.Subject == "" || info.LearningTopic == "" {
		return "/resources"
	}

	url := fmt.Sprintf("/%s/%s", info.Subject, info.LearningTopic)
	return g.appendURLComponents(url, info)
}

// buildBasicURL 构建基础URL
func (g *URLGenerator) buildBasicURL(info ResourceInfo) string {
	// 三维交叉聚合：学科 x 年级 x 内容类型
	if info.Subject != "" && info.Grade != "" && info.ResourceType != "" {
		return fmt.Sprintf("/%s-%s-for-%s", info.Subject, info.ResourceType, info.Grade)
	}

	// 两维交叉聚合：学科 x 年级
	if info.Subject != "" && info.Grade != "" {
		return fmt.Sprintf("/%s-for-%s", info.Subject, info.Grade)
	}

	// 两维交叉聚合：学科 x 内容类型
	if info.Subject != "" && info.ResourceType != "" {
		return fmt.Sprintf("/%s-%s", info.Subject, info.ResourceType)
	}

	// 两维交叉聚合：年级 x 内容类型
	if info.Grade != "" && info.ResourceType != "" {
		return fmt.Sprintf("/%s-for-%s", info.ResourceType, info.Grade)
	}

	// 按学科聚合
	if info.Subject != "" {
		return fmt.Sprintf("/%s", info.Subject)
	}

	// 按年级聚合
	if info.Grade != "" {
		return fmt.Sprintf("/%s", info.Grade)
	}

	// 按内容类型聚合
	if info.ResourceType != "" {
		return fmt.Sprintf("/%s", info.ResourceType)
	}

	// 默认
	return "/resources"
}

// appendURLComponents 向URL添加组件
func (g *URLGenerator) appendURLComponents(baseURL string, info ResourceInfo) string {
	url := baseURL

	// 添加资源类型到URL
	if info.ResourceType != "" {
		url = fmt.Sprintf("%s-%s", url, info.ResourceType)
	}

	// 添加年级到URL
	if info.Grade != "" {
		url = fmt.Sprintf("%s-for-%s", url, info.Grade)
	}

	return url
}

// isActivityResource 判断是否为活动类资源
func (g *URLGenerator) isActivityResource(resourceType string) bool {
	activityTypes := map[string]bool{
		"coloring-pages": true,
		// 可以添加更多活动类资源类型
	}
	return activityTypes[resourceType]
}

// 标准化函数
func normalizeGrade(grade string) string {
	switch strings.ToLower(strings.TrimSpace(grade)) {
	case "grade pre-k", "pre-kindergarten":
		return "cc-pre-kindergarten"
	case "grade k", "kindergarten":
		return "cc-kindergarten"
	case "1", "grade 1":
		return "cc-first-grade"
	case "2", "grade 2":
		return "cc-second-grade"
	case "3", "grade 3":
		return "cc-third-grade"
	case "4", "grade 4":
		return "cc-fourth-grade"
	case "5", "grade 5":
		return "cc-fifth-grade"
	case "6", "grade 6":
		return "cc-sixth-grade"
	default:
		return SanitizeString(grade)
	}
}

func normalizeSubject(subject string) string {
	switch strings.ToLower(strings.TrimSpace(subject)) {
	case "english", "ela":
		return "ela"
	case "math", "mathematics":
		return "math"
	default:
		return SanitizeString(subject)
	}
}

func normalizeResourceType(resourceType string) string {
	switch strings.ToLower(strings.TrimSpace(resourceType)) {
	case "worksheets":
		return "worksheets"
	case "coloring pages":
		return "coloring-pages"
	case "videos":
		return "videos"
	case "glossary":
		return "glossary"
	default:
		return SanitizeString(resourceType)
	}
}

func normalizeTopic(topic string) string {
	return SanitizeString(topic)
}

// 格式化函数
func formatResourceTypeTitle(resourceType string) string {
	trimmed := strings.TrimSpace(resourceType)
	// 处理连字符分隔的情况
	if strings.Contains(trimmed, "-") {
		words := strings.Split(trimmed, "-")
		for i, word := range words {
			if len(word) > 0 {
				words[i] = strings.ToUpper(word[:1]) + strings.ToLower(word[1:])
			}
		}
		return strings.Join(words, " ")
	}
	// 处理空格分隔的情况
	words := strings.Fields(trimmed)
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(word[:1]) + strings.ToLower(word[1:])
		}
	}
	return strings.Join(words, " ")
}

func formatTopicTitle(topic string) string {
	trimmed := strings.TrimSpace(topic)
	// 处理连字符分隔的情况
	if strings.Contains(trimmed, "-") {
		words := strings.Split(trimmed, "-")
		for i, word := range words {
			if len(word) > 0 {
				words[i] = cases.Title(language.English).String(strings.ToLower(word))
			}
		}
		return strings.Join(words, " ")
	}
	// 处理空格分隔的情况
	return cases.Title(language.English).String(strings.ToLower(trimmed))
}

func formatGradeTitle(grade string) string {
	switch strings.ToLower(strings.TrimSpace(grade)) {
	case "cc-pre-kindergarten":
		return "pre-Kindergarten"
	case "cc-kindergarten":
		return "Kindergarten"
	case "cc-first-grade":
		return "Grade 1"
	case "cc-second-grade":
		return "Grade 2"
	case "cc-third-grade":
		return "Grade 3"
	case "cc-fourth-grade":
		return "Grade 4"
	case "cc-fifth-grade":
		return "Grade 5"
	case "cc-sixth-grade":
		return "Grade 6"
	default:
		return cases.Title(language.English).String(strings.ToLower(strings.TrimSpace(grade)))
	}
}

func formatSubjectTitle(subject string) string {
	switch strings.ToLower(strings.TrimSpace(subject)) {
	case "ela":
		return "English"
	case "math":
		return "Math"
	default:
		return cases.Title(language.English).String(strings.ToLower(strings.TrimSpace(subject)))
	}
}

func SanitizeString(s string) string {
	// 1. 空格替换为 -
	s = regexp.MustCompile(`\s+`).ReplaceAllString(s, "-")
	// 2. 非 [A-Za-z0-9-] 替换为空
	s = regexp.MustCompile(`[^A-Za-z0-9-]`).ReplaceAllString(s, "")
	// 3. 连续 - 合并为一个
	s = regexp.MustCompile(`-+`).ReplaceAllString(s, "-")
	// 4. 去除开头和结尾的 -
	s = regexp.MustCompile(`^-+|-+$`).ReplaceAllString(s, "")
	return strings.ToLower(s)
}
