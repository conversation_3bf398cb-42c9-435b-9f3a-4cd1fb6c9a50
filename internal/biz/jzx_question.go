package biz

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/internal/pkg/custom_context"
	"hw-paas-service/internal/pkg/logger"
	"strings"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"

	pb "hw-paas-service/api/ai/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
)

type JzxQuestionUseCase struct {
	log         *log.Helper
	confBiz     *conf.Biz
	services    *services.Services
	questionDao *dao.AiJzxQuestionDao
}

func NewJzxQuestionUseCase(questionDao *dao.AiJzxQuestionDao, services *services.Services, confBiz *conf.Biz, logger log.Logger) *JzxQuestionUseCase {
	return &JzxQuestionUseCase{
		questionDao: questionDao,
		services:    services,
		confBiz:     confBiz,
		log:         log.NewHelper(logger),
	}
}

func (j *JzxQuestionUseCase) ListWithPage(ctx context.Context, req *pb.ListQuestionsRequest) (res *pb.ListQuestionsReply, err error) {
	res = &pb.ListQuestionsReply{}
	questions, total, err := j.questionDao.ListWithPage(ctx, int64(req.Id), int64(req.QType), int64(req.Difficulty), int64(req.Status), req.Grade, req.Subject, req.Knowledge, req.KnowledgeNo, req.Content, int64(req.Page), int64(req.PageSize))
	if err != nil {
		logger.Errorf(ctx, "Failed to list questions: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("Failed to list questions")
	}
	res.Total = int32(total)
	for _, question := range questions {
		res.Questions = append(res.Questions, &pb.Question{
			Id:          int32(question.ID),
			QType:       int32(question.QType),
			Difficulty:  int32(question.Difficulty),
			Status:      int32(question.Status),
			Grade:       question.Grade,
			Subject:     question.Subject,
			Knowledge:   question.Knowledge,
			KnowledgeNo: question.KnowledgeNo,
			Question:    question.Question,
			Answer:      question.Answer,
			Solution:    question.Solution,
			Note:        question.Note,
		})
	}
	return res, nil
}

func (j *JzxQuestionUseCase) GetQuestion(ctx context.Context, req *pb.GetQuestionRequest) (res *pb.Question, err error) {
	res = &pb.Question{}
	question, err := j.questionDao.Find(ctx, int64(req.Id))
	if err != nil {
		return nil, err
	}
	if question == nil {
		return nil, pb.ErrorHwPaasUnexceptError("question not found")
	}
	res.Id = int32(question.ID)
	res.QType = int32(question.QType)
	res.Difficulty = int32(question.Difficulty)
	res.Status = int32(question.Status)
	res.Grade = question.Grade
	res.Subject = question.Subject
	res.Knowledge = question.Knowledge
	res.KnowledgeNo = question.KnowledgeNo
	res.Question = question.Question
	res.Answer = question.Answer
	res.Solution = question.Solution
	res.Note = question.Note

	return res, nil
}

func (j *JzxQuestionUseCase) GetQuestionBatch(ctx context.Context, req *pb.GetQuestionBatchRequest) (res *pb.GetQuestionBatchReply, err error) {
	res = &pb.GetQuestionBatchReply{}
	ids := strings.Split(req.Id, ",")
	if len(ids) == 0 {
		return nil, pb.ErrorHwPaasUnexceptError("qid is required")
	}
	if len(ids) > 20 {
		return nil, pb.ErrorHwPaasUnexceptError("qid is too many")
	}
	var idArray []int64
	for _, id := range ids {
		idArray = append(idArray, cast.ToInt64(id))
	}
	questions, err := j.questionDao.GetAiJzxQuestionBatch(ctx, idArray)
	if err != nil {
		logger.Errorf(ctx, "Failed to get question batch: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("Failed to get question batch")
	}

	questionMap := make(map[int64]*pb.Question)
	for _, question := range questions {
		questionMap[question.ID] = &pb.Question{
			Id:          int32(question.ID),
			QType:       int32(question.QType),
			Difficulty:  int32(question.Difficulty),
			Status:      int32(question.Status),
			Grade:       question.Grade,
			Subject:     question.Subject,
			Knowledge:   question.Knowledge,
			KnowledgeNo: question.KnowledgeNo,
			Question:    question.Question,
			Answer:      question.Answer,
			Solution:    question.Solution,
			Note:        question.Note,
		}
	}
	for _, id := range idArray {
		if question, ok := questionMap[id]; ok {
			res.Questions = append(res.Questions, question)
		}
	}
	return res, nil
}

func (j *JzxQuestionUseCase) UpsertQuestion(ctx context.Context, req *pb.UpsertQuestionRequest) (res *structpb.Struct, err error) {
	res = &structpb.Struct{}
	question, err := j.questionDao.Find(ctx, int64(req.Id))
	if err != nil {
		logger.Errorf(ctx, "Failed to find question: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("Failed to find question")
	}
	if question.Status == 5 {
		isBand, err := j.services.BadouSvc.CheckIsBind(ctx, question.ID)
		if err != nil {
			logger.Errorf(ctx, "Failed to check is bind: %v", err)
			return nil, pb.ErrorHwPaasUnexceptError("Failed to check is bind")
		}
		if isBand {
			return nil, pb.ErrorHwPaasQuestionIsBand("题目已绑定,无法修改")
		}
	}
	if question == nil {
		question = &model.AiJzxQuestion{}
		question.ID = int64(req.Id)
	}
	question.QType = int64(req.QType)
	question.Difficulty = int64(req.Difficulty)
	question.Status = int64(req.Status)
	question.Grade = req.Grade
	question.Subject = req.Subject
	question.Knowledge = req.Knowledge
	question.KnowledgeNo = req.KnowledgeNo
	question.Question = req.Question
	question.Answer = req.Answer
	question.Solution = req.Solution
	question.Note = req.Note

	err = j.questionDao.Create(ctx, question)
	if err != nil {
		logger.Errorf(ctx, "Failed to create question: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("Failed to create question")
	}
	q, _ := json.Marshal(question)
	_ = protojson.Unmarshal(q, res)
	return res, nil
}

func (j *JzxQuestionUseCase) UpdateQuestionWithKey(ctx context.Context, req *pb.UpdateQuestionWithKeyRequest) (res *structpb.Struct, err error) {
	res = &structpb.Struct{}
	question, err := j.questionDao.Find(ctx, int64(req.Id))
	if err != nil {
		logger.Errorf(ctx, "Failed to find question: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("Failed to find question")
	}
	if question == nil {
		question = &model.AiJzxQuestion{
			ID: int64(req.Id),
		}
	}
	if question.Status == 5 {
		isBand, err := j.services.BadouSvc.CheckIsBind(ctx, question.ID)
		if err != nil {
			logger.Errorf(ctx, "Failed to check is bind: %v", err)
			return nil, pb.ErrorHwPaasUnexceptError("Failed to check is bind")
		}
		if isBand {
			return nil, pb.ErrorHwPaasQuestionIsBand("题目已绑定,无法修改")
		}
	}

	if _, ok := req.Fields["question"]; ok {
		question.Question = req.Fields["question"]
	}
	if _, ok := req.Fields["answer"]; ok {
		question.Answer = req.Fields["answer"]
	}
	if _, ok := req.Fields["solution"]; ok {
		question.Solution = req.Fields["solution"]
	}
	if _, ok := req.Fields["note"]; ok {
		question.Note = req.Fields["note"]
	}
	if _, ok := req.Fields["status"]; ok {
		question.Status = cast.ToInt64(req.Fields["status"])
	}
	if _, ok := req.Fields["difficulty"]; ok {
		question.Difficulty = cast.ToInt64(req.Fields["difficulty"])
	}
	if _, ok := req.Fields["q_type"]; ok {
		question.QType = cast.ToInt64(req.Fields["q_type"])
	}
	if _, ok := req.Fields["grade"]; ok {
		question.Grade = req.Fields["grade"]
	}
	if _, ok := req.Fields["subject"]; ok {
		question.Subject = req.Fields["subject"]
	}
	if _, ok := req.Fields["knowledge"]; ok {
		question.Knowledge = req.Fields["knowledge"]
	}
	if _, ok := req.Fields["knowledge_no"]; ok {
		question.KnowledgeNo = req.Fields["knowledge_no"]
	}

	err = j.questionDao.Create(ctx, question)
	if err != nil {
		logger.Errorf(ctx, "Failed to create question: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("Failed to create question")
	}
	q, _ := json.Marshal(question)
	_ = protojson.Unmarshal(q, res)
	return
}

// GetQuestionsStatus gets the status of multiple questions
func (j *JzxQuestionUseCase) GetQuestionStatus(ctx context.Context, req *pb.GetQuestionStatusRequest) (*pb.GetQuestionStatusReply, error) {
	ids := strings.Split(req.Ids, ",")
	if len(ids) == 0 {
		return nil, pb.ErrorHwPaasAppidError("qid is required")
	}

	var idArray []int64
	for _, id := range ids {
		idArray = append(idArray, cast.ToInt64(id))
	}

	questions, err := j.questionDao.GetAiJzxQuestionStatus(ctx, idArray)
	if err != nil {
		logger.Errorf(ctx, "Failed to get question status: %v", err)
		return nil, err
	}

	questionStatus := make([]*pb.GetQuestionStatusReply_QuestionStatus, 0)
	for _, q := range questions {
		questionStatus = append(questionStatus, &pb.GetQuestionStatusReply_QuestionStatus{
			Id:     cast.ToInt32(q.ID),
			Status: cast.ToInt32(q.Status),
		})
	}

	return &pb.GetQuestionStatusReply{
		Questions: questionStatus,
	}, nil
}

func (j *JzxQuestionUseCase) UploadFile(ctx context.Context, req *pb.UploadFileRequest) (res *pb.UploadFileReply, err error) {
	res = &pb.UploadFileReply{}
	file, err := base64.StdEncoding.DecodeString(req.File)
	if err != nil {
		logger.Errorf(ctx, "Failed to decode file: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("Failed to decode file")
	}
	fileName := custom_context.GetTraceId(ctx) + "." + req.Suffix
	ossUrl := ""
	if strings.ToLower(req.Suffix) == "pdf" {
		ossUrl, err = j.services.AzureBlob.UploadFile(ctx, fileName, file)
	} else {
		ossUrl, err = j.services.OssSvc.Upload2Oss(ctx, file, fileName)
	}
	if err != nil {
		logger.Errorf(ctx, "Failed to upload file: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("Failed to upload file")
	}
	res.Url = ossUrl
	return res, nil
}
