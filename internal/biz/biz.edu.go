package biz

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/internal/pkg/custom_context"
	"strings"
	"time"

	pb "hw-paas-service/api/ai/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/structpb"
	"gorm.io/gorm"
)

type EduUseCase struct {
	log      *log.Helper
	confBiz  *conf.Biz
	services *services.Services
	eduDao   *dao.EduDao
}

func NewEduUseCase(eduDao *dao.EduDao, services *services.Services, confBiz *conf.Biz, logger log.Logger) *EduUseCase {
	return &EduUseCase{
		eduDao:   eduDao,
		services: services,
		confBiz:  confBiz,
		log:      log.<PERSON>Helper(logger),
	}
}

func (b *EduUseCase) ListBlogWithPage(ctx context.Context, req *pb.BlogListReq) (res *pb.BlogListResp, err error) {
	res = &pb.BlogListResp{}
	articles, total, err := b.eduDao.ListBlogWithPage(ctx, int64(req.Page), int64(req.PageSize), req.Category)
	if err != nil {
		return nil, pb.ErrorHwPaasUnexceptError("Failed to list blog articles")
	}
	res.List = make([]*pb.BlogArticle, 0)
	for _, article := range articles {
		res.List = append(res.List, &pb.BlogArticle{
			Id:           int32(article.ID),
			Path:         article.Path,
			Category:     article.Category,
			ArticleTitle: article.ArticleTitle,
			ShortContent: article.ShortContent,
			CoverImg:     article.CoverImg,
			AuthorAvatar: article.AuthorAvatar,
			AuthorName:   article.AuthorName,
			UpdatedAt:    article.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	res.Total = int32(total)
	return res, nil
}

func (b *EduUseCase) SetBlog(ctx context.Context, req *pb.BlogArticle) (res *structpb.Struct, err error) {
	res = &structpb.Struct{}

	// 只有测试环境和灰度环境开启
	if !b.confBiz.BlogSetEnable {
		return nil, pb.ErrorHwPaasUnexceptError("Blog set is not enabled")
	}

	article := &model.BlogArticle{
		ID:              int64(req.Id),
		Path:            req.Path,
		Category:        req.Category,
		ArticleTitle:    req.ArticleTitle,
		ShortContent:    req.ShortContent,
		CoverImg:        req.CoverImg,
		AuthorAvatar:    req.AuthorAvatar,
		AuthorName:      req.AuthorName,
		ArticleContent:  req.ArticleContent,
		PageTitle:       req.PageTitle,
		MetaKeywords:    req.MetaKeywords,
		MetaDescription: req.MetaDescription,
	}

	// 使用本地时区解析时间，避免时区转换问题
	loc, _ := time.LoadLocation("Local")
	if req.CreatedAt != "" {
		article.CreatedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", req.CreatedAt, loc)
	}
	if req.UpdatedAt != "" {
		article.UpdatedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", req.UpdatedAt, loc)
	}
	fmt.Println(article, loc, req.UpdatedAt, article.UpdatedAt)

	err = b.eduDao.SaveBlog(ctx, article)
	if err != nil {
		return nil, pb.ErrorHwPaasUnexceptError("Failed to set blog article")
	}

	return res, nil
}

func (b *EduUseCase) FirstBlogWithPath(ctx context.Context, req *pb.BlogDetailReq) (res *pb.BlogDetailResp, err error) {
	res = &pb.BlogDetailResp{}

	var article *model.BlogArticle
	if req.Id > 0 {
		article, err = b.eduDao.GetBlogById(ctx, req.Id)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, pb.ErrorHwPaasUnexceptError("Blog article not found")
		}
	} else {
		//req.Path是base64编码的，需要解码
		path, err := base64.StdEncoding.DecodeString(req.Path)
		if err != nil {
			return nil, pb.ErrorHwPaasUnexceptError("Failed to decode blog path")
		}

		//path需要去掉前缀/和后缀/
		pathStr := string(path)
		pathStr = strings.TrimPrefix(pathStr, "/")
		pathStr = strings.TrimSuffix(pathStr, "/")
		article, err = b.eduDao.FirstBlogWithPath(ctx, pathStr)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, pb.ErrorHwPaasUnexceptError("Blog article not found")
		}
	}

	if err != nil {
		return nil, pb.ErrorHwPaasUnexceptError("Failed to get blog article")
	}
	res.Article = &pb.BlogArticle{
		Id:              int32(article.ID),
		Path:            article.Path,
		Category:        article.Category,
		CoverImg:        article.CoverImg,
		ArticleTitle:    article.ArticleTitle,
		ShortContent:    article.ShortContent,
		AuthorAvatar:    article.AuthorAvatar,
		AuthorName:      article.AuthorName,
		ArticleContent:  article.ArticleContent,
		PageTitle:       article.PageTitle,
		MetaKeywords:    article.MetaKeywords,
		MetaDescription: article.MetaDescription,
		UpdatedAt:       article.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	return res, nil
}

func (b *EduUseCase) BlogCategory(ctx context.Context, req *pb.BlogCategoryReq) (res *pb.BlogCategoryResp, err error) {
	res = &pb.BlogCategoryResp{}
	res.List = b.confBiz.BlogCategories
	return res, nil
}

func (b *EduUseCase) BlogFeedback(ctx context.Context, req *pb.BlogFeedbackReq) (res *structpb.Struct, err error) {
	traceId := custom_context.GetTraceId(ctx)

	feedback, _ := json.Marshal(req)
	b.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(traceId, "blog", "blog_feedback", string(feedback)))
	return nil, nil
}

func (b *EduUseCase) EduTermList(ctx context.Context, req *pb.EduTermListReq) (res *pb.EduTermListResp, err error) {
	res = &pb.EduTermListResp{}
	terms, err := b.eduDao.ListTerm(ctx, req.Subject)
	if err != nil {
		return nil, pb.ErrorHwPaasUnexceptError("Failed to list edu terms")
	}
	res.List = make([]*pb.EduTermInfo, 0)
	for _, term := range terms {
		tags := make([]string, 0)
		for _, tag := range strings.Split(term.Tag, ",") {
			if tag != "" {
				tags = append(tags, strings.TrimSpace(tag))
			}
		}
		res.List = append(res.List, &pb.EduTermInfo{
			Id:          int32(term.ID),
			Term:        term.Term,
			Knowledge_1: term.Knowledge1,
			Knowledge_2: term.Knowledge2,
			Title:       term.Title,
			Tag:         tags,
			Path:        term.Path,
		})
	}
	res.Total = int32(len(terms))
	return res, nil
}

func (b *EduUseCase) EduTermDetail(ctx context.Context, path string) (res *pb.EduTermDetailResp, err error) {
	res = &pb.EduTermDetailResp{}
	terms, err := b.eduDao.GetTermByPath(ctx, path)
	if err != nil {
		return nil, pb.ErrorHwPaasUnexceptError("Failed to get edu term detail")
	}
	if len(terms) == 0 {
		return nil, pb.ErrorHwPaasUnexceptError("Edu term not found")
	}
	res.BlockList = make([]*pb.EduTermBlock, 0)
	for _, term := range terms {
		res.BlockList = append(res.BlockList, &pb.EduTermBlock{
			ModuleType:   term.ModuleType,
			ModuleIndex:  int32(term.ModuleIndex),
			BlockType:    term.BlockType,
			BlockOrder:   int32(term.BlockOrder),
			BlockContent: term.BlockContent,
		})
		if term.ModuleIndex == 1 && term.BlockOrder == 1 {
			tags := make([]string, 0)
			for _, tag := range strings.Split(term.Tag, ",") {
				if tag != "" {
					tags = append(tags, strings.TrimSpace(tag))
				}
			}
			res.Id = int32(term.ID)
			res.Term = term.Term
			res.Knowledge_1 = term.Knowledge1
			res.Knowledge_2 = term.Knowledge2
			res.Title = term.Title
			res.Tag = tags
			res.MetaDescription = term.MetaDescription
			res.CreatedAt = term.CreatedAt.Format("2006-01-02 15:04:05")
			res.UpdatedAt = term.UpdatedAt.Format("2006-01-02 15:04:05")
		}
	}
	return res, nil
}
