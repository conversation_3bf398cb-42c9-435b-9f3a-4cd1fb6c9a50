package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	pb "hw-paas-service/api/comment/v1"
	"hw-paas-service/internal/data/dao"
)

type CommentUseCase struct {
	log         *log.Helper
	commentRepo *dao.CommentRepo
}

func NewCommentUseCase(logger log.Logger, commentRepo *dao.CommentRepo) *CommentUseCase {
	return &CommentUseCase{
		log:         log.<PERSON><PERSON><PERSON>(logger),
		commentRepo: commentRepo,
	}
}

func (uc *CommentUseCase) ListComments(ctx context.Context, subjectType int, subjectId string, parentID uint64, page, pageSize int) (*pb.ListCommentReply, error) {
	comments, total, err := uc.commentRepo.ListComments(ctx, subjectType, subjectId, parentID, page, pageSize)
	if err != nil {
		return nil, err
	}

	commentInfos := make([]*pb.CommentInfo, 0)
	for _, comment := range comments {
		commentInfos = append(commentInfos, &pb.CommentInfo{
			Id:          int64(comment.ID),
			SubjectType: int32(comment.SubjectType),
			SubjectId:   comment.SubjectID,
			ParentId:    int64(comment.ParentID),
			UserAvatar:  comment.UserAvatar,
			UserName:    comment.UserName,
			Content:     comment.Content,
			CreatedAt:   comment.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return &pb.ListCommentReply{
		Comments: commentInfos,
		Total:    int32(total),
	}, nil
}
