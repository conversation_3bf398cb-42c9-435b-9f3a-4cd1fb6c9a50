package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
)

type CodeLoginResp struct {
	TalID        string //好未来用户 ID
	TalRole      int    //好未来用户角色
	TalType      int    //好未来用户类型
	TalMode      int    //好未来用户身份
	TalToken     string //用户登录标识
	ClientID     int    //终端应用ID
	TargetCG     int    //用户所属业务线
	CurrentCG    int    //当前登录业务线
	Expire       int64  //登录标识有效时间
	Life         int64  //存活时间
	Ext          string //扩展信息
	OriginTalID  string //好未来临时用户 ID
	TouristTalID string //好未来游客用户 ID
	OauthType    string //三方登录类型
	OauthAppID   string //三方登录应用ID
	OauthOpenID  string //三方登录用户ID
}

type LoginBaseParam struct {
	Ticket     string
	ClientID   string
	DeviceID   string
	VersionNum string
}

type CheckLoginReq struct {
	LoginBaseParam
	Token string
}

type CheckLoginReply struct {
	TalID     string
	TalRole   int
	TalType   int
	TalMode   int
	TalToken  string
	ClientID  int
	TargetCG  int
	CurrentCG int
	Expire    int64
	Life      int64
	Ext       string
}

type CodeLoginReq struct {
	LoginBaseParam
	Code string
}

type CodeLoginRepo interface {
	CodeLogin(ctx context.Context, req *data.CodeLoginReq) (*data.CodeLoginResp, error)
	CheckLogin(ctx context.Context, req *data.CheckLoginReq) (*data.CheckLoginReply, error)
	GetTicket(ctx context.Context) (string, error)
	LoginOut(ctx context.Context, req *data.LoginBaseParam, token string) (string, error)
}

type CodeLoginUseCase struct {
	repo       *data.UcenterRepo
	accountDao *dao.AccountDao
	log        *log.Helper
}

func NewCodeLoginUseCase(repo *data.UcenterRepo, accountDao *dao.AccountDao, logger log.Logger) *CodeLoginUseCase {
	return &CodeLoginUseCase{
		repo:       repo,
		accountDao: accountDao,
		log:        log.NewHelper(logger),
	}
}

func (uc *CodeLoginUseCase) CodeLogin(ctx context.Context, req *CodeLoginReq) (*CodeLoginResp, error) {
	reqData := &data.CodeLoginReq{}
	if err := copier.Copy(reqData, req); err != nil {
		uc.log.WithContext(ctx).Errorf("CodeLogin copier.Copy error: %v", err)
		return nil, err
	}
	rlt, err := uc.repo.CodeLogin(ctx, reqData)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("CodeLogin error: %v", err)
		return nil, err
	}

	uc.SyncAccount(ctx, rlt.TalID)

	resp := &CodeLoginResp{}
	if err := copier.Copy(resp, rlt); err != nil {
		uc.log.WithContext(ctx).Errorf("CodeLogin copier.Copy error: %v", err)
		return nil, err
	}
	return resp, nil
}

func (uc *CodeLoginUseCase) SyncAccount(ctx context.Context, talID string) error {
	uc.log.WithContext(ctx).Infof("SyncAccount talID: %s", talID)
	account, err := uc.accountDao.Find(ctx, talID)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("SyncAccount error: %v", err)
	}
	if account != nil {
		return nil
	}
	userInfo, err := uc.repo.GetUserProfile(ctx, talID)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("SyncAccount GetUserProfile error: %v", err)
		return err
	}

	account = &model.Account{
		TalID: talID,
		Email: userInfo.Email,
	}
	if err := uc.accountDao.Create(ctx, account); err != nil {
		uc.log.WithContext(ctx).Errorf("Create account error: %v", err)
		return err
	}
	uc.log.WithContext(ctx).Infof("SyncAccount success: %+v", account)
	return nil
}

func (uc *CodeLoginUseCase) LoginOut(ctx context.Context, token string) (string, error) {
	res, err := uc.repo.LoginOut(ctx, &data.LoginBaseParam{}, token)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("LoginOut error: %v", err)
		return "", err
	}
	return res, nil
}

func (uc *CodeLoginUseCase) CheckLogin(ctx context.Context, token string) (*CheckLoginReply, error) {
	res, err := uc.repo.CheckLogin(ctx, &data.CheckLoginReq{Token: token})
	if err != nil {
		uc.log.WithContext(ctx).Errorf("CheckLogin error: %v", err)
		return nil, err
	}
	reply := &CheckLoginReply{
		TalID:    res.TalID,
		TalToken: res.TalToken,
	}
	return reply, nil
}

// 检查email的用户是否存在
func (uc *CodeLoginUseCase) CheckEmailExists(ctx context.Context, email string) (bool, error) {
	account, err := uc.accountDao.FindByEmal(ctx, email)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("CheckEmailExists error: %v", err)
		return false, err
	}
	if account != nil {
		return true, nil // 用户存在
	}
	return false, nil // 用户不存在
}
