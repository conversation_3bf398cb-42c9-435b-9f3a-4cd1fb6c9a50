package biz

import (
	"context"
	"encoding/json"
	"git.100tal.com/znxx_xpp/go-libs/util"
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	pb "hw-paas-service/api/ai/v1"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/internal/data/services/kousuan"
	"hw-paas-service/internal/data/services/task_drive"
	"hw-paas-service/internal/pkg/custom_context"
	"hw-paas-service/internal/pkg/sync"
	"io"
	"net/http"
	"time"
)

type CorrectUseCase struct {
	log           *log.Helper
	biz           *conf.Biz
	kousuanClient *kousuan.Client
	ftDao         *dao.FeedTraceDao
	services      *services.Services
}
type traceCorrectResult struct {
	TraceID  string      `json:"trace_id"`  //traceID
	UserId   string      `json:"user_id"`   //用户ID
	DeviceId string      `json:"device_id"` //设备ID
	ImageUrl string      `json:"image_url"`
	Resp     interface{} `json:"resp"`
}

type Feedback struct {
	Result string `json:"result"`
}

func NewCorrectUseCase(conf *conf.Biz, logger log.Logger, ftDao *dao.FeedTraceDao, services *services.Services) *CorrectUseCase {
	return &CorrectUseCase{
		log:           log.NewHelper(logger),
		biz:           conf,
		kousuanClient: kousuan.NewClient(conf, logger),
		ftDao:         ftDao,
		services:      services,
	}
}

func (uc *CorrectUseCase) QueryCorrect(ctx context.Context, imageUrl string) (res interface{}, err error) {
	traceId := custom_context.GetTraceId(ctx)
	traceLog := &traceCorrectResult{
		TraceID:  custom_context.GetTraceId(ctx),
		UserId:   custom_context.GetJwtUserId(ctx),
		DeviceId: custom_context.GetDeviceId(ctx),
		ImageUrl: imageUrl,
	}
	defer func() {
		gCtx := util.NewTraceContext(nil, traceId)
		sync.Go(gCtx, uc.log, func() {
			traceB, _ := jsoniter.Marshal(traceLog)
			uc.log.WithContext(gCtx).Info(common.MakeLogBackFlowMsgInfo(traceId, "kousuan_query_correct", "dw_query_correct_trace", string(traceB)))
			return
		})
	}()
	//图片下载
	start := time.Now().UnixNano() / 1e6
	uc.log.WithContext(ctx).Infof("downloading image start: %d", start)
	imgBytes, err := uc.downloadImage(ctx, imageUrl)
	end := time.Now().UnixNano() / 1e6
	uc.log.WithContext(ctx).Infof("downloading image sub: %d", end-start)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("download image error: %v", err)
		return nil, err
	}
	//图片下载
	startCorrect := time.Now().UnixNano() / 1e6
	uc.log.WithContext(ctx).Infof("KousuanCorrect start: %d", startCorrect)
	correct, err := uc.kousuanClient.KousuanCorrect(ctx, imgBytes)
	traceLog.Resp = correct
	endCorrect := time.Now().UnixNano() / 1e6
	uc.log.WithContext(ctx).Infof("KousuanCorrect sub: %d", endCorrect-startCorrect)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("kousuan correct error: %v", err)
		return nil, err
	}
	return correct, nil
}

func (uc *CorrectUseCase) downloadImage(ctx context.Context, imageURL string) (imgBytes []byte, err error) {

	uc.log.WithContext(ctx).Infof("downloading image: %s", imageURL)
	// Download the image
	resp, err := http.Get(imageURL)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("error downloading image: %v", err)
		return
	}
	defer func() {
		_ = resp.Body.Close()
	}()
	// Read the image data
	imgBytes, err = io.ReadAll(resp.Body)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("error reading image data: %v", err)
	}
	//ioutil.WriteFile("test.jpg", imgBytes, 0666)
	return
}

func (uc *CorrectUseCase) FeedbackTrace(ctx context.Context, req *pb.FeedbackTraceRequest) (*pb.FeedbackTraceReply, error) {
	uc.log.WithContext(ctx).Infof("FeedbackTrace: %v", req)

	if req.Biz == 0 || req.TraceId == "" || req.Feedback == "" {
		return nil, pb.ErrorHwPaasParamError("反馈参数错误")
	}
	biz := ""
	switch req.Biz { //1:口算批改;2:指尖查词;3:作业批改;4:指尖查词;5:语音查词;6:绘本指读
	case 4:
		biz = "finger_words"
	case 5:
		biz = "audio_words"
	case 6:
		biz = "reading_book"
	}
	if biz != "" {
		uc.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(req.TraceId, biz, "dw_feedback", req.Feedback))

		feedback := &Feedback{}
		_ = json.Unmarshal([]byte(req.Feedback), feedback)
		uc.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(req.TraceId, biz, "base_info_feedback", "{\"feedback\": \""+feedback.Result+"\"}"))
	}

	_ = uc.ftDao.Create(ctx, &model.FeedbackTrace{
		Biz:     uint8(req.Biz),
		TraceID: req.TraceId,
		Content: req.Feedback,
	})
	return nil, nil
}

func (uc *CorrectUseCase) CommonConfig() (*pb.CommonConfigResp, error) {
	commonConfig, _ := json.Marshal(uc.biz.CommonConfig)
	commonConfigResp := &pb.CommonConfigResp{}
	_ = json.Unmarshal(commonConfig, commonConfigResp)
	return commonConfigResp, nil
}

var TaskDriveMap = map[string]string{
	"snap word":  "novice_task_5",
	"read along": "novice_task_6",
	"snap math":  "novice_task_7",
}

func (uc *CorrectUseCase) TaskDrive(ctx context.Context, app string) (*pb.TaskDriveResp, error) {
	if _, ok := TaskDriveMap[app]; !ok {
		uc.log.WithContext(ctx).Errorf("TaskDrive 不支持此app: %v", app)
		return &pb.TaskDriveResp{}, pb.ErrorHwPaasDefaultErr("不支持此app")
	}
	_, err := uc.services.TaskDriveSvc.TaskDrive(ctx, &task_drive.Request{
		ActivityID: "634802730490551036",
		TaskID:     TaskDriveMap[app],
		TalID:      custom_context.GetJwtUserId(ctx),
	})
	if err != nil {
		uc.log.WithContext(ctx).Errorf("TaskDrive error: %v", err)
		return nil, err
	}
	return &pb.TaskDriveResp{}, nil
}
