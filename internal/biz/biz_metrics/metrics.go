package biz_metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/mem"
	"runtime"
	"time"
)

const (
	Namespace = "HwPaasService"
)

var MetricSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
	Namespace: Namespace,
	Subsystem: "http",
	Name:      "duration_sec",
	Help:      "server requests duratio(sec).",
	Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.250, 0.5, 1, 1.5, 2.0},
}, []string{"kind", "operation"})

var MetricRequests = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: Namespace,
	Subsystem: "http",
	Name:      "total",
	Help:      "The total number of processed requests",
}, []string{"kind", "operation", "code", "reason"})

var MetricClientSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
	Namespace: Namespace,
	Subsystem: "third_client_http",
	Name:      "duration_sec",
	Help:      "server requests duratio(sec).",
	Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.250, 0.5, 1, 1.5, 2.0},
}, []string{"kind", "operation"})
var (
	startTime = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Namespace: Namespace,
			Name:      "start_time",
			Help:      "start_time(s)",
		})
	allMemUsage = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Namespace: Namespace,
			Name:      "all_mem",
			Help:      "all_mem",
		})
)
var (
	cpuUsage = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "go_cpu_usage",
		Help: "The fraction of CPU time consumed by the process.",
	})
	usedMemUsage = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "go_used_mem_usage",
		Help: "使用的内存",
	})
)

// MetricRedisPoolStats Redis连接池统计
var MetricRedisPoolStats = prometheus.NewGaugeVec(prometheus.GaugeOpts{
	Namespace: "redis",
	Subsystem: "pool",
	Name:      "stats",
	Help:      "Redis connection pool statistics.",
}, []string{"operation"})

func updateUsage() {
	ticker := time.NewTicker(1 * time.Second) // 每秒更新一次 CPU 使用率百分比
	for range ticker.C {
		percent, _ := cpu.Percent(time.Second, false) // 获取 CPU 使用率百分比数组（通常是所有CPU核心的平均值）
		cpuUsage.Set(percent[0])                      // 取第一个值作为示例，实际应用中可能需要根据实际情况选择或计算平均值。
		usedMemUsage.Set(getMemoryUsage())            //当前使用内存
	}
}
func getMemoryUsage() float64 {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	//fmt.Printf("Alloc = %v MiB\n", bToMb(memStats.Alloc))
	//fmt.Printf("TotalAlloc = %v MiB\n", bToMb(memStats.TotalAlloc))
	//fmt.Printf("Sys = %v MiB\n", bToMb(memStats.Sys))
	//fmt.Printf("HeapAlloc = %v MiB\n", bToMb(memStats.HeapAlloc))
	//fmt.Printf("HeapSys = %v MiB\n", bToMb(memStats.HeapSys))
	return float64(bToMb(memStats.Alloc))
}

func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

func getTotalMemory() uint64 {
	v, _ := mem.VirtualMemory()
	return v.Total / 1024 / 1024
}
func init() {
	//getMemoryUsage()
	//allMemUsage.Set(float64(getTotalMemory()))
	//startTime.Set(float64(time.Now().Unix()))
	//go updateUsage()
	//prometheus.MustRegister(allMemUsage, usedMemUsage, startTime, cpuUsage, MetricSeconds, MetricRequests, MetricClientSeconds)
	prometheus.MustRegister(MetricSeconds, MetricRequests, MetricClientSeconds, MetricRedisPoolStats)
}

func RedisPoolStats(hits, misses, timeouts, totalConns, idleConns, stalledConns uint32) {
	MetricRedisPoolStats.WithLabelValues("hits").Set(float64(hits))
	MetricRedisPoolStats.WithLabelValues("misses").Set(float64(misses))
	MetricRedisPoolStats.WithLabelValues("timeouts").Set(float64(timeouts))
	MetricRedisPoolStats.WithLabelValues("totalConns").Set(float64(totalConns))
	MetricRedisPoolStats.WithLabelValues("idleConns").Set(float64(idleConns))
	MetricRedisPoolStats.WithLabelValues("stalledConns").Set(float64(stalledConns))
}
