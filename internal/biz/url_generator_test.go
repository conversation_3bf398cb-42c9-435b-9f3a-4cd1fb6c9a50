package biz

import (
	"reflect"
	"testing"
)

func TestGenerateURLAndBreadcrumb(t *testing.T) {
	tests := []struct {
		name               string
		resourceType       string
		subject            string
		grade              string
		learningTopic      string
		learningModule     string
		resource           string
		resourceDetail     string
		expectedURL        string
		expectedBreadcrumb []BreadcrumbItem
	}{
		{
			name:           "活动类资源 - Coloring Pages",
			resourceType:   "coloring pages",
			subject:        "",
			grade:          "",
			learningTopic:  "animal coloring pages",
			learningModule: "",
			resource:       "",
			resourceDetail: "",
			expectedURL:    "/coloring-pages/animal-coloring-pages",
			expectedBreadcrumb: []BreadcrumbItem{
				{Title: "Home", URL: "/"},
				{Title: "Coloring Pages", URL: "/coloring-pages"},
				{Title: "Animal Coloring Pages", URL: "/coloring-pages/animal-coloring-pages"},
			},
		},
		{
			name:           "学习类资源 - 三维交叉聚合",
			resourceType:   "worksheets",
			subject:        "math",
			grade:          "grade 1",
			learningTopic:  "geometry",
			learningModule: "",
			resource:       "",
			resourceDetail: "",
			expectedURL:    "/math/geometry-worksheets-for-cc-first-grade",
			expectedBreadcrumb: []BreadcrumbItem{
				{Title: "Home", URL: "/"},
				{Title: "Math", URL: "/math"},
				{Title: "Grade 1", URL: "/cc-first-grade"},
				{Title: "Worksheets", URL: "/worksheets"},
				{Title: "Geometry", URL: "/math/geometry-worksheets-for-cc-first-grade"},
			},
		},
		{
			name:           "学习类资源 - 二维交叉聚合",
			resourceType:   "worksheets",
			subject:        "ela",
			grade:          "",
			learningTopic:  "",
			learningModule: "",
			resource:       "",
			resourceDetail: "",
			expectedURL:    "/ela-worksheets",
			expectedBreadcrumb: []BreadcrumbItem{
				{Title: "Home", URL: "/"},
				{Title: "English", URL: "/ela"},
				{Title: "Worksheets", URL: "/worksheets"},
			},
		},
		{
			name:           "学习类资源 - 按学科聚合",
			resourceType:   "",
			subject:        "math",
			grade:          "",
			learningTopic:  "",
			learningModule: "",
			resource:       "",
			resourceDetail: "",
			expectedURL:    "/math",
			expectedBreadcrumb: []BreadcrumbItem{
				{Title: "Home", URL: "/"},
				{Title: "Math", URL: "/math"},
			},
		},
		{
			name:           "学习类资源 - 按年级聚合",
			resourceType:   "",
			subject:        "",
			grade:          "grade 2",
			learningTopic:  "",
			learningModule: "",
			resource:       "",
			resourceDetail: "",
			expectedURL:    "/cc-second-grade",
			expectedBreadcrumb: []BreadcrumbItem{
				{Title: "Home", URL: "/"},
				{Title: "Grade 2", URL: "/cc-second-grade"},
			},
		},
		{
			name:           "学习类资源 - 有二级知识点",
			resourceType:   "worksheets",
			subject:        "math",
			grade:          "grade 1",
			learningTopic:  "geometry",
			learningModule: "basic shapes",
			resource:       "",
			resourceDetail: "",
			expectedURL:    "/math/basic-shapes-worksheets-for-cc-first-grade",
			expectedBreadcrumb: []BreadcrumbItem{
				{Title: "Home", URL: "/"},
				{Title: "Math", URL: "/math"},
				{Title: "Grade 1", URL: "/cc-first-grade"},
				{Title: "Worksheets", URL: "/worksheets"},
				{Title: "Basic Shapes", URL: "/math/basic-shapes-worksheets-for-cc-first-grade"},
			},
		},
		{
			name:           "学习类资源 - 年级和内容类型交叉聚合",
			resourceType:   "videos",
			subject:        "",
			grade:          "grade 3",
			learningTopic:  "",
			learningModule: "",
			resource:       "",
			resourceDetail: "",
			expectedURL:    "/videos-for-cc-third-grade",
			expectedBreadcrumb: []BreadcrumbItem{
				{Title: "Home", URL: "/"},
				{Title: "Grade 3", URL: "/cc-third-grade"},
				{Title: "Videos", URL: "/videos"},
			},
		},
		{
			name:           "默认资源",
			resourceType:   "",
			subject:        "",
			grade:          "",
			learningTopic:  "",
			learningModule: "",
			resource:       "",
			resourceDetail: "",
			expectedURL:    "/resources",
			expectedBreadcrumb: []BreadcrumbItem{
				{Title: "Home", URL: "/"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GenerateURLAndBreadcrumb(
				tt.resourceType, tt.subject, tt.grade, tt.learningTopic,
				tt.learningModule, tt.resource, tt.resourceDetail,
			)

			if result.URL != tt.expectedURL {
				t.Errorf("URL = %v, want %v", result.URL, tt.expectedURL)
			}

			if !reflect.DeepEqual(result.Breadcrumb, tt.expectedBreadcrumb) {
				t.Errorf("Breadcrumb = %v, want %v", result.Breadcrumb, tt.expectedBreadcrumb)
			}
		})
	}
}

func TestNormalizeGrade(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"Grade Pre-K", "cc-kindergarten"},
		{"pre-kindergarten", "cc-kindergarten"},
		{"Grade K", "cc-kindergarten"},
		{"kindergarten", "cc-kindergarten"},
		{"1", "cc-first-grade"},
		{"Grade 1", "cc-first-grade"},
		{"2", "cc-second-grade"},
		{"Grade 2", "cc-second-grade"},
		{"3", "cc-third-grade"},
		{"Grade 3", "cc-third-grade"},
		{"4", "cc-fourth-grade"},
		{"Grade 4", "cc-fourth-grade"},
		{"5", "cc-fifth-grade"},
		{"Grade 5", "cc-fifth-grade"},
		{"6", "cc-sixth-grade"},
		{"Grade 6", "cc-sixth-grade"},
		{"Grade 7", "grade-7"},
		{"", ""},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := normalizeGrade(tt.input)
			if result != tt.expected {
				t.Errorf("normalizeGrade(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestNormalizeSubject(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"English", "ela"},
		{"english", "ela"},
		{"ELA", "ela"},
		{"ela", "ela"},
		{"Math", "math"},
		{"math", "math"},
		{"Mathematics", "math"},
		{"mathematics", "math"},
		{"Science", "science"},
		{"", ""},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := normalizeSubject(tt.input)
			if result != tt.expected {
				t.Errorf("normalizeSubject(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestNormalizeResourceType(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"worksheets", "worksheets"},
		{"Worksheets", "worksheets"},
		{"coloring pages", "coloring-pages"},
		{"Coloring Pages", "coloring-pages"},
		{"videos", "videos"},
		{"Videos", "videos"},
		{"glossary", "glossary"},
		{"Glossary", "glossary"},
		{"games", "games"},
		{"", ""},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := normalizeResourceType(tt.input)
			if result != tt.expected {
				t.Errorf("normalizeResourceType(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestIsActivityResourceType(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"coloring-pages", true},
		{"worksheets", false},
		{"videos", false},
		{"games", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			generator := NewURLGenerator()
			result := generator.isActivityResource(tt.input)
			if result != tt.expected {
				t.Errorf("isActivityResourceType(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestFormatResourceTypeTitle(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"worksheets", "Worksheets"},
		{"coloring pages", "Coloring Pages"},
		{"videos", "Videos"},
		{"glossary", "Glossary"},
		{"", ""},
		{"  worksheets  ", "Worksheets"},
		{"COLORING PAGES", "Coloring Pages"},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := formatResourceTypeTitle(tt.input)
			if result != tt.expected {
				t.Errorf("formatResourceTypeTitle(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestFormatGradeTitle(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"cc-kindergarten", "Kindergarten"},
		{"cc-first-grade", "Grade 1"},
		{"cc-second-grade", "Grade 2"},
		{"cc-third-grade", "Grade 3"},
		{"cc-fourth-grade", "Grade 4"},
		{"cc-fifth-grade", "Grade 5"},
		{"cc-sixth-grade", "Grade 6"},
		{"cc-seventh-grade", "Cc-Seventh-Grade"},
		{"", ""},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := formatGradeTitle(tt.input)
			if result != tt.expected {
				t.Errorf("formatGradeTitle(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestFormatSubjectTitle(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"ela", "English"},
		{"math", "Math"},
		{"science", "Science"},
		{"", ""},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := formatSubjectTitle(tt.input)
			if result != tt.expected {
				t.Errorf("formatSubjectTitle(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestNormalizeTopic(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"animal coloring pages", "animal-coloring-pages"},
		{"Animal Coloring Pages", "animal-coloring-pages"},
		{"basic shapes", "basic-shapes"},
		{"geometry", "geometry"},
		{"", ""},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := normalizeTopic(tt.input)
			if result != tt.expected {
				t.Errorf("normalizeTopic(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestFormatTopicTitle(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"animal coloring pages", "Animal Coloring Pages"},
		{"basic shapes", "Basic Shapes"},
		{"geometry", "Geometry"},
		{"", ""},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := formatTopicTitle(tt.input)
			if result != tt.expected {
				t.Errorf("formatTopicTitle(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

// Benchmark测试
func BenchmarkGenerateURLAndBreadcrumb(b *testing.B) {
	for i := 0; i < b.N; i++ {
		GenerateURLAndBreadcrumb(
			"worksheets", "math", "grade 1", "geometry",
			"basic shapes", "", "",
		)
	}
}

func BenchmarkNormalizeGrade(b *testing.B) {
	for i := 0; i < b.N; i++ {
		normalizeGrade("Grade 1")
	}
}

func BenchmarkNormalizeSubject(b *testing.B) {
	for i := 0; i < b.N; i++ {
		normalizeSubject("Math")
	}
}
