package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	pb "hw-paas-service/api/skill/v1"
	"hw-paas-service/internal/data/services"
)

type SkillUsecase struct {
	log      *log.Helper
	services *services.Services
}

func NewSkillUsecase(logger log.Logger, services *services.Services) *SkillUsecase {
	return &SkillUsecase{
		log:      log.NewHelper(logger),
		services: services,
	}
}

func (s *SkillUsecase) GetRnList(_ context.Context, appVersion string) (reply *pb.GetRnListReply, err error) {
	bundles, err := s.services.RnVersionControl.GetRnListByAppVersion(appVersion)
	if err != nil {
		return
	}
	rnList := make([]*pb.GetRnListReplyRn, 0, len(bundles))
	for _, bundle := range bundles {
		list := make([]*pb.GetRnListReplyBundleItem, 0, len(bundle.BundleList))
		for _, b := range bundle.BundleList {
			list = append(list, &pb.GetRnListReplyBundleItem{
				BundleName:    b.BundleName,
				BundleVersion: b.BundleVersion,
				Level:         b.Level,
				Force:         b.Force,
				CheckSum:      b.Checksum,
				DownloadUrl:   b.DownloadUrl,
			})
		}
		rnList = append(rnList, &pb.GetRnListReplyRn{
			SkillName:  bundle.SkillName,
			BundleList: list,
		})
	}
	reply = &pb.GetRnListReply{
		RnList: rnList,
	}
	return
}
