package biz

import (
	"context"
	"crypto/md5"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"slices"
	"strconv"
	"strings"

	pb "hw-paas-service/api/ai/v1"
	"hw-paas-service/internal/data/model"
	"hw-paas-service/internal/pkg/custom_context"
	"hw-paas-service/internal/pkg/logger"
	"hw-paas-service/internal/pkg/sync"

	"git.100tal.com/znxx_xpp/go-libs/util"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"google.golang.org/protobuf/types/known/structpb"
)

type ContentItem struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

type AnswerOption struct {
	AOVal   string        `json:"aoVal"`
	Content []ContentItem `json:"content"`
}

type QuestionFormat struct {
	Content          []ContentItem    `json:"content"`
	Answer           [][]string       `json:"answer"`
	AnswerOptionList [][]AnswerOption `json:"answerOptionList"`
}

type Question struct {
	ID           string   `json:"id"`
	Content      string   `json:"content"`
	Images       []string `json:"images"`
	Options      []string `json:"options"`
	Answer       string   `json:"answer"`
	QuestionType int64    `json:"question_type"`
	TypeDesc     string   `json:"type_desc"`
	Grade        string   `json:"grade"`
	Analysis     string   `json:"analysis"`
	Knowledge    string   `json:"knowledge"`
	KnowledgeID  string   `json:"knowledge_id"`
	Difficulty   string   `json:"difficulty"`
	Remark       string   `json:"remark"`
	Tag1         string   `json:"tag1"`
	Tag2         string   `json:"tag2"`
}

var questionTypeMap = map[string]int64{
	"单选题":   1,
	"多选题":   2,
	"无序多选题": 2,
	"有序多选题": 3,
	"判断题":   4,
	"拖拽题":   5,
	"阅读题":   6,
	"阅读理解题": 6,
}

var supportGrade = []string{"G1", "G2", "G3", "G4", "G5", "G6"}
var excelHeader = []string{"题目序号(id)", "题目", "题目配图1", "题目配图2", "题目配图3", "选项A", "选项B", "选项C", "选项D", "选项E", "选项F", "答案", "题型", "题型描述", "题目解析", "知识点", "知识点序号", "难度", "备注", "类别A", "类别B"}

func (j *JzxQuestionUseCase) CreateQuestionImport(ctx context.Context, req *pb.CreateQuestionImportRequest) (res *structpb.Struct, err error) {
	data := &model.AiJzxImport{
		FileName: req.FileName,
		FileUrl:  req.FileUrl,
		Subject:  req.Subject,
		Status:   model.ImportStatusPending,
	}
	err = j.questionDao.CreateImport(ctx, data)
	if err != nil {
		logger.Warnf(ctx, "Failed to create question import: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("导入题目失败")
	}

	gCtx := util.NewTraceContext(nil, custom_context.GetTraceId(ctx))
	sync.Go(gCtx, j.log, func() {
		j.handleImportQuestion(gCtx, data)
	})

	return nil, nil
}

func (j *JzxQuestionUseCase) ListQuestionImport(ctx context.Context, req *pb.ListQuestionImportRequest) (res *pb.ListQuestionImportReply, err error) {
	datas, total, err := j.questionDao.ListImportWithPage(ctx, int64(req.Page), int64(req.PageSize))
	if err != nil {
		logger.Warnf(ctx, "Failed to list question import: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("查询导入数据失败")
	}

	importsData := make([]*pb.QuestionImportData, 0)
	for _, data := range datas {
		importsData = append(importsData, &pb.QuestionImportData{
			Id:         int32(data.ID),
			FileName:   data.FileName,
			FileUrl:    data.FileUrl,
			Subject:    data.Subject,
			ImportTime: data.CreatedAt.Format("2006-01-02 15:04:05"),
			Status:     int32(data.Status),
			NumSuccess: int32(data.NumSuccess),
			NumError:   int32(data.NumErr),
			NumRepeat:  int32(data.NumRepeat),
		})
	}
	return &pb.ListQuestionImportReply{
		Total: int32(total),
		List:  importsData,
	}, nil
}

func (j *JzxQuestionUseCase) QuestionImportAction(ctx context.Context, req *pb.QuestionImportActionRequest) (res *structpb.Struct, err error) {
	importData, err := j.questionDao.FindImport(ctx, int64(req.Id))
	if err != nil {
		logger.Warnf(ctx, "Failed to find question import: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("查询导入数据失败")
	}
	if importData == nil {
		return nil, pb.ErrorHwPaasUnexceptError("导入数据不存在")
	}
	if importData.Status != model.ImportStatusConfirm {
		return nil, pb.ErrorHwPaasUnexceptError("非待确认状态无法操作")
	}

	if req.Action == "cancel" {
		importData.Status = model.ImportStatusCancelled
		err = j.questionDao.UpdateImport(ctx, importData)
		if err != nil {
			logger.Warnf(ctx, "Failed to update question import: %v", err)
			return nil, pb.ErrorHwPaasUnexceptError("操作失败")
		}
	}

	if req.Action == "confirm" {
		err = j.questionCopy(ctx, importData.ID)
		if err != nil {
			logger.Warnf(ctx, "Failed to handle import question: %v", err)
			return nil, pb.ErrorHwPaasUnexceptError("操作失败")
		}
		importData.Status = model.ImportStatusCompleted
		err = j.questionDao.UpdateImport(ctx, importData)
		if err != nil {
			logger.Warnf(ctx, "Failed to update question import: %v", err)
			return nil, pb.ErrorHwPaasUnexceptError("操作失败")
		}
	}

	return nil, nil
}

func (j *JzxQuestionUseCase) GetQuestionImportDetail(ctx context.Context, req *pb.GetQuestionImportDetailRequest) (res *pb.GetQuestionImportDetailReply, err error) {
	res = &pb.GetQuestionImportDetailReply{}
	importData, err := j.questionDao.FindImport(ctx, int64(req.Id))
	if err != nil {
		logger.Warnf(ctx, "Failed to find question import: %v", err)
		return nil, pb.ErrorHwPaasUnexceptError("查询导入数据失败")
	}
	if importData == nil {
		return nil, pb.ErrorHwPaasUnexceptError("导入数据不存在")
	}

	res.Status = int32(importData.Status)
	if res.Status == model.ImportStatusFailed {
		res.FailedReason = importData.FailedReason
		return res, nil
	}

	if importData.NumRepeat > 0 && req.Page < 2 {
		repeatQuestionIdList, err := j.questionDao.GetAllRepeatQuestionIdByImportId(ctx, importData.ID)
		if err != nil {
			logger.Warnf(ctx, "Failed to get all repeat question id: %v", err)
			return nil, pb.ErrorHwPaasUnexceptError("查询导入数据失败")
		}
		res.RepeatQuestionId = repeatQuestionIdList
	}

	if importData.NumErr > 0 {
		reasonList, total, err := j.questionDao.GetAllFailedReasonByImportId(ctx, importData.ID, int64(req.Page), int64(req.PageSize))
		if err != nil {
			logger.Warnf(ctx, "Failed to get all failed reason: %v", err)
			return nil, pb.ErrorHwPaasUnexceptError("查询导入数据失败")
		}
		res.ErrorReasons = make([]*pb.ImportErrorReason, 0)
		for _, reason := range reasonList {
			res.ErrorReasons = append(res.ErrorReasons, &pb.ImportErrorReason{
				QuestionId:  int32(reason.QuestionID),
				ErrorReason: reason.FailedReason,
			})
		}
		res.TotalError = int32(total)
	}
	return res, nil
}

func (j *JzxQuestionUseCase) handleImportQuestion(ctx context.Context, importData *model.AiJzxImport) (err error) {
	successNum, errorNum, repeatNum, err := j.importQuestionFromExcel(ctx, importData.ID, importData.Subject, importData.FileUrl)
	if err != nil {
		importData.Status = model.ImportStatusFailed
		importData.FailedReason = err.Error()
	} else {
		importData.Status = model.ImportStatusCompleted
		importData.FailedReason = ""
		importData.NumSuccess = successNum
		importData.NumErr = errorNum
		importData.NumRepeat = repeatNum

		if repeatNum > 0 {
			importData.Status = model.ImportStatusConfirm
		}
	}

	if importData.Status == model.ImportStatusCompleted {
		err = j.questionCopy(ctx, importData.ID)
		if err != nil {
			logger.Warnf(ctx, "Failed to copy question: %v", err)
			return err
		}
	}

	err = j.questionDao.UpdateImport(ctx, importData)
	if err != nil {
		logger.Warnf(ctx, "Failed to update question import: %v", err)
		return err
	}

	return nil
}

func (j *JzxQuestionUseCase) importQuestionFromExcel(ctx context.Context, importId int64, subject, fileUrl string) (successNum, errorNum, repeatNum int64, err error) {
	filePath, err := j.getExcelFilePath(importId, fileUrl)
	if err != nil {
		logger.Warnf(ctx, "Failed to download excel file: %v", err)
		err = errors.New("获取导入文件失败")
		return
	}

	defer func() {
		os.Remove(filePath)
	}()

	f, err := excelize.OpenFile(filePath)
	if err != nil {
		logger.Warnf(ctx, "Failed to open excel file: %v", err)
		err = errors.New("打开导入文件失败")
		return
	}
	defer f.Close()

	sheetList := f.GetSheetList()
	if len(sheetList) == 0 {
		err = errors.New("导入的文件没有Sheet")
		return
	}
	//检查sheet和excelHeader是否一致
	for _, sheet := range sheetList {
		// 检查sheet名称是否为年级名称
		if !slices.Contains(supportGrade, sheet) {
			err = errors.New("sheet必须以年级命名，支持的年级为：" + strings.Join(supportGrade, ","))
			return
		}
		// 检查sheet是否存在excelHeader
		rows, err2 := f.GetRows(sheet)
		if err2 != nil {
			logger.Warnf(ctx, "Failed to get rows: %v", err2)
			err = errors.New("获取导入文件失败")
			return
		}
		if len(rows) == 0 {
			err = errors.New(sheet + "中没有数据，如果不需要，请删除该sheet")
			return
		}
		if len(rows[0]) < len(excelHeader)-2 { // "类别A", "类别B"列可以不填
			err = errors.New(sheet + "中列数不正确，请参考模板仔细检查！")
			return
		}
		for i, header := range rows[0] {
			if header != excelHeader[i] {
				err = errors.New(sheet + "中第" + string(rune('A'+i)) + "列的列名应该为' " + excelHeader[i] + "'，请参考模板仔细检查！")
				return
			}
		}
	}

	var allQuestions []Question
	var importDetailList []*model.AiJzxImportDetail

	var errorQuestionMap = make(map[string]string)
	// 检查内容是否正确
	for _, sheet := range sheetList {
		rows, err2 := f.GetRows(sheet)
		if err2 != nil {
			logger.Warnf(ctx, "Failed to get rows: %v", err2)
			err = errors.New("获取导入文件失败")
			return
		}
		for i := 1; i < len(rows); i++ {
			question := Question{}
			row := rows[i]
			if safeGetString(row, 0) == "" {
				continue
			}
			//检查A列是不是数字类型的题目ID
			if _, err2 := strconv.Atoi(safeGetString(row, 0)); err2 != nil {
				err = errors.New(sheet + "中A" + cast.ToString(i+1) + "题目序号应该为数字，请参考模板仔细检查！")
				return
			}
			question.ID = safeGetString(row, 0)

			//检查B列是不是题目
			if safeGetString(row, 1) == "" {
				errorQuestionMap[question.ID] = "题目内容为空"
				continue
			}
			question.Content = safeGetString(row, 1)

			// 获取图片
			images := make([]string, 0)
			for j := 2; j <= 4; j++ { // 处理3个图片列
				cell := fmt.Sprintf("%c%d", 'C'+j-2, i+1)
				pics, err := f.GetPictures(sheet, cell)
				if err == nil && len(pics) > 0 {
					// 将图片转换为base64
					base64Str := base64.StdEncoding.EncodeToString(pics[0].File)
					images = append(images, base64Str)
				}
			}
			question.Images = images

			// 安全地获取选项数组
			options := make([]string, 0, 6)
			for j := 5; j <= 10; j++ {
				cell := fmt.Sprintf("%c%d", 'F'+j-5, i+1)
				pics, err := f.GetPictures(sheet, cell)
				if err == nil && len(pics) > 0 {
					// 如果单元格包含图片，将图片转换为base64
					base64Str := base64.StdEncoding.EncodeToString(pics[0].File)
					options = append(options, base64Str)
				} else {
					// 如果没有图片，使用文本内容
					options = append(options, safeGetString(row, j))
				}
			}

			// 如果options长度为0，则跳过
			if len(options) == 0 {
				errorQuestionMap[question.ID] = "该题目缺少选项内容"
				continue
			}
			question.Options = options

			questionType := questionTypeMap[safeGetString(row, 12)]
			if questionType == 0 {
				errorQuestionMap[question.ID] = "题型不正确"
				continue
			}
			question.QuestionType = questionType

			question.Answer = safeGetString(row, 11)
			if question.Answer == "" {
				errorQuestionMap[question.ID] = "该题目缺少答案"
				continue
			}
			question.Grade = sheet
			question.TypeDesc = safeGetString(row, 13)
			question.Analysis = safeGetString(row, 14)
			question.Knowledge = safeGetString(row, 15)
			question.KnowledgeID = safeGetString(row, 16)
			question.Difficulty = safeGetString(row, 17)
			question.Remark = safeGetString(row, 18)
			question.Tag1 = safeGetString(row, 19)
			question.Tag2 = safeGetString(row, 20)

			allQuestions = append(allQuestions, question)
		}
	}

	// 使用 channel 来控制并发数
	type result struct {
		detail *model.AiJzxImportDetail
		err    error
	}

	// 创建一个带缓冲的 channel 来存储结果
	resultChan := make(chan result, len(allQuestions))
	// 创建一个 channel 来控制并发数
	semaphore := make(chan struct{}, 10)

	// 并发处理所有题目
	for _, question := range allQuestions {
		semaphore <- struct{}{} // 获取信号量
		go func(q Question) {
			defer func() { <-semaphore }() // 释放信号量
			importDetail, err := j.importQuestion(ctx, importId, subject, q)
			resultChan <- result{detail: importDetail, err: err}
		}(question)
	}

	// 收集所有结果
	for i := 0; i < len(allQuestions); i++ {
		res := <-resultChan
		if res.err != nil {
			logger.Warnf(ctx, "Failed to import question: %v", res.err)
			continue
		}
		importDetailList = append(importDetailList, res.detail)
	}

	for questionId, reason := range errorQuestionMap {
		importDetail := &model.AiJzxImportDetail{
			ImportID:     importId,
			QuestionID:   cast.ToInt64(questionId),
			Status:       model.ImportDetailStatusFailed,
			FailedReason: reason,
		}
		importDetailList = append(importDetailList, importDetail)
	}

	// 删除之前的数据
	j.questionDao.DeleteImportDetailByImportId(ctx, importId)

	err = j.questionDao.BatchCreateImportDetail(ctx, importDetailList)
	if err != nil {
		logger.Warnf(ctx, "Failed to create import detail: %v", err)
		return successNum, errorNum, repeatNum, errors.New("导入题目失败")
	}

	for _, importDetail := range importDetailList {
		if importDetail.Status == model.ImportDetailStatusSuccess {
			successNum++
		} else if importDetail.Status == model.ImportDetailStatusFailed {
			errorNum++
		} else if importDetail.Status == model.ImportDetailStatusRepeat {
			repeatNum++
		}
	}

	return successNum, errorNum, repeatNum, nil
}

// 将EXCEL文件下载到本地,并命名为id.xlsx，如果本地存在则不下载，返回本地文件路径
func (j *JzxQuestionUseCase) getExcelFilePath(id int64, fileUrl string) (filePath string, err error) {
	filePath = fmt.Sprintf("%d.xlsx", id)
	if _, err := os.Stat(filePath); err == nil {
		return filePath, nil
	}

	response, err := http.Get(fileUrl)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to download file: %s", response.Status)
	}

	file, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	_, err = io.Copy(file, response.Body)
	if err != nil {
		return "", err
	}

	return filePath, nil
}

// 安全获取数组元素，如果索引超出范围则返回空字符串
func safeGetString(arr []string, index int) string {
	if index < 0 || index >= len(arr) {
		return ""
	}
	// 去除空格
	return strings.TrimSpace(arr[index])
}

func (j *JzxQuestionUseCase) importQuestion(ctx context.Context, importID int64, subject string, q Question) (importDetail *model.AiJzxImportDetail, err error) {
	importDetail = &model.AiJzxImportDetail{
		ImportID:   importID,
		QuestionID: cast.ToInt64(q.ID),
		Status:     model.ImportDetailStatusSuccess,
	}

	question, err := j.questionDao.Find(ctx, cast.ToInt64(q.ID))
	if err != nil {
		logger.Warnf(ctx, "Failed to find question: %v", err)
		importDetail.Status = model.ImportDetailStatusFailed
		importDetail.FailedReason = "查询题目失败，请重新导入"
		return importDetail, err
	}

	//检查题目是否存在,如果存在则检查是否绑定
	if question != nil && question.ID > 0 {
		isBind, err := j.services.BadouSvc.CheckIsBind(ctx, question.ID)
		if err != nil {
			logger.Warnf(ctx, "Failed to check is bind: %v", err)
			importDetail.Status = model.ImportDetailStatusFailed
			importDetail.FailedReason = "检查题目是否绑定失败，请重新导入"
			return importDetail, err
		}
		if isBind {
			importDetail.Status = model.ImportDetailStatusFailed
			importDetail.FailedReason = "该题目已绑定，请解绑后重新导入"
			return importDetail, nil
		}

		importDetail.Status = model.ImportDetailStatusRepeat
	}

	// 构建 content 数组
	content := []ContentItem{
		{
			Type:    "text",
			Content: q.Content,
		},
	}

	// 处理图片
	for _, imgBase64 := range q.Images {
		imgURL, err := j.uploadImageRetry(ctx, imgBase64, 5)
		if err != nil || imgURL == "" {
			logger.Warnf(ctx, "Failed to upload image: %v", err)
			importDetail.Status = model.ImportDetailStatusFailed
			importDetail.FailedReason = "该题目上传图片失败，请重新导入"
			return importDetail, err
		}
		content = append(content, ContentItem{
			Type:    "image",
			Content: imgURL,
		})
	}

	// 构建答案选项列表
	var answerOptions [][]AnswerOption
	optionLetters := []string{"A", "B", "C", "D", "E", "F"}
	for i, opt := range q.Options {
		if opt != "" {
			optionType := "text"
			//如果opt以http开头，则认为是音频
			if strings.HasPrefix(opt, "https") {
				optionType = "audio"
			} else if len(opt) > 500 {
				optionType = "image"
				url, err := j.uploadImageRetry(ctx, opt, 5)
				if err != nil {
					logger.Warnf(ctx, "Failed to upload image: %v", err)
					importDetail.Status = model.ImportDetailStatusFailed
					importDetail.FailedReason = "该题目上传图片失败，请重新导入"
					return importDetail, err
				}
				opt = url
			}
			answerOptions = append(answerOptions, []AnswerOption{
				{
					AOVal: optionLetters[i],
					Content: []ContentItem{
						{
							Type:    optionType,
							Content: opt,
						},
					},
				},
			})
		}
	}

	//构建类别AB-tag
	if q.Tag1 != "" {
		content = append(content, ContentItem{
			Type:    "tag",
			Content: q.Tag1,
		})
	}
	if q.Tag2 != "" {
		content = append(content, ContentItem{
			Type:    "tag",
			Content: q.Tag2,
		})
	}

	// 构建最终的问题格式
	questionFormat := QuestionFormat{
		Content:          content,
		AnswerOptionList: answerOptions,
	}

	for _, answer := range strings.Split(q.Answer, "|") {
		if answer == "" {
			continue
		}
		questionFormat.Answer = append(questionFormat.Answer, strings.Split(answer, ","))
	}

	// 将结果转换为JSON
	jsonData, err := json.Marshal(questionFormat)
	if err != nil {
		logger.Warnf(ctx, "Failed to marshal question format: %v", err)
		importDetail.Status = model.ImportDetailStatusFailed
		importDetail.FailedReason = "该题目转换为JSON失败，请重新导入"
		return importDetail, err
	}

	// 题目存在则修改
	if question != nil && question.ID > 0 {
		question.QType = q.QuestionType
		question.Grade = q.Grade
		question.Subject = subject
		question.Difficulty = cast.ToInt64(q.Difficulty)
		question.Question = string(jsonData)
		question.Knowledge = q.Knowledge
		question.KnowledgeNo = q.KnowledgeID
		question.Note = q.Remark
	} else {
		question = &model.AiJzxQuestion{
			ID:          cast.ToInt64(q.ID),
			QType:       q.QuestionType,
			Grade:       q.Grade,
			Subject:     subject,
			Difficulty:  cast.ToInt64(q.Difficulty),
			Question:    string(jsonData),
			Answer:      "",
			Solution:    q.Analysis,
			Knowledge:   q.Knowledge,
			KnowledgeNo: q.KnowledgeID,
			Note:        q.Remark,
			Status:      1,
		}
	}

	questionDetail, err := json.Marshal(question)
	if err != nil {
		logger.Warnf(ctx, "Failed to marshal question: %v", err)
		importDetail.Status = model.ImportDetailStatusFailed
		importDetail.FailedReason = "该题目转换为JSON失败，请重新导入"
		return importDetail, err
	}

	importDetail.Detail = string(questionDetail)

	return importDetail, nil
}

func (j *JzxQuestionUseCase) uploadImageRetry(ctx context.Context, image string, retryTimes int) (imgURL string, err error) {
	for retryTimes > 0 {
		file, err := base64.StdEncoding.DecodeString(image)
		if err != nil {
			logger.Errorf(ctx, "Failed to decode file: %v", err)
			return "", err
		}
		// 生成随机数的 MD5 值作为文件名
		randomBytes := make([]byte, 16)
		if _, err := io.ReadFull(rand.Reader, randomBytes); err != nil {
			logger.Errorf(ctx, "Failed to generate random bytes: %v", err)
			return "", err
		}
		fileName := fmt.Sprintf("%x.png", md5.Sum(randomBytes))
		imgURL, err = j.services.OssSvc.Upload2Oss(ctx, file, fileName)
		if err == nil {
			break
		}
		retryTimes--
	}
	return
}

func (j *JzxQuestionUseCase) questionCopy(ctx context.Context, importId int64) (err error) {
	var importDetailList []*model.AiJzxImportDetail

	page := int64(1)
	for {
		list, _, err := j.questionDao.ListImportDetailWithPage(ctx, importId, page, 200)
		if err != nil {
			logger.Warnf(ctx, "Failed to list import detail: %v", err)
			return err
		}
		if len(list) == 0 {
			break
		}
		importDetailList = append(importDetailList, list...)
		page++
	}

	for _, importDetail := range importDetailList {
		if importDetail.Status == model.ImportDetailStatusFailed {
			continue
		}

		var questionDetail model.AiJzxQuestion
		err = json.Unmarshal([]byte(importDetail.Detail), &questionDetail)
		if err != nil {
			logger.Warnf(ctx, "Failed to unmarshal question detail: %v", err)
			continue
		}

		if importDetail.Status == model.ImportDetailStatusSuccess {
			err = j.questionDao.Create(ctx, &questionDetail)
			if err != nil {
				return err
			}
			continue
		}

		err = j.questionDao.UpdateWithKey(ctx, questionDetail.ID, map[string]interface{}{
			"q_type":       questionDetail.QType,
			"grade":        questionDetail.Grade,
			"subject":      questionDetail.Subject,
			"difficulty":   questionDetail.Difficulty,
			"question":     questionDetail.Question,
			"answer":       questionDetail.Answer,
			"solution":     questionDetail.Solution,
			"knowledge":    questionDetail.Knowledge,
			"knowledge_no": questionDetail.KnowledgeNo,
			"note":         questionDetail.Note,
		})
		if err != nil {
			logger.Warnf(ctx, "Failed to update question: %v", err)
			return err
		}
	}
	return nil
}
