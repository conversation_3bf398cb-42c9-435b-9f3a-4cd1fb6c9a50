package biz

import (
	"context"
	"git.100tal.com/znxx_xpp/go-libs/util"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
	ai_pb "hw-paas-service/api/ai/v1"
	pb "hw-paas-service/api/query_words/v1"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"math"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
)

type QueryWordsUseCase struct {
	log     *log.Helper
	confBiz *conf.Biz
	esDao   dao.ES
}

func NewQueryWordsUseCase(esDao dao.ES, confBiz *conf.Biz, logger log.Logger) *QueryWordsUseCase {
	return &QueryWordsUseCase{
		esDao:   esDao,
		confBiz: confBiz,
		log:     log.New<PERSON>elper(logger),
	}
}

type SearchWhereTypes struct {
	Word  string                  `json:"word"`
	Query *pb.QueryWordsListQuery `json:"query"`
}

func (fw *QueryWordsUseCase) List(ctx context.Context, req *pb.QueryWordsListRequest) (*pb.QueryWordsListReply, error) {
	var queryWhere *SearchWhereTypes

	if req.Word != "" {
		req.Query = nil
		word := strings.ToLower(req.Word)
		doc, err := fw.getDetail(ctx, word)
		if err != nil {
			return nil, err
		}

		queryWhere = &SearchWhereTypes{
			Query: &pb.QueryWordsListQuery{
				British:  doc.British,
				American: doc.American,
			},
		}
	}

	if req.Query != nil {
		queryWhere = &SearchWhereTypes{
			Query: req.Query,
		}
	}

	total, esRes, err := fw.esDao.Search(ctx, fw.confBiz.EnWordIndex, fw.getListSearchWhere(queryWhere), 1, 5)

	if err != nil {
		return nil, ai_pb.ErrorHwPaasUnexceptError("es查询错误")
	}
	if len(esRes) == 0 {
		return nil, ai_pb.ErrorHwPaasNotFoundError("未找到单词")
	}

	reply := &pb.QueryWordsListReply{
		Total: int32(total),
	}

	list := make([]*pb.QueryWordsListItem, 0)
	for _, re := range esRes {
		doc := &model.EnWordItem{}
		err = jsoniter.Unmarshal(re.Source, doc)
		if err != nil || doc.Word == "" {
			continue
		}
		if doc.Status == model.EnWordStatusCurse {
			reply.Total = 0
			reply.List = list
			reply.Curse = true
			return reply, nil
		}
		if doc.VideoUrl != "" {
			doc.Speech = util.AesEncrypt(doc.VideoUrl, fw.confBiz.AesKey)
			doc.VideoUrl = ""
		}

		item := &pb.QueryWordsListItem{
			Score: float32(re.Score),
		}
		docByte, err := jsoniter.Marshal(doc)
		if err != nil {
			continue
		}

		st := &structpb.Struct{}
		_ = protojson.Unmarshal(docByte, st)
		item.Detail = st

		list = append(list, item)
	}
	reply.List = list

	return reply, nil
}

func (fw *QueryWordsUseCase) getDetail(ctx context.Context, word string) (*model.EnWordItem, error) {
	_, esRes, err := fw.esDao.Search(ctx, fw.confBiz.EnWordIndex, fw.getDetailSearchWhere(word), 1, 1)
	if err != nil {
		return nil, ai_pb.ErrorHwPaasUnexceptError("es查询错误")
	}
	if len(esRes) == 0 {
		return nil, ai_pb.ErrorHwPaasNotFoundError("未找到单词")
	}

	doc := &model.EnWordItem{}
	_ = jsoniter.Unmarshal(esRes[0].Source, doc)

	if doc.Word == "" {
		return nil, ai_pb.ErrorHwPaasNotFoundError("未找到单词")
	}
	return doc, nil
}

func (fw *QueryWordsUseCase) getDetailSearchWhere(word string) *elastic.BoolQuery {
	boolQuery := elastic.NewBoolQuery()

	queryOptions := make([]elastic.Query, 0, 8)

	queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
		elastic.NewTermsQuery("status", model.EnWordStatusShelf, model.EnWordStatusCurse),
	}...))

	if word != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewTermQuery("word", word),
		}...))
	}

	return boolQuery.Must(
		queryOptions...,
	)
}

func (fw *QueryWordsUseCase) getListSearchWhere(req *SearchWhereTypes) *elastic.BoolQuery {
	boolQuery := elastic.NewBoolQuery()

	queryOptions := make([]elastic.Query, 0, 8)

	queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
		elastic.NewTermsQuery("status", model.EnWordStatusShelf, model.EnWordStatusCurse),
	}...))

	if req.Word != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewTermQuery("word", req.Word),
		}...))
	}

	if req.Query == nil {
		return boolQuery.Must(
			queryOptions...,
		)
	}

	if req.Query.Word != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewMatchQuery("word", req.Query.Word),
		}...))
	}

	// Add Should clause for British and American
	shouldQueries := make([]elastic.Query, 0, 2)
	if req.Query.British != "" {
		shouldQueries = append(shouldQueries, elastic.NewTermQuery("british.keyword", req.Query.British))
	}
	if req.Query.American != "" {
		shouldQueries = append(shouldQueries, elastic.NewTermQuery("american.keyword", req.Query.American))
	}
	if len(shouldQueries) > 0 {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Should(shouldQueries...).MinimumShouldMatch("1"))
	}

	if req.Query.Synonym != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewMatchQuery("synonyms", req.Query.Synonym),
		}...))
	}
	if req.Query.Antonym != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewMatchQuery("antonyms", req.Query.Antonym),
		}...))
	}
	if req.Query.Inflection != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewMatchQuery("inflections", req.Query.Inflection),
		}...))
	}
	if req.Query.Prefix != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewMatchQuery("prefix", req.Query.Prefix),
		}...))
	}
	if req.Query.Suffix != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewMatchQuery("suffix", req.Query.Suffix),
		}...))
	}
	if req.Query.Phrase != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewMatchQuery("phrases", req.Query.Phrase),
		}...))
	}
	if req.Query.Meaning != "" {
		minimum := cast.ToString(math.Round(0.3 * float64(len(strings.Split(req.Query.Meaning, " ")))))
		nestedQuery := elastic.NewNestedQuery("meanings",
			elastic.NewMatchQuery("meanings.definition", req.Query.Meaning).Operator("or").MinimumShouldMatch(minimum).Boost(2),
		)
		queryOptions = append(queryOptions, nestedQuery)
	}

	if req.Query.Sentence != "" {
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewMatchQuery("sentences", req.Query.Sentence),
		}...))
	}

	return boolQuery.Must(
		queryOptions...,
	)
}
