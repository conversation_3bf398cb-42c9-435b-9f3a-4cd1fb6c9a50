package biz

import (
	"context"
	"encoding/base64"
	ai_pb "hw-paas-service/api/ai/v1"
	pb "hw-paas-service/api/finger_words/v1"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/internal/data/services/en_correct"
	"hw-paas-service/internal/data/services/hybrid_ocr"
	"hw-paas-service/internal/data/services/llm"
	"hw-paas-service/internal/data/services/safety"
	"hw-paas-service/internal/pkg/custom_context"
	sync_util "hw-paas-service/internal/pkg/sync"
	"hw-paas-service/internal/pkg/utils"
	"strings"
	"sync"
	"unicode"

	"git.100tal.com/znxx_xpp/go-libs/util"
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"github.com/olivere/elastic/v7"
)

type FingerWordsUseCase struct {
	log      *log.Helper
	confBiz  *conf.Biz
	services *services.Services
	fwDao    *dao.FingerWordsDao
	esDao    dao.ES
}

func NewFingerWordsUseCase(esDao dao.ES, fwDao *dao.FingerWordsDao, services *services.Services, confBiz *conf.Biz, logger log.Logger) *FingerWordsUseCase {
	return &FingerWordsUseCase{
		esDao:    esDao,
		fwDao:    fwDao,
		services: services,
		confBiz:  confBiz,
		log:      log.NewHelper(logger),
	}
}

type EntryResult struct {
	Pos   int32    `json:"pos"`
	Words []string `json:"words"`

	Detail                    map[string]*model.EnWordItem `json:"detail"`
	IsMainWordContainsChinese bool                         `json:"is_main_word_contains_chinese"`
}

// 打印指查链路日志
type traceResult struct {
	TraceID         string                           `json:"trace_id"`          //traceID
	UserId          string                           `json:"user_id"`           //用户ID
	DeviceId        string                           `json:"device_id"`         //设备ID
	OcrImageUrl     string                           `json:"ocr_image_url"`     //ocr图片
	OcrReq          *hybrid_ocr.HybridOcrReq         `json:"ocr_req"`           //ocr请求
	OcrResp         *hybrid_ocr.HybridOcrData        `json:"ocr_resp"`          //ocr响应结果
	EnCorrectReq    *en_correct.EnCorrectReplaceReq  `json:"en_correct_req"`    //纠错请求
	EnCorrectRes    *en_correct.EnCorrectReplaceData `json:"en_correct_res"`    //纠错响应结果
	EnCorrectSwitch int32                            `json:"en_correct_switch"` //纠错开关
	Detail          *model.EnWordItem                `json:"detail"`            //单词详情
	Details         map[string]*model.EnWordItem     `json:"details"`           //单词详情
}

// Entry 指查入口
func (fw *FingerWordsUseCase) Entry(ctx context.Context, req *pb.FingerWordsEntryRequest) (*pb.FingerWordsEntryReply, error) {
	//打印链路日志
	traceId := custom_context.GetTraceId(ctx)
	traceLog := &traceResult{
		TraceID:         traceId,
		UserId:          custom_context.GetJwtUserId(ctx),
		DeviceId:        custom_context.GetDeviceId(ctx),
		EnCorrectSwitch: fw.confBiz.EnCorrectSwitch,
	}
	defer func() {
		gCtx := util.NewTraceContext(nil, traceId)
		sync_util.Go(gCtx, fw.log, func() {
			//去掉base64图片数据,新增ocr图片url
			base64B, _ := base64.StdEncoding.DecodeString(req.TextBmp)
			traceLog.OcrImageUrl, _ = fw.services.OssSvc.Upload2Oss(gCtx, base64B, traceLog.TraceID+".jpg")
			traceLog.OcrReq.TextBmp = ""
			traceB, _ := jsoniter.Marshal(traceLog)
			fw.log.WithContext(gCtx).Info(common.MakeLogBackFlowMsgInfo(traceId, "finger_words", "dw_entry_trace", string(traceB)))
			return
		})
	}()

	//ocr
	hybridOcrReq := hybrid_ocr.HybridOcrReq{
		ClientTraceID: req.ClientTraceId,
		TextBmp:       req.TextBmp,
		FingerPos2Ma:  req.FingerPos_2Ma,
		TextPos2Ma:    req.TextPos_2Ma,
		RetryStatus:   req.RetryStatus,
		Version:       req.Version,
	}
	traceLog.OcrReq = &hybridOcrReq
	ocrRes, err := fw.services.HOSvc.HybridOcr(ctx, &hybridOcrReq)
	traceLog.OcrResp = ocrRes
	if err != nil {
		return nil, ai_pb.ErrorHwPaasFingerOcrError("ocr错误")
	}
	if ocrRes == nil || ocrRes.NearWord == "" {
		return nil, ai_pb.ErrorHwPaasFingerOcrError("ocr未获取到数据")
	}
	pos, word, originWords, lowWords, leftPos, rightPos, leftWord, rightWord, mainWordContainsCn := fw.getNearWords(ocrRes.NearWord, ocrRes.NearIndex, ocrRes.LineWordList)
	res := EntryResult{
		Pos:                       int32(pos),
		Words:                     originWords,
		Detail:                    make(map[string]*model.EnWordItem),
		IsMainWordContainsChinese: mainWordContainsCn,
	}
	fw.log.WithContext(ctx).Infof("lowWords:%v,word:%s,originWords:%v", lowWords, word, originWords)
	//指查单词db查询
	docs, _ := fw.getDetailFromDb(ctx, lowWords)
	if _, ok := docs[word]; !ok && fw.confBiz.EnCorrectSwitch == 1 {
		//指查单词es未找到,纠错
		enNearWords := en_correct.EnCorrectReplaceNearWord{
			Key: word,
			Pos: en_correct.EnCorrectReplaceNearWordPos{
				LeftPos:  leftPos,
				RightPos: rightPos,
			},
			Words: en_correct.EnCorrectReplaceNearWordWords{
				LeftWord:  leftWord,
				RightWord: rightWord,
			},
		}
		enCorrectReq := en_correct.EnCorrectReplaceReq{
			OcrAlllineInfo:   ocrRes.LineWords[0],
			OcrKeywordInfo:   word,
			OcrKeyInfoOffset: int(ocrRes.NearIndex),
			TraceID:          req.ClientTraceId,
			NearWords:        enNearWords,
		}
		correctRes, _ := fw.services.EnCRSvc.EnCorrectReplace(ctx, &enCorrectReq)
		if correctRes != nil {
			words := make([]string, 0)
			if correctRes.NearWords.Words.LeftWord != "" {
				words = append(words, correctRes.NearWords.Words.LeftWord)
			}
			if correctRes.NearWords.Key != "" {
				words = append(words, correctRes.NearWords.Key)
				word = correctRes.NearWords.Key // 更新word为纠正后的单词
			}
			if correctRes.NearWords.Words.RightWord != "" {
				words = append(words, correctRes.NearWords.Words.RightWord)
			}
			if len(words) > 0 {
				originWords = words
				res.Words = words
				docs, _ = fw.getDetailFromDb(ctx, words)
			}
		}
		traceLog.EnCorrectReq = &enCorrectReq
		traceLog.EnCorrectRes = correctRes
	}
	if len(docs) > 0 {
		//给原始word也赋值
		for i, v := range originWords {
			if enWordItem, ok := docs[v]; ok {
				docs[strings.ToLower(originWords[i])] = enWordItem
			}
		}

		res.Detail = docs
	} else {
		fw.log.WithContext(ctx).Warnf("dw_entry_word_not_found:%s", word)
	}
	llmWordReq := []string{}
	for _, v := range originWords {
		if IsContainsCnOrNotEnglish(v) {
			continue
		}

		if _, ok := res.Detail[strings.ToLower(v)]; !ok {
			llmWordReq = append(llmWordReq, v)
		}
	}
	fw.log.WithContext(ctx).Infof("llmWordReq:%v", llmWordReq)
	if len(llmWordReq) > 0 {
		hwEnWordCreate := make([]*model.HwEnWord, 0)
		llmItems, err := fw.EntryFromGpt(ctx, llmWordReq)
		if err != nil {
			fw.log.WithContext(ctx).Errorf("EntryFromGpt:%s", err.Error())
			return nil, err
		}
		for k, v := range llmItems {
			inflections, _ := jsoniter.Marshal(v.Inflections)
			synonyms, _ := jsoniter.Marshal(v.Synonyms)
			antonyms, _ := jsoniter.Marshal(v.Antonyms)
			sentences, _ := jsoniter.Marshal(v.Sentences)
			phrases, _ := jsoniter.Marshal(v.Phrases)
			meanings, _ := jsoniter.Marshal(v.Meanings)
			hwEnWordCreate = append(hwEnWordCreate, &model.HwEnWord{
				Word:        k,
				British:     v.British,
				American:    v.American,
				Inflections: string(inflections),
				Prefix:      v.Prefix,
				Suffix:      v.Suffix,
				Phrases:     string(phrases),
				Meanings:    string(meanings),
				Synonyms:    string(synonyms),
				Antonyms:    string(antonyms),
				Sentences:   string(sentences),
				Status:      model.EnWordStatusShelf,
			})
			v.Frequency = 1
			res.Detail[k] = v
		}

		if len(hwEnWordCreate) > 0 {
			err = fw.fwDao.BatchCreate(ctx, hwEnWordCreate)
			if err != nil {
				fw.log.WithContext(ctx).Errorf("BatchCreate:%s", err.Error())
			}
		}
	}

	for _, theWord := range res.Words {
		if enWordItem, ok := res.Detail[strings.ToLower(theWord)]; ok {
			res.Detail[theWord] = enWordItem
		}
	}

	traceLog.Details = res.Detail
	for k, v := range res.Detail {
		if strings.EqualFold(k, word) {
			traceLog.Detail = v
			break
		}
	}

	// 更新单词频率
	err = fw.fwDao.IncrementHwEnWordsFrequency(ctx, originWords)
	if err != nil {
		fw.log.WithContext(ctx).Errorf("IncrementHwEnWordsFrequency:%s", err.Error())
	}

	result, _ := utils.FormatStructPb(res)

	data := &pb.FingerWordsEntryReply{
		NearWord:    ocrRes.NearWord,
		LineWords:   ocrRes.LineWords,
		NearIndex:   ocrRes.NearIndex,
		Ocr:         ocrRes.Ocr,
		RetryStatus: ocrRes.RetryStatus,
		Result:      result,
	}
	return data, nil
}

// 判断word是否包含中文, 或者不是英文单词(只用简单的规则判断)
func IsContainsCnOrNotEnglish(word string) bool {
	if IsContainsCn(word) {
		return true
	}

	if len(word) == 0 {
		return true
	}

	if !unicode.IsLetter(rune(word[0])) {
		return true
	}

	for _, w := range word {
		if unicode.IsLetter(w) {
			return false
		}
	}
	return true
}

func IsContainsCn(word string) bool {
	for _, w := range []rune(word) {
		if unicode.Is(unicode.Han, w) {
			return true
		}
	}
	return false
}

// Query 指查单词快服务卡片详情
func (fw *FingerWordsUseCase) Query(ctx context.Context, req *pb.FingerWordsQueryRequest) (*pb.FingerWordsQueryReply, error) {
	//打印链路日志
	traceId := custom_context.GetTraceId(ctx)
	traceLog := &traceResult{
		TraceID:  traceId,
		UserId:   custom_context.GetJwtUserId(ctx),
		DeviceId: custom_context.GetDeviceId(ctx),
	}
	defer func() {
		gCtx := util.NewTraceContext(nil, traceId)
		sync_util.Go(gCtx, fw.log, func() {
			traceB, _ := jsoniter.Marshal(traceLog)
			fw.log.WithContext(gCtx).Info(common.MakeLogBackFlowMsgInfo(traceId, "finger_word_query", "dw_query_trace", string(traceB)))
			return
		})
	}()

	//指查单词es查询
	word := strings.ToLower(req.Word)
	docs, err := fw.getDetail(ctx, []string{word})
	if err != nil {
		return nil, err
	}
	traceLog.Detail = docs[word]

	detail, _ := utils.FormatStructPb(docs[word])

	data := &pb.FingerWordsQueryReply{
		Detail: detail,
	}
	return data, nil
}

func (fw *FingerWordsUseCase) getDetailFromDb(ctx context.Context, words []string) (map[string]*model.EnWordItem, error) {
	hwEnWordList, err := fw.fwDao.FindHwEnWords(ctx, words)
	if err != nil {
		return nil, err
	}
	docs := make(map[string]*model.EnWordItem)
	for _, hwEnWord := range hwEnWordList {
		inflections := make([]string, 0)
		synonyms := make([]string, 0)
		antonyms := make([]string, 0)
		sentences := make([]string, 0)
		phrases := make([]string, 0)
		meanings := make([]model.EnWordMeaning, 0)
		_ = jsoniter.Unmarshal([]byte(hwEnWord.Inflections), &inflections)
		_ = jsoniter.Unmarshal([]byte(hwEnWord.Synonyms), &synonyms)
		_ = jsoniter.Unmarshal([]byte(hwEnWord.Antonyms), &antonyms)
		_ = jsoniter.Unmarshal([]byte(hwEnWord.Sentences), &sentences)
		_ = jsoniter.Unmarshal([]byte(hwEnWord.Phrases), &phrases)
		_ = jsoniter.Unmarshal([]byte(hwEnWord.Meanings), &meanings)

		doc := &model.EnWordItem{}
		doc.Id = hwEnWord.ID
		doc.Word = hwEnWord.Word
		doc.British = hwEnWord.British
		doc.American = hwEnWord.American
		doc.Inflections = inflections
		doc.Prefix = hwEnWord.Prefix
		doc.Suffix = hwEnWord.Suffix
		doc.Phrases = phrases
		doc.Meanings = meanings
		doc.Synonyms = synonyms
		doc.Antonyms = antonyms
		doc.Sentences = sentences
		doc.Frequency = hwEnWord.Frequency
		doc.Status = hwEnWord.Status
		if doc.VideoUrl != "" {
			doc.Speech = util.AesEncrypt(doc.VideoUrl, fw.confBiz.AesKey)
			doc.VideoUrl = ""
		}

		docs[strings.ToLower(doc.Word)] = doc
	}
	if len(docs) == 0 {
		return nil, ai_pb.ErrorHwPaasNotFoundError("未找到单词")
	}
	return docs, nil
}

func (fw *FingerWordsUseCase) getDetail(ctx context.Context, words []string) (map[string]*model.EnWordItem, error) {
	_, esRes, err := fw.esDao.Search(ctx, fw.confBiz.EnWordIndex, fw.getDetailSearchWhere(words), 1, 10)
	if err != nil {
		return nil, ai_pb.ErrorHwPaasUnexceptError("es查询错误")
	}
	if len(esRes) == 0 {
		return nil, ai_pb.ErrorHwPaasNotFoundError("未找到单词")
	}

	docs := make(map[string]*model.EnWordItem)
	for _, re := range esRes {
		doc := &model.EnWordItem{}
		err = jsoniter.Unmarshal(re.Source, doc)
		if err != nil || doc.Word == "" {
			continue
		}
		if doc.VideoUrl != "" {
			doc.Speech = util.AesEncrypt(doc.VideoUrl, fw.confBiz.AesKey)
			doc.VideoUrl = ""
		}
		docs[doc.Word] = doc
	}
	if len(docs) == 0 {
		return nil, ai_pb.ErrorHwPaasNotFoundError("未找到单词")
	}
	return docs, nil
}

func (fw *FingerWordsUseCase) getDetailSearchWhere(words []string) *elastic.BoolQuery {
	boolQuery := elastic.NewBoolQuery()

	queryOptions := make([]elastic.Query, 0, 8)

	//返回上架和审核失败的单词
	queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
		elastic.NewTermsQuery("status", model.EnWordStatusShelf, model.EnWordStatusCurse),
	}...))
	if len(words) > 0 {
		_words := make([]interface{}, len(words))
		for i, v := range words {
			_words[i] = v
		}
		queryOptions = append(queryOptions, elastic.NewBoolQuery().Must([]elastic.Query{
			elastic.NewTermsQuery("word.keyword", _words...),
		}...))
	}

	return boolQuery.Must(
		queryOptions...,
	)
}

// 处理ocr行数据，返回邻近词
func (fw *FingerWordsUseCase) getNearWords(nearWord string, nearIndex int32, lineWordList []string) (pos int, word string, originWords, lowWords []string, leftPos, rightPos int, leftWord, rightWord string, mainWordContainsCn bool) {
	// 编译正则表达式（考虑将此作为常量以避免重复编译）
	//re := regexp.MustCompile(`[^\p{L}\s]+`)
	// 使用正则表达式替换所有非字母和非空白字符
	//lineWordStr = re.ReplaceAllString(lineWordStr, "")

	// 将字符串转换为小写并分割成单词
	lowNearWord := strings.ToLower(nearWord)

	// 查找指定单词的索引位置
	pos = -1
	if nearIndex >= 0 {
		pos = int(nearIndex)
	}

	if pos == -1 {
		return
	}

	word = lowNearWord
	lowWords = make([]string, 0, 3)    // 预分配容量为3的小写切片
	originWords = make([]string, 0, 3) // 预分配容量为3的原始切片
	l := len(lineWordList)
	if pos > 0 {
		leftPos = pos - 1
		leftWord = lineWordList[leftPos]
		if !IsContainsCnOrNotEnglish(leftWord) {
			lowWords = append(lowWords, strings.ToLower(leftWord))
			originWords = append(originWords, leftWord)
		}
	} else {
		leftPos = -1
	}
	if IsContainsCnOrNotEnglish(word) {
		mainWordContainsCn = true
		return
	}
	lowWords = append(lowWords, word)
	originWords = append(originWords, nearWord)
	if pos < l-1 {
		rightPos = pos + 1
		rightWord = lineWordList[rightPos]
		if !IsContainsCnOrNotEnglish(rightWord) {
			lowWords = append(lowWords, strings.ToLower(rightWord))
			originWords = append(originWords, rightWord)
		}
	} else {
		rightPos = -1
	}

	//去除words中的重复单词
	lowWords = removeDuplicateWord(lowWords)
	originWords = removeDuplicateWord(originWords)

	// 重置 pos 为指定单词在 words 切片中的索引
	for i, v := range lowWords {
		if v == word {
			pos = i
			break
		}
	}
	return
}

// 去除切片中的重复单词
func removeDuplicateWord(words []string) []string {
	wordMap := make(map[string]struct{})
	var newWords []string
	for _, word := range words {
		if _, ok := wordMap[word]; !ok {
			newWords = append(newWords, word)
			wordMap[word] = struct{}{}
		}
	}
	return newWords
}

func (fw *FingerWordsUseCase) EntryFromGpt(ctx context.Context, words []string) (map[string]*model.EnWordItem, error) {
	var wg sync.WaitGroup
	var m = make(map[string]*model.EnWordItem, len(words))
	wg.Add(len(words))
	for _, v := range words {
		v := v
		sync_util.Go(ctx, fw.log, func() {
			defer wg.Done()
			item := &model.EnWordItem{}
			res, err := fw.services.LlmSvc.Send(ctx, v)
			if err != nil {
				fw.log.WithContext(ctx).Errorf("send gpt error: %v", err)
				item.Msg = "Oops! We couldn't find that word.We'll add it as soon as possible!"
			}
			fw.log.WithContext(ctx).Infof("EntryFromGpt res: %s", res)
			isSafe, _, _ := fw.services.SafetySvc.CheckContentSafety(ctx, &safety.TextReq{Text: res})
			if !isSafe {
				item.Msg = "Oops! This word might not be the best choice. Let's find a kinder way to express ourselves!"
				fw.log.WithContext(ctx).Infof("isSafe: %v, word: %s, res: %s", isSafe, v, res)
			} else {
				item, err = fw.ParseStr(ctx, v, res)
				if err != nil {
					fw.log.WithContext(ctx).Errorf("parse str error: %v", err)
					item.Msg = "Oops! We couldn't find that word.We'll add it as soon as possible!"
				}
			}
			m[v] = item
		})
	}
	wg.Wait()
	return m, nil
}
func (fw *FingerWordsUseCase) ParseStr(ctx context.Context, word, str string) (*model.EnWordItem, error) {
	var content llm.Gpt4oContent
	err := jsoniter.Unmarshal([]byte(str), &content)
	if err != nil {
		fw.log.WithContext(ctx).Errorf("parse str error: %v, word: %s, str: %s", err, word, str)
		return nil, err
	}
	// 写入DB
	var british, american, prefix, suffix string
	british = content.Pronunciation.British
	american = content.Pronunciation.American
	prefix = content.RootVariations.Prefix
	suffix = content.RootVariations.Suffix

	meaningsStr, _ := jsoniter.Marshal(content.Meanings)
	meanings := make([]model.EnWordMeaning, 0)
	_ = jsoniter.Unmarshal([]byte(meaningsStr), &meanings)

	synonyms := make([]string, 0)
	for _, synonym := range content.Synonyms {
		if synonym != "" {
			synonyms = append(synonyms, synonym)
		}
	}
	antonyms := make([]string, 0)
	for _, antonym := range content.Antonyms {
		if antonym != "" {
			antonyms = append(antonyms, antonym)
		}
	}
	sentences := make([]string, 0)
	for _, sentence := range content.Sentences {
		if sentence != "" {
			sentences = append(sentences, sentence)
		}
	}
	inflections := make([]string, 0)
	for _, inflection := range content.Inflections {
		if inflection != "" {
			inflections = append(inflections, inflection)
		}
	}
	phrases := make([]string, 0)
	for _, phrase := range content.Phrases {
		if phrase != "" {
			phrases = append(phrases, phrase)
		}
	}

	doc := model.EnWordItem{
		Word:        word,
		British:     british,
		American:    american,
		Inflections: inflections,
		Prefix:      prefix,
		Suffix:      suffix,
		Phrases:     phrases,
		Meanings:    meanings,
		Synonyms:    synonyms,
		Antonyms:    antonyms,
		Sentences:   sentences,
		Frequency:   -1,
		Status:      3,
	}
	return &doc, nil
}
