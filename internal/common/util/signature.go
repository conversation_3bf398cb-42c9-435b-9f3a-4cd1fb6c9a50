package util

import (
	"hw-paas-service/internal/common"
)

// 排序并拼接参数

func GenerateUrl() string {
	wsURL := "ws://openai.100tal.com/aispeech/evl-realtime/en-standard-next"

	var urlParams = make(map[string]string)
	urlParams["key1"] = "value1"
	urlParams["key2"] = "value2"

	//北京时间（东8区）
	timestamp := GetCurrentDate()

	//获取带有鉴权参数的url
	requestUrl, err := GetWsSign(
		common.Accesskeyid,
		common.Accesskeysecret,
		timestamp,
		wsURL,
		urlParams)

	if err != nil {
		println(err)
	}

	return requestUrl
}
