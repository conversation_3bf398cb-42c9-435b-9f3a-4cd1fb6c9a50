package util

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/gob"
	"encoding/json"
	"net/url"
	"reflect"
	"sort"
	"strings"
)

const request_body = "request_body"
const Application_json = "application/json"
const Application_x_www_form_urlencoded = "application/x-www-form-urlencoded"
const Multipart_formdata = "multipart/form-data"
const Multipart_formdata_body = "multipartformDataBody"
const Binary = "binary"
const BinaryBody = "BinaryBody"

// 使用HmacSha1计算签名
func HmacSha1(secret string, query string) string {
	println(query)
	secret = secret + "&"
	key := []byte(secret)
	mac := hmac.New(sha1.New, key)
	mac.Write([]byte(query))
	query = base64.StdEncoding.EncodeToString(mac.Sum(nil))
	return query
}

// 格式化入参，并计算签名
func GetSignature(
	urlParams map[string]string,
	bodyParams map[string]interface{},
	requestMethod string,
	contentType string,
	accessKeySecret string) (signature string, signatureNonce string) {

	signatureNonce = GenUUIDv4()

	signParams := make(map[string]interface{})
	signParams["signature_nonce"] = signatureNonce

	//只有Application_x_www_form_urlencoded和Application_x_www_form_urlencoded，且是POST/PATCH/PUT时，body才参与鉴权
	if bodyParams != nil && len(bodyParams) != 0 && (requestMethod == "POST" || requestMethod == "PATCH" || requestMethod == "PUT") && (contentType == Application_x_www_form_urlencoded || contentType == Application_json) {
		if contentType == Application_x_www_form_urlencoded {
			bodyParamsEncode := url.Values{}
			for k, v := range bodyParams {
				//str, _ := json.Marshal(v)
				//bodyParamsEncode.Add(k, string(str))

				switch reflect.TypeOf(v).Kind() {
				case reflect.String:
					bodyParamsEncode.Add(k, v.(string))
					break
				default:
					vJson, _ := json.Marshal(v)
					bodyParamsEncode.Add(k, string(vJson))
					break
				}
			}
			//对body进行format，并不是URLEncode
			body := bodyParamsEncode.Encode()
			signParams[request_body] = body
		} else {
			bodyJson, _ := json.Marshal(bodyParams)
			signParams[request_body] = string(bodyJson)
		}
	}

	for k, v := range urlParams {
		signParams[k] = v
	}

	sortKeys := SortMapKey(signParams)

	stringToSign := SingFormat(sortKeys, signParams)
	signature = HmacSha1(accessKeySecret, stringToSign)
	println(signature)
	return signature, signatureNonce
}

//func url_format(sort_key[]string,sign_params map[string]interface{})(result string)  {
//	var params []string
//	for _, key := range sort_key {
//		value := sign_params[key]
//		//param, _ := json.MarshalIndent(value, "", "    ")
//		value_str, _ := json.Marshal(value)
//
//		param := key + "=" + string(value_str)
//		println(param)
//		params = append(params, param)
//	}
//
//	result = strings.Join(params,"&")
//	return result
//}

func GetInterfaceToBytes(key interface{}) (result []byte, err error) {
	//var buf bytes.Buffer
	//enc := gob.NewEncoder(&buf)
	//err := enc.Encode(key)
	//if err != nil {
	//	return nil, err
	//}
	//return buf.Bytes(), nil

	var rawRoomIdBuffer bytes.Buffer
	enc := gob.NewEncoder(&rawRoomIdBuffer)
	if err = enc.Encode(key); err != nil {
		return nil, err
	}
	return rawRoomIdBuffer.Bytes(), nil

}

// 计算签名参数格式化
func SingFormat(sortKeys []string, parameters map[string]interface{}) (result string) {
	var paramList []string
	for _, k := range sortKeys {
		v, _ := parameters[k]

		var buffer bytes.Buffer
		buffer.WriteString(k)
		buffer.WriteString("=")
		//vByte,_  := GetInterfaceToBytes(v)
		//println(string(vByte))
		//buffer.Write(vByte)

		switch reflect.TypeOf(v).Kind() {
		case reflect.String:
			buffer.WriteString(v.(string))
			break
		default:
			vJson, _ := json.Marshal(v)
			buffer.WriteString(string(vJson))
			break

		}
		println(buffer.String())
		paramList = append(paramList, buffer.String())
	}
	return strings.Join(paramList, "&")
}

// 入参格式化为URL参数形式
func UrlFormat(parameters map[string]string) (result string) {
	params := url.Values{}
	for k, v := range parameters {
		params.Add(k, v)
	}
	return params.Encode()
}

// 排序sourceMap，升序
func SortMapKey(sourceMap map[string]interface{}) (sortKeys []string) {
	for key := range sourceMap {
		sortKeys = append(sortKeys, key)
	}
	sort.Strings(sortKeys)
	return sortKeys
}
