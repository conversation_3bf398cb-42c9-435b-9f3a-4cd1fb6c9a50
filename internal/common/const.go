package common

const (
	TraceId        = "trace-id"
	JwtUserId      = "jwt_user_id"
	JwtDeviceId    = "jwt_device_id"
	JwtToken       = "jwt_token"
	JwtPlatform    = "jwt_platform"
	JwtApplication = "jwt_application"
	JwtAppId       = "jwt_app_id"
	JwtVersion     = "jwt_version"
	TalIdType      = "tal_id_type"
	HeaderTraceId  = "TraceId"
	XRealIp        = "X-Real-Ip"
	XForwardedFor  = "X-Forwarded-For"
	TalToken       = "tal_token"
)

type Subject uint32

const (
	SubjectChinese Subject = 1
	SubjectEnglish Subject = 2
)

const (
	Accesskeyid = "1212773725200453632"

	Accesskeysecret = "8fb86a1e5d214ce0ab3160c214df6b2f"
)

const ColoringPages = "Coloring Pages"
const WorkSheets = "WorkSheets"

func (s Subject) String() string {
	return [...]string{"语文", "英语"}[s-1]
}
