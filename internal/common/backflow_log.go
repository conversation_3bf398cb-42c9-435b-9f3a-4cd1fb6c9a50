package common

import (
	jsoniter "github.com/json-iterator/go"
)

// LogBackFlowMsgInfo 日志msg字段结构
type LogBackFlowMsgInfo struct {
	BizPrimaryKey   string `json:"biz_primarykey"`
	BizPrimaryValue string `json:"biz_primaryvalue"`
	Biz             string `json:"biz"`
	BizFunc         string `json:"biz_func"`
	Message         string `json:"message"`
}

func MakeLogBackFlowMsgInfo(traceId, biz, bizFunc, message string) string {
	ret := LogBackFlowMsgInfo{
		BizPrimaryKey:   "trace_id",
		BizPrimaryValue: traceId,
		Biz:             biz,
		BizFunc:         bizFunc,
		Message:         message,
	}
	j, _ := jsoniter.Marshal(ret)
	return string(j)
}
