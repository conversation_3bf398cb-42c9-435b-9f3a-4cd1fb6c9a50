package common

import (
	"math/rand"
	"time"
)

// RandInt [min, max]
func RandInt(min, max int) int {
	rd := rand.New(rand.NewSource(time.Now().UnixNano()))
	randNum := rd.Intn(max - min + 1)
	randNum = randNum + min
	return randNum
}

func RandInt64(min, max int64) int64 {
	rd := rand.New(rand.NewSource(time.Now().UnixNano()))
	randNum := rd.Int63n(max - min)
	randNum = randNum + min
	return randNum
}
