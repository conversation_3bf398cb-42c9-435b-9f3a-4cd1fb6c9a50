package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/uptrace/opentelemetry-go-extra/otelgorm"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"hw-paas-service/internal/biz/biz_metrics"
	"hw-paas-service/internal/conf"
	"time"

	"github.com/redis/go-redis/extra/redisotel/v9"
	"github.com/redis/go-redis/v9"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewUcenterRepo)

// Data .
type Data struct {
	DB  *gorm.DB
	Rdb *redis.Client
}

// NewData .
func NewData(conf *conf.Data, logger log.Logger) (*Data, func(), error) {
	db, err := gorm.Open(mysql.Open(conf.Database.Source), &gorm.Config{})
	if err != nil {
		return nil, nil, err
	}
	if err := db.Use(otelgorm.NewPlugin()); err != nil {
		return nil, nil, errors.Wrap(err, "data: db.Use error")
	}

	rdb := redis.NewClient(&redis.Options{
		Addr:         conf.Redis.Addr,
		Password:     conf.Redis.Password,
		DB:           int(conf.Redis.Db),
		DialTimeout:  conf.Redis.DialTimeout.AsDuration(),
		WriteTimeout: conf.Redis.WriteTimeout.AsDuration(),
		ReadTimeout:  conf.Redis.ReadTimeout.AsDuration(),
		PoolSize:     int(conf.Redis.PoolSize),
	})

	_, err = rdb.Ping(context.Background()).Result()
	if err != nil {
		return nil, nil, err
	}

	if err := redisotel.InstrumentTracing(rdb); err != nil {
		return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentTracing error")
	}
	if err := redisotel.InstrumentMetrics(rdb); err != nil {
		return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentMetrics error")
	}
	metricRedisPoolStats(rdb)

	d := &Data{
		DB:  db,
		Rdb: rdb,
	}

	return d, func() {
		_db, err := d.DB.DB()
		if err != nil {
			log.NewHelper(logger).Errorf("database close err:%+v", err)
		}
		_ = _db.Close()
		log.NewHelper(logger).Info("closing the mysql")

		d.Rdb.Close()
		log.NewHelper(logger).Info("closing the redis")
	}, nil
}

// metricRedisPoolStats 监控redis连接池状态
func metricRedisPoolStats(rdb *redis.Client) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("metricRedisPoolStats panic: %v", r)
			}
		}()
		for range time.Tick(time.Second * 5) {
			stats := rdb.PoolStats()
			biz_metrics.RedisPoolStats(stats.Hits, stats.Misses, stats.Timeouts, stats.TotalConns, stats.IdleConns, stats.StaleConns)
		}
	}()
}
