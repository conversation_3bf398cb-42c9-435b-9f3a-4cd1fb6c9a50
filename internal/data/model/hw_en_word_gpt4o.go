package model

import "time"

// HwEnWordGpt4o 指尖查词-英文单词-GPT4o
type HwEnWordGpt4o struct {
	ID        int64     `gorm:"primary_key;AUTO_INCREMENT;column:id" json:"id"`                                                                    // 自增ID
	Word      string    `gorm:"type:varchar(50);not null;default:'';column:word" json:"word"`                                                      // 单词
	Content   string    `gorm:"type:text;not null;column:content" json:"content"`                                                                  // gpt-4o结果
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`                             // 创建时间
	UpdatedAt time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;column:updated_at" json:"updated_at"` // 更新时间
}

// TableName sets the insert table name for this struct type
func (HwEnWordGpt4o) TableName() string {
	return "hw_en_word_gpt4o"
}

// HwEnWordGpt4oSearch 指尖查词-英文单词-GPT4o查询条件
type HwEnWordGpt4oSearch struct {
	Word string `json:"word"` // 单词
}
