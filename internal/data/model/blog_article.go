package model

import "time"

type BlogArticle struct {
	ID              int64     `gorm:"column:id" json:"id"`
	Path            string    `gorm:"column:path" json:"path"`
	Category        string    `gorm:"column:category" json:"category"`
	ArticleTitle    string    `gorm:"column:article_title" json:"article_title"`
	ShortContent    string    `gorm:"column:short_content" json:"short_content"`
	CoverImg        string    `gorm:"column:cover_img" json:"cover_img"`
	AuthorAvatar    string    `gorm:"column:author_avatar" json:"author_avatar"`
	AuthorName      string    `gorm:"column:author_name" json:"author_name"`
	ArticleContent  string    `gorm:"column:article_content" json:"article_content"`
	PageTitle       string    `gorm:"column:page_title" json:"page_title"`
	MetaKeywords    string    `gorm:"column:meta_keywords" json:"meta_keywords"`
	MetaDescription string    `gorm:"column:meta_description" json:"meta_description"`
	CreatedAt       time.Time `gorm:"column:created_at;autoCreateTime:false" json:"created_at"`
	UpdatedAt       time.Time `gorm:"column:updated_at;autoUpdateTime:false" json:"updated_at"`
}

func (BlogArticle) TableName() string {
	return "blog_articles"
}
