package model

import "time"

/*
create table ai_jzx_imports (
    id int auto_increment primary key,
    file_name varchar(225) not null default '' comment '文件名称',
    file_url varchar(512) not null default '' comment '文件URL',
    subject      varchar(32)  not null default '' comment '学科',
    status tinyint not null default 1 comment '1创建中 2导入失败 3待确认 4已完成 5已取消',
    num_success int not null default 0 comment '导入成功数量',
    num_err int not null default 0 comment '导入失败数量',
    num_repeat int not null default 0 comment 'ID重复数量',
    failed_reason varchar(512) not null default '' comment '失败原因描述',
    created_at            timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
);
*/

const (
	ImportStatusPending   = 1
	ImportStatusFailed    = 2
	ImportStatusConfirm   = 3
	ImportStatusCompleted = 4
	ImportStatusCancelled = 5
)

type AiJzxImport struct {
	ID           int64     `gorm:"column:id;primary_key;auto_increment" json:"id"`
	FileName     string    `gorm:"column:file_name" json:"file_name"`
	FileUrl      string    `gorm:"column:file_url" json:"file_url"`
	Subject      string    `gorm:"column:subject" json:"subject"`
	Status       int64     `gorm:"column:status" json:"status"`
	NumSuccess   int64     `gorm:"column:num_success" json:"num_success"`
	NumErr       int64     `gorm:"column:num_err" json:"num_err"`
	NumRepeat    int64     `gorm:"column:num_repeat" json:"num_repeat"`
	FailedReason string    `gorm:"column:failed_reason" json:"failed_reason"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updated_at"`
}

func (q *AiJzxImport) TableName() string {
	return "ai_jzx_imports"
}

/*
create table ai_jzx_import_details (
    id int auto_increment primary key,
    import_id int not null comment '导入ID',
    question_id int not null comment '题目ID',
    detail mediumtext null comment '数据详情',
    status tinyint not null default 1 comment '1成功 2失败 3重复',
    failed_reason varchar(512) not null default '' comment '失败原因描述',
    created_at            timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
);
*/

const (
	ImportDetailStatusSuccess = 1
	ImportDetailStatusFailed  = 2
	ImportDetailStatusRepeat  = 3
)

type AiJzxImportDetail struct {
	ID           int64     `gorm:"column:id;primary_key;auto_increment" json:"id"`
	ImportID     int64     `gorm:"column:import_id" json:"import_id"`
	QuestionID   int64     `gorm:"column:question_id" json:"question_id"`
	Detail       string    `gorm:"column:detail" json:"detail"`
	Status       int64     `gorm:"column:status" json:"status"`
	FailedReason string    `gorm:"column:failed_reason" json:"failed_reason"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updated_at"`
}

func (q *AiJzxImportDetail) TableName() string {
	return "ai_jzx_import_details"
}
