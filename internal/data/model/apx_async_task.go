package model

import (
	"time"
)

type ApxAsyncTask struct {
	ID        uint32    `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL"`
	Word      string    `gorm:"column:word;default:;NOT NULL"`
	TaskID    string    `gorm:"column:task_id;default:;NOT NULL"`
	Status    uint8     `gorm:"column:status;NOT NULL;comment:'任务状态，3:成功;4:失败'"`
	Response  string    `gorm:"column:response;NOT NULL;comment:'模型响应数据'"`
	RelWord   string    `gorm:"type:varchar(50);not null;default:'';column:rel_word" json:"rel_word"` // 关联的原形词
	CreatedAt time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL;comment:'创建时间'"`
	UpdatedAt time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL;comment:'更新时间'"`
}

func (a *ApxAsyncTask) TableName() string {
	return "apx_async_task"
}

type ApxAsyncTaskSearch struct {
	Id     uint32 `json:"id"`
	Status uint8  `json:"status"`
}
