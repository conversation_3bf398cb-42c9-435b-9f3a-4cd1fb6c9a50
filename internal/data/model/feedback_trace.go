package model

import (
	"time"
)

// FeedbackTrace 反馈
type FeedbackTrace struct {
	ID        int64     `gorm:"primary_key;AUTO_INCREMENT;column:id" json:"id"`                                                                    // 自增ID
	Biz       uint8     `gorm:"type:tinyint(1) unsigned;not null;column:biz" json:"biz"`                                                           // 工具,1:口算批改;2:指尖查词;3:作业批改
	TraceID   string    `gorm:"type:varchar(20);not null;default:'';column:trace_id" json:"trace_id"`                                              // trace ID
	Content   string    `gorm:"type:text;not null;column:content" json:"content"`                                                                  // 反馈内容,json
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`                             // 创建时间
	UpdatedAt time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;column:updated_at" json:"updated_at"` // 更新时间
}

// TableName sets the insert table name for this struct type
func (FeedbackTrace) TableName() string {
	return "feedback_trace"
}
