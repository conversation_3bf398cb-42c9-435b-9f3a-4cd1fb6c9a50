package model

import "time"

type Comment struct {
	ID          int       `gorm:"primaryKey;autoIncrement"`
	SubjectType int       `gorm:"column:subject_type"` // 1 blog，2 worksheet，3 Glossary，4 Coloring Pages
	SubjectID   string    `gorm:"column:subject_id"`
	ParentID    uint64    `gorm:"column:parent_id"` // 父评论ID，0为一级评论
	UserAvatar  string    `gorm:"column:user_avatar"`
	UserName    string    `gorm:"column:user_name"`
	Content     string    `gorm:"column:content"`
	CreatedAt   time.Time `gorm:"column:created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at"`
}

func (Comment) TableName() string {
	return "comments"
}
