package model

import "time"

type EduTerm struct {
	ID              int       `gorm:"primaryKey;autoIncrement"`
	Path            string    `gorm:"column:path;type:varchar(255);not null;default:''"`
	Subject         string    `gorm:"column:subject;type:varchar(64);not null;default:''"`
	Term            string    `gorm:"column:term;type:varchar(255);not null;default:''"`
	Knowledge1      string    `gorm:"column:knowledge_1;type:varchar(64);not null;default:''"`
	Knowledge2      string    `gorm:"column:knowledge_2;type:varchar(64);not null;default:''"`
	MetaDescription string    `gorm:"column:meta_description;type:varchar(512);not null;default:''"`
	Title           string    `gorm:"column:title;type:varchar(255);not null;default:''"`
	Tag             string    `gorm:"column:tag;type:varchar(255);not null;default:''"`
	ModuleType      string    `gorm:"column:module_type;type:varchar(64);not null;default:''"`
	ModuleIndex     int       `gorm:"column:module_index;type:tinyint unsigned;not null;default:0"`
	BlockType       string    `gorm:"column:block_type;type:varchar(64);not null;default:''"`
	BlockOrder      int       `gorm:"column:block_order;type:tinyint unsigned;not null;default:0"`
	BlockContent    string    `gorm:"column:block_content;type:text;not null"`
	CreatedAt       time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt       time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP"`
}

func (EduTerm) TableName() string {
	return "edu_terms"
}
