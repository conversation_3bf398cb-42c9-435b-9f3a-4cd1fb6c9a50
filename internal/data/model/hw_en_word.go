package model

import (
	"time"
)

// EnWordStatus 英文单词状态, 1:待机审;2:待上架;3:已上架;4:已下架;5:违禁词
type EnWordStatus uint8

const (
	EnWordStatusWaitAudit EnWordStatus = iota + 1
	EnWordStatusWaitShelf
	EnWordStatusShelf
	EnWordStatusUnShelf
	EnWordStatusCurse
)

// HwEnWord 指尖查词-英文单词
type HwEnWord struct {
	ID          int64        `gorm:"primary_key;AUTO_INCREMENT;column:id" json:"id"`                                                                    // 自增ID
	Word        string       `gorm:"type:varchar(50);not null;default:'';column:word" json:"word"`                                                      // 单词
	British     string       `gorm:"type:varchar(10);not null;default:'';column:british" json:"british"`                                                // 英式音标
	American    string       `gorm:"type:varchar(10);not null;default:'';column:american" json:"american"`                                              // 美式音标
	Meanings    string       `gorm:"type:varchar(2048);not null;default:'';column:meanings" json:"meanings"`                                            // 释义
	Synonyms    string       `gorm:"type:varchar(256);not null;default:'';column:synonyms" json:"synonyms"`                                             // 同义词
	Antonyms    string       `gorm:"type:varchar(256);not null;default:'';column:antonyms" json:"antonyms"`                                             // 反义词
	Sentences   string       `gorm:"type:varchar(2048);not null;default:'';column:sentences" json:"sentences"`                                          // 例句
	Inflections string       `gorm:"type:varchar(256);not null;default:'';column:inflections" json:"inflections"`                                       // 变形词
	Prefix      string       `gorm:"type:varchar(20);not null;default:'';column:prefix" json:"prefix"`                                                  // 前缀
	Suffix      string       `gorm:"type:varchar(20);not null;default:'';column:suffix" json:"suffix"`                                                  // 后缀
	Phrases     string       `gorm:"type:varchar(512);not null;default:'';column:phrases" json:"phrases"`                                               // 短语
	Frequency   int64        `gorm:"type:int(10);not null;column:frequency" json:"frequency"`                                                           // 词频
	Status      EnWordStatus `gorm:"type:tinyint(1) unsigned;not null;column:status" json:"status"`                                                     // 状态,1:待机审;2:待上架;3:已上架;4:已下架
	OptUID      string       `gorm:"type:varchar(20);not null;default:'';column:opt_uid" json:"opt_uid"`                                                // 创建人工号
	OptName     string       `gorm:"type:varchar(20);not null;default:'';column:opt_name" json:"opt_name"`                                              // 创建人名称
	RelWord     string       `gorm:"type:varchar(50);not null;default:'';column:rel_word" json:"rel_word"`                                              // 关联的原形词
	VideoUrl    string       `gorm:"type:varchar(512);not null;default:'';column:video_url" json:"video_url"`                                           // 视频讲解
	VideoImg    string       `gorm:"type:varchar(512);not null;default:'';column:video_img" json:"video_img"`                                           // 视频封面
	CreatedAt   time.Time    `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`                             // 创建时间
	UpdatedAt   time.Time    `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;column:updated_at" json:"updated_at"` // 更新时间
}

// TableName sets the insert table name for this struct type
func (HwEnWord) TableName() string {
	return "hw_en_word"
}

type HwEnWordSearch struct {
	MaxId  int64
	Id     int64
	Word   string
	Status EnWordStatus

	Page     int
	PageSize int
}

type EnWordItem struct {
	Id          int64           `json:"id"`
	Word        string          `json:"word"`
	British     string          `json:"british"`
	American    string          `json:"american"`
	Inflections []string        `json:"inflections"`
	Prefix      string          `json:"prefix"`
	Suffix      string          `json:"suffix"`
	Phrases     []string        `json:"phrases"`
	Meanings    []EnWordMeaning `json:"meanings"`
	Synonyms    []string        `json:"synonyms"`
	Antonyms    []string        `json:"antonyms"`
	Sentences   []string        `json:"sentences"`
	Frequency   int64           `json:"frequency"`
	Status      EnWordStatus    `json:"status"`
	VideoUrl    string          `json:"video_url,omitempty"`
	//VideoImg    string          `json:"video_img,omitempty"`
	Speech string `json:"speech,omitempty"`
	Msg    string `json:"msg"`
}
type EnWordMeaning struct {
	PartOfSpeech string `json:"part_of_speech"`
	Definition   string `json:"definition"`
}
