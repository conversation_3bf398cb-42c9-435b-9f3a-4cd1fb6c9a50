package model

import "time"

/*
create table ai_jzx_questions
(
    id           int auto_increment
        primary key,
    q_type       tinyint               default 0 not null comment '题型:1选择题单选 2选择题无序多选 3选择题有序多选 4连线题一对一 5连线题一对多 6填空题单空 7填空题多空 8拖拽题排序 9拖拽题填空 10阅读理',
    grade        varchar(32)  not null default '' comment '年级',
    subject      varchar(32)  not null default '' comment '学科',
    difficulty   tinyint      not null default 0 comment '难度值',
    question     mediumtext   null comment '题干',
    answer       mediumtext   null comment '答案',
    solution     mediumtext   null comment '题目解析',
    knowledge    varchar(128) not null default '' comment '知识点',
    knowledge_no varchar(128) not null default '' comment '知识点序号',
    note         text         null comment '备注',
    status       tinyint               default 0 null comment '题目状态: 1待审核 2审核不通过 3审核通过 4已下架 5已上架'
);
*/

const (
	QuestionStatusPending  = 1
	QuestionStatusRejected = 2
	QuestionStatusApproved = 3
	QuestionStatusUnlisted = 4
	QuestionStatusListed   = 5
)

// AiJzxQuestion represents the ai_jzx_questions table
type AiJzxQuestion struct {
	ID          int64     `gorm:"column:id;primary_key;auto_increment" json:"id"`
	QType       int64     `gorm:"column:q_type" json:"q_type"`
	Grade       string    `gorm:"column:grade" json:"grade"`
	Subject     string    `gorm:"column:subject" json:"subject"`
	Difficulty  int64     `gorm:"column:difficulty" json:"difficulty"`
	Question    string    `gorm:"column:question" json:"question"`
	Answer      string    `gorm:"column:answer" json:"answer"`
	Solution    string    `gorm:"column:solution" json:"solution"`
	Knowledge   string    `gorm:"column:knowledge" json:"knowledge"`
	KnowledgeNo string    `gorm:"column:knowledge_no" json:"knowledge_no"`
	Note        string    `gorm:"column:note" json:"note"`
	Status      int64     `gorm:"column:status" json:"status"`
	CreatedAt   time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (q *AiJzxQuestion) TableName() string {
	return "ai_jzx_questions"
}
