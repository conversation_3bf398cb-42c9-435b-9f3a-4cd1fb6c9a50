package model

import (
	"time"
)

//cerate table edu_resources (
//id  int unsigned auto_increment comment '主键ID'primary key,
//resource_type varchar(20) default '' not null comment '资源类型 worksheet video coloringpage',
//subject varchar(20) default '' not null comment '学科',
//grade int default 0 not null. comment '年级 0 代表所有年级',
//title varchar(500) default '' not null comment '标题',
//learningtopic varchar(50) default '' not null comment '二知识点 activity_topic',
//resource varchar(50) default '' not null comment '三知识点',
//resource_detail varchar(500) default '' not null comment '四级知识点',
//standards varchar(100) default '' not null comment '最细粒度知识点',
//resource_description varchar(1024) default '' not null comment '简介',
//extra_data text default '' not null comment '内容 以json格式存储',
//status int default 1 not null comment '状态 1 上线 2 删除',
//created_at       datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
//updated_at       datetime         default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
//)

type EduResources struct {
	ID                  int64     `gorm:"column:id" json:"id"`
	ResourceType        string    `gorm:"column:resource_type" json:"resource_type"`
	ResourceCode        string    `gorm:"column:resource_code" json:"resource_code"`
	Subject             string    `gorm:"column:subject" json:"subject"`
	Grade               string    `gorm:"column:grade" json:"grade"`
	Title               string    `gorm:"column:title" json:"title"`
	Topic               string    `gorm:"column:topic" json:"topic"`
	LearningModule      string    `gorm:"column:learning_module" json:"learning_module"`
	Resource            string    `gorm:"column:resource" json:"resource"`
	ResourceDetail      string    `gorm:"column:resource_detail" json:"resource_detail"`
	Standards           string    `gorm:"column:standards" json:"standards"`
	ResourceDescription string    `gorm:"column:resource_description" json:"resource_description"`
	ExtraData           string    `gorm:"column:extra_data" json:"extra_data"`
	Status              int       `gorm:"column:status" json:"status"`
	Url                 string    `gorm:"column:url" json:"url"`
	MetaDescription     string    `gorm:"column:meta_description" json:"meta_description"`
	MetaTitle           string    `gorm:"column:meta_title" json:"meta_title"`
	MetaKeywords        string    `gorm:"column:meta_keywords" json:"meta_keywords"`
	Schemas             string    `gorm:"column:schemas" json:"schemas"`
	CreatedAt           time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt           time.Time `gorm:"column:updated_at" json:"updated_at"`
}

func (EduResources) TableName() string {
	return "edu_resources"
}

type Grade struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type ResourceTopic struct {
	Name    string   `json:"name"`
	Modules []string `json:"modules"`
}

type SubjectNode struct {
	Name  string         `json:"name"`
	Nodes []*SubjectNode `json:"nodes"`
}

type Subject struct {
	Nodes []*SubjectNode `json:"nodes"`
}

type ResourceType struct {
	Name  string `json:"name"`
	Topic string `json:"topic"`
}

type ResourceReq struct {
	ResourceType   string `json:"resource_type"`
	Grade          string `json:"grade"`
	Subject        string `json:"subject"`
	Topic          string `json:"topic"`           // 二知识点
	LearningModule string `json:"learning_module"` // 三知识点
	ResourceDetail string `json:"resource_detail"` // 四级知识点
	Resource       string `json:"resource"`        // 三知识点
	Page           int    `json:"page"`
	PageSize       int    `json:"page_size"`
}

// 合并年级、学科、资源类型的元信息结构体
// EduResourceMeta 用于一次性返回所有元信息
type EduResourceMeta struct {
	Grades        []*Grade        `json:"grades"`
	Subjects      []*Subject      `json:"subjects"`
	ResourceTypes []*ResourceType `json:"resource_types"`
}

type EduResourceGroup struct {
	Title     string          `json:"title"`
	Count     int             `json:"count"`
	Resources []*EduResources `json:"resources"`
}

type EduResourcesTitle struct {
	ID              uint      `gorm:"column:id" json:"id"`
	URL             string    `gorm:"column:url'" json:"url"`
	H1Title         string    `gorm:"column:h1_title" json:"h1_title"`
	Description     string    `gorm:"column:description" json:"description"`
	MetaTitle       string    `gorm:"column:meta_title" json:"meta_title"`
	MetaDescription string    `gorm:"column:meta_description" json:"meta_description"`
	MetaKeywords    string    `gorm:"column:meta_keywords" json:"meta_keywords"`
	Schemas         string    `gorm:"column:schemas" json:"schemas"`
	Filter          string    `gorm:"column:filter" json:"filter"`
	CreatedAt       time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt       time.Time `gorm:"column:updated_at" json:"updated_at"`
}

func (EduResourcesTitle) TableName() string {
	return "edu_resources_title"
}

type EduResourcesURL struct {
	ID           int64     `gorm:"column:id" json:"id"`
	ResourceCode string    `gorm:"column:resource_code" json:"resource_code"`
	URL          string    `gorm:"column:url" json:"url"`
	URLMd5       string    `gorm:"column:url_md5" json:"url_md5"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updated_at"`
}

func (EduResourcesURL) TableName() string {
	return "edu_resources_url"
}
