package jobs

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.100tal.com/znxx_xpp/go-libs/traces/xhttp"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	"github.com/xuri/excelize/v2"
	"golang.org/x/sync/errgroup"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"os"
	"strings"
	"sync"
	"testing"
	"time"
)

type TaskResponse struct {
	TaskId    string      `json:"id"`
	CreatedAt string      `json:"created_at"`
	UpdatedAt string      `json:"updated_at"`
	Status    uint8       `json:"status"`
	Response  interface{} `json:"response"`
}

// TestPushApxAsyncTaskFromExcel 第一步：1，基于excel基础词请求gpt-4o
func TestPushApxAsyncTaskFromExcel(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	host := "http://apx-api-gray.tal.com"
	//getTaskUrl := "/v1/async/results/"
	client := xhttp.MakeHttpClient(log.NewHelper(logger)).SetBaseURL(host)
	taskDao := dao.NewApxAsyncTaskDao(dataData, logger)
	//fileHandle, err := os.Open("base_en_word_1101_13301.xlsx")
	//fileHandle, err := os.Open("filter_en_word_1219.xlsx")
	fileHandle, err := os.Open("expand_en_word_0123.xlsx")
	if err != nil {
		str := fmt.Sprintf("os open err:%v\n", err)
		fmt.Printf(str)
		return
	}
	xlsx, err := excelize.OpenReader(fileHandle)
	if err != nil {
		fmt.Println(err)
	}
	rows, err := xlsx.GetRows("Sheet1")
	if err != nil {
		fmt.Println(err)
	}

	fn := "gpt4o_offline_en_word_1731654732.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	//2025.01.03，task表第一批数据最大ID：52849

	eg, _ := errgroup.WithContext(ctx)
	eg.SetLimit(10)
	for i, v := range rows {
		if i == 0 {
			continue
		}
		if len(v) < 2 {
			continue
		}
		row := v
		fmt.Printf("i:%d, word:%v\n", i, row[0])
		eg.Go(func() error {
			word := strings.ToLower(row[0])
			_, err = addApxAsyncTask(ctx, client, taskDao, word, "")
			if err != nil {
				fileLog.WriteString(fmt.Sprintf("word: %s\nerr: %v\n", row[0], err))
			}
			return nil
		})
	}
	eg.Wait()
	fmt.Printf("push done\n")
}

// TestPushApxAsyncTaskFromDb 第一步：2，从数据库中读取变形词请求gpt-4o
func TestPushApxAsyncTaskFromDb(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	host := "http://apx-api-gray.tal.com"
	//getTaskUrl := "/v1/async/results/"
	client := xhttp.MakeHttpClient(log.NewHelper(logger)).SetBaseURL(host)
	taskDao := dao.NewApxAsyncTaskDao(dataData, logger)
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	//2025.01.03，task表第一批数据最大ID：52849

	rows, _ := fwDao.FindFingerWords(ctx, &model.HwEnWordSearch{Status: model.EnWordStatusShelf})

	fn := "gpt4o_offline_db_en_word_1731654732.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	eg, _ := errgroup.WithContext(ctx)
	eg.SetLimit(10)
	for i, v := range rows {
		inflections := make([]string, 0)
		err = jsoniter.Unmarshal([]byte(v.Inflections), &inflections)
		if err != nil {
			fmt.Printf("unmarshal inflections, word: %s, err:%v\n", v.Word, err)
			continue
		}
		if len(inflections) == 0 {
			continue
		}
		row := v
		fmt.Printf("i:%d, word:%v\n", i, row.Word)
		eg.Go(func() error {
			for _, inflection := range inflections {
				_, err = addApxAsyncTask(ctx, client, taskDao, inflection, row.Word)
				if err != nil {
					fileLog.WriteString(fmt.Sprintf("word: %s, inflection: %s\nerr: %v\n", row.Word, inflection, err))
				}
			}
			return nil
		})
	}
	eg.Wait()
	fmt.Printf("push done\n")
}

// 第二步：持续获取gpt-4o结果
func TestGetApxAsyncTaskResult(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	host := "http://apx-api-gray.tal.com"
	//getTaskUrl := "/v1/async/results/"
	client := xhttp.MakeHttpClient(log.NewHelper(logger)).SetBaseURL(host)
	taskDao := dao.NewApxAsyncTaskDao(dataData, logger)
	_ = updateApxAsyncTask(ctx, client, taskDao)
}

func updateApxAsyncTask(ctx context.Context, client *resty.Client, taskDao *dao.ApxAsyncTaskDao) error {
	list, err := taskDao.FindNoFinishTask(ctx)
	if err != nil {
		return err
	}
	if len(list) == 0 {
		fmt.Println("task已结束")
		return nil
	}
	fmt.Printf("task len: %d\n", len(list))
	time.Sleep(3 * time.Second)
	//taskM := map[string]struct{}{}
	var taskM sync.Map
	location, _ := time.LoadLocation("Asia/Shanghai")
	for {
		//if len(taskM) == len(list) {
		//	break
		//}
		eg, _ := errgroup.WithContext(ctx)
		eg.SetLimit(50)
		for _, v := range list {
			task := v
			if _, ok := taskM.Load(task.TaskID); ok {
				continue
			}
			eg.Go(func() error {
				resp, err := getApxAsyncResult(ctx, task.TaskID, client)
				if err != nil {
					return err
				}
				fmt.Println("id:", task.ID, ",status:", resp.Status)
				if resp.Status == 3 || resp.Status == 4 { //3成功，4失败
					//taskM[task.TaskID] = struct{}{}
					taskM.Store(task.TaskID, resp.Status)
					response, _ := json.Marshal(resp.Response)
					task.Status = resp.Status
					task.Response = string(response)
					updatedAt, _ := time.ParseInLocation("2006-01-02 15:04:05", resp.UpdatedAt, location)
					task.UpdatedAt = updatedAt
					err = taskDao.Update(ctx, task)
					if err != nil {
						return err
					}
					err := deleteApxAsyncTask(ctx, task.TaskID, client)
					if err != nil {
						return err
					}
				}
				return nil
			})
		}
		eg.Wait()
		fmt.Printf("wait 5 minutes\n")
		//time.Sleep(5 * time.Minute)
		break
	}

	return nil
}

func addApxAsyncTask(ctx context.Context, client *resty.Client, taskDao *dao.ApxAsyncTaskDao, word, realWord string) ([]string, error) {
	//查找db是否存在
	rows, _ := taskDao.FindWords(ctx, word)
	if len(rows) > 0 {
		return nil, nil
	}

	// 请求4o服务
	messages := make([]Gpt4oMessage, 0)
	messages = append(messages, Gpt4oMessage{
		Role:    "system",
		Content: prompt,
	})
	messages = append(messages, Gpt4oMessage{
		Role:    "user",
		Content: word,
	})
	req := Gpt4oRequest{
		Model:    "gpt-4o",
		Messages: messages,
	}
	var taskResp TaskResponse
	pushTaskUrl := "/v1/async/chat?api-version=2024-10-21"
	location, _ := time.LoadLocation("Asia/Shanghai")
	_, err := client.R().
		SetHeaders(map[string]string{
			"Content-Type": "application/json",
			"api-key":      "1000080779:4ce58724bd745414626e1b940a3bfb15",
		}).
		SetBody(req).SetResult(&taskResp).Post(pushTaskUrl)
	if err != nil {
		fmt.Printf("request gpt-4o, word: %s, err:%v\n", word, err)
		return nil, err
	}
	//fmt.Println("resp:", string(resp.Body()))
	fmt.Printf("word: %s, taskId:%s, status:%d, createdAt: %s, updatedAt: %s\n", word, taskResp.TaskId, taskResp.Status, taskResp.CreatedAt, taskResp.UpdatedAt)
	createdAt, _ := time.ParseInLocation("2006-01-02 15:04:05", taskResp.CreatedAt, location)
	updatedAt, _ := time.ParseInLocation("2006-01-02 15:04:05", taskResp.UpdatedAt, location)
	err = taskDao.Create(ctx, &model.ApxAsyncTask{
		Word:      word,
		TaskID:    taskResp.TaskId,
		Status:    taskResp.Status,
		RelWord:   realWord,
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	})

	return nil, err
}

func getApxAsyncResult(ctx context.Context, taskId string, client *resty.Client) (TaskResponse, error) {
	var taskResp TaskResponse
	getRespUrl := "/v1/async/results/"
	_, err := client.R().
		SetHeaders(map[string]string{
			"Content-Type": "application/json",
			"api-key":      "1000080779:4ce58724bd745414626e1b940a3bfb15",
		}).SetResult(&taskResp).Get(getRespUrl + taskId)
	if err != nil {
		fmt.Printf("request gpt-4o, taskId: %s, err:%v\n", taskId, err)
		return taskResp, err
	}
	return taskResp, nil
}

func deleteApxAsyncTask(ctx context.Context, taskId string, client *resty.Client) error {
	deleteRespUrl := "/v1/async/results/"
	resp, err := client.R().
		SetHeaders(map[string]string{
			"api-key": "1000080779:4ce58724bd745414626e1b940a3bfb15",
		}).Delete(deleteRespUrl + taskId)
	if err != nil {
		fmt.Printf("delete, taskId: %s, err:%v\n", taskId, err)
		return err
	}
	fmt.Println("delete resp:", string(resp.Body()))
	return nil
}

// 第三步：gpt-4o结果解析，并更新到单词表 hw_en_word
func TestFormatEnWordBaseOfflineGpt4o(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	taskDao := dao.NewApxAsyncTaskDao(dataData, logger)
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	fn := "offline_en_word_1731654732.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	//hw_en_word, 2025.01.03之前id最大值 52769

	//2025.01.03，task表第一批数据最大ID：52849

	rows, err := taskDao.FindAsyncTasks(ctx, &model.ApxAsyncTaskSearch{Id: 52849, Status: 3}) //todo:todo:每次调整最小ID:52849
	if len(rows) == 0 {
		fmt.Printf("no data\n")
		return
	}

	//words := map[string]struct{}{}

	eg, _ := errgroup.WithContext(ctx)
	eg.SetLimit(10)
	for i, v := range rows {
		row := v
		//word := row.Word
		//relWord := row.RelWord
		fmt.Printf("i: %d, word: %s\n", i, row.Word)
		//if _, ok := words[word]; ok {
		//	continue
		//}

		eg.Go(func() error {
			err := addEnWordOffline(ctx, fwDao, row)
			if err != nil {
				fileLog.WriteString(fmt.Sprintf("gpt4o_id: %d, word: %s\nerr: %v\n", row.ID, row.Word, err))
			}
			return nil
		})
	}
	eg.Wait()

	fmt.Printf("create done\n")
}

func addEnWordOffline(ctx context.Context, fwDao *dao.FingerWordsDao, task *model.ApxAsyncTask) error {
	word := task.Word
	relWord := task.RelWord
	response := task.Response
	//查找db是否存在
	rows, _ := fwDao.FindFingerWords(ctx, &model.HwEnWordSearch{Word: word})
	if len(rows) > 0 {
		return nil
	}

	var gpt4oResp Gpt4oResponse
	err := jsoniter.Unmarshal([]byte(response), &gpt4oResp)
	if err != nil {
		fmt.Printf("unmarshal gpt-4o, word: %s, err:%v\n", word, err)
		return err
	}
	if len(gpt4oResp.Choices) == 0 {
		fmt.Printf("gpt-4o response empty, word: %s\n", word)
		return err
	}
	str := gpt4oResp.Choices[0].Message.Content
	if !strings.Contains(str, "{") {
		errStr := fmt.Sprintf("gpt-4o response not contain {, word: %s\n", word)
		fmt.Printf(errStr)
		//return errors.New(errStr)
		return nil
	}
	str = strings.Replace(str, "```json", "", -1)
	str = strings.Replace(str, "```", "", -1)

	var content Gpt4oContent
	err = jsoniter.Unmarshal([]byte(str), &content)
	if err != nil {
		fmt.Printf("unmarshal content, word: %s, err:%v\n", word, err)
		return errors.New(fmt.Sprintf("unmarshal content err, content: %s\n", str))
	}

	// 写入DB
	var british, american, prefix, suffix string
	british = content.Pronunciation.British
	american = content.Pronunciation.American
	prefix = content.RootVariations.Prefix
	suffix = content.RootVariations.Suffix
	//保留[字母-],过滤[@=]
	/*prefix = strings.Replace(prefix, "@", "", -1)
	prefix = strings.Replace(prefix, "=", "", -1)
	suffix = strings.Replace(suffix, "@", "", -1)
	suffix = strings.Replace(suffix, "=", "", -1)*/

	//meanings, _ := jsoniter.Marshal(content.Meanings)
	meanings := make([]Gpt4oContentMeaning, 0)
	for _, meaning := range content.Meanings {
		if meaning.PartOfSpeech != "" && meaning.Definition != "" {
			meanings = append(meanings, meaning)
		}
	}
	meaningsB, _ := jsoniter.Marshal(meanings)
	synonyms := make([]string, 0)
	for _, synonym := range content.Synonyms {
		if synonym != "" {
			synonyms = append(synonyms, synonym)
		}
	}
	synonymsB, _ := jsoniter.Marshal(synonyms)
	antonyms := make([]string, 0)
	for _, antonym := range content.Antonyms {
		if antonym != "" {
			antonyms = append(antonyms, antonym)
		}
	}
	antonymsB, _ := jsoniter.Marshal(antonyms)
	sentences := make([]string, 0)
	for _, sentence := range content.Sentences {
		if sentence != "" {
			sentences = append(sentences, sentence)
		}
	}
	sentencesB, _ := jsoniter.Marshal(sentences)
	inflections := make([]string, 0)
	for _, inflection := range content.Inflections {
		if inflection != "" {
			inflections = append(inflections, inflection)
		}
	}
	inflectionsB, _ := jsoniter.Marshal(inflections)
	phrases := make([]string, 0)
	for _, phrase := range content.Phrases {
		if phrase != "" {
			phrases = append(phrases, phrase)
		}
	}
	phrasesB, _ := jsoniter.Marshal(phrases)
	err = fwDao.Create(ctx, &model.HwEnWord{
		Word:        word,
		British:     british,
		American:    american,
		Meanings:    string(meaningsB),
		Synonyms:    string(synonymsB),
		Antonyms:    string(antonymsB),
		Sentences:   string(sentencesB),
		Inflections: string(inflectionsB),
		Prefix:      prefix,
		Suffix:      suffix,
		Phrases:     string(phrasesB),
		Status:      model.EnWordStatusWaitAudit, //待机审
		RelWord:     relWord,
	})

	return err
}
