package jobs

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

type Question struct {
	ID           string   `json:"id"`
	Content      string   `json:"content"`
	Images       []string `json:"images"`
	Options      []string `json:"options"`
	Answer       string   `json:"answer"`
	QuestionType int64    `json:"question_type"`
	TypeDesc     string   `json:"type_desc"`
	Grade        string   `json:"grade"`
	Analysis     string   `json:"analysis"`
	Knowledge    string   `json:"knowledge"`
	KnowledgeID  string   `json:"knowledge_id"`
	Difficulty   string   `json:"difficulty"`
	Remark       string   `json:"remark"`
	Tag1         string   `json:"tag1"`
	Tag2         string   `json:"tag2"`
}

var questionTypeMap = map[string]int64{
	"单选题":   1,
	"多选题":   2,
	"无序多选题": 2,
	"有序多选题": 3,
	"判断题":   4,
	"拖拽题":   5,
	"阅读题":   6,
	"阅读理解题": 6,
}

var subject = "ela"              //math,ela
var fileName = "ela-041817.xlsx" //math.xlsx,ela.xlsx

// 安全获取数组元素，如果索引超出范围则返回空字符串
func safeGetString(arr []string, index int) string {
	if index < 0 || index >= len(arr) {
		return ""
	}
	// 去除空格
	return strings.TrimSpace(arr[index])
}

func TestJzxQuestion(t *testing.T) {
	dataData, _, logger := initConfig()
	jzxDao := dao.NewAiJzxQuestionDao(dataData, logger)

	// 获取用户Downloads目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		t.Fatalf("获取用户目录失败: %v", err)
	}
	excelPath := filepath.Join(homeDir, "Downloads", fileName)

	// 打开Excel文件
	f, err := excelize.OpenFile(excelPath)
	if err != nil {
		t.Fatalf("打开Excel文件失败: %v", err)
	}
	defer f.Close()

	var allQuestions []Question

	fmt.Println("开始处理题目...")
	// 遍历所有sheet
	for _, sheet := range f.GetSheetList() {
		rows, err := f.GetRows(sheet)
		if err != nil {
			t.Fatalf("读取sheet %s 失败: %v, [rows]:%+v", sheet, err, rows)
			return
		}

		// 跳过标题行
		for i := 1; i < len(rows); i++ {
			row := rows[i]

			// 获取图片
			images := make([]string, 0)
			for j := 2; j <= 4; j++ { // 处理3个图片列
				cell := fmt.Sprintf("%c%d", 'C'+j-2, i+1)
				pics, err := f.GetPictures(sheet, cell)
				if err == nil && len(pics) > 0 {
					// 将图片转换为base64
					base64Str := base64.StdEncoding.EncodeToString(pics[0].File)
					images = append(images, base64Str)
				}
			}

			// 安全地获取选项数组
			options := make([]string, 0, 6)
			for j := 5; j <= 10; j++ {
				cell := fmt.Sprintf("%c%d", 'F'+j-5, i+1)
				pics, err := f.GetPictures(sheet, cell)
				if err == nil && len(pics) > 0 {
					// 如果单元格包含图片，将图片转换为base64
					base64Str := base64.StdEncoding.EncodeToString(pics[0].File)
					options = append(options, base64Str)
				} else {
					// 如果没有图片，使用文本内容
					options = append(options, safeGetString(row, j))
				}
			}

			// 如果options长度为0，则跳过
			if len(options) == 0 {
				t.Logf("sheet %s 行 %d 数据缺少选项", sheet, i+1)
				continue
			}

			questionType := questionTypeMap[safeGetString(row, 12)]
			if questionType == 0 {
				t.Logf("sheet %s 行 %d 数据缺少题型, %s", sheet, i+1, safeGetString(row, 12))
				continue
			}

			question := Question{
				ID:           safeGetString(row, 0),
				Content:      safeGetString(row, 1),
				Grade:        sheet,
				Images:       images,
				Options:      options,
				Answer:       safeGetString(row, 11),
				QuestionType: questionType,
				TypeDesc:     safeGetString(row, 13),
				Analysis:     safeGetString(row, 14),
				Knowledge:    safeGetString(row, 15),
				KnowledgeID:  safeGetString(row, 16),
				Difficulty:   safeGetString(row, 17),
				Remark:       safeGetString(row, 18),
				Tag1:         safeGetString(row, 19),
				Tag2:         safeGetString(row, 20),
			}
			if question.Content == "" {
				t.Logf("sheet %s 行 %d 数据缺少题目", sheet, i+1)
				continue
			}
			if question.Answer == "" {
				t.Logf("sheet %s 行 %d 数据缺少答案", sheet, i+1)
				//return
			}
			allQuestions = append(allQuestions, question)
		}
	}
	fmt.Println("题目读取完成，共处理", len(allQuestions), "个题目")

	err = importQuestions(allQuestions, jzxDao)
	if err != nil {
		t.Fatalf("导入题目失败: %v", err)
	}

	// // 将结果转换为JSON
	// jsonData, err := json.MarshalIndent(allQuestions, "", "  ")
	// if err != nil {
	// 	t.Fatalf("转换为JSON失败: %v", err)
	// }

	// // 将JSON保存到文件
	// outputPath := filepath.Join(homeDir, "Downloads", "questions.json")
	// err = os.WriteFile(outputPath, jsonData, 0644)
	// if err != nil {
	// 	t.Fatalf("保存JSON文件失败: %v", err)
	// }

	//t.Logf("成功处理 %d 个题目，结果已保存到: %s", len(allQuestions), outputPath)
}

type ContentItem struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

type AnswerOption struct {
	AOVal   string        `json:"aoVal"`
	Content []ContentItem `json:"content"`
}

type QuestionFormat struct {
	Content          []ContentItem    `json:"content"`
	Answer           [][]string       `json:"answer"`
	AnswerOptionList [][]AnswerOption `json:"answerOptionList"`
}

var regenerateIds = []string{"2020180", "2030163", "2030177"}

// 判断ID是否在regenerateIds数组中
func isInRegenerateIds(id string) bool {
	for _, regenerateId := range regenerateIds {
		if regenerateId == id {
			return true
		}
	}
	return false
}

func importQuestions(questions []Question, jzxDao *dao.AiJzxQuestionDao) error {
	for _, q := range questions {
		//if q.Grade != "G1" {
		//	continue
		//}
		//if cast.ToInt64(q.ID) <= 310215 {
		//	continue
		//}
		////如果q.Id不在regenerateIds中，则跳过
		//if !isInRegenerateIds(q.ID) {
		//	continue
		//}

		question, err := jzxDao.Find(context.Background(), cast.ToInt64(q.ID))

		//if question != nil && question.ID > 0 {
		//	fmt.Printf("题目 %d 已存在\n", question.ID)
		//	continue
		//}

		// 构建 content 数组
		content := []ContentItem{
			{
				Type:    "text",
				Content: q.Content,
			},
		}

		// 处理图片
		for _, imgBase64 := range q.Images {
			imgURL, err := uploadImageRetry(imgBase64, 5)
			if err != nil || imgURL == "" {
				return fmt.Errorf("上传图片失败: %v", err)
			}
			content = append(content, ContentItem{
				Type:    "image",
				Content: imgURL,
			})
		}

		// 构建答案选项列表
		var answerOptions [][]AnswerOption
		optionLetters := []string{"A", "B", "C", "D", "E", "F"}
		for i, opt := range q.Options {
			if opt != "" {
				optionType := "text"
				//如果opt以http开头，则认为是音频
				if strings.HasPrefix(opt, "https") {
					optionType = "audio"
					//opt = "https://taloversea.blob.core.chinacloudapi.cn/homeworkcorrect101/analysis-editor/upload_file/2a496a90-0955-11f0-803e-538fde01d710104_E_5.mp3"
				} else if len(opt) > 500 {
					optionType = "image"
					url, err := uploadImageRetry(opt, 5)
					if err != nil {
						return fmt.Errorf("上传图片失败: %v", err)
					}
					opt = url
				}
				answerOptions = append(answerOptions, []AnswerOption{
					{
						AOVal: optionLetters[i],
						Content: []ContentItem{
							{
								Type:    optionType,
								Content: opt,
							},
						},
					},
				})
			}
		}

		//构建类别AB-tag
		if q.Tag1 != "" {
			content = append(content, ContentItem{
				Type:    "tag",
				Content: q.Tag1,
			})
		}
		if q.Tag2 != "" {
			content = append(content, ContentItem{
				Type:    "tag",
				Content: q.Tag2,
			})
		}

		// 构建最终的问题格式
		questionFormat := QuestionFormat{
			Content:          content,
			AnswerOptionList: answerOptions,
		}

		for _, answer := range strings.Split(q.Answer, "|") {
			if answer == "" {
				continue
			}
			questionFormat.Answer = append(questionFormat.Answer, strings.Split(answer, ","))
		}

		// 将结果转换为JSON
		jsonData, err := json.Marshal(questionFormat)
		if err != nil {
			return fmt.Errorf("转换为JSON失败: %v", err)
		}

		// 题目存在则修改
		if question != nil && question.ID > 0 {
			question.QType = q.QuestionType
			question.Grade = q.Grade
			question.Subject = subject
			question.Difficulty = cast.ToInt64(q.Difficulty)
			question.Question = string(jsonData)
			question.Knowledge = q.Knowledge
			question.KnowledgeNo = q.KnowledgeID
			question.Note = q.Remark
			err = jzxDao.Update(context.Background(), question)
			if err != nil {
				return fmt.Errorf("更新题目失败: %v", err)
			}
			fmt.Printf("更新题目: %d\n", question.ID)
			continue
		}

		jzxQuestion := &model.AiJzxQuestion{
			ID:          cast.ToInt64(q.ID),
			QType:       q.QuestionType,
			Grade:       q.Grade,
			Subject:     subject,
			Difficulty:  cast.ToInt64(q.Difficulty),
			Question:    string(jsonData),
			Answer:      "",
			Solution:    q.Analysis,
			Knowledge:   q.Knowledge,
			KnowledgeNo: q.KnowledgeID,
			Note:        q.Remark,
			Status:      1,
		}
		err = jzxDao.Create(context.Background(), jzxQuestion)
		if err != nil {
			return fmt.Errorf("创建题目失败: %v", err)
		}

		// 打印结果（实际使用时可能需要保存到文件或发送到API）
		fmt.Printf("处理后的题目: %d\n", jzxQuestion.ID)
	}

	return nil
}

type UploadResponse struct {
	ErrorReason string `json:"error_reason"`
	ErrorMsg    string `json:"error_msg"`
	MetaData    any    `json:"meta_data"`
	TraceID     string `json:"trace_id"`
	ServerTime  int64  `json:"server_time"`
	Data        struct {
		URL string `json:"url"`
	} `json:"data"`
}

type UploadRequest struct {
	Suffix string `json:"suffix"`
	File   string `json:"file"`
}

func uploadImageRetry(image string, retryTimes int) (imgURL string, err error) {
	for retryTimes > 0 {
		imgURL, err = uploadImage(image)
		if err == nil {
			break
		}
		retryTimes--
	}
	return
}

func uploadImage(image string) (string, error) {
	// 准备请求数据
	reqBody := UploadRequest{
		Suffix: "png",
		File:   image,
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("marshal request failed: %v", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", "https://pad-api.thinkbuddy.com/intelligence/api/ai/v1/upload_file", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("create request failed: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("send request failed: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("read response failed: %v", err)
	}

	// 解析响应
	var uploadResp UploadResponse
	if err := json.Unmarshal(body, &uploadResp); err != nil {
		return "", fmt.Errorf("unmarshal response failed: %v", err)
	}

	// 检查响应状态
	if uploadResp.ErrorReason != "success" {
		return "", fmt.Errorf("upload failed: %s", uploadResp.ErrorMsg)
	}

	return strings.ReplaceAll(uploadResp.Data.URL, "https://taloversea.blob.core.windows.net", "https://cfcdn.thinkbuddy.com"), nil
}
