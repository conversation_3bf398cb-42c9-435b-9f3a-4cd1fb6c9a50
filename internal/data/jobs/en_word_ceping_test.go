package jobs

import (
	"encoding/csv"
	"git.100tal.com/znxx_xpp/go-libs/pkg/xexcel"
	"github.com/go-kratos/kratos/v2/log"
	jsoniter "github.com/json-iterator/go"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/data/model"
	"hw-paas-service/internal/data/services/en_correct"
	"hw-paas-service/internal/data/services/hybrid_ocr"
	"os"
	"testing"
)

type FwDwTrace struct {
	Level          string `json:"level"`
	Ts             string `json:"ts"`
	Caller         string `json:"caller"`
	Msg            string `json:"msg"`
	Project        string `json:"project"`
	ServiceID      string `json:"service.id"`
	ServiceName    string `json:"service.name"`
	ServiceVersion string `json:"service.version"`
	TraceID        string `json:"trace_id"`
	SpanID         string `json:"span_id"`
	DayuTraceID    string `json:"dayu_trace_id"`
}

type traceResult struct {
	TraceID         string                           `json:"trace_id"`          //traceID
	UserId          string                           `json:"user_id"`           //用户ID
	DeviceId        string                           `json:"device_id"`         //设备ID
	OcrImageUrl     string                           `json:"ocr_image_url"`     //ocr图片
	OcrReq          *hybrid_ocr.HybridOcrReq         `json:"ocr_req"`           //ocr请求
	OcrResp         *hybrid_ocr.HybridOcrData        `json:"ocr_resp"`          //ocr响应结果
	EnCorrectReq    *en_correct.EnCorrectReplaceReq  `json:"en_correct_req"`    //纠错请求
	EnCorrectRes    *en_correct.EnCorrectReplaceData `json:"en_correct_res"`    //纠错响应结果
	EnCorrectSwitch int32                            `json:"en_correct_switch"` //纠错开关
	Detail          *model.EnWordItem                `json:"detail"`            //单词详情
}

func TestCePingFwTrace(t *testing.T) {

	fn := "pingce_en_word_1731654732.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	// 打开CSV文件
	file, err := os.Open("0102_dw_entry_trace.csv")
	if err != nil {
		log.Fatalf("Error opening CSV file: %s", err)
	}
	defer file.Close()

	// 创建CSV reader
	reader := csv.NewReader(file)

	// 读取所有行
	rows, err := reader.ReadAll()
	if err != nil {
		log.Fatalf("Errorreading rows from CSV: %s", err)
	}

	list := make([]*traceResult, 0)
	for _, row := range rows[1:] { // 假设第一行为标题行，从第二行开始读
		trace := FwDwTrace{}
		err = jsoniter.Unmarshal([]byte(row[1]), &trace)
		if err != nil {
			fileLog.WriteString("FwDwTrace, json Unmarshal error: " + err.Error() + "\n")
			continue
		}
		//msg := strings.Replace(trace.Msg, "dw_finger_words_trace:", "", 1)
		msgInfo := common.LogBackFlowMsgInfo{}
		err = jsoniter.Unmarshal([]byte(trace.Msg), &msgInfo)
		if err != nil {
			fileLog.WriteString("LogBackFlowMsgInfo, json Unmarshal error: " + err.Error() + "\n")
			continue
		}
		item := traceResult{}
		err = jsoniter.Unmarshal([]byte(msgInfo.Message), &item)
		if err != nil {
			fileLog.WriteString("traceResult, json Unmarshal error: " + err.Error() + "\n")
			continue
		}

		list = append(list, &item)
	}

	//写入excel文件
	if err = xexcel.Marshal(list, "Sheet1", "finger_words_trace_0102.xlsx"); err != nil {
		t.Fatal(err)
	}
}
