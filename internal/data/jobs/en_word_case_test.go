package jobs

import (
	"context"
	"encoding/json"
	"fmt"
	"git.100tal.com/znxx_xpp/go-libs/traces/xelastic"
	"git.100tal.com/znxx_xpp/go-libs/util"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/olivere/elastic/v7"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"strings"
	"testing"
)

var hwEnWordCaseMappings = `
{
    "settings": {
        "number_of_shards": 30,
        "number_of_replicas": 2,
        "refresh_interval": "10s",
        "analysis": {
            "analyzer": {
                "word_analyzer": {
                    "type": "custom",
                    "tokenizer": "keyword",
                    "filter": ["lowercase"]
                }
            }
        }
    },
    "mappings": {
        "dynamic": "strict",
        "properties": {
            "id": {
                "type": "integer"
            },
            "word": {
                "type": "text",
                "analyzer": "word_analyzer",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "normalizer": "lowercase"
                    }
                }
            },
            "british": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "american": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "meanings": {
                "type": "nested",
                "properties": {
                    "part_of_speech": {
                        "type": "text",
                        "fields": {
                            "keyword": {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "definition": {
                        "type": "text"
                    }
                }
            },
            "synonyms": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "antonyms": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "sentences": {
                "type": "text"
            },
            "inflections": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "prefix": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "suffix": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "phrases": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "frequency": {
                "type": "long"
            },
            "status": {
                "type": "short"
            },
			"video_url": {
                "type": "text"
            },
			"video_img": {
                "type": "text"
            }
        }
    }
}
`

// 创建测试索引
func TestCreateCaseIndex(t *testing.T) {
	_, confData, logger := initConfig()
	ctx := context.Background()
	esRepo := dao.NewEsRepo(confData, logger)

	// 先删除已存在的索引
	b, err := esRepo.DelIndex(ctx, "oversea_en_word")
	fmt.Printf("delete index res: %v, err: %v\n", b, err)
	// 创建新索引
	res, err := esRepo.CreateIndex(ctx, "oversea_en_word", hwEnWordCaseMappings)
	fmt.Printf("create index res: %v, err: %v\n", res, err)
}

// 测试数据导入
func TestImportCaseData(t *testing.T) {
	ctx := context.Background()
	_, _, logger := initConfig()
	es, err := xelastic.MakeEsClient(&xelastic.ESConfig{
		Host:     "http://**********:9200",
		UserName: "superuser",
		Password: "d737da0188348DD1d^hkda",
	}, log.NewHelper(logger))
	if err != nil {
		log.Fatalf("getEsClient error, [err]:%+v", err)
	}

	// 测试数据
	testWords := []string{
		"Wi-Fi",
		"who'd",
		"centerpiece",
	}

	bulkRequest := es.Bulk()
	for i, word := range testWords {
		wordLower := strings.ToLower(word)
		doc := struct {
			Word      string `json:"word"`
			WordLower string `json:"word_lower"`
		}{
			Word:      word,
			WordLower: wordLower,
		}

		id := util.Md5(wordLower)
		fmt.Printf("i: %d, word: %s, wordLower: %s, id: %s\n", i, word, wordLower, id)

		req := elastic.NewBulkIndexRequest().Index("oversea_en_word").Id(id).Doc(doc)
		bulkRequest = bulkRequest.Add(req)
	}

	if bulkRequest.NumberOfActions() > 0 {
		res, err := bulkRequest.Do(ctx)
		if err != nil {
			t.Fatalf("bulk import failed: %v", err)
		}
		fmt.Printf("bulk import success: %v\n", res)
	}
}

// 测试查询
func TestSearchCaseWord(t *testing.T) {
	ctx := context.Background()
	_, confData, logger := initConfig()
	esRepo := dao.NewEsRepo(confData, logger)

	testCases := []string{
		"Wi-Fi",
		"who'd",
		"centerpiece",
	}

	for _, word := range testCases {
		// 使用精确匹配
		query := elastic.NewBoolQuery().
			Should(
				elastic.NewTermQuery("word.keyword", word),
				elastic.NewTermQuery("word_lower", strings.ToLower(word)),
			)

		_, res, err := esRepo.Search(ctx, "oversea_en_word", query, 0, 10)
		if err != nil {
			t.Errorf("search word %s failed: %v", word, err)
			continue
		}

		fmt.Printf("\nSearch for %s:\n", word)
		for _, hit := range res {
			fmt.Printf("Found: %s\n", hit.Source)
			var doc model.EnWordItem
			_ = json.Unmarshal(hit.Source, &doc)
			fmt.Printf("Found: %s (ID: %d)\n", doc.Word, doc.Id)
		}
	}
}
