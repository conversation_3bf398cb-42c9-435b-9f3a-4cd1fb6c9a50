package jobs

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"git.100tal.com/znxx_xpp/go-libs/pkg/xexcel"
	"git.100tal.com/znxx_xpp/go-libs/traces/xelastic"
	"git.100tal.com/znxx_xpp/go-libs/traces/xhttp"
	"git.100tal.com/znxx_xpp/go-libs/util"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	"github.com/olivere/elastic/v7"
	"github.com/xuri/excelize/v2"
	"golang.org/x/sync/errgroup"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/dao"
	"hw-paas-service/internal/data/model"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/internal/data/services/safety"
	"hw-paas-service/internal/pkg/dayu_trace"
	"hw-paas-service/pkg/zlog"
	"os"
	"strconv"
	"strings"
	"sync"
	"testing"
)

func initConfig() (dataData *data.Data, confData *conf.Data, logger log.Logger) {
	c := config.New(
		config.WithSource(
			file.NewSource("../../../configs"),
		),
	)
	if err := c.Load(); err != nil {
		panic(err)
	}
	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	confData = bc.Data
	zlog.Init("data_test", bc.Log.Filename, int(bc.Log.MaxSize), int(bc.Log.MaxBackup), int(bc.Log.MaxAge), bc.Log.Compress)
	defer func() {
		_ = zlog.Sync()
	}()
	logger = log.With(zlog.NewZapLogger(zlog.STDInstance()),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
		"dayu_trace_id", dayu_trace.TraceID(),
	)
	dataData, _, _ = data.NewData(bc.Data, logger)
	return
}

var hwEnWordMappings = `
{
    "settings": {
        "number_of_shards": 30,
        "number_of_replicas": 2,
        "refresh_interval": "10s"
    },
    "mappings": {
        "dynamic": "strict",
        "properties": {
            "id": {
                "type": "integer"
            },
            "word": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "british": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "american": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "meanings": {
                "type": "nested",
                "properties": {
                    "part_of_speech": {
                        "type": "text",
                        "fields": {
                            "keyword": {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "definition": {
                        "type": "text"
                    }
                }
            },
            "synonyms": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "antonyms": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "sentences": {
                "type": "text"
            },
            "inflections": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "prefix": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "suffix": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "phrases": {
                "type": "text",
                "fields": {
                    "keyword": {
                        "type": "keyword",
                        "ignore_above": 256
                    }
                }
            },
            "frequency": {
                "type": "long"
            },
            "status": {
                "type": "short"
            },
			"video_url": {
                "type": "text"
            }
        }
    }
}
`

var (
	enWordIndex = "oversea_en_word"
)

// 创建海外英文单词索引
func TestEnWordEsIndex(t *testing.T) {
	_, confData, logger := initConfig()
	ctx := context.Background()
	esRepo := dao.NewEsRepo(confData, logger)
	res, err := esRepo.CreateIndex(ctx, enWordIndex, hwEnWordMappings)
	fmt.Printf("res: %v, err: %v\n", res, err)

	//res, err := esRepo.DelIndex(ctx, enWordIndex)
	//fmt.Printf("res: %v, err: %v\n", res, err)
	return
}

// 将国内学习机的英文单词词频数据导入ES，作为纠错词库
func TestEnWordFrequencyInit(t *testing.T) {
	ctx := context.Background()
	_, _, logger := initConfig()
	es, err := xelastic.MakeEsClient(&xelastic.ESConfig{
		//test
		Host:     "http://**********:9200",
		UserName: "superuser",
		Password: "d737da0188348DD1d^hkda",

		//online
		//Host:     "http://************:9200,http://************:9200,http://************:9200",
		//UserName: "superuser",
		//Password: "523e15d21825@531f9b#fc1654e353",
	}, log.NewHelper(logger))
	if err != nil {
		log.Fatalf("getEsClient error, [err]:%+v", err)
	}

	// 打开CSV文件
	file, err := os.Open("en_word_1112.csv")
	if err != nil {
		log.Fatalf("Error opening CSV file: %s", err)
	}
	defer file.Close()

	type Document struct {
		Word      string `json:"word"`
		Frequency int    `json:"frequency"`
	}

	// 创建CSV reader
	reader := csv.NewReader(file)

	// 读取所有行
	rows, err := reader.ReadAll()
	if err != nil {
		log.Fatalf("Errorreading rows from CSV: %s", err)
	}

	bulkRequest := es.Bulk()
	for i, row := range rows[1:] { // 假设第一行为标题行，从第二行开始读
		frequency, _ := strconv.Atoi(row[1])
		doc := Document{
			Word:      row[0],
			Frequency: frequency,
		}

		id := util.Md5(doc.Word)
		fmt.Printf("i: %d, word: %s, id: %s\n", i, doc.Word, id)
		req := elastic.NewBulkIndexRequest().Index(enWordIndex).Id(id).Doc(doc)
		bulkRequest = bulkRequest.Add(req)

		if bulkRequest.NumberOfActions() >= 1000 { // 每累积1000个请求就提交一次批量操作，根据需要调整此数字以平衡速度和内存消耗。
			_, err = bulkRequest.Do(ctx)
			if err != nil {
				fmt.Printf("Bulk request failed: %s\n", err)
				continue
			}
			bulkRequest = es.Bulk() // 重置bulk请求对象以避免重复添加已处理过的action。
		}
	}

	if bulkRequest.NumberOfActions() > 0 { // 确保最后不足批量大小的操作也被提交。
		_, err := bulkRequest.Do(ctx)
		if err != nil {
			fmt.Printf("Final bulk request failed: %s\n", err)
			return
		}
	}

	fmt.Printf("init done\n")
}

// 第四步：将DB英文单词数据导入ES，前三步在 apx_async_task_test.go 中
func TestFlushEnWordToEs(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	es, err := xelastic.MakeEsClient(&xelastic.ESConfig{
		//test
		Host:     "http://**********:9200",
		UserName: "superuser",
		Password: "d737da0188348DD1d^hkda",

		//online
		//Host:     "http://************:9200,http://************:9200,http://************:9200",
		//UserName: "superuser",
		//Password: "523e15d21825@531f9b#fc1654e353",
	}, log.NewHelper(logger))
	if err != nil {
		log.Fatalf("getEsClient error, [err]:%+v", err)
	}
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	// 打开CSV文件
	file, err := os.Open("en_word_1112.csv")
	if err != nil {
		log.Fatalf("Error opening CSV file: %s", err)
	}
	defer file.Close()
	// 创建CSV reader
	reader := csv.NewReader(file)
	// 读取所有行
	fileRows, err := reader.ReadAll()
	if err != nil {
		log.Fatalf("Errorreading rows from CSV: %s", err)
	}
	//单词词频
	wordsMap := make(map[string]int64)
	for _, row := range fileRows[1:] { // 假设第一行为标题行，从第二行开始读
		frequency, _ := strconv.ParseInt(row[1], 10, 64)
		id := util.Md5(row[0])
		wordsMap[id] = frequency
	}

	//hw_en_word, 2025.01.03之前id最大值 52769
	rows, _ := fwDao.FindFingerWords(ctx, &model.HwEnWordSearch{}) //todo: 修改id最大值:52769
	if len(rows) == 0 {
		fmt.Printf("no data\n")
		return
	}

	bulkRequest := es.Bulk()
	for i, row := range rows {

		inflections := make([]string, 0)
		synonyms := make([]string, 0)
		antonyms := make([]string, 0)
		sentences := make([]string, 0)
		phrases := make([]string, 0)
		meanings := make([]model.EnWordMeaning, 0)
		_ = jsoniter.Unmarshal([]byte(row.Inflections), &inflections)
		_ = jsoniter.Unmarshal([]byte(row.Synonyms), &synonyms)
		_ = jsoniter.Unmarshal([]byte(row.Antonyms), &antonyms)
		_ = jsoniter.Unmarshal([]byte(row.Sentences), &sentences)
		_ = jsoniter.Unmarshal([]byte(row.Phrases), &phrases)
		_ = jsoniter.Unmarshal([]byte(row.Meanings), &meanings)
		doc := model.EnWordItem{
			Id:          row.ID,
			Word:        row.Word,
			British:     row.British,
			American:    row.American,
			Inflections: inflections,
			Prefix:      row.Prefix,
			Suffix:      row.Suffix,
			Phrases:     phrases,
			Meanings:    meanings,
			Synonyms:    synonyms,
			Antonyms:    antonyms,
			Sentences:   sentences,
			Frequency:   row.Frequency,
			Status:      row.Status,
			VideoUrl:    row.VideoUrl,
			//VideoImg:    row.VideoImg,
		}

		word := row.Word
		id := util.Md5(word)
		fmt.Printf("i: %d, word: %s, id: %s\n", i, row.Word, id)

		//词频
		if v, ok := wordsMap[id]; ok {
			doc.Frequency = v
		}

		req := elastic.NewBulkIndexRequest().Index(enWordIndex).Id(id).Doc(doc)
		bulkRequest = bulkRequest.Add(req)
		if bulkRequest.NumberOfActions() >= 1000 { // 每累积1000个请求就提交一次批量操作，根据需要调整此数字以平衡速度和内存消耗。
			_, err = bulkRequest.Do(ctx)
			if err != nil {
				fmt.Printf("Bulk request failed: %s\n", err)
				continue
			}
			bulkRequest = es.Bulk() // 重置bulk请求对象以避免重复添加已处理过的action。
		}
	}

	if bulkRequest.NumberOfActions() > 0 { // 确保最后不足批量大小的操作也被提交。
		bulkResponse, err := bulkRequest.Do(ctx)
		fmt.Printf("bulkResponse: %v, err: %v\n", bulkResponse, err)
		if err != nil {
			fmt.Printf("Final bulk request failed: %s\n", err)
			return
		}
	}

	fmt.Printf("init done\n")
}

func TestUpdateEnWord(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	fileHandle, err := os.Open("241127_替换美音版词单.xlsx")
	if err != nil {
		str := fmt.Sprintf("os open err:%v\n", err)
		fmt.Printf(str)
		return
	}
	xlsx, err := excelize.OpenReader(fileHandle)
	if err != nil {
		fmt.Println(err)
	}
	rows, err := xlsx.GetRows("Sheet1")
	if err != nil {
		fmt.Println(err)
	}

	fn := "update_en_word_1731654732.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	eg, _ := errgroup.WithContext(ctx)
	eg.SetLimit(50)
	for i, v := range rows {
		if i == 0 {
			continue
		}
		row := v
		eg.Go(func() error {
			id, _ := strconv.ParseInt(row[0], 10, 64)
			if id == 0 {
				return nil
			}
			american := row[3]
			result, _ := fwDao.FindFingerWords(ctx, &model.HwEnWordSearch{Id: id})
			if len(result) == 0 {
				fileLog.WriteString(fmt.Sprintf("update_id: %d, word: %s, not found\n", id, row[1]))
				return nil
			}
			wordData := result[0]
			if wordData.American == american {
				return nil
			}
			fmt.Printf("i: %d, id: %s, word: %s\n", i, row[0], row[1])
			wordData.American = american
			err = fwDao.UpdateFingerWords(ctx, wordData)
			if err != nil {
				fileLog.WriteString(fmt.Sprintf("update_id: %d, word: %s, err: %v\n", id, row[1], err))
			}
			return nil
		})
	}
	eg.Wait()

	fmt.Printf("update done\n")
}

// 过滤并获取海外不存在的国内学习机的英文单词数据
func TestFilterEnWord(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	fn := "filter_en_word_1731654732.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	// 打开CSV文件
	file, err := os.Open("en_word_1112.csv")
	if err != nil {
		log.Fatalf("Error opening CSV file: %s", err)
	}
	defer file.Close()
	// 创建CSV reader
	reader := csv.NewReader(file)
	// 读取所有行
	fileRows, err := reader.ReadAll()
	if err != nil {
		log.Fatalf("Errorreading rows from CSV: %s", err)
	}
	type wordRow struct {
		Word      string `json:"word"`
		Frequency int64  `json:"frequency"`
	}
	list := make([]*wordRow, 0)
	for i, row := range fileRows[1:] { // 假设第一行为标题行，从第二行开始读
		word := row[0]
		frequency, _ := strconv.ParseInt(row[1], 10, 64)
		//检查db是否存在
		result, _ := fwDao.FindFingerWords(ctx, &model.HwEnWordSearch{Word: word})
		if len(result) > 0 {
			continue
		}
		fmt.Printf("i: %d, word: %s\n", i, word)
		list = append(list, &wordRow{Word: word, Frequency: frequency})
	}
	//写入excel文件
	if err = xexcel.Marshal(list, "Sheet1", "filter_en_word_1219.xlsx"); err != nil {
		t.Fatal(err)
	}

	fmt.Printf("filter done\n")
}

// 海外英文单词安全检查
func TestEnWordSafeCheck(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	var cfg = conf.ServiceBaseConfig{
		Host: "https://overseas-pad.chengjiukehu.com",
	}
	safetySvc := safety.NewSvc(services.NewRestyClient(&cfg), logger)

	fn := "safe_check_en_word_1731654732.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	//hw_en_word, 2025.01.03之前id最大值 52769
	rows, _ := fwDao.FindFingerWords(ctx, &model.HwEnWordSearch{MaxId: 52769}) //todo: 修改id最大值:52769

	type wordRow struct {
		WordId int64           `json:"word_id"`
		Word   string          `json:"word"`
		Row    *model.HwEnWord `json:"row"`
		Text   string          `json:"text"`
		Safety string          `json:"safety"`
	}
	list := make([]*wordRow, 0)

	var mux sync.Mutex

	eg, _ := errgroup.WithContext(ctx)
	eg.SetLimit(50)
	for i, v := range rows {
		if i == 0 {
			continue
		}
		row := v
		eg.Go(func() error {
			text := row.Word + ", " + row.British + ", " + row.American + ", " + row.Meanings + ", " + row.Synonyms + ", " + row.Antonyms + ", " + row.Sentences + ", " + row.Inflections + ", " + row.Prefix + ", " + row.Suffix + ", " + row.Phrases
			isSafe, safeData, _ := safetySvc.CheckContentSafety(ctx, &safety.TextReq{Text: text})
			logStr := fmt.Sprintf("id:%d,word:%s,isSafe:%v\n", row.ID, row.Word, isSafe)
			fileLog.WriteString(logStr)
			fmt.Printf(logStr)
			if !isSafe {
				mux.Lock()
				safeDataB, _ := jsoniter.Marshal(safeData)
				list = append(list, &wordRow{WordId: row.ID, Word: row.Word, Row: row, Text: text, Safety: string(safeDataB)})
				mux.Unlock()
			}
			return nil
		})
	}
	eg.Wait()

	//写入excel文件
	if err := xexcel.Marshal(list, "Sheet1", "safe_check_en_word_1219.xlsx"); err != nil {
		t.Fatal(err)
	}

	fmt.Printf("safe check done\n")
}

func TestCurseWordsImport(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	fileHandle, err := os.Open("curse_words_1007_20250110.xlsx")
	if err != nil {
		str := fmt.Sprintf("os open err:%v\n", err)
		fmt.Printf(str)
		return
	}
	xlsx, err := excelize.OpenReader(fileHandle)
	if err != nil {
		fmt.Println(err)
	}
	rows, err := xlsx.GetRows("Sheet1")
	if err != nil {
		fmt.Println(err)
	}

	fn := "curse_words_1731654732.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	eg, _ := errgroup.WithContext(ctx)
	eg.SetLimit(10)
	for i, v := range rows {
		if i == 0 {
			continue
		}
		row := v
		fmt.Printf("i:%d, word:%v\n", i, row[0])
		eg.Go(func() error {
			word := strings.ToLower(row[0])
			if word == "" {
				return nil
			}
			//查找db是否存在
			result, _ := fwDao.FindFingerWords(ctx, &model.HwEnWordSearch{Word: word})
			if len(result) > 0 {
				//更新为违禁状态
				wordData := result[0]
				if wordData.Status == model.EnWordStatusCurse {
					return nil
				}
				wordData.Status = model.EnWordStatusCurse
				err = fwDao.UpdateFingerWords(ctx, wordData)
				if err != nil {
					fileLog.WriteString(fmt.Sprintf("curse_update_id: %d, word: %s, err: %v\n", wordData.ID, wordData.Word, err))
				}
				return nil
			}
			//插入db
			err = fwDao.Create(ctx, &model.HwEnWord{
				Word:   word,
				Status: model.EnWordStatusCurse, //违禁词
			})
			if err != nil {
				fileLog.WriteString(fmt.Sprintf("curse_insert_word: %s, err: %v\n", word, err))
			}
			return nil
		})
	}
	eg.Wait()
	fmt.Printf("curse insert done\n")
}

func TestEnWordExportExcel(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	rows, _ := fwDao.FindFingerWords(ctx, &model.HwEnWordSearch{})

	//写入excel文件
	if err := xexcel.Marshal(rows, "Sheet1", "all_en_word_1225.xlsx"); err != nil {
		t.Fatal(err)
	}

	fmt.Printf("export done\n")
}

type Gpt4oResponse struct {
	ID                string `json:"id"`
	Object            string `json:"object"`
	Model             string `json:"model"`
	Created           int    `json:"created"`
	SystemFingerprint string `json:"system_fingerprint"`
	Usage             struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		Logprobs     interface{} `json:"logprobs"`
		FinishReason string      `json:"finish_reason"`
	} `json:"choices"`
}
type Gpt4oRequest struct {
	Model    string         `json:"model"`
	Messages []Gpt4oMessage `json:"messages"`
}
type Gpt4oMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type Gpt4oContent struct {
	Pronunciation struct {
		British  string `json:"British"`
		American string `json:"American"`
	} `json:"pronunciation"`
	Inflections    []string `json:"inflections"`
	RootVariations struct {
		Prefix string `json:"prefix"`
		Suffix string `json:"suffix"`
	} `json:"root_variations"`
	Meanings  []Gpt4oContentMeaning `json:"meanings"`
	Synonyms  []string              `json:"synonyms"`
	Antonyms  []string              `json:"antonyms"`
	Phrases   []string              `json:"phrases"`
	Sentences []string              `json:"sentences"`
}
type Gpt4oContentMeaning struct {
	PartOfSpeech string `json:"part_of_speech"`
	Definition   string `json:"definition"`
}

var prompt = `You are now an experienced education expert and lexicographer working on a dictionary for children under 12 years old. After you are given a word, you should output the returned information fully containing the following details in JSON format.

British pronunciation [using International Phonetic Alphabet (IPA)],
American pronunciation [using International Phonetic Alphabet (IPA)],
The word's inflections (plural forms of countable nouns, past tense/past participle of verbs, present participle of verbs, comparative/superlative degrees of adjectives/adverbs, adverb forms of adjectives, etc., just show the words without indicating their forms and if none, A single element is displayed as "",
The word's root variations (prefixes, suffixes),  if none, A single element is displayed as "".

In cases of polysemy, each meaning should include its part of speech (abbreviated in English, like c.n. / vt., etc.) and its definition based on different parts of speech (simplified explanation that children can understand). Next, provide the following information for the most common meaning: two synonyms(if none, A single element is displayed as ""),two antonyms (if none, A single element is displayed as ""), two phrases containing the word, two sentences containing the word. 

Ensure all content avoids any inappropriate elements for children or political elements. If the word has multiple meanings, please display them all and illustrate them clearly.

Provide an example in JSON format. All your output formats must follow this example, Brackets cannot be changed. The input word for this example is 'bright'.
{
  "pronunciation": {
    "British": "brɑːt",
    "American": "bræt"
   },
  "inflections": [
    "brighter",
    "brightest",
    "brightly"
  ],
  "root_variations":  {
    "prefix": "",
    "suffix": ""
   },
  "meanings": [
    {
      "part_of_speech": "adj.",
      "definition": "Shining with lots of light."
    },
    {
      "part_of_speech": "adj.",
      "definition": "Full of hope and happiness."
    }
  ],
  "synonyms": [
    "shiny",
    "cheerful"
  ],
  "antonyms": [
    "dull",
    "gloomy"
  ],
  "phrases": [
    "bright future",
    "bright idea"
  ],
  "sentences": [
    "The sun is very bright today.",
    "She has a bright smile that makes everyone happy."
  ]
}

The input word for this example is 'inglorious'.
{
  "pronunciation": {
    "British": "ɪnˈɡlɔːrɪəs",
    "American": "ɪnˈɡlɔːriəs"
   },
  "inflections": [
    ""
  ],
  "root_variations":  {
    "prefix": "in-",
    "suffix": "-ious"
   },
  "meanings": [
    {
      "part_of_speech": "adj.",
      "definition": "Not bringing honor or fame"
    },
    {
      "part_of_speech": "adj.",
      "definition": "shameful."
    }
  ],
  "synonyms": [
    "dishonorable",
    "shameful"
  ],
  "antonyms": [
    "honorable",
    "respectable"
  ],
  "phrases": [
    "an inglorious defeat",
    "an inglorious end"
  ],
  "sentences": [
    "The team suffered an inglorious defeat in the final match.",
    "His actions led to an inglorious end to his career."
  ]
}`

// 同步方案， 第2步：格式化单词数据结构基于GPT-4o
func TestFormatEnWordBaseGpt4o(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	gpt4oDao := dao.NewGpt4oWordsDao(dataData, logger)
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	//fn := "base_en_word_" + strconv.FormatInt(time.Now().Unix(), 10) + ".log"
	fn := "base_en_word_0320.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	rows, _ := gpt4oDao.FindGpt4oWords(ctx, &model.HwEnWordGpt4oSearch{})
	if len(rows) == 0 {
		fmt.Printf("no data\n")
		return
	}
	fmt.Println("len:", len(rows))
	eg, _ := errgroup.WithContext(ctx)
	eg.SetLimit(100)
	for _, row := range rows {
		word := row.Word
		fmt.Printf("id: %d, word: %s\n", row.ID, word)

		eg.Go(func() error {
			err = addEnWord(ctx, fwDao, word, row.Content)
			if err != nil {
				fileLog.WriteString(fmt.Sprintf("gpt4o_id: %d, word: %s\nerr: %v\n", row.ID, word, err))
			}
			return nil
		})
	}
	eg.Wait()

	fmt.Printf("create done\n")
}
func TestFormatEnWordBaseGpt4oV2(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	gpt4oDao := dao.NewGpt4oWordsDao(dataData, logger)
	fwDao := dao.NewFingerWordsDao(dataData, logger)

	//fn := "base_en_word_" + strconv.FormatInt(time.Now().Unix(), 10) + ".log"
	fn := "base_en_word_0320.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	rows, _ := gpt4oDao.FindGpt4oWords(ctx, &model.HwEnWordGpt4oSearch{})
	if len(rows) == 0 {
		fmt.Printf("no data\n")
		return
	}
	fmt.Println("len:", len(rows))
	for _, row := range rows {
		fmt.Printf("id: %d, word: %s\n", row.ID, row.Word)
		err = addEnWord(ctx, fwDao, row.Word, row.Content)
		if err != nil {
			fileLog.WriteString(fmt.Sprintf("gpt4o_id: %d, word: %s\nerr: %v\n", row.ID, row.Word, err))
			fmt.Printf("gpt4o_id: %d, word: %s\nerr: %v\n", row.ID, row.Word, err)
			return
		}
	}

	fmt.Printf("create done\n")
}
func addEnWord(ctx context.Context, fwDao *dao.FingerWordsDao, word, str string) error {
	//查找db是否存在
	rows, err := fwDao.FindFingerWords(ctx, &model.HwEnWordSearch{Word: word})
	if err != nil {
		return err
	}
	if len(rows) > 0 {
		fmt.Println("已存在:", word)
		return nil
	}

	var content Gpt4oContent
	err = jsoniter.Unmarshal([]byte(str), &content)
	if err != nil {
		fmt.Printf("unmarshal content, word: %s, err:%v\n", word, err)
		return err
	}

	// 写入DB
	var british, american, prefix, suffix string
	british = content.Pronunciation.British
	american = content.Pronunciation.American
	prefix = content.RootVariations.Prefix
	suffix = content.RootVariations.Suffix

	meanings, _ := jsoniter.Marshal(content.Meanings)
	synonyms := make([]string, 0)
	for _, synonym := range content.Synonyms {
		if synonym != "" {
			synonyms = append(synonyms, synonym)
		}
	}
	synonymsB, _ := jsoniter.Marshal(synonyms)
	antonyms := make([]string, 0)
	for _, antonym := range content.Antonyms {
		if antonym != "" {
			antonyms = append(antonyms, antonym)
		}
	}
	antonymsB, _ := jsoniter.Marshal(antonyms)
	sentences := make([]string, 0)
	for _, sentence := range content.Sentences {
		if sentence != "" {
			sentences = append(sentences, sentence)
		}
	}
	sentencesB, _ := jsoniter.Marshal(sentences)
	inflections := make([]string, 0)
	for _, inflection := range content.Inflections {
		if inflection != "" {
			inflections = append(inflections, inflection)
		}
	}
	inflectionsB, _ := jsoniter.Marshal(inflections)
	phrases := make([]string, 0)
	for _, phrase := range content.Phrases {
		if phrase != "" {
			phrases = append(phrases, phrase)
		}
	}
	phrasesB, _ := jsoniter.Marshal(phrases)
	err = fwDao.Create(ctx, &model.HwEnWord{
		Word:        word,
		British:     british,
		American:    american,
		Meanings:    string(meanings),
		Synonyms:    string(synonymsB),
		Antonyms:    string(antonymsB),
		Sentences:   string(sentencesB),
		Inflections: string(inflectionsB),
		Prefix:      prefix,
		Suffix:      suffix,
		Phrases:     string(phrasesB),
		Status:      model.EnWordStatusWaitAudit, //待机审
	})

	return err
}

// 同步方案，第1步：海外英文单词GPT-4o原数据
func TestEnWordGpt4oOrigin(t *testing.T) {
	ctx := context.Background()
	dataData, _, logger := initConfig()
	client := xhttp.MakeHttpClient(log.NewHelper(logger)).SetBaseURL("http://ai-service-test.tal.com/")
	gpt4oDao := dao.NewGpt4oWordsDao(dataData, logger)

	//fn := "en_word_gpt4o_" + strconv.FormatInt(time.Now().Unix(), 10) + ".log"
	fn := "en_word_gpt4o_1731654732.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()

	fileHandle, err := os.Open("base_en_word_1101_13301.xlsx")
	if err != nil {
		str := fmt.Sprintf("os open err:%v\n", err)
		fmt.Printf(str)
		return
	}
	xlsx, err := excelize.OpenReader(fileHandle)
	if err != nil {
		fmt.Println(err)
	}
	rows, err := xlsx.GetRows("Sheet1")
	if err != nil {
		fmt.Println(err)
	}

	eg, _ := errgroup.WithContext(ctx)
	eg.SetLimit(10)
	for i, row := range rows {
		if i == 0 {
			continue
		}
		if len(row) < 2 {
			continue
		}
		word := row[0]
		fmt.Printf("i: %d, word: %s\n", i, word)

		eg.Go(func() error {
			_, err = addEnWordGtp4o(ctx, client, gpt4oDao, word)
			if err != nil {
				fileLog.WriteString(fmt.Sprintf("word: %s\nerr: %v\n", word, err))
			}
			return nil
		})
	}

	eg.Wait()

	fmt.Printf("create done\n")
}

func TestEnWord0319(t *testing.T) {
	ctx := context.Background()
	fn := "en_word_gpt4o_0319.log"
	fileLog, err := os.Create(fn)
	if err != nil {
		fileLog, _ = os.Open(fn)
	}
	defer fileLog.Close()
	dataData, _, logger := initConfig()
	client := xhttp.MakeHttpClient(log.NewHelper(logger)).SetBaseURL("http://ai-service-test.tal.com/")
	gpt4oDao := dao.NewGpt4oWordsDao(dataData, logger)
	words := "while\nwatercolor\nvisualisation\nvisualise\nvictimise\nvictimisation\nvigor\nverandah\nvapor\nunfavorable\nunauthorised\ntire\ntumor\ntraveler\ntraveling\ntheater\nsulfur\nsplendor\nspecter\nspecialty\nsouthwest\nsoutheast\nsomber\nskillful\nself-defense\nrigour\nrumor\npajamas\nprogram\npretense\npractice\nplow\nparlor\npanelist\npediatric\npediatrician\norganise\norganisation\norganising\norganised\norganisational\noptimise\norganiser\noffence\nestrogen\nodor\noffense\nnon-existent\nneighboring\nneighbor\nneighborhood\nmustache\nmold\nhighway\nmodeling\nmisdemeanor\nmillimeter\nmedalist\nmeter\nmeager\nmarvelous\nmaneuver\nluster\ntruck\nliter\nlicense\nliberalisation\nleukemia\nlegitimise\nlawmaking\nlegalise\nland use\nlaborer\nkilometer\njewelry\njeweler\nlabor\ninstill\ninstallment\nhumor\nhumourous\nhonorable\nhonored\nhonor\nhomegrown\nharbor\ngray\nglobalisation\ngeneralise\ngeneralisation\ngeneralised\nfulfillment\nfulfill\nflavor\nfervor\nfiber\nfavorably\nfavorable\nfavor\nfavored\nfavorite\nenroll\nenrollment\nendeavor\nencyclopedia\nemail\ndraft\ndonut\ndiarrhea\ndialog\ndestabilise\ndemocratisation\ndemeanor\ndefense\ndecentralised\ncounseling\ncouncilor\ncounselor\ncozy\ncolorful\ncolor\nclamor\nchilli\ncheck\ncenterpiece\ncenter\ncentimeter\ncatalog\ncandor\ncaliber\nburned\ncookie\nbehavior\nbehavioral\narmored\narmor\narcheology\narcheological\narcheologist\narbor\nappetiser\napologise\nany more\nflat\nantiwar\nantidepressant\nartifact\nanalog\nanalyze\nanesthesia\namphitheater\namong\nanemia\namid\naluminum\nafterward\nesthetic\nesthetics\nairplane\nadvisor\nadvert\nacknowledgment\nskepticism\nskeptic\nskeptical\nsavior\nsavor\ngasoline\nworshiping\nworshiped\nwhiskeys\nwatercolors\nwatercoloring\nverandas\nvapors\ntires\ntumors\ntrolleys\ntravelers\ntraveled\ntotaled\ntotaling\ntotalled\ntotalling\ntravelled\ntheaters\nswiveling\nsymposia\nswiveled\nsupernovas\nsummarises\nsquirrelling\nsquirrelled\nspelled\nspilled\nsped\nspiralled\nspiralling\nspecialises\nspecialising\nsoutheastward\nsoutheastern\nspecialties\nskillfulness\nskillfully\nsignaled\nsignaling\nshovelled\nshovelling\nshoveled\nshoveling\nscrutinising\nscrutinised\nrival\nrevolutionised\nrevolutionising\nrevitalising\nrevitalised\nrevelling\nrumouring\nrumoured\nrumors\nreorganising\nreorganised\nremodelling\nreorganises\nremodelled\nrecognising\nrealisation\nrealised\nrealising\nquarrelled\nquarrelling\nprograms\npreemptively\npracticed\npracticing\nplowing\nplowed\nplateaus\nplows\nperiling\nperiled\nparlors\npanelists\nparalyzing\nparalyzed\noffenses\nodors\nnortheastern\nneighbors\nneighbored\nneighborhoods\nmustaches\nmolding\nmolds\nmolded\nmottos\nmosquitos\nmodeled\nmillimeters\nmementos\nmedalists\nmeters\nmarveled\nmarveling\nmeagerness\nmarvelously\nmaneuvering\nmaneuvers\nmanifestos\nmaneuvered\nliters\nlabelling\njewelling\njewelled\nlabors\nlaboring\nlabored\ninstallments\ninitialling\ninitialled\ninnuendos\nhumoring\nhumors\nhumored\nhonoring\nhonors\nharbored\nharbors\nharboring\ngrayest\nglamours\nglamoring\nfunneling\nfunneled\nfueling\nfueled\nflavoring\nflavored\nflavors\nfibers\nfavors\nfavorites\nfavoring\nequaling\nequaled\nendeavors\nendeavored\nendeavoring\nencyclopedias\ndialing\ndialled\ndialogs\ndestabilising\ndestabilised\ndefence\ndefenses\ndefencing\ndefenced\ncrueler\ncruelest\ncounselling\ncoziest\ncounselors\ncozily\ncozier\ncolored\ncoloring\ncolors\nclamoring\nclamors\nclamored\nchecks\nchanneling\nchanneled\ncenterpieces\ncentering\ncentered\ncenters\ncataloged\ncataloging\ncatalogs\ncarolling\ncanceling\ncapabler\ncanceled\ncalibers\nbureaus\nbenefitting\nbusing\nbused\narbors\nappalls\napparelling\napparelled\nanalyzing\nanalyzes\nanalyzed\namphitheaters\nairplanes\nacknowledgments\nskeptics\nskeptically\nsavors\nsaviors\nsavored\nsavoring\ndecimeter\nsterilising\nmoulds\ntantalise\ndepartmentalise\nurbanises\nlabeling\nlustre\ncipher\ncivilize\nneighbours\nindustrializes\nscrutinize\nvalour\nfibres\nwilful\nsentineled\nmoldy\nmolt\nsepulcher\naunty\nuntrammelled\ncentres\nmoults\nidealise\nsummarizes\ncannibalising\nlouvers\ndeviling\nmeowing\ndiscolored\nemphasized\nrubles\ngoiter\ntranquilize\nsympathizes\ncivilizing\ncataloguing\ncolonizes\nlaborious\ntravellers\ngreyest\nmarvellous\ncatalogue\ntranquility\nartefacts\nharbour\nreverie\nleafleted\nquarreling\ndyspnoea\npaneling\nsalvos\nvendor\ncharacterizing\nsquirreling\nspecializations\neggplants\nchaperon\npaleontology\nparceled\nprivatization\nfairies\nmiter\netiologies\netiology\ninterpretive\nzip codes\ndecimeters\nnormalized\ncaravaned\ndemeanors\noceanariums\nrecognized\npencilling\nanalogs\nsummarize\nsatirises\nvandalised\ninstilled\nsensitises\ncolour\noedema\nmagnetisation\njeopardizes\nampul\nleveled\nhoveled\ntabor\ncentralise\nenrol\nterrorized\nhybridised\nhomogenise\ntyrannise\nflavorings\nrevolutionises\napologize\nwindshield\ndraughtiest\nteetotalling\nsynchronisations\ntantalised\nbrainstorms\npulverisations\nbeautified\ncentimeters\nmilliliter\nrealize\nindicting\nsignaler\nhonorably\nspecialize\nexorcizes\nimmortalises\nmemorises\nlustring\nwagons\nrouble\ngreyer\nfulfilments\nanaesthesia\ncarburetor\nindustrialize\npractices\nthreatened\ntensioning\njeweled\nunraveling\ndispatch\nvisualizes\nfetuses\ncentilitre\nantagonising\nhaemorrhaged\nwaggoning\nratable\nruble\nrancour\ncolours\nteetotalled\nhandselling\nexorcisized\ninquire\nneighbourhood\nrancor\nemphasize\nraveling\nnevus\nprefaced\nuntying\nrealized\nvapours\nlaborers\nform\nauroras\nphotos\nmemorized\npurled\ncharacterize\ndriveled\nworshipers\nspecializes\npyorrhea\nbriquets\nneighbour\ngrey\norganize\nendeavour\ndiscolors\nsympathize\ncudgeling\nemphasizing\nice-cream\nlaboured\nfinalizes\nminimizing\nfossilised\nsplendours\nappareling\ntheorizes\narbours\ndigitising\nterrorizes\nfinalized\nphilosophising\nurbanise\narmours\ninternalises\ncentilitres\ncombating\nsceptre\ndiscolour\norganization\nfamiliarise\nwagon\nstandardisations\nvisualized\nrevitalisation\nenrolls\ncapitalisations\nhumourist\ninterstice\ncalipers\ndiscoloring\ndenouements\nochers\nidyl\nphonies\nremold\npunching bags\ncriticizing\nlicorice\nstenciling\nsaltpeter\norganizer\ndependents\nbandanas\nsnickering\nneighbourhoods\ncozies\nexorcises\ncheckbook\nremodeling\nself-centered\ndiscolor\nmemorizes\nginkgos\nmoldier\nplowboy\nkilogram\ncolorless\njeweller\nclamoured\nexorcise\nanaemic\nepicentre\nsensitise\nrumored\nurbanising\nantagonise\ngrayish\njeopardize\nprioritises\nquarreled\noverstaffing\nspecialized\nstabiliser\nboweling\nhaemoglobin\narmour\nmicrofibres\nbarreling\nstenciled\nmeowed\ncriteria\naura\nvapour\nploughed\nsemi-final\nbenefited\naggrandising\nhumours\ntheorized\nploughs\nworshiper\ndyked\ndysmenorrhoea\nburglarizes\nrealization\nmailbox\nyogurt\nconnection\nremolded\npeddlers\njudgment\neggplant\nrefueling\nminimize\nglamorous\nnickeled\nspecializing\nstabilizes\nprivatizations\nyogurts\nsympathizing\nharmonised\npatronise\nmoulted\ntheorize\ngonorrhoea\nsavoured\ncolonizing\nimmunizes\nmolts\nmolting\nfavourable\nlustred\nempathises\norganized\nchiseling\nsavories\npublicize\ntoweling\noctopuses\nestivation\ncolonize\nterrorizing\npummeling\nmollusks\nocher\nscrutinized\ncriticizes\nlibeled\ninquiring\nbest-sellers\ninquired\nleveling\nbonuses\npedestaled\nfantasises\nwillful\nformalize\nlicenses\nedemas\nup-to-date\ncyber-cafe\nleukaemia\ncandour\nright-hand\nlitre\nminimizes\nmouldy\nnorthward\nclamouring\ncivilises\nvaporing\ncivilized\ncaroled\nlearned\nkilometers\ninstall\nfinalize\nrivaled\nfertilizers\nmobilize\nsoliloquise\nfeces\nsunburnt\nvapoured\nteetotaled\nraveled\nutilizes\nploughshares\ncaroling\ncarbonados\npretenses\npedestaling\nwomanises\nenergises\nunrivaled\nunraveled\nzebra-crossing\nteaseled\nchimeras\nvandalising\nmobile phone\nreal estate\nrumours\nfull-time\ngrayed\ncoca-cola\ngrams\ngraying\nenameled\nutilize\ncomputerize\nbain-marie\ngraveling\ninquires\nharboured\nenameling\naging\nmultistory\njudgments\ntrapezoids\nenrollments\nsavory\nmemorize\nstories\nsceptres\ntunneled\ndrafted\nharbouring\ntheorizing\nanesthetic\nmoldiest\ncivilizes\neconomized\nserialising\narchipelagos\nby-products\nfetus\norganizing\nsensitising\nmulticolored\ncriticized\nempathised\nbevelling\nshriveled\nmetred\nhumorless\nrigor\nrefueled\nradicalise\nmiters\nteetotaling\nbiased\ncentiliters\nmailman\nbiasing\ngrays\ncivilization\nstabilized\nvice chancellor\nmulti-gym\nbehaviorism\ntunneling\nanesthetics\nsaber\nvisualizing\nmodelings\nglamors\nwhiskey\nmillimetre\nsnags\nmonopolising\nspecialization\nrumormonger\nutilized\ncolorings\nzipcode\ncarburetors\nhaemorrhaging\norganizes\nappareled\nfervour\njail\nfavour\nlibeling\nvitrioled\nimmunized\nscrutinizes\ngiggle\ngreyed\nfinalizing\ncentre\nomelets\nellipses\nhood\nunspoilt\npistoled\nasses\nplowboys\nnickeling\nrumoring\npedalos\ncounseled\nmeows\nicon\nsalable\nzucchini\nglamor\nringroads\npedaled\npublicizes\nexorcize\nflanneling\nstabilize\nmedaling\nmobilizes\nvillainies\naluminium\ndualing\nwoolens\nutilizing\nparalyze\ncounselings\ncomputerizes\narmoury\nwoolen\nseance\nremolds\nlunging\ncharacterizes\nstory\nmoustache\nsledding\nstabilizing\ntasseling\nlitres\njewellers\ntinselled\nparlour\ninstills\nvisualize\nrumour\nladybugs\ncentred\nsubsidize\nsocialized\nshort-tempered\nflavours\npublicizing\ncolourings\nsocializing\ntranquillity\nwrench\nwrenches\nclamour\ncheckbooks\nsubsidized\nhalos\nsentineling\ncharacterized\nshort-sighted\nneighbouring\nhaemoptyses\nindustrializing\ndialogues\nfairy tale\nchiseled\ncriticize\nsocializes\nnormalizing\nmillimetres\norganizations\ncomputerized\nrealizations\npublicized\nflanneled\ngrovelling\nsynesthesia\nurbanization\nfertilizer\ninstalls\ngoiters\npen pal\nnormalize\nrose-colored\nscrutinizing\nmincemeat\nshops\ngamboling\nleukocyte\nsubtotalling\nplimsoles\npaneled\ndickie\norganizers\nwell-being\ndeviled\nmobilized\nmarshaling\ntasseled\ncaldron\ngaveled\ncentiliter\nvaporized\noesophaguses\naristocracies\nfullness\ncigarettes\ncombated\nqueuing\nauntie\ndaydreamed\nsniveling\njeopardizing\nvitiligos\nrigors\npenciled\nblondes\nomelettes\nwomanising\nsaviours\nsubsidizes\nreassert\npummeled\ncolourful\npenpal\nparlours\ndepartmentalising\nlouvres\nartifacts\nmanoeuvre\ncomputerises\nhigh-toned\nsplendors\ngambolling\ndiarising\nfeminises\nstomach-aches\nimmunizing\nimmunize\ntumours\nunrecognisable\ndialed\nesthete\nanaesthetist\ntroweled\nsnivelled\nparceling\ndrafting\ncauldron\nmobilizing\nsepulchers\nfranchising\ncentimetre\nsynchronise\nbehaviours\ninternationalisation\nbehaviors\nsmoldering\naetiologies\nremolding\nnormalizes\nhanselled\nremodeled\ngram\nsmolders\nchili\nkilometre\natomise\nrecognize\nkilograms\nsouvenired\ntinseled\nprominences\nanalyser\nsympathized\nsubtotalled\nesthetes\nexorcising\namortisations\nmicrofibre\npasteurised\nsynchronised\nindustrialises\nendeavouring\ncallusing\nmillilitres\nbarreled\nneutralises\nvictimisations\ntantalising\ninquiry\nodouriferous\njeopardized\nsabers\nmedieval\nsquirreled\njeweling\nbuses\nsmolder\ninstilling\ndriveling\nmeow\ngeneralize\nbuilt-in\nterrorize\nU.A.S\nleukorrhea\nmemorizing\ncolonized\nmonetising\ngypsies\nsatirised\ncomputerizing\nsummarizing\ncatalyses\nsummarized\ndeary\nodour\nprologing\nginkgo\nemphasizes\ntoweled\ntenderise\nindustrialized"
	wordsArr := strings.Split(words, "\n")
	fmt.Println("len:", len(wordsArr))
	var m = make(map[string]struct{})
	for i, word := range wordsArr {
		fmt.Printf("i: %d, word: %s\n", i, word)
		if _, ok := m[word]; ok {
			fmt.Println("重复：", word)
			continue
		}
		m[word] = struct{}{}
		_, err = addEnWordGtp4o(ctx, client, gpt4oDao, word)
		if err != nil {
			fileLog.WriteString(fmt.Sprintf("word: %s err: %v\n", word, err))
			fmt.Println("err:", err)
			return
		}
	}
}
func addEnWordGtp4o(ctx context.Context, client *resty.Client, gpt4oDao *dao.Gpt4oWordsDao, word string) ([]string, error) {
	//查找db是否存在
	rows, _ := gpt4oDao.FindGpt4oWords(ctx, &model.HwEnWordGpt4oSearch{Word: word})
	if len(rows) > 0 {
		fmt.Println("已存在：", word)
		return nil, nil
	}

	// 请求4o服务
	messages := make([]Gpt4oMessage, 0)
	messages = append(messages, Gpt4oMessage{
		Role:    "system",
		Content: prompt,
	})
	messages = append(messages, Gpt4oMessage{
		Role:    "user",
		Content: word,
	})
	req := Gpt4oRequest{
		Model:    "gpt-4o",
		Messages: messages,
	}
	resp, err := client.R().
		SetHeaders(map[string]string{
			"Content-Type":  "application/json",
			"Authorization": "Bearer 1000080563:fae79a1fa9c01a965f71cd23b22b4762",
		}).
		SetBody(req).SetResult(nil).Post("/openai-compatible/v1/chat/completions")
	if err != nil {
		fmt.Printf("request gpt-4o, word: %s, err:%v\n", word, err)
		return nil, err
	}
	var gpt4oResp Gpt4oResponse
	err = jsoniter.Unmarshal(resp.Body(), &gpt4oResp)
	if err != nil {
		fmt.Printf("unmarshal gpt-4o, word: %s, err:%v\n", word, err)
		return nil, err
	}
	if len(gpt4oResp.Choices) == 0 {
		fmt.Printf("gpt-4o response empty, word: %s\n", word)
		return nil, err
	}
	str := gpt4oResp.Choices[0].Message.Content
	if !strings.Contains(str, "{") {
		errStr := fmt.Sprintf("gpt-4o response not contain {, err: %s\n", str)
		fmt.Printf(errStr)
		return nil, errors.New(errStr)
	}
	str = strings.Replace(str, "```json", "", -1)
	str = strings.Replace(str, "```", "", -1)

	err = gpt4oDao.Create(ctx, &model.HwEnWordGpt4o{
		Word:    word,
		Content: str,
	})

	return nil, err
}

func TestGpt4o(t *testing.T) {
	_, _, logger := initConfig()
	client := xhttp.MakeHttpClient(log.NewHelper(logger)).SetBaseURL("http://ai-service-test.tal.com/")

	word := "brighter"

	// 请求4o服务
	messages := make([]Gpt4oMessage, 0)
	messages = append(messages, Gpt4oMessage{
		Role:    "system",
		Content: prompt,
	})
	messages = append(messages, Gpt4oMessage{
		Role:    "user",
		Content: word,
	})
	req := Gpt4oRequest{
		Model:    "gpt-4o",
		Messages: messages,
	}
	resp, err := client.R().
		SetHeaders(map[string]string{
			"Content-Type":  "application/json",
			"Authorization": "Bearer 1000080563:fae79a1fa9c01a965f71cd23b22b4762",
		}).
		SetBody(req).SetResult(nil).Post("/openai-compatible/v1/chat/completions")
	if err != nil {
		fmt.Printf("request gpt-4o, word: %s, err:%v\n", word, err)
		return
	}
	var gpt4oResp Gpt4oResponse
	err = jsoniter.Unmarshal(resp.Body(), &gpt4oResp)
	if err != nil {
		fmt.Printf("unmarshal gpt-4o, word: %s, err:%v\n", word, err)
		return
	}
	if len(gpt4oResp.Choices) == 0 {
		fmt.Printf("gpt-4o response empty, word: %s\n", word)
		return
	}
	str := gpt4oResp.Choices[0].Message.Content
	str = strings.Replace(str, "```json", "", -1)
	str = strings.Replace(str, "```", "", -1)

	fmt.Printf("str: %s\n", str)
}
