package jobs

import (
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"testing"
	"time"

	"github.com/xuri/excelize/v2"
)

var imageWidthMap = map[string]int{
	"cover_img":       600,
	"article_content": 1200,
}

// 提取图片URL的正则表达式,地址以图片后缀结尾
var imageURLRegex = regexp.MustCompile(`\(\s?http[^)]+\.(jpg|jpeg|png|gif|webp|svg|ico|bmp|tiff|tif|heic|heif|avif|webp|svg|ico|bmp|tiff|tif|heic|heif|avif)\s?\)`)

// 下载图片并转换为base64
func downloadImageToBase64(url string) (string, error) {
	// 发送HTTP GET请求
	resp, err := http.Get(url)
	if err != nil {
		return "", fmt.Errorf("下载图片失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("下载图片失败，状态码: %d", resp.StatusCode)
	}

	// 读取图片数据
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取图片数据失败: %v", err)
	}

	// 转换为base64
	return base64.StdEncoding.EncodeToString(imageData), nil
}

// 添加CDN参数到URL
func addCDNParams(url string, width int) string {
	// 如果URL已经包含CDN参数，则直接返回
	if strings.Contains(url, "/cdn-cgi/image/") {
		return url
	}

	// 在域名后添加CDN参数
	parts := strings.SplitN(url, "/", 4)
	if len(parts) < 4 {
		return url
	}

	return fmt.Sprintf("%s//%s/cdn-cgi/image/width=%d/%s", parts[0], parts[2], width, parts[3])
}

// 替换图片URL
func replaceImageURLs(content string, key string) (string, error) {
	matches := imageURLRegex.FindAllString(content, -1)
	if len(matches) == 0 {
		return content, nil
	}

	result := content
	for _, match := range matches {
		// 去掉括号
		url := strings.Trim(match, "()")
		// 去掉两头的空格
		url = strings.TrimSpace(url)
		fmt.Printf("处理图片URL: %s\n", url)

		// 检查是否是 cfcdn.thinkbuddy.com 域名，如果是则跳过替换
		if strings.Contains(url, "cfcdn.thinkbuddy.com") {
			fmt.Printf("跳过 cfcdn.thinkbuddy.com 域名的图片下载上传，仅添加CDN参数: %s\n", url)
			// 添加CDN参数
			newURL := addCDNParams(url, imageWidthMap[key])
			fmt.Printf("CDN参数添加完成，新URL: %s\n", newURL)

			if key == "cover_img" {
				return newURL, nil
			}

			// 替换原URL
			result = strings.Replace(result, match, "("+newURL+")", 1)
			continue
		}

		// 下载图片并转换为base64
		base64Data, err := downloadImageToBase64(url)
		if err != nil {
			return "", fmt.Errorf("处理图片失败: %v", err)
		}

		// 上传图片
		newURL, err := uploadImageRetry(base64Data, 5)
		if err != nil {
			return "", fmt.Errorf("上传图片失败: %v", err)
		}

		// 添加CDN参数
		newURL = addCDNParams(newURL, imageWidthMap[key])
		fmt.Printf("图片处理完成，新URL: %s\n", newURL)

		if key == "cover_img" {
			return newURL, nil
		}

		// 替换原URL
		result = strings.Replace(result, match, "("+newURL+")", 1)
	}

	return result, nil
}

// 转换日期格式
func convertDateFormat(dateStr string) string {
	if dateStr == "" {
		return time.Now().Format("2006-01-02 15:04:05")
	}

	// 解析Excel中的日期格式
	t, err := time.Parse("2006/1/2 15:04:05", dateStr)
	if err != nil {
		t, err = time.Parse("2006-1-2 15:04:05", dateStr)
		if err != nil {
			fmt.Printf("日期格式转换失败: %s, 使用当前时间\n", dateStr)
			return time.Now().Format("2006-01-02 15:04:05")
		}
	}

	// 转换为数据库格式
	return t.Format("2006-01-02 15:04:05")
}

func TestBlogImport(t *testing.T) {
	// 打开Excel文件
	excelPath := "/Users/<USER>/Downloads/blog-063018.xlsx"
	f, err := excelize.OpenFile(excelPath)
	if err != nil {
		t.Fatalf("无法打开Excel文件: %v", err)
	}
	defer f.Close()

	// 创建输出文件
	timeStr := time.Now().Format("20060102150405")
	outputPath := strings.TrimSuffix(excelPath, filepath.Ext(excelPath)) + "_" + timeStr + ".sql"
	outputFile, err := os.Create(outputPath)
	if err != nil {
		t.Fatalf("无法创建输出文件: %v", err)
	}
	defer outputFile.Close()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		t.Fatalf("无法读取工作表: %v", err)
	}

	// 用于收集所有ID
	var ids []string

	// 跳过标题行
	for i, row := range rows {
		if i == 0 {
			continue
		}

		// 确保行有足够的列
		if len(row) < 13 {
			fmt.Printf("第%d行数据不足\n", i)
			continue
		}
		fmt.Printf("开始处理第%d行数据\n", i)

		// 处理F列和H列的图片URL
		coverImgContent := row[5]
		articleContent := row[7]

		// 替换short_content中的图片URL（F列，宽度600）
		coverImgContent, err := replaceImageURLs(coverImgContent, "cover_img")
		if err != nil {
			t.Fatalf("处理short_content图片失败: %v", err)
		}

		// 替换article_content中的图片URL（H列，宽度1200）
		newArticleContent, err := replaceImageURLs(articleContent, "article_content")
		if err != nil {
			t.Fatalf("处理article_content图片失败: %v", err)
		}

		// 更新行数据
		row[5] = coverImgContent
		row[7] = newArticleContent

		// 收集ID
		ids = append(ids, row[0])

		// 转换日期格式
		createdAt := convertDateFormat(row[11])
		updatedAt := convertDateFormat(row[12])

		// 构建INSERT语句
		insertSQL := fmt.Sprintf(
			"INSERT INTO blog_articles (id, path, category, article_title, short_content, cover_img, author_name, article_content, page_title, meta_keywords, meta_description, created_at, updated_at) VALUES (%s, '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s');\n",
			row[0],                // id
			base64Encode(row[1]),  // path
			base64Encode(row[2]),  // category
			base64Encode(row[3]),  // article_title
			base64Encode(row[4]),  // short_content
			row[5],                // cover_img
			base64Encode(row[6]),  // author_name
			base64Encode(row[7]),  // article_content
			base64Encode(row[8]),  // page_title
			base64Encode(row[9]),  // meta_keywords
			base64Encode(row[10]), // meta_description
			createdAt,             // created_at
			updatedAt,             // updated_at
		)

		// 写入文件
		if _, err := outputFile.WriteString(insertSQL); err != nil {
			t.Fatalf("写入文件失败: %v", err)
		}
	}

	// 添加UPDATE语句
	if len(ids) > 0 {
		updateSQL := fmt.Sprintf("\nUPDATE blog_articles SET\n"+
			"path = FROM_BASE64(path),\n"+
			"category = FROM_BASE64(category),\n"+
			"article_title = FROM_BASE64(article_title),\n"+
			"short_content = FROM_BASE64(short_content),\n"+
			"author_name = FROM_BASE64(author_name),\n"+
			"article_content = FROM_BASE64(article_content),\n"+
			"page_title = FROM_BASE64(page_title),\n"+
			"meta_keywords = FROM_BASE64(meta_keywords),\n"+
			"meta_description = FROM_BASE64(meta_description)\n"+
			"WHERE id IN (%s);\n", strings.Join(ids, ","))

		if _, err := outputFile.WriteString(updateSQL); err != nil {
			t.Fatalf("写入UPDATE语句失败: %v", err)
		}
	}

	t.Logf("SQL语句已成功写入到文件: %s", outputPath)
}

// base64Encode 对字符串进行base64编码
func base64Encode(s string) string {
	//去掉两头空格
	s = strings.TrimSpace(s)
	if s == "" {
		return ""
	}
	return base64.StdEncoding.EncodeToString([]byte(s))
}
