package data

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.100tal.com/tal_ucenter_sdk/ucenter_go"
	"github.com/gin-gonic/gin"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"github.com/jinzhu/copier"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/pkg/custom_context"
	"net"
	"net/http"
	"time"
)

// ticket Cache key
const (
	TicketCacheKey = "hw-pass-service:ucenter:ticket"
)

type ticketBody struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
	Data    struct {
		Ticket    string `json:"ticket"`
		ExpiresIn int    `json:"expires_in"`
	} `json:"data"`
}

type UserProfileBody struct {
	Errcode int      `json:"errcode"`
	Errmsg  string   `json:"errmsg"`
	Data    UserInfo `json:"data"`
}

type UserInfo struct {
	TalId        string `json:"tal_id"`
	TalName      string `json:"tal_name"`
	StuNo        string `json:"stu_no"`
	Sex          string `json:"sex"`
	Realname     string `json:"realname"`
	Nickname     string `json:"nickname"`
	EnName       string `json:"en_name"`
	SecretPhone  any    `json:"secret_phone"`
	SchoolYear   string `json:"school_year"`
	Birthday     string `json:"birthday"`
	AvatarUrl    string `json:"avatar_url"`
	Status       string `json:"status"`
	Email        string `json:"email"`
	TalNameFreq  int    `json:"tal_name_freq"`
	RealnameFreq int    `json:"realname_freq"`
	PhoneCode    int    `json:"phone_code"`
	HidePhoe     string `json:"hide_phoe"`
	Province     string `json:"province"`
	City         string `json:"city"`
	County       string `json:"county"`
	Role         string `json:"role"`
	CreateTime   string `json:"create_time"`
	ModifyTime   string `json:"modify_time"`
}

type CodeLoginResp struct {
	TalID        string //好未来用户 ID
	TalRole      int    //好未来用户角色
	TalType      int    //好未来用户类型
	TalMode      int    //好未来用户身份
	TalToken     string //用户登录标识
	ClientID     int    //终端应用ID
	TargetCG     int    //用户所属业务线
	CurrentCG    int    //当前登录业务线
	Expire       int64  //登录标识有效时间
	Life         int64  //存活时间
	Ext          string //扩展信息
	OriginTalID  string //好未来临时用户 ID
	TouristTalID string //好未来游客用户 ID
	OauthType    string //三方登录类型
	OauthAppID   string //三方登录应用ID
	OauthOpenID  string //三方登录用户ID
}

type LoginBaseParam struct {
	Ticket     string
	ClientID   string
	DeviceID   string
	VersionNum string
}

type CheckLoginReq struct {
	LoginBaseParam
	Token string
}

type LoginOutReq struct {
	LoginBaseParam
	Token string
}

type CheckLoginReply struct {
	TalID     string
	TalRole   int
	TalType   int
	TalMode   int
	TalToken  string
	ClientID  int
	TargetCG  int
	CurrentCG int
	Expire    int64
	Life      int64
	Ext       string
}

type CodeLoginReq struct {
	LoginBaseParam
	Code string
}

type UcenterRepo struct {
	client      *ucenter_go.UCenter
	conf        *conf.Services
	data        *Data
	log         *log.Helper
	restyClient *resty.Client
}

func NewUcenterRepo(conf *conf.Services, data *Data, logger log.Logger) *UcenterRepo {
	var obj = new(ucenter_go.UCenter)
	obj = obj.PublicKey(conf.Ucenter.PublicKey).Env(conf.Ucenter.Env).Suffix(conf.Ucenter.Suffix).Init()
	obj.Debug()

	restyClient := resty.New().SetBaseURL(conf.Ucenter.Host)

	return &UcenterRepo{
		client:      obj,
		conf:        conf,
		data:        data,
		log:         log.NewHelper(logger),
		restyClient: restyClient,
	}
}

func (r *UcenterRepo) getArgs(ctx context.Context) (ucenter_go.BaseParams, error) {
	clientId := r.conf.Ucenter.ClientId
	ticket, err := r.GetTicket(ctx)
	if err != nil {
		return ucenter_go.BaseParams{}, fmt.Errorf("get ticket faild！%v", err)
	}
	deviceId := custom_context.GetDeviceId(ctx)
	version := "1.0.0" // Default version, can be replaced with a dynamic value if needed
	args := ucenter_go.BaseParams{Ticket: ticket, DeviceID: deviceId, ClientID: clientId,
		VersionNum: version}
	if clientId == "" || ticket == "" {
		return ucenter_go.BaseParams{}, errors.New("codeLogin faild！参数校验失败")
	}
	return args, nil
}

func (r *UcenterRepo) CodeLogin(ctx context.Context, req *CodeLoginReq) (*CodeLoginResp, error) {
	args, err := r.getArgs(ctx)
	if err != nil {
		return nil, err
	}

	c := r.createGinContext(ctx)
	codeLoginResp, err := r.client.CodeLogin(c.Request, c.Writer, args, req.Code)
	if err != nil {
		return nil, fmt.Errorf("codeLogin faild！%v", err)
	}

	reply := &CodeLoginResp{}
	copier.Copy(reply, codeLoginResp)
	return reply, nil
}

func (r *UcenterRepo) CheckLogin(ctx context.Context, req *CheckLoginReq) (*CheckLoginReply, error) {
	args, err := r.getArgs(ctx)
	if err != nil {
		return nil, err
	}

	c := r.createGinContext(ctx)
	res, err := r.client.CheckLogin(c.Request, c.Writer, args, req.Token)
	if err != nil {
		return nil, fmt.Errorf("tag: [checkLogin.CheckLogin] info:%v", err)
	}
	reply := &CheckLoginReply{}
	copier.Copy(&reply, &res)
	return reply, nil
}

func (r *UcenterRepo) createGinContext(ctx context.Context) gin.Context {
	c := gin.Context{}
	header := http.Header{}

	realIP := custom_context.GetRealIp(ctx)
	if realIP == "" {
		realIP = "127.0.0.1"
	}
	forwardedFor := custom_context.GetForwardedFor(ctx)
	header.Set(common.XRealIp, realIP)
	header.Set(common.XForwardedFor, forwardedFor)
	c.Request = &http.Request{
		Header: header,
	}

	r.log.WithContext(ctx).Infof("[createGinContext] header: %+v", c.Request.Header)

	c.Writer = &ResponseWriterWrapper{
		Body: &bytes.Buffer{},
	}

	return c
}

func (r *UcenterRepo) GetTicket(ctx context.Context) (string, error) {
	ticketValue, err := r.data.Rdb.Get(ctx, TicketCacheKey).Result()
	if err == nil && ticketValue != "" {
		return ticketValue, nil
	}

	client := resty.New()
	resp, err := client.R().
		SetQueryParams(map[string]string{
			"appid":  r.conf.Ucenter.AppInfo.AppId,
			"secret": r.conf.Ucenter.AppInfo.AppSecret,
		}).
		Get(r.conf.Ucenter.TicketUrl)

	if err != nil {
		return "", err
	}

	ticket := &ticketBody{}
	err = json.Unmarshal(resp.Body(), ticket)

	if err != nil {
		return "", fmt.Errorf("failed to unmarshal ticket response: %v", err)
	}
	if ticket.Errcode != 0 {
		return "", errors.New(ticket.Errmsg)
	}

	if ticket.Data.Ticket != "" {
		// Cache the ticket
		err = r.data.Rdb.Set(ctx, TicketCacheKey, ticket.Data.Ticket, time.Second*time.Duration(ticket.Data.ExpiresIn-300)).Err()
		if err != nil {
			r.log.WithContext(ctx).Errorf("failed to cache ticket: %v", err)
		}
	}
	return ticket.Data.Ticket, nil
}

func (r *UcenterRepo) LoginOut(ctx context.Context, req *LoginBaseParam, token string) (string, error) {
	args, err := r.getArgs(ctx)
	if err != nil {
		return "退出登陆失败", err
	}
	c := r.createGinContext(ctx)
	request := c.Request
	response := c.Writer
	_, err = r.client.Logout(request, response, args, token)
	if err != nil {
		return "退出登陆失败", fmt.Errorf("tag: [loginOut.LoginOut] info:%v", err)
	}

	return "退出登陆成功", nil
}

// /v1/api/user/profile
func (r *UcenterRepo) GetUserProfile(ctx context.Context, talID string) (*UserInfo, error) {
	ticket, err := r.GetTicket(ctx)
	if err != nil {
		return nil, err
	}
	res, err := r.restyClient.SetQueryParams(map[string]string{
		"tal_id":    talID,
		"client_id": r.conf.Ucenter.ClientId,
		"ticket":    ticket,
	}).
		SetHeader("Accept", "application/json").
		R().Get("/v1/api/user/profile")
	if err != nil {
		return nil, fmt.Errorf("get user profile failed: %v", err)
	}

	reply := &UserProfileBody{}
	if err := json.Unmarshal(res.Body(), reply); err != nil {
		return nil, fmt.Errorf("unmarshal user profile response failed: %v", err)
	}
	return &reply.Data, nil
}

type ResponseWriterWrapper struct {
	gin.ResponseWriter
	Body       *bytes.Buffer // 缓存
	statusCode int
}

func (w ResponseWriterWrapper) Write(b []byte) (int, error) {
	w.Body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w ResponseWriterWrapper) WriteString(s string) (int, error) {
	w.Body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

func (w *ResponseWriterWrapper) Status() int {

	return w.statusCode
}
func (w *ResponseWriterWrapper) Size() int {
	return len(w.Body.Bytes())
}

func (w *ResponseWriterWrapper) Written() bool {
	return w.statusCode != 0
}
func (w *ResponseWriterWrapper) WriteHeaderNow() {

}
func (w *ResponseWriterWrapper) Pusher() http.Pusher {
	return nil
}

func (w *ResponseWriterWrapper) WriteHeader(statusCode int) {
	w.statusCode = statusCode
}

func (w *ResponseWriterWrapper) Header() http.Header {
	return http.Header{}
}

func (w *ResponseWriterWrapper) Hijack() (net.Conn, *bufio.ReadWriter, error) {

	return nil, nil, nil
}
func (w *ResponseWriterWrapper) Flush() {

}
func (w *ResponseWriterWrapper) CloseNotify() <-chan bool {
	return make(chan bool)
}
