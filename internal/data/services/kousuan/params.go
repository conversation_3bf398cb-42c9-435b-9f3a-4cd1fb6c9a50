package kousuan

type KousuanCorrectReq struct {
	ImgBase64 []byte `json:"image"`
	TraceId   string `json:"trace_id"`
}

type KousuanCorrectResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data *Data  `json:"data"`
}

type Data struct {
	ImgWidth      int        `json:"img_width"`
	ImgHeight     int        `json:"img_height"`
	RotationAngle int        `json:"rotation_angle"`
	Questions     []Question `json:"questions"`
}

type Question struct {
	Bbox        []float64 `json:"bbox"`
	TextContent string    `json:"text_content"`
	CorrectTxt  string    `json:"correct_txt"`
	AnsResult   int       `json:"ans_result"`
}
