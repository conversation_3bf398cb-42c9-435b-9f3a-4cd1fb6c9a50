package kousuan

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/pkg/custom_context"
	"net/http"
	"time"
)

const (
	Kousuan = "/kousuan"
)

type Client struct {
	log    *log.Helper
	client *resty.Client
}
type traceCorrectResult struct {
	TraceID     string      `json:"trace_id"`  //traceID
	UserId      string      `json:"user_id"`   //用户ID
	DeviceId    string      `json:"device_id"` //设备ID
	ImageBase64 []byte      `json:"image_base64"`
	Resp        interface{} `json:"resp"`
}

func NewClient(config *conf.Biz, logger log.Logger) *Client {
	if config == nil || config.Kousuan == nil {
		return nil
	}
	kousuan := config.Kousuan
	client := resty.New()
	client.SetBaseURL(kousuan.Host).
		SetRetryCount(2).
		AddRetryCondition(
			func(r *resty.Response, err error) bool {
				if r.StatusCode() == http.StatusTooManyRequests || r.StatusCode() >= 500 {
					return true
				}
				return false
			},
		).
		SetTimeout(kousuan.Timeout.AsDuration()).
		SetRetryWaitTime(time.Millisecond * 100).
		OnBeforeRequest(func(c *resty.Client, r *resty.Request) error {
			//requestId := trace.SpanContextFromContext(r.Context()).TraceID().String()
			//r.SetHeaders(map[string]string{
			//	"Content-Type": "application/json",
			//	"request-id":   requestId,
			//})
			return nil
		})
	return &Client{
		log:    log.NewHelper(logger),
		client: client,
	}
}

func (c *Client) KousuanCorrect(ctx context.Context, imgBase64 []byte) (*Data, error) {
	traceId := custom_context.GetTraceId(ctx)
	//
	//traceLog := &traceCorrectResult{
	//	TraceID:     custom_context.GetTraceId(ctx),
	//	UserId:      custom_context.GetJwtUserId(ctx),
	//	DeviceId:    custom_context.GetDeviceId(ctx),
	//	ImageBase64: imgBase64,
	//}
	//defer func() {
	//	gCtx := util.NewTraceContext(nil, traceId)
	//	sync.Go(gCtx, c.log, func() {
	//		traceB, _ := jsoniter.Marshal(traceLog)
	//		c.log.WithContext(gCtx).Info(common.MakeLogBackFlowMsgInfo(traceId, "QueryCorrect", "dw_KousuanCorrect_trace", string(traceB)))
	//		return
	//	})
	//}()
	req := &KousuanCorrectReq{
		ImgBase64: imgBase64,
		TraceId:   traceId,
	}
	var result KousuanCorrectResp
	res, err := c.client.
		R().
		EnableTrace().
		SetBody(req).
		SetResult(&result).
		Post(Kousuan)

	//traceLog.Resp = result
	if err != nil {
		return nil, errors.New(fmt.Sprintf("KousuanCorrect error: %+v, res:%+v,traceId:%s", err, res, traceId))
	}
	if res.StatusCode() != 200 {
		return nil, errors.New(fmt.Sprintf("KousuanCorrect response: %s,traceId:%s", string(res.Body()), traceId))
	}
	c.log.WithContext(ctx).Infof("KousuanCorrect response: %s,traceId:%s", res, traceId)
	err = json.Unmarshal(res.Body(), &result)
	if err != nil {
		c.log.WithContext(ctx).Errorf("KousuanCorrect response unmarshal error: %v,traceId:%s", err, traceId)
		return nil, err
	}
	code := result.Code
	if code != 20000 {
		c.log.WithContext(ctx).Errorf("KousuanCorrect response code: %d, msg: %s,traceId:%s", code, result.Msg, traceId)
		return nil, errors.New(fmt.Sprintf("KousuanCorrect response: %s", string(res.Body())))
	}

	return result.Data, nil
}
