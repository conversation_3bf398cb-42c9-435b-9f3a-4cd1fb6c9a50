package safety

import (
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	"hw-paas-service/internal/conf"
	"time"
)

const contentSafetyTextPath = "/inner/public/content/safety/text"

// 安全级别最小阈值
var categoryMap = map[string]int{
	"Hate":     1, // 仇恨
	"SelfHarm": 1, // 自残
	"Sexual":   0, // 性相关
	"Violence": 1, // 暴力
}

type Svc struct {
	config *conf.ServiceBaseConfig
	log    *log.Helper
	Client *resty.Client
}

func NewSvc(client *resty.Client, logger log.Logger) *Svc {
	return &Svc{
		log:    log.NewHelper(logger),
		Client: client,
	}
}

// ContentSafetyText 内容安全审核
func (s *Svc) ContentSafetyText(ctx context.Context, req *TextReq) ([]*TextData, error) {
	body, _ := jsoniter.Marshal(req)
	s.log.WithContext(ctx).Infof("dw_safety_text_req: %v", string(body))

	start := time.Now()
	res, err := s.Client.R().
		SetContext(ctx).
		SetBody(req).
		Post(contentSafetyTextPath)
	// 计算响应时间
	duration := time.Since(start)
	if err != nil || !res.IsSuccess() {
		s.log.WithContext(ctx).Errorf("content safety text failed, err:%v, res:%+v", err, res)
		return nil, err
	}

	resp := TextResponse{}
	err = jsoniter.Unmarshal(res.Body(), &resp)
	if err != nil {
		s.log.WithContext(ctx).Errorf("content safety text failed, err:%v, res:%+v", err, res)
		return nil, err
	}
	if resp.Code != 200 {
		s.log.WithContext(ctx).Errorf("content safety text failed, err:%v, res:%+v", err, res)
		return nil, errors.New(resp.Message)
	}

	traceInfo := map[string]interface{}{
		"resp_time": duration.Milliseconds(),
		"reply":     resp,
	}
	traceRes, _ := jsoniter.Marshal(traceInfo)
	s.log.WithContext(ctx).Infof("dw_safety_text_resp: %v", string(traceRes))

	return resp.Data, nil
}

func (s *Svc) CheckContentSafety(ctx context.Context, req *TextReq) (bool, []*TextData, error) {
	data, err := s.ContentSafetyText(ctx, req)
	if err != nil {
		return false, data, err //false:不安全; true:安全
	}

	for _, v := range data {
		if _v, ok := categoryMap[v.Category]; ok && (v.Severity > _v) {
			traceInfo := map[string]interface{}{
				"req":  req,
				"resp": data,
			}
			res, _ := jsoniter.Marshal(traceInfo)
			s.log.WithContext(ctx).Warnf("dw_check_safety_resp: %v", string(res))
			return false, data, nil
		}
	}

	return true, data, nil
}
