package safety

type TextReq struct {
	Text string `json:"text"`
}
type TextResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    []*TextData `json:"data"`
}
type TextData struct {
	Category string `json:"category"`
	Severity int    `json:"severity"`
}

// {"code":200,"message":"success","data":[{"category":"Hate","severity":0},{"category":"SelfHarm","severity":0},{"category":"Sexual","severity":0},{"category":"Violence","severity":0}]}
