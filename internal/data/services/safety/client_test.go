package safety

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"hw-paas-service/internal/conf"
	"testing"
)

var cfg = conf.ServiceBaseConfig{
	Host: "https://overseas-pad.chengjiukehu.com",
}

func TestContentSafetyText(t *testing.T) {
	c := resty.New().SetBaseURL(cfg.Host)
	svc := NewSvc(c, log.With(log.DefaultLogger))
	if svc == nil {
		t.Fatalf("svc is nil")
	}

	req := &TextReq{
		Text: "hello world, fuck, bitch",
	}
	resp, err := svc.CheckContentSafety(context.Background(), req)
	if err != nil {
		t.Errorf("ContentSafetyText() error = %v", err)
		return
	}
	t.Logf("resp: %+v", resp)
}
