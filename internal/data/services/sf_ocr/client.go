package sf_ocr

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/pkg/custom_context"
	"net/http"
	"time"
)

const (
	trans = "/finger_translation"
)

type Client struct {
	log    *log.Helper
	client *resty.Client
}

func NewClient(config *conf.Biz, logger log.Logger) *Client {
	if config == nil || config.ReadingBookOcr == nil {
		return nil
	}
	rb := config.ReadingBookOcr
	client := resty.New()
	client.SetBaseURL(rb.Host).
		SetRetryCount(2).
		AddRetryCondition(
			func(r *resty.Response, err error) bool {
				if r.StatusCode() == http.StatusTooManyRequests || r.StatusCode() >= 500 {
					return true
				}
				return false
			},
		).
		SetTimeout(rb.Timeout.AsDuration()).
		SetRetryWaitTime(time.Millisecond * 100).
		OnBeforeRequest(func(c *resty.Client, r *resty.Request) error {
			//requestId := trace.SpanContextFromContext(r.Context()).TraceID().String()
			r.SetHeaders(map[string]string{
				"Content-Type": "application/json",
				//"request-id":   requestId,
			})
			return nil
		})
	return &Client{
		log:    log.NewHelper(logger),
		client: client,
	}
}

func (c *Client) SfFingerTranslate(ctx context.Context, req OCRRequest) (string, error) {
	traceId := custom_context.GetTraceId(ctx)
	c.log.WithContext(ctx).Infof("SfFingerTranslate request: %v,traceId:%s", req, traceId)
	var result OCRResponse
	start := time.Now()
	res, err := c.client.
		R().
		EnableTrace().
		SetBody(req).
		Post(trans)
	// 计算响应时间
	duration := time.Since(start)
	if err != nil {
		c.log.WithContext(ctx).Errorf("translate error: %+v, res:%+v,traceId:%s", err, res, traceId)
		return "", errors.New(fmt.Sprintf("translate error: %+v, res:%+v,traceId:%s", err, res, traceId))
	}
	err = json.Unmarshal(res.Body(), &result)
	if err != nil {
		c.log.WithContext(ctx).Errorf("translate response unmarshal error: %v,traceId:%s", err, traceId)
		return "", err
	}
	if result.Code != 20000 {
		c.log.WithContext(ctx).Errorf("translate response: %s,traceId:%s", string(res.Body()), traceId)
		return "", errors.New(fmt.Sprintf("translate response: %s,traceId:%s", string(res.Body()), traceId))
	}
	c.log.WithContext(ctx).Infof("translate response: %s,traceId:%s", res, traceId)
	traceInfo := map[string]interface{}{
		"resp_time": duration.Milliseconds(),
		"data":      result.Data,
	}
	traceRes, _ := jsoniter.Marshal(traceInfo)

	req.ImageBase64 = ""
	body, _ := jsoniter.Marshal(req)
	c.log.WithContext(ctx).Infof(common.MakeLogBackFlowMsgInfo(traceId, "reading_book", "dw_finger_ocr_req", string(body)))
	c.log.WithContext(ctx).Infof(common.MakeLogBackFlowMsgInfo(traceId, "reading_book", "dw_finger_ocr_resp", string(traceRes)))
	return result.Data.Text, nil
}
