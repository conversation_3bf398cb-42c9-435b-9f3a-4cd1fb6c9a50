package sf_ocr

type OCRRequest struct {
	ImageBase64 string   `json:"image_base64"`
	FingerPos   [2]int32 `json:"finger_pos"`
	TraceID     string   `json:"trace_id"`
}

type OCRResponse struct {
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	TraceID string `json:"trace_id"`
	Data    struct {
		Text  string  `json:"text"`
		Score float64 `json:"score"`
		OCR   []struct {
			Text  string     `json:"text"`
			Box   [8]float64 `json:"box"` // 注意这里的 box 是浮动坐标
			Score float64    `json:"score"`
		} `json:"ocr"`
	} `json:"data"`
	Time struct {
		Base64ToNumpyTime       float64 `json:"base64_to_numpy_time"`
		TextDetTime             float64 `json:"text_det_time"`
		FindFingerLineTime      float64 `json:"find_finger_line_time"`
		TextRecReprocess        float64 `json:"text_rec_reprocess"`
		TextRecTime             float64 `json:"text_rec_time"`
		TextRecDataProcess      float64 `json:"text_rec_data_process"`
		FingerTextFilterProcess float64 `json:"finger_text_filter_process"`
		Pipeline                float64 `json:"pipeline"`
		FingerTextConcatBeauty  float64 `json:"finger_text_concat_beauty"`
	} `json:"time"`
}
