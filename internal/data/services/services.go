package services

import (
	"hw-paas-service/internal/biz/biz_metrics"
	"hw-paas-service/internal/data/services/badou"
	"hw-paas-service/internal/data/services/base64_upload"
	"hw-paas-service/internal/data/services/en_correct"
	"hw-paas-service/internal/data/services/hybrid_ocr"
	"hw-paas-service/internal/data/services/llm"
	"hw-paas-service/internal/data/services/oss"
	"hw-paas-service/internal/data/services/rn_version_control_svc"
	"hw-paas-service/internal/data/services/safety"
	"hw-paas-service/internal/data/services/task_drive"
	"net/url"
	"time"

	"hw-paas-service/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(NewServices)

type Services struct {
	HOSvc            *hybrid_ocr.Svc //指查OCR
	EnCRSvc          *en_correct.Svc //英文纠错
	RnVersionControl *rn_version_control_svc.RnVersionControlSvc
	Base64Upload     *base64_upload.Svc
	SafetySvc        *safety.Svc
	OssSvc           *oss.Svc
	BadouSvc         *badou.Svc
	TaskDriveSvc     *task_drive.Svc
	LlmSvc           *llm.Svc
	AzureBlob        *oss.AzureBlob // Azure Blob存储服务
}

func NewServices(configs *conf.Services, logger log.Logger) *Services {
	return &Services{
		HOSvc:            hybrid_ocr.NewSvc(NewRestyClient(configs.HybridOcr), logger),
		EnCRSvc:          en_correct.NewSvc(NewRestyClient(configs.EnCorrect), logger),
		RnVersionControl: rn_version_control_svc.NewSvc(NewRestyClient(configs.RnVersionControl), logger),
		Base64Upload:     base64_upload.NewSvc(NewRestyClient(configs.Base64Upload), logger),
		SafetySvc:        safety.NewSvc(NewRestyClient(configs.Safety), logger),
		OssSvc:           oss.NewSvc(NewRestyClient(configs.Oss), logger),
		BadouSvc:         badou.NewSvc(NewRestyClient(configs.Badou), logger),
		TaskDriveSvc:     task_drive.NewSvc(NewRestyClient(configs.TaskDrive), logger),
		LlmSvc:           llm.NewSvc(configs.Llm, logger),
		AzureBlob:        oss.NewAzureBlob(logger),
	}
}

func NewRestyClient(config *conf.ServiceBaseConfig) *resty.Client {
	client := resty.New()
	client.SetBaseURL(config.Host).
		SetHeader("Content-Type", "application/json").
		SetTimeout(config.Timeout.AsDuration()).
		SetRetryWaitTime(time.Millisecond * 100).
		OnBeforeRequest(func(c *resty.Client, r *resty.Request) error {
			return nil
		}).
		OnAfterResponse(func(c *resty.Client, r *resty.Response) error {
			u, err := url.Parse(r.Request.URL)
			if err != nil {
				return nil
			}
			biz_metrics.MetricClientSeconds.WithLabelValues(r.Request.Method, u.Path).Observe(r.Time().Seconds())
			return nil
		})
	return client
}
