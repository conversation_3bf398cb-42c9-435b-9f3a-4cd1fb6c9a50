package task_drive

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	pb "hw-paas-service/api/ai/v1"
	"hw-paas-service/internal/conf"
	"time"
)

const (
	taskDrivePath = "/api/v1/activity/novice/task/drive"
)

type Svc struct {
	config *conf.ServiceBaseConfig
	log    *log.Helper
	Client *resty.Client
}

func NewSvc(client *resty.Client, logger log.Logger) *Svc {
	return &Svc{
		log:    log.<PERSON><PERSON>per(logger),
		Client: client,
	}
}

func (s *Svc) TaskDrive(ctx context.Context, req *Request) (*Response, error) {
	body, _ := jsoniter.Marshal(req)
	s.log.WithContext(ctx).Infof("dw_task_drive_req: %v", string(body))

	start := time.Now()
	res, err := s.Client.R().
		SetContext(ctx).
		SetBody(req).
		Post(taskDrivePath)
	// 计算响应时间
	duration := time.Since(start)
	if err != nil || !res.IsSuccess() {
		s.log.WithContext(ctx).Errorf("TaskDriveFailed, err:%v, res:%+v", err, res)
		return nil, err
	}

	resp := &Response{}
	err = jsoniter.Unmarshal(res.Body(), &resp)
	if err != nil {
		s.log.WithContext(ctx).Errorf("TaskDriveUnmarshalFailed, err:%v, res:%+v", err, res)
		return nil, err
	}
	if resp.Code != 0 {
		s.log.WithContext(ctx).Errorf("TaskDriveCodeNot200, res:%+v", res)
		return nil, pb.ErrorHwPaasThirdPartError(resp.Msg)
	}

	traceInfo := map[string]interface{}{
		"resp_time": duration.Milliseconds(),
		"reply":     resp,
	}
	traceRes, _ := jsoniter.Marshal(traceInfo)
	s.log.WithContext(ctx).Infof("dw_task_drive_resp: %v", string(traceRes))

	return resp, nil
}
