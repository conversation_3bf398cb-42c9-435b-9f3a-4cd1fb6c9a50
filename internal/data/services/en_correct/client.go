package en_correct

import (
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	"hw-paas-service/internal/conf"
	"time"
)

const enCorrectReplacePath = "/"

type Svc struct {
	config *conf.ServiceBaseConfig
	log    *log.Helper
	Client *resty.Client
}

func NewSvc(client *resty.Client, logger log.Logger) *Svc {
	return &Svc{
		log:    log.<PERSON>elper(logger),
		Client: client,
	}
}

// EnCorrectReplace 英文指查纠错, 只纠错指查的单词,不做邻近词纠错
func (s *Svc) EnCorrectReplace(ctx context.Context, req *EnCorrectReplaceReq) (*EnCorrectReplaceData, error) {
	body, _ := jsoniter.Marshal(req)
	// TODO: 打sundebiao确认日志打印
	s.log.WithContext(ctx).Infof("dw_en_correct_replace_req: %v", string(body))

	start := time.Now()
	res, err := s.Client.R().
		SetContext(ctx).
		SetBody(req).
		Post(enCorrectReplacePath)
	// 计算响应时间
	duration := time.Since(start)
	if err != nil || !res.IsSuccess() {
		s.log.WithContext(ctx).Errorf("en correct replace failed, err: %v, res:%+v", err, res)
		return nil, err
	}

	resp := EnCorrectReplaceResponse{}
	err = jsoniter.Unmarshal(res.Body(), &resp)
	if err != nil {
		s.log.WithContext(ctx).Errorf("en correct replace failed, unmarshal err: %v, res:%+v", err, resp)
		return nil, err
	}
	if resp.Code != 0 {
		s.log.WithContext(ctx).Errorf("en correct replace failed, response res:%+v", resp)
		return nil, errors.New(resp.Msg)
	}

	traceInfo := map[string]interface{}{
		"resp_time": duration.Milliseconds(),
		"reply":     resp,
	}
	traceRes, _ := jsoniter.Marshal(traceInfo)
	s.log.WithContext(ctx).Infof("dw_en_correct_replace_resp: %v", string(traceRes))

	return resp.Data, nil
}
