package en_correct

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"hw-paas-service/internal/conf"
	"testing"
)

var cfg = conf.ServiceBaseConfig{
	Host: "http://***********:80",
}

func TestEnCorrectReplace(t *testing.T) {
	c := resty.New().SetBaseURL(cfg.Host)
	svc := NewSvc(c, log.With(log.DefaultLogger))
	if svc == nil {
		t.Fatalf("svc is nil")
	}

	req := EnCorrectReplaceReq{
		OcrAlllineInfo:   "hello world",
		OcrKeywordInfo:   "hello",
		OcrKeyInfoOffset: 0,
	}
	resp, err := svc.EnCorrectReplace(context.Background(), &req)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(resp)
}
