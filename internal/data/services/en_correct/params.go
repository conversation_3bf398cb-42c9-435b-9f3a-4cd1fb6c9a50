package en_correct

type EnCorrectReplaceReq struct {
	OcrAlllineInfo   string `json:"ocr_allline_info"`
	OcrKeywordInfo   string `json:"ocr_keyword_info"`
	OcrKeyInfoOffset int    `json:"ocr_key_info_offset"`
	TraceID          string `json:"trace_id"`

	NearWords EnCorrectReplaceNearWord `json:"near_words"`
}
type EnCorrectReplaceNearWord struct {
	Key   string                        `json:"key"`
	Pos   EnCorrectReplaceNearWordPos   `json:"pos"`
	Words EnCorrectReplaceNearWordWords `json:"words"`
}
type EnCorrectReplaceNearWordPos struct {
	LeftPos  int `json:"left_pos"`
	RightPos int `json:"right_pos"`
}
type EnCorrectReplaceNearWordWords struct {
	LeftWord  string `json:"left_word"`
	RightWord string `json:"right_word"`
}
type EnCorrectReplaceResponse struct {
	Code    int                   `json:"code"`
	Msg     string                `json:"msg"`
	TraceID string                `json:"trace_id"`
	Data    *EnCorrectReplaceData `json:"data"`
	Version string                `json:"_version"`
}
type EnCorrectReplaceData struct {
	OrigText    string `json:"orig_text"`
	CorrectText string `json:"correct_text"`
	NearWords   struct {
		Key string `json:"key"`
		Pos struct {
			LeftPos  int `json:"left_pos"`
			RightPos int `json:"right_pos"`
		} `json:"pos"`
		Words struct {
			LeftWord  string `json:"left_word"`
			RightWord string `json:"right_word"`
		} `json:"words"`
	} `json:"near_words"`
}
