package llm

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	"hw-paas-service/internal/conf"
	"strings"
)

type Svc struct {
	log    *log.Helper
	Client *resty.Client
	Key    string
}

func NewSvc(llm *conf.Llm, logger log.Logger) *Svc {
	client := resty.New().SetBaseURL(llm.Host)
	return &Svc{
		log:    log.NewHelper(logger),
		Client: client,
		Key:    llm.Key,
	}
}

func (s *Svc) Send(ctx context.Context, word string) (string, error) {
	// 请求4o服务
	messages := make([]Gpt4oMessage, 0)
	messages = append(messages, Gpt4oMessage{
		Role:    "system",
		Content: prompt,
	})
	messages = append(messages, Gpt4oMessage{
		Role:    "user",
		Content: word,
	})
	req := Gpt4oRequest{
		Model:    "gpt-4o",
		Messages: messages,
	}
	resp, err := s.Client.R().
		SetHeaders(map[string]string{
			"Content-Type":  "application/json",
			"Authorization": "Bearer " + s.Key,
		}).
		SetBody(req).SetResult(nil).Post("/openai-compatible/v1/chat/completions")
	if err != nil {
		fmt.Printf("request gpt-4o, word: %s, err:%v\n", word, err)
		return "", err
	}
	var gpt4oResp Gpt4oResponse
	err = jsoniter.Unmarshal(resp.Body(), &gpt4oResp)
	if err != nil {
		fmt.Printf("unmarshal gpt-4o, word: %s, err:%v\n", word, err)
		return "", err
	}
	if len(gpt4oResp.Choices) == 0 {
		fmt.Printf("gpt-4o response empty, word: %s\n", word)
		return "", fmt.Errorf("gpt-4o response empty")
	}
	str := gpt4oResp.Choices[0].Message.Content
	str = strings.Replace(str, "```json", "", -1)
	str = strings.Replace(str, "```", "", -1)

	return str, nil
}
