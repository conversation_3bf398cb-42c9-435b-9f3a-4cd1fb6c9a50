package llm

type Gpt4oResponse struct {
	ID                string `json:"id"`
	Object            string `json:"object"`
	Model             string `json:"model"`
	Created           int    `json:"created"`
	SystemFingerprint string `json:"system_fingerprint"`
	Usage             struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		Logprobs     interface{} `json:"logprobs"`
		FinishReason string      `json:"finish_reason"`
	} `json:"choices"`
}
type Gpt4oRequest struct {
	Model    string         `json:"model"`
	Messages []Gpt4oMessage `json:"messages"`
}
type Gpt4oMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type Gpt4oContent struct {
	Pronunciation struct {
		British  string `json:"British"`
		American string `json:"American"`
	} `json:"pronunciation"`
	Inflections    []string `json:"inflections"`
	RootVariations struct {
		Prefix string `json:"prefix"`
		Suffix string `json:"suffix"`
	} `json:"root_variations"`
	Meanings  []Gpt4oContentMeaning `json:"meanings"`
	Synonyms  []string              `json:"synonyms"`
	Antonyms  []string              `json:"antonyms"`
	Phrases   []string              `json:"phrases"`
	Sentences []string              `json:"sentences"`
}
type Gpt4oContentMeaning struct {
	PartOfSpeech string `json:"part_of_speech"`
	Definition   string `json:"definition"`
}

var prompt = `You are now an experienced education expert and lexicographer working on a dictionary for children under 12 years old. After you are given a word, you should output the returned information fully containing the following details in JSON format.

British pronunciation [using International Phonetic Alphabet (IPA)],
American pronunciation [using International Phonetic Alphabet (IPA)],
The word's inflections (plural forms of countable nouns, past tense/past participle of verbs, present participle of verbs, comparative/superlative degrees of adjectives/adverbs, adverb forms of adjectives, etc., just show the words without indicating their forms and if none, A single element is displayed as "",
The word's root variations (prefixes, suffixes),  if none, A single element is displayed as "".

In cases of polysemy, each meaning should include its part of speech (abbreviated in English, like c.n. / vt., etc.) and its definition based on different parts of speech (simplified explanation that children can understand). Next, provide the following information for the most common meaning: two synonyms(if none, A single element is displayed as ""),two antonyms (if none, A single element is displayed as ""), two phrases containing the word, two sentences containing the word. 

Ensure all content avoids any inappropriate elements for children or political elements. If the word has multiple meanings, please display them all and illustrate them clearly.

Provide an example in JSON format. All your output formats must follow this example, Brackets cannot be changed. The input word for this example is 'bright'.
{
  "pronunciation": {
    "British": "brɑːt",
    "American": "bræt"
   },
  "inflections": [
    "brighter",
    "brightest",
    "brightly"
  ],
  "root_variations":  {
    "prefix": "",
    "suffix": ""
   },
  "meanings": [
    {
      "part_of_speech": "adj.",
      "definition": "Shining with lots of light."
    },
    {
      "part_of_speech": "adj.",
      "definition": "Full of hope and happiness."
    }
  ],
  "synonyms": [
    "shiny",
    "cheerful"
  ],
  "antonyms": [
    "dull",
    "gloomy"
  ],
  "phrases": [
    "bright future",
    "bright idea"
  ],
  "sentences": [
    "The sun is very bright today.",
    "She has a bright smile that makes everyone happy."
  ]
}

The input word for this example is 'inglorious'.
{
  "pronunciation": {
    "British": "ɪnˈɡlɔːrɪəs",
    "American": "ɪnˈɡlɔːriəs"
   },
  "inflections": [
    ""
  ],
  "root_variations":  {
    "prefix": "in-",
    "suffix": "-ious"
   },
  "meanings": [
    {
      "part_of_speech": "adj.",
      "definition": "Not bringing honor or fame"
    },
    {
      "part_of_speech": "adj.",
      "definition": "shameful."
    }
  ],
  "synonyms": [
    "dishonorable",
    "shameful"
  ],
  "antonyms": [
    "honorable",
    "respectable"
  ],
  "phrases": [
    "an inglorious defeat",
    "an inglorious end"
  ],
  "sentences": [
    "The team suffered an inglorious defeat in the final match.",
    "His actions led to an inglorious end to his career."
  ]
}`
