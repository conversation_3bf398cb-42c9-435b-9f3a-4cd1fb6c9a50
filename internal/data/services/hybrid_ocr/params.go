package hybrid_ocr

type HybridOcrReq struct {
	ClientTraceID string  `json:"client_trace_id"`
	TextBmp       string  `json:"text_bmp"`
	FingerPos2Ma  []int32 `json:"finger_pos_2ma"`
	TextPos2Ma    []int32 `json:"text_pos_2ma"`
	RetryStatus   int32   `json:"retry_status"`
	Version       string  `json:"version"`
}

type HybridOcrResponse struct {
	ClientTraceID string         `json:"client_trace_id"`
	ErrorCode     int            `json:"error_code"`
	ErrorMessage  string         `json:"error_message"`
	RetryStatus   int32          `json:"retry_status"`
	CostTime      float64        `json:"cost_time"`
	Data          *HybridOcrData `json:"data"`
}

type HybridOcrData struct {
	NearWord     string   `json:"near_word"`
	LineWords    []string `json:"line_words"`
	NearIndex    int32    `json:"near_index"`
	Ocr          string   `json:"ocr"`
	RetryStatus  int32    `json:"retry_status"`
	LineWordList []string `json:"line_word_list"`
}
