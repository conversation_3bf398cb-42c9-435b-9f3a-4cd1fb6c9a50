package hybrid_ocr

import (
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	"time"
)

const hybridOcrPath = "/"

type Svc struct {
	log    *log.Helper
	Client *resty.Client
}

func NewSvc(client *resty.Client, logger log.Logger) *Svc {
	return &Svc{
		log:    log.<PERSON>Helper(logger),
		Client: client,
	}
}

func (s *Svc) HybridOcr(ctx context.Context, req *HybridOcrReq) (*HybridOcrData, error) {
	body, _ := jsoniter.Marshal(req)
	s.log.WithContext(ctx).Infof("dw_hybrid_ocr_req: %v", string(body))

	start := time.Now()
	res, err := s.Client.R().
		SetContext(ctx).
		SetBody(req).
		Post(hybridOcrPath)
	// 计算响应时间
	duration := time.Since(start)
	if err != nil || !res.IsSuccess() {
		s.log.WithContext(ctx).Errorf("hybrid ocr failed, err: %v, res:%+v", err, res)
		return nil, err
	}

	resp := HybridOcrResponse{}
	err = jsoniter.Unmarshal(res.Body(), &resp)
	if err != nil {
		s.log.WithContext(ctx).Errorf("hybrid ocr failed, unmarshal err: %v, res:%+v", err, resp)
		return nil, err
	}
	if resp.ErrorCode != 0 {
		s.log.WithContext(ctx).Errorf("hybrid ocr failed, response res:%+v", resp)
		return nil, errors.New(resp.ErrorMessage)
	}

	reply := resp.Data

	traceInfo := map[string]interface{}{
		"resp_time": duration.Milliseconds(),
		"data":      reply,
	}
	traceRes, _ := jsoniter.Marshal(traceInfo)
	s.log.WithContext(ctx).Infof("dw_hybrid_ocr_resp: %v", string(traceRes))

	return reply, nil
}
