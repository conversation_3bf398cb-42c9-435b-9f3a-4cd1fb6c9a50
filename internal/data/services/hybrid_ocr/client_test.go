package hybrid_ocr

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"hw-paas-service/internal/conf"
	"testing"
)

var cfg = conf.ServiceBaseConfig{
	Host: "https://hmi.chengjiukehu.com/padCV-det-recog/det",
}

func TestHybridOcr(t *testing.T) {
	c := resty.New().SetBaseURL(cfg.Host)
	svc := NewSvc(c, log.With(log.DefaultLogger))
	if svc == nil {
		t.Fatalf("svc is nil")
	}

	req := HybridOcrReq{}
	resp, err := svc.HybridOcr(context.Background(), &req)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println(resp)
}
