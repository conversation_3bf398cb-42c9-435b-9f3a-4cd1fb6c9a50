package oss

import (
	"bytes"
	"context"
	"errors"
	"github.com/go-resty/resty/v2"
	"hw-paas-service/internal/pkg/utils"
	"path"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-module/carbon"
)

type Svc struct {
	log    *log.Helper
	client *resty.Client
}

func NewSvc(client *resty.Client, logger log.Logger) *Svc {
	return &Svc{
		client: client,
		log:    log.<PERSON><PERSON>per(logger),
	}
}
func (s *Svc) Upload2Oss(ctx context.Context, file []byte, fileName string) (string, error) {
	var form = make(map[string]string)
	uploadTargetPath := path.Join("/ai_tools/image", carbon.Now().Layout(carbon.DateLayout)) + "/"
	form["containerName"] = "homeworkcorrect101"
	form["targetPath"] = uploadTargetPath
	form["fileName"] = fileName

	var data Response
	resp, err := s.client.R().
		SetFileReader("file", fileName, bytes.NewReader(file)).
		SetFormData(form).
		SetResult(&data).
		Post("/inner/public/blob/form-data/upload")
	if err != nil {
		s.log.WithContext(ctx).Errorf("upload file to oss failed, err: %v", err)
		return "", err
	}

	if resp.StatusCode() != 200 {
		s.log.WithContext(ctx).Errorf("upload file to oss failed, err: %v", errors.New(string(resp.Body())))
		return "", errors.New(string(resp.Body()))
	}

	return utils.ImageOss2Cdn(data.Data.Url), nil
}

type Response struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Url string `json:"url"`
	} `json:"data"`
}
