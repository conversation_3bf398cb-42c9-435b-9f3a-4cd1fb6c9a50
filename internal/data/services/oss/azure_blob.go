package oss

import (
	"context"
	"fmt"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-module/carbon"
)

const serviceUrl = "***************************************************?sp=racwli&st=2025-07-02T10:56:38Z&se=2025-12-31T16:00:00Z&spr=https&sv=2024-11-04&sr=c&sig=HujV8Tsz67a0uSWKCt7Q1%2FFtd4uJ0qOhblLaDunLkGQ%3D"

type AzureBlob struct {
	log    *log.Helper
	client *azblob.Client
}

func NewAzureBlob(logger log.Logger) *AzureBlob {
	client, err := azblob.NewClientWithNoCredential(serviceUrl, &azblob.ClientOptions{})
	if err != nil {
		panic(err) // Handle error appropriately in production code
	}
	return &AzureBlob{
		client: client,
		log:    log.NewHelper(logger),
	}
}

// https://yach-doc-shimo.zhiyinlou.com/docs/dPkpKZKxJDhNnrqO/ 《Azure blob桌面客户端-使用手册》
func (s *AzureBlob) UploadFile(ctx context.Context, fileName string, fileBytes []byte) (string, error) {
	containerName := fmt.Sprintf("%s/%s", "pdf", carbon.Now().Layout(carbon.ShortDateLayout))
	_, err := s.client.UploadBuffer(ctx, containerName, fileName, fileBytes, &azblob.UploadBufferOptions{})

	url := fmt.Sprintf("%s/%s/%s", "https://static.thinkbuddycdn.com/edu-com101", containerName, fileName)
	return url, err
}
