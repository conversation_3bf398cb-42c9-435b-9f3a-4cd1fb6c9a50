package rn_version_control_svc

import (
	"context"
	"time"

	"hw-paas-service/internal/common"
)

var BundleVersions = map[string][]Skill{} // version -> skillItem

func (r *RnVersionControlSvc) refreshData() {
	defer func() {
		if err := recover(); err != nil {
			r.log.Errorf("refreshData panic: %v", err)
		}
	}()
	// 1. 加载版本信息
	if err := r.loadVersions(); err != nil {
		r.log.Errorf("loadVersions failed: %v", err)
	}
	var reloadTime int64 = 0
	t := time.NewTimer(0)
	defer t.Stop()
	for {
		<-t.C
		_ = r.loadVersions()
		r.log.Info("Start load RecommendTypes and RecommendRules")
		t.Reset(time.Duration(common.RandInt64(10, 60)+reloadTime) * time.Second)
	}
}

func (r *RnVersionControlSvc) loadVersions() (err error) {
	r.log.Info("loadVersions start")
	// 1. 获取所有版本
	appVersions, err := r.GetAllVersions(context.Background())
	if err != nil {
		return
	}
	bundleVersions := make(map[string][]Skill)
	// 2. 获取所有版本的bundle
	for _, version := range appVersions {
		var rnList []Skill
		rnList, err = r.GetRnList(context.Background(), version)
		if err != nil {
			return
		}
		bundleVersions[version] = rnList
	}
	BundleVersions = bundleVersions
	return
}
