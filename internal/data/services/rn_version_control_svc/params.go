package rn_version_control_svc

type GetVersionsResponse struct {
	Code int      `json:"code"`
	Data []string `json:"data"`
}

type BundleItem struct {
	BundleName    string `json:"bundle_name"`
	BundleVersion string `json:"bundle_version"`
	Level         string `json:"level"`
	Force         int32  `json:"force"`
	Checksum      string `json:"checksum"`
	DownloadUrl   string `json:"download_url"`
}

type Skill struct {
	SkillName  string       `json:"skill_name"`
	BundleList []BundleItem `json:"bundle_list"`
}

type GetRnListResponse struct {
	Code int     `json:"code"`
	Data []Skill `json:"data"`
}
