package rn_version_control_svc

import (
	"context"
	"testing"

	"github.com/go-resty/resty/v2"
	"hw-paas-service/internal/conf"
	"hw-paas-service/pkg/zlog"
)

var cfg = &conf.ServiceBaseConfig{
	Host: "https://xiaosi-test.xuepaipai.com/rn-version-control-be",
}

func TestRnVersionControlSvc_GetAllVersions(t1 *testing.T) {
	c := resty.New().SetBaseURL(cfg.Host)
	client := NewSvc(c, zlog.NewZapLogger(nil))
	versions, _ := client.GetAllVersions(context.Background())
	t1.Log(versions)
}

func TestRnVersionControlSvc_GetRnList(t1 *testing.T) {
	c := resty.New().SetBaseURL(cfg.Host)
	client := NewSvc(c, zlog.NewZapLogger(nil))
	rnList, _ := client.GetRnList(context.Background(), "********")
	t1.Log(rnList)
}
