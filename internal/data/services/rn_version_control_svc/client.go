package rn_version_control_svc

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
)

const (
	GetVersionsUrl = "skill-app-versions/api/v1/version/list"
	GetRnListUrl   = "publish-versions/api/v1/details"
)

type RnVersionControlSvc struct {
	log    *log.Helper
	Client *resty.Client
}

func NewSvc(client *resty.Client, logger log.Logger) (svc *RnVersionControlSvc) {
	svc = &RnVersionControlSvc{
		log:    log.<PERSON><PERSON>elper(logger),
		Client: client,
	}
	go svc.refreshData()
	return
}

func (r *RnVersionControlSvc) GetAllVersions(ctx context.Context) (versions []string, err error) {
	result := GetVersionsResponse{}
	res, err := r.Client.R().
		SetContext(ctx).
		SetResult(&result).
		Get(GetVersionsUrl)
	if err != nil || !res.IsSuccess() {
		r.log.WithContext(ctx).Errorf("get all versions failed: %v, res:%+v", err, res)
		return
	}
	versions = result.Data
	return
}

func (r *RnVersionControlSvc) GetRnList(ctx context.Context, version string) (rnList []Skill, err error) {
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	params := map[string]string{
		"app_version": version,
	}
	result := GetRnListResponse{}
	resp, err := r.Client.R().
		SetHeaders(headers).
		SetContext(ctx).
		SetResult(&result).
		SetQueryParams(params).
		Get(GetRnListUrl)
	if err != nil || !resp.IsSuccess() {
		r.log.WithContext(ctx).Errorf("get rn list failed: %v, version:%v, res:%+v", err, version, resp)
		return
	}
	rnList = result.Data
	return
}

func (r *RnVersionControlSvc) GetRnListByAppVersion(appVersion string) (rnList []Skill, err error) {
	rnList = BundleVersions[appVersion]
	return
}
