package base64_upload

type Base64UploadReq struct {
	Base64  []byte `json:"base64"`
	Dir     string `json:"dir"`
	FileExt string `json:"file_ext"`
}

type Base64UploadResponse struct {
	ClientTraceID string            `json:"client_trace_id"`
	ErrorCode     int               `json:"error_code"`
	ErrorMessage  string            `json:"error_message"`
	RetryStatus   int32             `json:"retry_status"`
	CostTime      float64           `json:"cost_time"`
	Data          *Base64UploadData `json:"data"`
}

type Base64UploadData struct {
	Url string `json:"url"`
}
