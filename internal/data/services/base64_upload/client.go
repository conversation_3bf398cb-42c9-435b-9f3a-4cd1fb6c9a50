package base64_upload

import (
	"context"
	"encoding/base64"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	jsoniter "github.com/json-iterator/go"
	"time"
)

const uploadPath = "/infra-service-test/api/v1/oss/upload/base64"

type Svc struct {
	log    *log.Helper
	Client *resty.Client
}

func NewSvc(client *resty.Client, logger log.Logger) *Svc {
	return &Svc{
		log:    log.<PERSON>Helper(logger),
		Client: client,
	}
}

func (s *Svc) Base64Upload(ctx context.Context, base64Img string) (*Base64UploadData, error) {
	base64B, _ := base64.StdEncoding.DecodeString(base64Img)
	req := Base64UploadReq{
		Base64:  base64B,
		Dir:     "tool-bff/voice",
		FileExt: ".jpg",
	}
	//body, _ := jsoniter.Marshal(req)
	//s.log.WithContext(ctx).Infof("dw_base64_upload_req: %v", string(body))
	start := time.Now()
	res, err := s.Client.R().
		SetContext(ctx).
		SetBody(req).
		Post(uploadPath)
	// 计算响应时间
	duration := time.Since(start)
	if err != nil || !res.IsSuccess() {
		s.log.WithContext(ctx).Errorf("base64 upload failed, err: %v, res:%+v", err, res)
		return nil, err
	}

	resp := Base64UploadResponse{}
	err = jsoniter.Unmarshal(res.Body(), &resp)
	if err != nil {
		s.log.WithContext(ctx).Errorf("base64 upload failed, unmarshal err: %v, res:%+v", err, resp)
		return nil, err
	}
	if resp.ErrorCode != 0 {
		s.log.WithContext(ctx).Errorf("base64 upload failed, response res:%+v", resp)
		return nil, errors.New(resp.ErrorMessage)
	}

	reply := resp.Data

	traceInfo := map[string]interface{}{
		"resp_time": duration.Milliseconds(),
		"data":      reply,
	}
	traceRes, _ := jsoniter.Marshal(traceInfo)
	s.log.WithContext(ctx).Infof("dw_base64_upload_resp len is : %v", len(traceRes))

	return reply, nil
}
