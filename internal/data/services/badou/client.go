package badou

import (
	"context"
	"fmt"
	"net/http"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
)

const (
	IsBindPath = "/leap_plat/innerapi/v1/aigc_session/is_bind"
)

type Svc struct {
	log    *log.Helper
	client *resty.Client
}

func NewSvc(client *resty.Client, logger log.Logger) *Svc {
	return &Svc{
		log:    log.<PERSON>Helper(logger),
		client: client,
	}
}

// CheckIsBind 检查用户是否绑定
func (c *Svc) CheckIsBind(ctx context.Context, id int64) (bool, error) {
	req := &IsBindReq{
		ID: id,
	}

	var result IsBindResp
	res, err := c.client.
		R().
		SetBody(req).
		SetResult(&result).
		Post(IsBindPath)

	if err != nil {
		return false, fmt.Errorf("CheckIsBind error: %v", err)
	}

	if res.StatusCode() != http.StatusOK {
		return false, fmt.Errorf("CheckIsBind response status: %d, body: %s", res.StatusCode(), string(res.Body()))
	}

	if result.Code != 0 {
		return false, fmt.Errorf("CheckIsBind response code: %d, msg: %s", result.Code, result.Msg)
	}

	return result.Data.IsBind, nil
}
