package dao

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
)

type Gpt4oWordsDao struct {
	data *data.Data
	log  *log.Helper
}

func NewGpt4oWordsDao(data *data.Data, logger log.Logger) *Gpt4oWordsDao {
	return &Gpt4oWordsDao{
		data: data,
		log:  log.<PERSON>elper(log.With(logger, "module", "data/gpt4o-words-dao")),
	}
}

func (d *Gpt4oWordsDao) Create(ctx context.Context, record *model.HwEnWordGpt4o) error {
	err := d.data.DB.WithContext(ctx).Create(&record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("Gpt4oWords Create error: %v", err)
	}
	return err
}

func (d *Gpt4oWordsDao) BatchCreate(ctx context.Context, records []*model.HwEnWordGpt4o) error {
	var err error
	for _, record := range records {
		e := d.Create(ctx, record)
		if e != nil {
			err = e
		}
	}
	return err
}

func (d *Gpt4oWordsDao) UpdateGpt4oWords(ctx context.Context, record *model.HwEnWordGpt4o) error {
	err := d.data.DB.WithContext(ctx).Model(&model.HwEnWordGpt4o{}).Where("id = ?", record.ID).Updates(record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("UpdateGpt4oWords error: %v", err)
		return err
	}
	return err
}

func (d *Gpt4oWordsDao) FindGpt4oWords(ctx context.Context, search *model.HwEnWordGpt4oSearch) ([]*model.HwEnWordGpt4o, error) {
	var rows []*model.HwEnWordGpt4o
	query := d.data.DB.WithContext(ctx)
	if search.Word != "" {
		query = query.Where("BINARY word = ?", search.Word)
	}
	err := query.Where("id>13269 and id<14823").Order("id desc").Find(&rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("FindGpt4oWords error: %v", err)
		return rows, err
	}
	return rows, nil
}
