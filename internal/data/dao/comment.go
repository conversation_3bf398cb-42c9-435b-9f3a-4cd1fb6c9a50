package dao

import (
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"

	"github.com/go-kratos/kratos/v2/log"

	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
)

type CommentRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewCommentRepo(data *data.Data, logger log.Logger) *CommentRepo {
	return &CommentRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// ListComments 查询评论列表
func (d *CommentRepo) ListComments(ctx context.Context, subjectType int, subjectId string, parentID uint64, page, pageSize int) ([]*model.Comment, int, error) {
	var comments []*model.Comment

	db := d.data.DB.WithContext(ctx).Model(&model.Comment{})

	// 构建查询条件
	if subjectType > 0 {
		db = db.Where("subject_type in ?", []int{subjectType, 0})
	}

	if subjectId != "" {
		db = db.Where("subject_id = ?", subjectId)
	}

	if parentID > 0 {
		db = db.Where("parent_id = ?", parentID)
	} else {
		// 如果parentID为0，查询一级评论
		db = db.Where("parent_id = ?", 0)
	}

	// 查询当前数据库中评论总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to query comments: %w", err)
	}

	// 分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		db = db.Offset(offset).Limit(pageSize)
	}

	// 按创建时间倒序排列
	db = db.Order("created_at DESC")

	if err := db.Find(&comments).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*model.Comment{}, 0, nil
		}
		return nil, 0, fmt.Errorf("failed to query comments: %w", err)
	}

	return comments, int(total), nil
}
