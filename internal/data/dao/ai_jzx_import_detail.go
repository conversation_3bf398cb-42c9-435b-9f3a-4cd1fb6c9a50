package dao

import (
	"context"
	"errors"
	"hw-paas-service/internal/data/model"

	"gorm.io/gorm"
)

func (d *AiJzxQuestionDao) CreateImportDetail(ctx context.Context, record *model.AiJzxImportDetail) error {
	err := d.data.DB.WithContext(ctx).Save(&record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail Create error: %v", err)
	}
	return err
}

func (d *AiJzxQuestionDao) BatchCreateImportDetail(ctx context.Context, records []*model.AiJzxImportDetail) error {
	if len(records) == 0 {
		return nil
	}

	// 开启事务
	tx := d.data.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 分批处理，每批最多100条
	batchSize := 100
	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}

		batch := records[i:end]
		if err := tx.Create(&batch).Error; err != nil {
			tx.Rollback()
			d.log.WithContext(ctx).Errorf("AiJzxImportDetail BatchCreate error: %v", err)
			return err
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail BatchCreate commit error: %v", err)
		return err
	}

	return nil
}

func (d *AiJzxQuestionDao) DeleteImportDetailByImportId(ctx context.Context, importId int64) error {
	err := d.data.DB.WithContext(ctx).Where("import_id = ?", importId).Delete(&model.AiJzxImportDetail{}).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail DeleteImportDetailByImportId error: %v", err)
		return err
	}
	return nil
}

func (d *AiJzxQuestionDao) FindImportDetail(ctx context.Context, id int64) (*model.AiJzxImportDetail, error) {
	var record model.AiJzxImportDetail
	err := d.data.DB.WithContext(ctx).Where("id = ?", id).First(&record).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail Find error: %v", err)
	}
	return &record, nil
}

func (d *AiJzxQuestionDao) UpdateImportDetail(ctx context.Context, record *model.AiJzxImportDetail) error {
	err := d.data.DB.WithContext(ctx).Model(&model.AiJzxImportDetail{}).Where("id = ?", record.ID).Save(record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail Update error: %v", err)
		return err
	}
	return nil
}

func (d *AiJzxQuestionDao) UpdateImportDetailWithKey(ctx context.Context, id int64, fields map[string]interface{}) error {
	err := d.data.DB.WithContext(ctx).Model(&model.AiJzxImportDetail{}).Where("id = ?", id).Updates(fields).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail UpdateWithKey error: %v", err)
		return err
	}
	return nil
}

func (d *AiJzxQuestionDao) ListImportDetailWithPage(ctx context.Context, importID int64, page, pageSize int64) ([]*model.AiJzxImportDetail, int64, error) {
	var rows []*model.AiJzxImportDetail
	var total int64
	query := d.data.DB.WithContext(ctx).Model(&model.AiJzxImportDetail{})
	if importID > 0 {
		query = query.Where("import_id = ?", importID)
	}
	err := query.Count(&total).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail ListWithPage error: %v", err)
		return nil, 0, err
	}
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	query = query.Offset(int((page - 1) * pageSize)).Limit(int(pageSize))
	err = query.Order("id desc").Find(&rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail ListWithPage error: %v", err)
		return nil, 0, err
	}
	return rows, total, nil
}

func (d *AiJzxQuestionDao) GetAllRepeatQuestionIdByImportId(ctx context.Context, importID int64) ([]int32, error) {
	var rows []int32
	err := d.data.DB.WithContext(ctx).Model(&model.AiJzxImportDetail{}).Where("import_id = ? AND status = ?", importID, model.ImportDetailStatusRepeat).Order("question_id desc").Pluck("question_id", &rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail GetAllImportDetailRepeatQuestionIdByImportId error: %v", err)
		return nil, err
	}
	return rows, nil
}

func (d *AiJzxQuestionDao) GetAllFailedReasonByImportId(ctx context.Context, importID, page, pageSize int64) (list []*model.AiJzxImportDetail, total int64, err error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	var rows []*model.AiJzxImportDetail
	db := d.data.DB.WithContext(ctx).Model(&model.AiJzxImportDetail{}).Where("import_id = ? AND status = ?", importID, model.ImportDetailStatusFailed)
	err = db.Count(&total).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail GetAllFailedReasonByImportId error: %v", err)
		return nil, 0, err
	}
	err = db.Order("question_id desc").Offset(int((page - 1) * pageSize)).Limit(int(pageSize)).Find(&rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail GetAllFailedReasonByImportId error: %v", err)
		return nil, 0, err
	}
	return rows, total, nil
}

func (d *AiJzxQuestionDao) BatchDeleteImportDetail(ctx context.Context, ids []int64) error {
	err := d.data.DB.WithContext(ctx).Where("id IN ?", ids).Delete(&model.AiJzxImportDetail{}).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail BatchDelete error: %v", err)
		return err
	}
	return nil
}

func (d *AiJzxQuestionDao) GetImportDetailByImportID(ctx context.Context, importID int64) ([]*model.AiJzxImportDetail, error) {
	var details []*model.AiJzxImportDetail
	err := d.data.DB.WithContext(ctx).Where("import_id = ?", importID).Find(&details).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImportDetail GetByImportID error: %v", err)
		return nil, err
	}
	return details, nil
}
