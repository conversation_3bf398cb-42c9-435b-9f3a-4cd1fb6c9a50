package dao

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
)

type FeedTraceDao struct {
	data *data.Data
	log  *log.Helper
}

func NewFeedTraceDao(data *data.Data, logger log.Logger) *FeedTraceDao {
	return &FeedTraceDao{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/feedback-trace-dao")),
	}
}

func (d *FeedTraceDao) Create(ctx context.Context, record *model.FeedbackTrace) error {
	err := d.data.DB.WithContext(ctx).Create(&record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("FeedTrace Create error: %v", err)
	}
	return err
}
