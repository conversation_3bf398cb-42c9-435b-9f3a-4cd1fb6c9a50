package dao

import (
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
)

type AccountDao struct {
	data *data.Data
	log  *log.Helper
}

func NewAccountDao(data *data.Data, logger log.Logger) *AccountDao {
	return &AccountDao{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/account-dao")),
	}
}

// Add methods for AccountDao as needed, e.g., Create, Find, Update, Delete, etc.
// Example method to create an account
func (d *AccountDao) Create(ctx context.Context, account *model.Account) error {
	err := d.data.DB.WithContext(ctx).Create(account).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("Account Create error: %v", err)
		return err
	}
	return nil
}

// Example method to find an account by ID
func (d *AccountDao) Find(ctx context.Context, talId string) (*model.Account, error) {
	var account model.Account
	err := d.data.DB.WithContext(ctx).Where("tal_id = ?", talId).First(&account).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Not found
		}
		d.log.WithContext(ctx).Errorf("Account Find error: %v", err)
		return nil, err
	}
	return &account, nil
}

func (d *AccountDao) FindByEmal(ctx context.Context, email string) (*model.Account, error) {
	var account model.Account
	err := d.data.DB.WithContext(ctx).Where("email = ?", email).First(&account).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // Not found
		}
		d.log.WithContext(ctx).Errorf("Account Find error: %v", err)
		return nil, err
	}
	return &account, nil
}

// Example method to update an account
func (d *AccountDao) Update(ctx context.Context, account *model.Account) error {
	err := d.data.DB.WithContext(ctx).Model(&model.Account{}).Where("tal_id = ?", account.TalID).Updates(account).Error
	if err != nil {
		return err
	}
	return nil
}

// Example method to delete an account
func (d *AccountDao) Delete(ctx context.Context, talID string) error {
	err := d.data.DB.WithContext(ctx).Where("tal_id = ?", talID).Delete(&model.Account{}).Error
	if err != nil {
		return err
	}
	return nil
}
