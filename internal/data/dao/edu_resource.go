package dao

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

const (
	cacheKeyGrades   = "hw_paas_service:edu_resource:grades"
	cacheKeySubjects = "hw_paas_service:edu_resource:subjects"
	cacheKeyTypes    = "hw_paas_service:edu_resource:types"

	// 缓存过期时间
	cacheExpiration = 12 * time.Hour

	// 默认分页大小
	defaultPageSize = 10

	// 随机查询额外数量
	randomExtraCount = 3
)

// subjectResult 学科查询结果
type subjectResult struct {
	Subject        string
	Topic          string
	LearningModule string
}

// resourceTypeResult 资源类型查询结果
type resourceTypeResult struct {
	ResourceType string
	Topic        string
}

type EduResourceDao struct {
	data *data.Data
	log  *log.Helper
	conf *conf.Biz
}

func NewEduResourceDao(data *data.Data, logger log.Logger, biz *conf.Biz) *EduResourceDao {
	return &EduResourceDao{
		data: data,
		log:  log.NewHelper(logger),
		conf: biz,
	}
}

// cacheManager 缓存管理器
type cacheManager struct {
	rdb *data.Data
}

func (cm *cacheManager) getFromCache(ctx context.Context, key string, result interface{}) bool {
	val, err := cm.rdb.Rdb.Get(ctx, key).Result()
	if err != nil || val == "" {
		return false
	}

	if err := json.Unmarshal([]byte(val), result); err != nil {
		return false
	}
	return true
}

func (cm *cacheManager) setCache(ctx context.Context, key string, data interface{}) {
	if marshaled, err := json.Marshal(data); err == nil {
		_ = cm.rdb.Rdb.Set(ctx, key, marshaled, cacheExpiration).Err()
	}
}

// deleteCache 删除指定缓存
func (cm *cacheManager) deleteCache(ctx context.Context, key string) error {
	return cm.rdb.Rdb.Del(ctx, key).Err()
}

// clearAllCache 清除所有教育资源相关缓存
func (cm *cacheManager) clearAllCache(ctx context.Context) error {
	keys := []string{cacheKeyGrades, cacheKeySubjects, cacheKeyTypes}
	return cm.rdb.Rdb.Del(ctx, keys...).Err()
}

// queryBuilder 查询构建器
type queryBuilder struct {
	db *gorm.DB
}

func newQueryBuilder(db *gorm.DB) *queryBuilder {
	return &queryBuilder{db: db}
}

func (qb *queryBuilder) applyGradeFilter(grade string) *queryBuilder {
	if grade != "" {
		qb.db = qb.db.Where("FIND_IN_SET(?,grade)", grade)
	}
	return qb
}

func (qb *queryBuilder) applySubjectFilter(subject string) *queryBuilder {
	if subject != "" {
		qb.db = qb.db.Where("subject in ?", []string{"SubjectAll", subject})
	}
	return qb
}

func (qb *queryBuilder) applyResourceTypeFilter(resourceType string) *queryBuilder {
	if resourceType != "" {
		qb.db = qb.db.Where("resource_type = ?", resourceType)
	}
	return qb
}

func (qb *queryBuilder) applyTopicFilter(topic string) *queryBuilder {
	if topic != "" {
		qb.db = qb.db.Where("topic = ?", topic)
	}
	return qb
}

func (qb *queryBuilder) applyLearningModuleFilter(learningModule string) *queryBuilder {
	if learningModule != "" {
		qb.db = qb.db.Where("learning_module = ?", learningModule)
	}
	return qb
}

func (qb *queryBuilder) applyResourceDetailFilter(resourceDetail string) *queryBuilder {
	if resourceDetail != "" {
		qb.db = qb.db.Where("resource_detail = ?", resourceDetail)
	}
	return qb
}

func (qb *queryBuilder) applyResourceFilter(resource string) *queryBuilder {
	if resource != "" {
		qb.db = qb.db.Where("resource = ?", resource)
	}
	return qb
}

func (qb *queryBuilder) applyPagination(page, pageSize int) *queryBuilder {
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		qb.db = qb.db.Offset(offset).Limit(pageSize)
	}
	return qb
}

func (qb *queryBuilder) applyOrderBy(orderBy string) *queryBuilder {
	if orderBy == "" {
		orderBy = "created_at desc"
	}
	qb.db = qb.db.Order(orderBy)
	return qb
}

func (qb *queryBuilder) build() *gorm.DB {
	return qb.db
}

// GetGrade 获取年级列表
func (d *EduResourceDao) GetGrade(ctx context.Context) ([]*model.Grade, error) {
	var result []*model.Grade

	// 尝试从缓存获取
	cm := &cacheManager{rdb: d.data}
	if cm.getFromCache(ctx, cacheKeyGrades, &result) {
		return result, nil
	}

	// 从配置生成年级列表
	result = d.buildGradeList()

	// 写入缓存
	cm.setCache(ctx, cacheKeyGrades, result)

	return result, nil
}

// buildGradeList 构建年级列表
func (d *EduResourceDao) buildGradeList() []*model.Grade {
	result := make([]*model.Grade, 0, len(d.conf.Grades))
	for _, grade := range d.conf.Grades {
		result = append(result, &model.Grade{
			Name:  grade.Name,
			Value: grade.Value,
		})
	}
	return result
}

// GetSubject 获取学科列表
func (d *EduResourceDao) GetSubject(ctx context.Context) ([]*model.SubjectNode, error) {
	var result []*model.SubjectNode

	// 尝试从缓存获取
	cm := &cacheManager{rdb: d.data}
	if cm.getFromCache(ctx, cacheKeySubjects, &result) {
		return result, nil
	}

	// 从数据库构建学科树
	result, err := d.buildSubjectTree(ctx)
	if err != nil {
		return nil, err
	}

	// 写入缓存
	cm.setCache(ctx, cacheKeySubjects, result)

	return result, nil
}

// buildSubjectTree 构建学科树
func (d *EduResourceDao) buildSubjectTree(ctx context.Context) ([]*model.SubjectNode, error) {
	var results []subjectResult
	db := d.data.DB.WithContext(ctx).Model(&model.EduResources{})

	db = db.Where("resource_type in ?", []string{"worksheets"})

	if err := db.Distinct("subject", "topic", "learning_module").
		Order("subject, topic, learning_module desc").
		Find(&results).Error; err != nil {
		return nil, err
	}

	return d.buildSubjectNodes(results), nil
}

// buildSubjectNodes 构建学科节点
func (d *EduResourceDao) buildSubjectNodes(results []subjectResult) []*model.SubjectNode {
	result := make([]*model.SubjectNode, 0)
	subjectMap := make(map[string]*model.SubjectNode)
	topicMap := make(map[string]*model.SubjectNode)

	for _, res := range results {
		subject, ok := subjectMap[res.Subject]
		if !ok {
			subject = &model.SubjectNode{
				Name:  res.Subject,
				Nodes: make([]*model.SubjectNode, 0),
			}
			subjectMap[res.Subject] = subject
			result = append(result, subject)
		}

		topicKey := fmt.Sprintf("%s-%s", res.Subject, res.Topic)
		topic, ok := topicMap[topicKey]
		if !ok {
			topic = &model.SubjectNode{
				Name:  res.Topic,
				Nodes: make([]*model.SubjectNode, 0),
			}
			subject.Nodes = append(subject.Nodes, topic)
			topicMap[topicKey] = topic
		}

		topic.Nodes = append(topic.Nodes, &model.SubjectNode{
			Name: res.LearningModule,
		})
	}

	return result
}

// GetResourceType 获取资源类型列表
func (d *EduResourceDao) GetResourceType(ctx context.Context) ([]*model.ResourceType, error) {
	var result []*model.ResourceType

	// 尝试从缓存获取
	cm := &cacheManager{rdb: d.data}
	if cm.getFromCache(ctx, cacheKeyTypes, &result) {
		return result, nil
	}

	// 从数据库构建资源类型列表
	result, err := d.buildResourceTypeList(ctx)
	if err != nil {
		return nil, err
	}

	// 写入缓存
	cm.setCache(ctx, cacheKeyTypes, result)

	return result, nil
}

// buildResourceTypeList 构建资源类型列表
func (d *EduResourceDao) buildResourceTypeList(ctx context.Context) ([]*model.ResourceType, error) {
	var results []*resourceTypeResult
	db := d.data.DB.WithContext(ctx).Model(&model.EduResources{})

	if err := db.Distinct("resource_type, topic").
		Order("resource_type, topic").
		Find(&results).Error; err != nil {
		return nil, err
	}

	return d.processResourceTypes(results), nil
}

// processResourceTypes 处理资源类型数据
func (d *EduResourceDao) processResourceTypes(results []*resourceTypeResult) []*model.ResourceType {
	result := make([]*model.ResourceType, 0, len(results))
	existMap := make(map[string]struct{})

	for _, res := range results {
		topic := res.Topic
		if !strings.Contains(strings.ToLower(strings.ReplaceAll(res.ResourceType, " ", "")), "coloringpages") {
			topic = ""
		}

		key := fmt.Sprintf("%s-%s", res.ResourceType, topic)
		if _, ok := existMap[key]; ok {
			continue
		}

		result = append(result, &model.ResourceType{
			Name:  res.ResourceType,
			Topic: topic,
		})
		existMap[key] = struct{}{}
	}

	return result
}

// GetResourceList 获取资源列表
func (d *EduResourceDao) GetResourceList(ctx context.Context, req *model.ResourceReq) ([]*model.EduResources, error) {
	var resources []*model.EduResources

	qb := newQueryBuilder(d.data.DB.WithContext(ctx).Model(&model.EduResources{}))
	qb.applyGradeFilter(req.Grade).
		applySubjectFilter(req.Subject).
		applyResourceTypeFilter(req.ResourceType).
		applyTopicFilter(req.Topic).
		applyLearningModuleFilter(req.LearningModule).
		applyResourceDetailFilter(req.ResourceDetail).
		applyResourceFilter(req.Resource).
		applyPagination(req.Page, req.PageSize).
		applyOrderBy("created_at desc")

	if err := qb.build().Find(&resources).Error; err != nil {
		return nil, err
	}

	return resources, nil
}

// GetResourceGroup 获取资源分组
func (d *EduResourceDao) GetResourceGroup(ctx context.Context, req *model.ResourceReq) ([]*model.EduResourceGroup, error) {
	groupField := d.determineGroupField(req)

	if groupField == "" {
		return d.getResourceGroupWithoutGrouping(ctx, req)
	}

	return d.getResourceGroupWithGrouping(ctx, req, groupField)
}

// determineGroupField 确定分组字段
func (d *EduResourceDao) determineGroupField(req *model.ResourceReq) string {
	if req.Topic == "" {
		return "topic"
	}

	if strings.Contains(strings.ToLower(strings.ReplaceAll(req.ResourceType, " ", "")), "coloringpages") {
		return ""
	}

	if req.LearningModule == "" {
		return "learning_module"
	}

	return ""
}

// getResourceGroupWithoutGrouping 无分组获取资源
func (d *EduResourceDao) getResourceGroupWithoutGrouping(ctx context.Context, req *model.ResourceReq) ([]*model.EduResourceGroup, error) {
	var resources []*model.EduResources
	var total int64

	qb := newQueryBuilder(d.data.DB.WithContext(ctx).Model(&model.EduResources{}))
	qb.applyGradeFilter(req.Grade).
		applySubjectFilter(req.Subject).
		applyResourceTypeFilter(req.ResourceType).
		applyTopicFilter(req.Topic).
		applyLearningModuleFilter(req.LearningModule).
		applyResourceDetailFilter(req.ResourceDetail).
		applyResourceFilter(req.Resource).
		applyPagination(req.Page, req.PageSize)

	db := qb.build()

	if err := db.Count(&total).Error; err != nil {
		return nil, err
	}

	if err := db.Order("created_at desc").Find(&resources).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*model.EduResourceGroup{}, nil
		}
		return nil, err
	}

	titleMapCount := make(map[string]int)
	titleMapCount[""] = int(total)
	return d.buildResourceGroups(resources, req, titleMapCount), nil
}

// GroupResult 分组结果
type GroupResult struct {
	Count     int    `json:"count"`
	Ids       string `json:"ids"`
	GroupName string `json:"group_name"`
}

// getResourceGroupWithGrouping 有分组获取资源
func (d *EduResourceDao) getResourceGroupWithGrouping(ctx context.Context, req *model.ResourceReq, groupField string) ([]*model.EduResourceGroup, error) {
	var groupResults []*GroupResult
	var resources []*model.EduResources

	// 获取分组统计
	fields := fmt.Sprintf("%s as group_name, count(id) as count, GROUP_CONCAT(id) as ids", groupField)
	qb := newQueryBuilder(d.data.DB.WithContext(ctx).Model(&model.EduResources{}))
	qb.applyGradeFilter(req.Grade).
		applySubjectFilter(req.Subject).
		applyResourceTypeFilter(req.ResourceType).
		applyTopicFilter(req.Topic).
		applyLearningModuleFilter(req.LearningModule).
		applyResourceDetailFilter(req.ResourceDetail).
		applyResourceFilter(req.Resource)

	if err := qb.build().Select(fields).Group(groupField).Find(&groupResults).Error; err != nil {
		return nil, err
	}

	// 获取资源详情
	idList := d.extractResourceIds(groupResults)
	if err := d.data.DB.Where("id in ?", idList).Find(&resources).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*model.EduResourceGroup{}, nil
		}
		return nil, err
	}

	titleCountMap := make(map[string]int)
	for _, resource := range groupResults {
		titleCountMap[resource.GroupName] = resource.Count
	}

	return d.buildResourceGroups(resources, req, titleCountMap), nil
}

// extractResourceIds 提取资源ID列表
func (d *EduResourceDao) extractResourceIds(groupResults []*GroupResult) []int {
	idList := make([]int, 0)

	for _, groupResult := range groupResults {
		ids := strings.Split(groupResult.Ids, ",")
		count := 0
		for _, id := range ids {
			if idInt, err := strconv.Atoi(id); err == nil {
				idList = append(idList, idInt)
				count++
				if count >= 4 {
					break
				}
			}
		}
	}

	return idList
}

// buildResourceGroups 构建资源分组
func (d *EduResourceDao) buildResourceGroups(resources []*model.EduResources, req *model.ResourceReq, titleMapCount map[string]int) []*model.EduResourceGroup {
	groupMap := make(map[string]*model.EduResourceGroup)

	for _, resource := range resources {
		groupKey, title := d.determineGroupKeyAndTitle(resource, req)

		total := 0
		if titleMapCount != nil {
			total = titleMapCount[title]
		}
		group, ok := groupMap[groupKey]
		if !ok {
			group = &model.EduResourceGroup{
				Title:     title,
				Count:     total,
				Resources: make([]*model.EduResources, 0),
			}
			groupMap[groupKey] = group
		}
		group.Resources = append(group.Resources, resource)
	}

	return d.sortResourceGroups(groupMap)
}

// determineGroupKeyAndTitle 确定分组键和标题
func (d *EduResourceDao) determineGroupKeyAndTitle(resource *model.EduResources, req *model.ResourceReq) (string, string) {
	if req.Topic == "" {
		return fmt.Sprintf("/%s", resource.Topic), resource.Topic
	}

	if req.LearningModule == "" {
		return fmt.Sprintf("/%s", resource.LearningModule), resource.LearningModule
	}

	if req.ResourceDetail == "" {
		return fmt.Sprintf("/%s", resource.ResourceDetail), resource.ResourceDetail
	}

	return "", ""
}

// sortResourceGroups 排序资源分组
func (d *EduResourceDao) sortResourceGroups(groupMap map[string]*model.EduResourceGroup) []*model.EduResourceGroup {
	result := make([]*model.EduResourceGroup, 0, len(groupMap))

	for _, group := range groupMap {
		result = append(result, group)
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].Title < result[j].Title
	})

	return result
}

// GetResourceTitle 获取资源标题
func (d *EduResourceDao) GetResourceTitle(ctx context.Context, url string) (*model.EduResourcesTitle, error) {
	var title *model.EduResourcesTitle
	db := d.data.DB.WithContext(ctx).Model(&model.EduResourcesTitle{})

	err := db.Where("url = ?", url).First(&title).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		db1 := d.data.DB.WithContext(ctx).Model(&model.EduResourcesTitle{})
		if err := db1.Where("url in ?", []string{"/", "/resources"}).First(&title).Error; err != nil {
			return nil, err
		}
	}

	if title.URL == "" {
		title.URL = url
	}

	return title, nil
}

// ListRandResources 随机获取资源列表
func (d *EduResourceDao) ListRandResources(ctx context.Context, req *model.ResourceReq) ([]*model.EduResources, error) {
	var resources []*model.EduResources

	filter := d.buildRandomFilter(req)

	if req.PageSize == 0 {
		req.PageSize = defaultPageSize
	}
	pageSize := req.PageSize + randomExtraCount

	query := fmt.Sprintf("SELECT * FROM edu_resources WHERE %s ORDER BY RAND() LIMIT %d", filter, pageSize)

	if err := d.data.DB.WithContext(ctx).Raw(query).Scan(&resources).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []*model.EduResources{}, nil
		}
		return nil, err
	}

	if len(resources) < pageSize {
		req.Topic = ""
		req.LearningModule = ""
		req.ResourceDetail = ""
		req.Resource = ""

		filter1 := d.buildRandomFilter(req)

		query := fmt.Sprintf("SELECT * FROM edu_resources WHERE %s ORDER BY RAND() LIMIT %d", filter1, pageSize)

		if err := d.data.DB.WithContext(ctx).Raw(query).Scan(&resources).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return []*model.EduResources{}, nil
			}
			return nil, err
		}
		return resources, nil
	}

	return resources, nil
}

// buildRandomFilter 构建随机查询过滤器
func (d *EduResourceDao) buildRandomFilter(req *model.ResourceReq) string {
	filters := []string{"1=1"}

	if req.Grade != "" {
		filters = append(filters, fmt.Sprintf("FIND_IN_SET('%s',grade)", req.Grade))
	}
	if req.Subject != "" {
		filters = append(filters, fmt.Sprintf("subject in ('SubjectAll', '%s')", req.Subject))
	}
	if req.ResourceType != "" {
		filters = append(filters, fmt.Sprintf("resource_type = '%s'", req.ResourceType))
	}
	if req.Topic != "" {
		filters = append(filters, fmt.Sprintf("topic = '%s'", req.Topic))
	}
	if req.LearningModule != "" {
		filters = append(filters, fmt.Sprintf("learning_module = '%s'", req.LearningModule))
	}
	if req.ResourceDetail != "" {
		filters = append(filters, fmt.Sprintf("resource_detail = '%s'", req.ResourceDetail))
	}
	if req.Resource != "" {
		filters = append(filters, fmt.Sprintf("resource = '%s'", req.Resource))
	}

	return strings.Join(filters, " AND ")
}

// GetResourceByUrl 根据URL获取资源
func (d *EduResourceDao) GetResourceByUrl(ctx context.Context, url string) (*model.EduResources, error) {
	// 计算URL的MD5值
	urlMd5 := fmt.Sprintf("%x", md5.Sum([]byte(url)))

	// 首先从edu_resources_url表中根据url_md5查询resource_code
	var urlRecord *model.EduResourcesURL
	db := d.data.DB.WithContext(ctx).Model(&model.EduResourcesURL{})
	// 根据resource_code从edu_resources表中查询资源
	var resource *model.EduResources

	if err := db.Where("url_md5 = ?", urlMd5).First(&urlRecord).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resource, nil
		} else {
			return nil, fmt.Errorf("failed to find resource by url_md5: %w", err)
		}
	}

	resourceDb := d.data.DB.WithContext(ctx).Model(&model.EduResources{})

	if err := resourceDb.Where("resource_code = ?", urlRecord.ResourceCode).First(&resource).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to find resource by resource_code: %w", err)
	}

	return resource, nil
}

// GetResourcePre 获取上一个资源
func (d *EduResourceDao) GetResourcePre(ctx context.Context, req *model.ResourceReq, id int64) (*model.EduResources, error) {
	var resource *model.EduResources

	qb := newQueryBuilder(d.data.DB.WithContext(ctx).Model(&model.EduResources{}))
	qb.applyGradeFilter(req.Grade).
		applySubjectFilter(req.Subject).
		applyResourceTypeFilter(req.ResourceType).
		applyTopicFilter(req.Topic).
		applyLearningModuleFilter(req.LearningModule).
		applyResourceDetailFilter(req.ResourceDetail).
		applyResourceFilter(req.Resource).
		applyOrderBy("created_at desc")

	db := qb.build().Where("id < ?", id)

	if err := db.First(&resource).Error; err != nil {
		return nil, err
	}

	return resource, nil
}

// GetResourceNext 获取下一个资源
func (d *EduResourceDao) GetResourceNext(ctx context.Context, req *model.ResourceReq, id int64) (*model.EduResources, error) {
	var resource *model.EduResources

	qb := newQueryBuilder(d.data.DB.WithContext(ctx).Model(&model.EduResources{}))
	qb.applyGradeFilter(req.Grade).
		applySubjectFilter(req.Subject).
		applyResourceTypeFilter(req.ResourceType).
		applyTopicFilter(req.Topic).
		applyLearningModuleFilter(req.LearningModule).
		applyResourceDetailFilter(req.ResourceDetail).
		applyResourceFilter(req.Resource).
		applyOrderBy("created_at")

	db := qb.build().Where("id > ?", id)

	if err := db.First(&resource).Error; err != nil {
		return nil, err
	}

	return resource, nil
}

// ClearCache 清除所有教育资源缓存
func (d *EduResourceDao) ClearCache(ctx context.Context) error {
	cm := &cacheManager{rdb: d.data}
	return cm.clearAllCache(ctx)
}

// DeleteGradeCache 删除年级缓存
func (d *EduResourceDao) DeleteGradeCache(ctx context.Context) error {
	cm := &cacheManager{rdb: d.data}
	return cm.deleteCache(ctx, cacheKeyGrades)
}

// DeleteSubjectCache 删除学科缓存
func (d *EduResourceDao) DeleteSubjectCache(ctx context.Context) error {
	cm := &cacheManager{rdb: d.data}
	return cm.deleteCache(ctx, cacheKeySubjects)
}

// DeleteResourceTypeCache 删除资源类型缓存
func (d *EduResourceDao) DeleteResourceTypeCache(ctx context.Context) error {
	cm := &cacheManager{rdb: d.data}
	return cm.deleteCache(ctx, cacheKeyTypes)
}

// RefreshCache 刷新所有缓存（删除后重新构建）
func (d *EduResourceDao) RefreshCache(ctx context.Context) error {
	// 清除所有缓存
	if err := d.ClearCache(ctx); err != nil {
		return fmt.Errorf("failed to clear cache: %w", err)
	}

	// 重新构建缓存
	if _, err := d.GetGrade(ctx); err != nil {
		d.log.Errorf("failed to refresh grade cache: %v", err)
	}

	if _, err := d.GetSubject(ctx); err != nil {
		d.log.Errorf("failed to refresh subject cache: %v", err)
	}

	if _, err := d.GetResourceType(ctx); err != nil {
		d.log.Errorf("failed to refresh resource type cache: %v", err)
	}

	return nil
}
