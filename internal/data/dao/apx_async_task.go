package dao

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
)

type ApxAsyncTaskDao struct {
	data *data.Data
	log  *log.Helper
}

func NewApxAsyncTaskDao(data *data.Data, logger log.Logger) *ApxAsyncTaskDao {
	return &ApxAsyncTaskDao{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/apx-async-task-dao")),
	}
}

func (d *ApxAsyncTaskDao) Create(ctx context.Context, task *model.ApxAsyncTask) error {
	err := d.data.DB.WithContext(ctx).Create(&task).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("ApxAsyncTask Create error: %v", err)
	}
	return err
}
func (d *ApxAsyncTaskDao) FindWords(ctx context.Context, word string) ([]*model.ApxAsyncTask, error) {
	var rows []*model.ApxAsyncTask
	query := d.data.DB.WithContext(ctx).Where("word = ?", word)
	err := query.Order("id desc").Find(&rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("FindWords error: %v", err)
		return rows, err
	}
	return rows, nil
}
func (d *ApxAsyncTaskDao) FindNoFinishTask(ctx context.Context) ([]*model.ApxAsyncTask, error) {
	var rows []*model.ApxAsyncTask
	query := d.data.DB.WithContext(ctx).Where("status = 1")
	err := query.Find(&rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("FindNoFinishTask error: %v", err)
		return rows, err
	}
	return rows, nil
}
func (d *ApxAsyncTaskDao) Update(ctx context.Context, record *model.ApxAsyncTask) error {
	err := d.data.DB.WithContext(ctx).Model(&model.ApxAsyncTask{}).Where("id = ?", record.ID).Updates(record).Error
	if err != nil {
		return err
	}
	return nil
}

func (d *ApxAsyncTaskDao) FindAsyncTasks(ctx context.Context, search *model.ApxAsyncTaskSearch) ([]*model.ApxAsyncTask, error) {
	var rows []*model.ApxAsyncTask
	query := d.data.DB.WithContext(ctx)
	if search.Id > 0 {
		query = query.Where("id > ?", search.Id)
	}
	if search.Status > 0 {
		query = query.Where("status = ?", search.Status)
	}
	err := query.Order("id desc").Find(&rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("FindAsyncTasks error: %v", err)
		return rows, err
	}
	return rows, nil
}
