package dao

import (
	"context"
	"fmt"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type EduDao struct {
	data *data.Data
	log  *log.Helper
}

func NewEduDao(data *data.Data, logger log.Logger) *EduDao {
	return &EduDao{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/blog-article-dao")),
	}
}

// list with page
func (d *EduDao) ListBlogWithPage(ctx context.Context, page, pageSize int64, category string) ([]*model.BlogArticle, int64, error) {
	var list []*model.BlogArticle
	var total int64

	db := d.data.DB.WithContext(ctx).Model(&model.BlogArticle{})
	if category != "" {
		db = db.Where("category = ?", category)
	}

	// 查询当前时间之前的数据
	db = db.Where("created_at < ?", time.Now().Format("2006-01-02 15:04:05"))
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}
	err = db.Order("updated_at desc").Offset(int((page - 1) * pageSize)).Limit(int(pageSize)).Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (d *EduDao) SaveBlog(ctx context.Context, article *model.BlogArticle) error {
	err := d.data.DB.WithContext(ctx).Save(article).Error
	if err != nil {
		return err
	}
	return nil
}

// first with path
func (d *EduDao) FirstBlogWithPath(ctx context.Context, path string) (*model.BlogArticle, error) {
	var article model.BlogArticle
	err := d.data.DB.WithContext(ctx).Model(&model.BlogArticle{}).Where("path = ?", path).First(&article).Error
	if err != nil {
		return nil, err
	}
	return &article, nil
}

func (d *EduDao) GetBlogById(ctx context.Context, id int32) (*model.BlogArticle, error) {
	var article model.BlogArticle
	err := d.data.DB.WithContext(ctx).Model(&model.BlogArticle{}).Where("id = ?", id).First(&article).Error
	if err != nil {
		return nil, err
	}
	return &article, nil
}

func (d *EduDao) ListTerm(ctx context.Context, subject string) ([]*model.EduTerm, error) {
	var list []*model.EduTerm
	db := d.data.DB.WithContext(ctx).Model(&model.EduTerm{})
	if subject != "" {
		db = db.Where("subject = ?", subject)
	}
	err := db.Where("module_index = 1 and block_order = 1").Order("term asc").Limit(1000).Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (d *EduDao) GetTermByPath(ctx context.Context, path string) ([]*model.EduTerm, error) {
	var list []*model.EduTerm
	err := d.data.DB.WithContext(ctx).Model(&model.EduTerm{}).Where("path = ?", path).Order("module_index asc, block_order asc").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func normalizeSubject(subject string) string {
	switch strings.ToLower(strings.TrimSpace(subject)) {
	case "english", "ela":
		return "ela"
	case "math", "mathematics":
		return "math"
	default:
		return subject
	}
}

func (d *EduDao) ListRandTerm(ctx context.Context, subject string, pageSize int) ([]*model.EduTerm, error) {
	var list []*model.EduTerm

	strFilter := "module_index = 1 and block_order = 1"

	subject = normalizeSubject(subject)

	if subject != "" {
		strFilter += fmt.Sprintf(" and subject = '%s'", subject)
	}

	if pageSize == 0 {
		pageSize = 10
	}

	if err := d.data.DB.WithContext(ctx).Raw(fmt.Sprintf("SELECT * FROM edu_terms where %s ORDER BY RAND() LIMIT %d", strFilter, pageSize)).
		Scan(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (d *EduDao) BatchInsertTerm(ctx context.Context, terms []*model.EduTerm) error {

	delMap := make(map[string]struct{})

	var maxId uint
	d.data.DB.Model(&model.EduTerm{}).Select("max(id)").Scan(&maxId)
	// 开启事务
	tx := d.data.DB.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开启事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, eduTerm := range terms {

		if _, ok := delMap[eduTerm.Term]; !ok {
			var exists int64
			tx.Model(&model.EduTerm{}).Where("term = ? and id <= ? and subject = ?", eduTerm.Term, maxId, eduTerm.Subject).Count(&exists)
			if exists > 0 {
				if err := tx.Where("term = ? and id <= ? and subject = ?", eduTerm.Term, maxId, eduTerm.Subject).Delete(&model.EduTerm{}).Error; err != nil {
					tx.Rollback()
					return err
				}
				d.log.WithContext(ctx).Infof("delete term: %s", eduTerm.Term)

			}
			delMap[eduTerm.Term] = struct{}{}
		}
	}

	if err := tx.Create(&terms).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		d.log.WithContext(ctx).Errorf("commit error: %v", err)
		return err
	}

	return nil
}
