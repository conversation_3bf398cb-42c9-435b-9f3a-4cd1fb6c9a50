package dao

import (
	"context"
	"errors"
	"git.100tal.com/znxx_xpp/go-libs/traces/xelastic"
	jsoniter "github.com/json-iterator/go"
	"hw-paas-service/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/olivere/elastic/v7"
)

type ESHits struct {
	Score  float64
	Source []byte
}

type ES interface {
	Search(ctx context.Context, index string, query *elastic.BoolQuery, page, pageSize int) (int64, []*ESHits, error)
	Get(ctx context.Context, index, id string) (*structpb.Struct, error)
	Save(ctx context.Context, index, id string, info interface{}) error
	Delete(ctx context.Context, index, id string) error
	CreateIndex(ctx context.Context, index, mappings string) (bool, error)
	DelIndex(ctx context.Context, index string) (bool, error)
}

type es struct {
	conf  *conf.Data
	log   *log.Helper
	esClt *elastic.Client
}

func NewEsRepo(conf *conf.Data, logger log.Logger) ES {
	client, err := xelastic.MakeEsClient(&xelastic.ESConfig{
		Host:     conf.Es.GetHost(),
		UserName: conf.Es.GetUsername(),
		Password: conf.Es.GetPassword(),
	}, log.NewHelper(logger))
	if err != nil {
		log.NewHelper(logger).Errorf("getEsClient error, [err]:%+v", err)
		return nil
	}
	return &es{
		conf:  conf,
		log:   log.NewHelper(logger),
		esClt: client,
	}
}

func (e *es) Search(ctx context.Context, index string, query *elastic.BoolQuery, page, pageSize int) (int64, []*ESHits, error) {
	if e.esClt == nil {
		return 0, nil, errors.New("es client not found")
	}

	total, err := e.esClt.Count().Index(index).Query(query).Do(ctx)
	if total == 0 {
		return 0, nil, nil
	}

	srv := e.esClt.Search().Index(index).Query(query)
	if page > 0 && pageSize > 0 {
		srv = srv.From((page - 1) * pageSize).Size(pageSize)
	}
	resp, err := srv.Do(ctx)
	if err != nil {
		return 0, nil, err
	}

	if len(resp.Hits.Hits) == 0 {
		return 0, nil, nil
	}

	type hit struct {
		Score  *float64
		Source *structpb.Struct
	}
	list := make([]*ESHits, 0)
	for k := range resp.Hits.Hits {
		item := ESHits{
			Score: *resp.Hits.Hits[k].Score,
		}
		source, _ := resp.Hits.Hits[k].Source.MarshalJSON()
		item.Source = source
		list = append(list, &item)
	}

	return total, list, nil
}

// Get 根据id获取一条数据
func (e *es) Get(ctx context.Context, index, id string) (*structpb.Struct, error) {
	if id == "" {
		return nil, nil
	}
	resp, err := e.esClt.Get().Index(index).Id(id).Do(ctx)
	if err != nil {
		return nil, err
	}
	resJson, _ := resp.Source.MarshalJSON()
	st := &structpb.Struct{}
	err = jsoniter.Unmarshal(resJson, &st)
	if err != nil {
		return nil, err
	}
	return st, nil
}

// Save 全字段更新
func (e *es) Save(ctx context.Context, index, id string, info interface{}) error {
	if id == "" || info == nil {
		return nil
	}
	_, err := e.esClt.Index().Index(index).Id(id).BodyJson(info).Do(ctx)
	if err != nil {
		return err
	}

	return nil
}

// Delete 根据id删除一条数据
func (e *es) Delete(ctx context.Context, index, id string) error {
	if id == "" {
		return nil
	}
	_, err := e.esClt.Delete().Index(index).Id(id).Do(ctx)
	if err != nil {
		return err
	}

	return nil
}

func (e *es) CreateIndex(ctx context.Context, index, mappings string) (bool, error) {
	exists, err := e.esClt.IndexExists(index).Do(ctx)
	if err != nil {
		return false, err
	}

	if exists {
		_, err = e.esClt.IndexGetSettings(index).Do(ctx)
		return true, nil
	}

	resCreate, err := e.esClt.CreateIndex(index).BodyJson(mappings).Do(ctx)
	if err != nil || !resCreate.Acknowledged {
		return false, err
	}

	resAlias, err := e.esClt.Alias().Add(index, index+"_alias").Do(ctx)
	if err != nil || !resAlias.Acknowledged {
		return false, err
	}

	return resCreate.Acknowledged && resAlias.Acknowledged, nil
}

func (e *es) DelIndex(ctx context.Context, index string) (bool, error) {
	resp, err := e.esClt.DeleteIndex(index).Do(ctx)
	if err != nil || !resp.Acknowledged {
		return false, err
	}

	return resp.Acknowledged, nil
}
