package dao

import (
	"context"
	"errors"
	"hw-paas-service/internal/data/model"

	"gorm.io/gorm"
)

func (d *AiJzxQuestionDao) CreateImport(ctx context.Context, record *model.AiJzxImport) error {
	err := d.data.DB.WithContext(ctx).Save(&record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImport Create error: %v", err)
	}
	return err
}

func (d *AiJzxQuestionDao) BatchCreateImport(ctx context.Context, records []*model.AiJzxImport) error {
	var err error
	for _, record := range records {
		e := d.CreateImport(ctx, record)
		if e != nil {
			err = e
		}
	}
	return err
}

func (d *AiJzxQuestionDao) FindImport(ctx context.Context, id int64) (*model.AiJzxImport, error) {
	var record model.AiJzxImport
	err := d.data.DB.WithContext(ctx).Where("id = ?", id).First(&record).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImport Find error: %v", err)
	}
	return &record, nil
}

func (d *AiJzxQuestionDao) UpdateImport(ctx context.Context, record *model.AiJzxImport) error {
	err := d.data.DB.WithContext(ctx).Model(&model.AiJzxImport{}).Where("id = ?", record.ID).Save(record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImport Update error: %v", err)
		return err
	}
	return nil
}

func (d *AiJzxQuestionDao) UpdateImportWithKey(ctx context.Context, id int64, fields map[string]interface{}) error {
	err := d.data.DB.WithContext(ctx).Model(&model.AiJzxImport{}).Where("id = ?", id).Updates(fields).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImport UpdateWithKey error: %v", err)
		return err
	}
	return nil
}

func (d *AiJzxQuestionDao) ListImportWithPage(ctx context.Context, page, pageSize int64) ([]*model.AiJzxImport, int64, error) {
	var rows []*model.AiJzxImport
	var total int64
	query := d.data.DB.WithContext(ctx).Model(&model.AiJzxImport{}).Where("status != ?", model.ImportStatusCancelled)
	err := query.Count(&total).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImport ListWithPage error: %v", err)
		return nil, 0, err
	}
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}
	query = query.Offset(int((page - 1) * pageSize)).Limit(int(pageSize))
	err = query.Order("id desc").Find(&rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImport ListWithPage error: %v", err)
		return nil, 0, err
	}
	return rows, total, nil
}

func (d *AiJzxQuestionDao) BatchDeleteImport(ctx context.Context, ids []int64) error {
	err := d.data.DB.WithContext(ctx).Where("id IN ?", ids).Delete(&model.AiJzxImport{}).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxImport BatchDelete error: %v", err)
		return err
	}
	return nil
}
