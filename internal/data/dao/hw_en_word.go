package dao

import (
	"context"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type FingerWordsDao struct {
	data *data.Data
	log  *log.Helper
}

func NewFingerWordsDao(data *data.Data, logger log.Logger) *FingerWordsDao {
	return &FingerWordsDao{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/finger-words-dao")),
	}
}

func (d *FingerWordsDao) Create(ctx context.Context, record *model.HwEnWord) error {
	err := d.data.DB.WithContext(ctx).Create(&record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("FingerWords Create error: %v", err)
	}
	return err
}

func (d *FingerWordsDao) BatchCreate(ctx context.Context, records []*model.HwEnWord) error {
	var err error
	for _, record := range records {
		e := d.Create(ctx, record)
		if e != nil {
			err = e
		}
	}
	return err
}

func (d *FingerWordsDao) UpdateFingerWords(ctx context.Context, record *model.HwEnWord) error {
	err := d.data.DB.WithContext(ctx).Model(&model.HwEnWord{}).Where("id = ?", record.ID).Updates(record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("UpdateFingerWords error: %v", err)
		return err
	}
	return err
}

func (d *FingerWordsDao) IncrementHwEnWordsFrequency(ctx context.Context, words []string) error {
	err := d.data.DB.WithContext(ctx).Model(&model.HwEnWord{}).Where("word IN ?", words).Update("frequency", gorm.Expr("frequency + 1")).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("IncrementHwEnWordsFrequency error: %v", err)
		return err
	}
	return err
}

func (d *FingerWordsDao) FindFingerWords(ctx context.Context, search *model.HwEnWordSearch) ([]*model.HwEnWord, error) {
	var rows []*model.HwEnWord
	query := d.data.DB.WithContext(ctx)
	if search.MaxId > 0 {
		query = query.Where("id > ?", search.MaxId)
	}
	if search.Id > 0 {
		query = query.Where("id = ?", search.Id)
	}
	if search.Word != "" {
		query = query.Where("BINARY word = ?", search.Word)
	}
	if search.Status != 0 {
		query = query.Where("status = ?", search.Status)
	}
	if search.Page > 0 && search.PageSize > 0 {
		query = query.Offset((search.Page - 1) * search.PageSize).Limit(search.PageSize)
	}

	err := query.Order("id desc").Find(&rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("FindFingerWords error: %v", err)
		return rows, err
	}
	return rows, nil
}

func (d *FingerWordsDao) FindHwEnWords(ctx context.Context, words []string) (hwEnWordList []*model.HwEnWord, err error) {
	// 将输入的words转换为小写，用于不区分大小写的查询
	lowerWords := make([]string, len(words))
	for i, word := range words {
		lowerWords[i] = strings.ToLower(word)
	}

	err = d.data.DB.WithContext(ctx).Where("LOWER(word) IN ?", lowerWords).Where("status in ?", []model.EnWordStatus{model.EnWordStatusShelf}).Find(&hwEnWordList).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("FindHwEnWords error: %v", err)
		return hwEnWordList, err
	}
	return hwEnWordList, nil
}
