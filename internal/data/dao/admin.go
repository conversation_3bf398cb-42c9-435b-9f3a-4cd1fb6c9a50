package dao

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"hw-paas-service/internal/data"
)

type AdminRepo struct {
	data *data.Data
	log  *log.Helper
}

func NewAdminRepo(data *data.Data, logger log.Logger) *AdminRepo {
	return &AdminRepo{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/admin-repo")),
	}
}

// 执行SQL文件
func (a *AdminRepo) ExecuteSQLFile(ctx context.Context, filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("打开SQL文件失败: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var sqlStatements []string
	var currentStatement strings.Builder

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释
		if line == "" || strings.HasPrefix(line, "--") || strings.HasPrefix(line, "#") {
			continue
		}

		currentStatement.WriteString(line)
		currentStatement.WriteString(" ")

		// 检查是否是语句结束
		if strings.HasSuffix(line, ";") {
			sql := strings.TrimSpace(currentStatement.String())
			if sql != "" {
				sqlStatements = append(sqlStatements, sql)
			}
			currentStatement.Reset()
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	// 执行SQL语句
	a.log.WithContext(ctx).Infof("开始执行 %d 条SQL语句...\n", len(sqlStatements))

	successCount := 0
	errorCount := 0

	for i, sql := range sqlStatements {
		a.log.WithContext(ctx).Infof("执行第 %d 条SQL: %s\n", i+1, truncateString(sql, 100))

		if err := a.executeSQL(ctx, sql); err != nil {
			a.log.WithContext(ctx).Infof("执行失败: %v\n", err)
			errorCount++
		} else {
			successCount++
		}
	}

	a.log.WithContext(ctx).Infof("执行完成! 成功: %d, 失败: %d\n", successCount, errorCount)

	if errorCount > 0 {
		return fmt.Errorf("有 %d 条SQL执行失败", errorCount)
	}

	return nil
}

// 执行单条SQL语句
func (a *AdminRepo) executeSQL(ctx context.Context, sql string) error {
	// 使用事务执行
	return a.data.DB.Transaction(func(tx *gorm.DB) error {
		result := tx.Exec(sql)
		if result.Error != nil {
			return fmt.Errorf("执行SQL失败: %v", result.Error)
		}

		// 打印影响的行数
		if result.RowsAffected > 0 {
			a.log.WithContext(ctx).Infof("影响行数: %d\n", result.RowsAffected)
		}

		return nil
	})
}

// 截断字符串用于显示
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
