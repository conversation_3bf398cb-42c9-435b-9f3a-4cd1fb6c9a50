package dao

import (
	"context"
	"errors"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type AiJzxQuestionDao struct {
	data *data.Data
	log  *log.Helper
}

func NewAiJzxQuestionDao(data *data.Data, logger log.Logger) *AiJzxQuestionDao {
	return &AiJzxQuestionDao{
		data: data,
		log:  log.New<PERSON>elper(log.With(logger, "module", "data/ai-jzx-question-dao")),
	}
}

func (d *AiJzxQuestionDao) Create(ctx context.Context, record *model.AiJzxQuestion) error {
	err := d.data.DB.WithContext(ctx).Save(&record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxQuestion Create error: %v", err)
	}
	return err
}

func (d *AiJzxQuestionDao) BatchCreate(ctx context.Context, records []*model.AiJzxQuestion) error {
	var err error
	for _, record := range records {
		e := d.Create(ctx, record)
		if e != nil {
			err = e
		}
	}
	return err
}

func (d *AiJzxQuestionDao) Find(ctx context.Context, id int64) (*model.AiJzxQuestion, error) {
	var record model.AiJzxQuestion
	err := d.data.DB.WithContext(ctx).Where("id = ?", id).First(&record).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxQuestion Find error: %v", err)
	}
	return &record, nil
}

func (d *AiJzxQuestionDao) GetAiJzxQuestionBatch(ctx context.Context, ids []int64) ([]*model.AiJzxQuestion, error) {
	var questions []*model.AiJzxQuestion
	err := d.data.DB.WithContext(ctx).Where("id IN ?", ids).Find(&questions).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxQuestion GetAiJzxQuestionBatch error: %v", err)
		return nil, err
	}
	return questions, nil
}

func (d *AiJzxQuestionDao) Update(ctx context.Context, record *model.AiJzxQuestion) error {
	err := d.data.DB.WithContext(ctx).Model(&model.AiJzxQuestion{}).Where("id = ?", record.ID).Save(record).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxQuestion Update error: %v", err)
		return err
	}
	return nil
}

func (d *AiJzxQuestionDao) UpdateWithKey(ctx context.Context, id int64, fields map[string]interface{}) error {
	err := d.data.DB.WithContext(ctx).Model(&model.AiJzxQuestion{}).Where("id = ?", id).Updates(fields).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxQuestion UpdateWithKey error: %v", err)
		return err
	}
	return nil
}

func (d *AiJzxQuestionDao) ListWithPage(ctx context.Context, id, qType, difficulty, status int64, grade, subject, knowledge, knowledgeNo, content string, page, pageSize int64) ([]*model.AiJzxQuestion, int64, error) {
	var rows []*model.AiJzxQuestion
	var total int64
	query := d.data.DB.WithContext(ctx).Model(&model.AiJzxQuestion{})
	if id > 0 {
		query = query.Where("id = ?", id)
	}
	if qType > 0 {
		query = query.Where("q_type = ?", qType)
	}
	if difficulty > 0 {
		query = query.Where("difficulty = ?", difficulty)
	}
	if status > 0 {
		query = query.Where("status = ?", status)
	}
	if grade != "" {
		query = query.Where("grade = ?", grade)
	}
	if subject != "" {
		query = query.Where("subject = ?", subject)
	}
	if knowledge != "" {
		query = query.Where("knowledge = ?", knowledge)
	}
	if knowledgeNo != "" {
		query = query.Where("knowledge_no = ?", knowledgeNo)
	}
	if content != "" { // question,answer,solution,note模糊查询
		query = query.Where("question LIKE ? OR answer LIKE ? OR solution LIKE ? OR note LIKE ?", "%"+content+"%", "%"+content+"%", "%"+content+"%", "%"+content+"%")
	}
	err := query.Count(&total).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxQuestion ListWithPage error: %v", err)
		return nil, 0, err
	}
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	query = query.Offset(int((page - 1) * pageSize)).Limit(int(pageSize))
	err = query.Order("id desc").Find(&rows).Error
	if err != nil {
		d.log.WithContext(ctx).Errorf("AiJzxQuestion ListWithPage error: %v", err)
		return nil, 0, err
	}
	return rows, total, nil
}

func (d *AiJzxQuestionDao) GetAiJzxQuestionStatus(ctx context.Context, ids []int64) ([]*model.AiJzxQuestion, error) {
	var questions []*model.AiJzxQuestion
	err := d.data.DB.WithContext(ctx).Where("id IN ?", ids).Select("id, status").Find(&questions).Error
	return questions, err
}
