package utils

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/transport"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
	v1 "hw-paas-service/api/ai/v1"
	"net/url"
	"regexp"
	"strings"
)

func ReplyAny(data interface{}, err error) (*structpb.Struct, error) {
	if err != nil {
		return nil, err
	}

	resJson, err := json.Marshal(data)
	if err != nil {
		return nil, v1.ErrorHwPaasUnexceptError("json marshal failed")
	}
	response := &structpb.Struct{}
	if err = protojson.Unmarshal(resJson, response); err != nil {
		err = v1.ErrorHwPaasUnexceptError("protojson Unmarshal failed")
	}

	return response, err
}

func TraceId(ctx context.Context) string {
	if span := trace.SpanContextFromContext(ctx); span.HasTraceID() {
		return span.TraceID().String()
	}
	return ""
}

func DeviceId(ctx context.Context) string {
	return Header(ctx, "X-Genie-DeviceId")
}

func Header(ctx context.Context, name string) string {
	if tr, ok := transport.FromServerContext(ctx); ok {
		return tr.RequestHeader().Get(name)
	}
	return ""
}

func CleanUri(uri string) string {
	sP, _ := url.Parse(uri)
	if sP.IsAbs() {
		return sP.Path
	} else {
		clearUri := strings.ReplaceAll(uri, sP.RawQuery, "")
		return strings.TrimRight(clearUri, "?")
	}
}

// CheckIsAllPunctuation 判断字符串的每个字符都是除了汉字、字母和数字之外的字符
func CheckIsAllPunctuation(str string) bool {
	pattern := "^[^\\p{Han}\\p{L}\\p{N}]+$"
	match, _ := regexp.MatchString(pattern, str)
	return match
}

func Min(x, y int) int {
	if x < y {
		return x
	}
	return y
}

func Max(x, y int) int {
	if x > y {
		return x
	}
	return y
}

func ReplaceWhitespace(text, replacement string) string {
	regex := regexp.MustCompile(`\s+`)
	return regex.ReplaceAllString(text, replacement)
}

func RemoveWhitespace(text string) string {
	regex := regexp.MustCompile(`\s+`)
	return regex.ReplaceAllString(text, "")
}

func FormatStructPb(data interface{}) (*structpb.Struct, error) {
	resJson, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	response := &structpb.Struct{}
	err = protojson.Unmarshal(resJson, response)

	return response, err
}

var OssCdnMap = map[string]string{
	// 测试环境
	"cfcdn.chengjiukehu.com": "taloversea.blob.core.chinacloudapi.cn",
	// 线上环境
	"cfcdn.thinkbuddy.com": "taloversea.blob.core.windows.net",
}

// ImageOss2Cdn https://yach-doc-shimo.zhiyinlou.com/docs/gXqme749JNu4lRqo/ 《CDN更换说明》
func ImageOss2Cdn(imageUrl string) string {
	if imageUrl == "" {
		return ""
	}
	for k, v := range OssCdnMap {
		imageUrl = strings.Replace(imageUrl, v, k, -1)
	}
	return imageUrl
}
