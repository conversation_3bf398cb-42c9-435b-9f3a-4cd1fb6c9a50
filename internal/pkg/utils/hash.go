package utils

import (
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"math"
	"math/rand"
	"strings"
	"time"
)

// MD5Hash MD5哈希值
func MD5Hash(b []byte) string {
	h := md5.New()
	h.Write(b)
	return fmt.Sprintf("%x", h.Sum(nil))
}

// MD5HashString MD5哈希值
func MD5HashString(s string) string {
	return MD5Hash([]byte(s))
}

// SHA1Hash SHA1哈希值
func SHA1Hash(b []byte) string {
	h := sha1.New()
	h.Write(b)
	return fmt.Sprintf("%x", h.Sum(nil))
}

// SHA1HashString SHA1哈希值
func SHA1HashString(s string) string {
	return SHA1Hash([]byte(s))
}

// GetRandomString 生成随机字符串
func GetRandomString(lenS int) string {
	str := "0123456789abcdef<PERSON>ijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	bytes := []byte(str)
	result := []byte{}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < lenS; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

// ShortHash 生成简短hash
func ShortHash(input int64) string {
	binary := int64(62)
	stack := make([]string, 0)
	sign := ""
	if input < 0 {
		sign = "-"
	}

	input = int64(math.Abs(float64(input)))

	table := func(num int) string {
		//打乱顺序
		var t = strings.Split("9132465780zbdcefghjiklnmoqprtsuwvyxaZBCEDFHGIKJMLNPURQSTOVWXYA", "")
		return t[num]
	}
	for input >= binary {
		i := int(input % binary)
		input = int64(math.Floor(float64(input) / float64(binary)))
		stack = append(stack, table(i))
	}
	if input > 0 {
		stack = append(stack, table(int(input)))
	}

	return sign + strings.Join(stack, "")
}

// NewUUID 创建UUID，参考：https://github.com/google/uuid
func NewUUID() (string, error) {
	var buf [16]byte
	_, err := rand.Read(buf[:])
	if err != nil {
		return "", err
	}

	buf[6] = (buf[6] & 0x0f) | 0x40
	buf[8] = (buf[8] & 0x3f) | 0x80

	dst := make([]byte, 36)
	hex.Encode(dst, buf[:4])
	dst[8] = '-'
	hex.Encode(dst[9:13], buf[4:6])
	dst[13] = '-'
	hex.Encode(dst[14:18], buf[6:8])
	dst[18] = '-'
	hex.Encode(dst[19:23], buf[8:10])
	dst[23] = '-'
	hex.Encode(dst[24:], buf[10:])
	return string(dst), nil
}
