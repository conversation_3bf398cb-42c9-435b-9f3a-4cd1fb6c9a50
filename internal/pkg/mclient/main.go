package main

import (
	"database/sql"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

var (
	host     = flag.String("host", "localhost", "MySQL host")
	port     = flag.Int("port", 3306, "MySQL port")
	user     = flag.String("user", "root", "MySQL user")
	password = flag.String("password", "", "MySQL password")
	database = flag.String("database", "", "MySQL database name")
	sqlFile  = flag.String("file", "", "SQL file path to execute")
)

// 格式化值的函数
func formatValue(v interface{}) string {
	if v == nil {
		return "NULL"
	}

	switch val := v.(type) {
	case []byte:
		return string(val)
	case time.Time:
		return val.Format("2006-01-02 15:04:05")
	case bool:
		if val {
			return "true"
		}
		return "false"
	default:
		return fmt.Sprintf("%v", v)
	}
}

func main() {
	flag.Parse()

	if *database == "" {
		log.Fatal("Database name is required")
	}

	if *sqlFile == "" {
		log.Fatal("SQL file path is required")
	}

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		*user, *password, *host, *port, *database)

	// 连接数据库
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	// 读取SQL文件
	content, err := ioutil.ReadFile(*sqlFile)
	if err != nil {
		log.Fatalf("Failed to read SQL file: %v", err)
	}

	// 分割SQL语句
	queries := strings.Split(string(content), ";")

	// 执行每个SQL语句
	for i, query := range queries {
		query = strings.TrimSpace(query)
		if query == "" {
			continue
		}

		fmt.Printf("\n执行第 %d 条SQL语句:\n%s\n", i+1, query)
		fmt.Println("----------------------------------------")

		rows, err := db.Query(query)
		if err != nil {
			log.Printf("执行SQL失败: %v\n", err)
			continue
		}

		// 获取列名
		columns, err := rows.Columns()
		if err != nil {
			log.Printf("获取列名失败: %v\n", err)
			continue
		}

		// 准备结果集
		values := make([]interface{}, len(columns))
		scanArgs := make([]interface{}, len(columns))
		for i := range values {
			scanArgs[i] = &values[i]
		}

		// 打印列名
		fmt.Println(strings.Join(columns, "\t"))

		// 遍历结果集
		for rows.Next() {
			err = rows.Scan(scanArgs...)
			if err != nil {
				log.Printf("扫描行失败: %v\n", err)
				continue
			}

			// 打印每行数据
			rowData := make([]string, len(columns))
			for i, v := range values {
				rowData[i] = formatValue(v)
			}
			fmt.Println(strings.Join(rowData, "\t"))
		}

		if err = rows.Err(); err != nil {
			log.Printf("遍历结果集失败: %v\n", err)
		}
		rows.Close()
	}
}
