package custom_context

import (
	"context"
	"testing"
)

func TestSetJwtUserId(t *testing.T) {
	var userId = "112233"
	ctx := context.Background()
	ctxNew := SetJwtUserId(ctx, userId)
	id := GetJwtUserId(ctxNew)
	if id != userId {
		panic("failed")
	}
}

func TestSetGetDevice(t *testing.T) {
	var deviceId = "dwadwadwa"
	ctx := context.Background()
	ctxNew := SetDeviceId(ctx, deviceId)
	device := GetDeviceId(ctxNew)
	if device != deviceId {
		panic(device)
	}
}

func TestSetGetToken(t *testing.T) {
	var token = "dwadwadwa"
	ctx := context.Background()
	ctxNew := SetDeviceId(ctx, token)
	tokenNew := GetDeviceId(ctxNew)
	if tokenNew != token {
		panic("error")
	}
}
