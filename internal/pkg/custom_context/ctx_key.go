package custom_context

import (
	"context"
	"github.com/spf13/cast"
	"hw-paas-service/internal/common"
)

func GetJwtUserId(ctx context.Context) (userId string) {
	return ctx.Value(common.JwtUserId).(string)
}

func SetJwtUserId(ctx context.Context, userId string) context.Context {
	return context.WithValue(ctx, common.JwtUserId, userId)
}

func GetDeviceId(ctx context.Context) (deviceId string) {
	return cast.ToString(ctx.Value(common.JwtDeviceId))
}

func SetDeviceId(ctx context.Context, deviceId string) context.Context {
	return context.WithValue(ctx, common.JwtDeviceId, deviceId)
}

func GetToken(ctx context.Context) (token string) {
	return cast.ToString(ctx.Value(common.JwtToken))
}

func GetTalToken(ctx context.Context) (token string) {
	return cast.ToString(ctx.Value(common.TalToken))
}

func SetTalToken(ctx context.Context, token string) context.Context {
	return context.WithValue(ctx, common.TalToken, token)
}

func GetRealIp(ctx context.Context) (token string) {
	return cast.ToString(ctx.Value(common.XRealIp))
}

func GetForwardedFor(ctx context.Context) (token string) {
	return cast.ToString(ctx.Value(common.XForwardedFor))
}

func SetToken(ctx context.Context, token string) context.Context {
	return context.WithValue(ctx, common.JwtToken, token)
}

func GetPlatform(ctx context.Context) (platform string) {
	return cast.ToString(ctx.Value(common.JwtPlatform))
}

func SetPlatform(ctx context.Context, platform string) context.Context {
	return context.WithValue(ctx, common.JwtPlatform, platform)
}

func SetApplication(ctx context.Context, application string) context.Context {
	return context.WithValue(ctx, common.JwtApplication, application)
}

func GetApplication(ctx context.Context) (application string) {
	return ctx.Value(common.JwtApplication).(string)
}

func SetVersion(ctx context.Context, version string) context.Context {
	return context.WithValue(ctx, common.JwtVersion, version)
}

func GetVersion(ctx context.Context) (version string) {
	return ctx.Value(common.JwtVersion).(string)
}

func SetAppId(ctx context.Context, appId string) context.Context {
	return context.WithValue(ctx, common.JwtAppId, appId)
}

func GetAppId(ctx context.Context) (appId string) {
	value := ctx.Value(common.JwtAppId)
	if value != nil {
		return value.(string)
	}
	return ""
}

func GetTraceId(ctx context.Context) (traceId string) {
	return cast.ToString(ctx.Value(common.TraceId))
}

func SetTraceId(ctx context.Context, traceId string) context.Context {
	return context.WithValue(ctx, common.TraceId, traceId)
}

func GetTalIdType(ctx context.Context) (talIdType int) {
	return cast.ToInt(ctx.Value(common.TalIdType))
}

func SetTalIdType(ctx context.Context, talIdType int) context.Context {
	return context.WithValue(ctx, common.TalIdType, talIdType)
}
