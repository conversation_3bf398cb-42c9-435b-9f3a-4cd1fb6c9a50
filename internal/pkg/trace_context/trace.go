package trace_context

import (
	"context"
	uuid "github.com/satori/go.uuid"
	"go.opentelemetry.io/otel/trace"
)

var DeviceIdKey struct{}

func NewTraceContext(ctx context.Context, traceIds ...string) context.Context {
	span := trace.SpanFromContext(ctx)
	if span.IsRecording() {
		return trace.ContextWithSpan(context.Background(), span)
	}

	var traceId trace.TraceID
	if len(traceIds) > 0 && len(traceIds[0]) == 32 {
		traceId, _ = trace.TraceIDFromHex(traceIds[0])
	} else {
		traceId = trace.TraceID(uuid.NewV4())
	}

	spanCtx := trace.NewSpanContext(trace.SpanContextConfig{
		TraceID:    traceId,
		SpanID:     trace.SpanID{},
		TraceFlags: 0,
		TraceState: trace.TraceState{},
		Remote:     false,
	})
	return trace.ContextWithSpanContext(ctx, spanCtx)
}

func NewDeviceContext(ctx context.Context, deviceId string) context.Context {
	return context.WithValue(ctx, DeviceIdKey, deviceId)
}

func TraceId(ctx context.Context) string {
	if span := trace.SpanContextFromContext(ctx); span.HasTraceID() {
		return span.TraceID().String()
	}
	return ""
}

func DeviceId(ctx context.Context) string {
	deviceId, _ := ctx.Value(DeviceIdKey).(string)
	if deviceId == "" {
		deviceId = "123456789"
	}
	return deviceId
}
