package util

func MapIsEmpt_StringInterface(m map[string]interface{}) (result bool) {
	if m == nil && len(m) == 0 {
		return true
	}
	return false
}

func MapIsNotEmpt_StringInterface(m map[string]interface{}) (result bool) {
	if m == nil && len(m) == 0 {
		return false
	}
	return true
}

func MapIsEmpt_StringString(m map[string]interface{}) (result bool) {
	if m == nil && len(m) == 0 {
		return true
	}
	return false
}

func MapIsNotEmpt_StringString(m map[string]interface{}) (result bool) {
	if m == nil && len(m) == 0 {
		return false
	}
	return true
}
