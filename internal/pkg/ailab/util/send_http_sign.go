package util

import (
	"errors"
)

// 计算签名并发送http请求
func SendRequest(
	accessKeyId string,
	accessKeySecret string,
	timestamp string,
	requestUrl string,
	urlParams map[string]string,
	bodyParams map[string]interface{},
	requestMethod string,
	contentType string) ([]byte, error) {

	if StrIsEmpty(accessKeyId) {
		return nil, errors.New("参数access_key_id不能为空")
	}
	if StrIsEmpty(accessKeySecret) {
		return nil, errors.New("参数access_key_secret不能为空")
	}
	if StrIsEmpty(timestamp) {
		return nil, errors.New("参数timestamp不能为空")
	}
	if StrIsEmpty(requestUrl) {
		return nil, errors.New("参数requestUrl不能为空")
	}
	if urlParams == nil {
		return nil, errors.New("参数urlParams不能为null,会带回签名，至少做初始化")
	}
	if bodyParams == nil {
		bodyParams = make(map[string]interface{})
	}
	if StrIsEmpty(requestMethod) {
		return nil, errors.New("参数requestMethod不能为空")
	}
	if StrIsEmpty(contentType) {
		return nil, errors.New("参数contentType不能为空")
	}

	urlParams["access_key_id"] = accessKeyId
	urlParams["timestamp"] = timestamp

	signature, signatureNonce := GetSignature(urlParams,
		bodyParams,
		requestMethod,
		contentType,
		accessKeySecret)

	urlParams["signature"] = signature
	urlParams["signature_nonce"] = signatureNonce
	urlParams["timestamp"] = timestamp

	requestUrl = requestUrl + "?" + UrlFormat(urlParams)

	switch requestMethod {
	case "POST":
		return DoPost(requestUrl, contentType, bodyParams)
	case "PATCH":
		return DoPatch(requestUrl, contentType, bodyParams)
	case "PUT":
		return DoPut(requestUrl, contentType, bodyParams)
	case "GET":
		return DoGet(requestUrl, contentType)
	case "DELETE":
		return DoDelete(requestUrl, contentType)
	default:
		return nil, errors.New("支持[GET、POST、PUT、PATCH、DELETE]请求方式")
	}
}
