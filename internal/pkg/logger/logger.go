package logger

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
)

var myLogger *log.Helper

func NewLogger(logger log.Logger) {
	myLogger = log.NewHelper(logger)
	return
}

func Clog(c context.Context) *log.Helper {
	return myLogger.WithContext(c)
}

func Infof(ctx context.Context, format string, args ...interface{}) {
	myLogger.WithContext(ctx).Infof(format, args...)
}

func Debugf(ctx context.Context, format string, args ...interface{}) {
	myLogger.WithContext(ctx).Debugf(format, args...)
}

func Warnf(ctx context.Context, format string, args ...interface{}) {
	myLogger.WithContext(ctx).Warnf(format, args...)
}

func Errorf(ctx context.Context, format string, args ...interface{}) {
	myLogger.WithContext(ctx).<PERSON><PERSON>rf(format, args...)
}
