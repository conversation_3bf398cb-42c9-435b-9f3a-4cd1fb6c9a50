package server

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"

	khttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/google/uuid"
)

type FileUploadServer struct {
	uploadDir string // 文件存储目录
}

func NewFileUploadServer() *FileUploadServer {
	// 你可以自定义上传目录，比如 ./uploads
	uploadDir := "./uploads"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		panic(fmt.Sprintf("无法创建上传目录: %v", err))
	}
	return &FileUploadServer{
		uploadDir: uploadDir,
	}
}

// RegisterRoutes 注册 HTTP 路由
func (s *FileUploadServer) RegisterRoutes(srv *khttp.Server) {
	srv.Handle("/intelligence/v1/api/edu-admin/upload_file", s.uploadHandler())
}

// uploadHandler 处理文件上传
func (s *FileUploadServer) uploadHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 只接受 POST 请求
		if r.Method != http.MethodPost {
			http.Error(w, "只支持 POST 方法", http.StatusMethodNotAllowed)
			return
		}

		// 解析 multipart 表单，限制内存使用（剩余部分会存临时文件）
		// 假设表单字段名为 "file"
		err := r.ParseMultipartForm(10 << 20) // 10 MB
		if err != nil {
			http.Error(w, "解析表单失败: "+err.Error(), http.StatusBadRequest)
			return
		}

		// 获取文件
		file, handler, err := r.FormFile("file")
		if err != nil {
			http.Error(w, "获取文件失败: "+err.Error(), http.StatusBadRequest)
			return
		}
		defer file.Close()

		// 创建目标文件
		ext := filepath.Ext(handler.Filename)
		baseName := uuid.NewString()

		fileName := fmt.Sprintf("%s%s", baseName, ext)
		dstPath := filepath.Join(s.uploadDir, fileName)
		dst, err := os.Create(dstPath)
		if err != nil {
			http.Error(w, "无法创建文件: "+err.Error(), http.StatusInternalServerError)
			return
		}
		defer dst.Close()

		// 复制文件内容
		written, err := io.Copy(dst, file)
		if err != nil {
			http.Error(w, "写入文件失败: "+err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回成功响应
		w.WriteHeader(http.StatusOK)

		w.Header().Set("Content-Type", "application/json")
		resp := map[string]interface{}{
			"success":  true,
			"fileName": fileName,
			"size":     written,
			"message":  "文件上传成功",
		}
		json.NewEncoder(w).Encode(resp)
	}
}
