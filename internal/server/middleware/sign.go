package middleware

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	errorV1 "hw-paas-service/api/ai/v1"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/pkg/custom_context"
	"hw-paas-service/internal/pkg/utils"
	"strconv"
)

func Sign(signConf map[string]*conf.SignConf) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if header, ok := transport.FromServerContext(ctx); ok {
				deviceId := header.RequestHeader().Get("X-Genie-DeviceId")
				timeStamp := header.RequestHeader().Get("X-Genie-Timestamp")
				nonce := header.RequestHeader().Get("X-Genie-Nonce")
				sign := header.RequestHeader().Get("X-Genie-Sign")
				appId := header.RequestHeader().Get("X-Genie-AppId") //放配置文件
				version := header.RequestHeader().Get("X-Genie-Version")
				platform := header.RequestHeader().Get("X-Genie-Platform")

				if sign == "" || timeStamp == "" || nonce == "" || deviceId == "" || appId == "" || version == "" || platform == "" {
					return nil, errorV1.ErrorHwPaasCorrectError("必选参数不合法")
				}
				_, err := strconv.Atoi(timeStamp)
				if err != nil {
					return nil, errorV1.ErrorHwPaasSignError("timestamp format error")
				}

				appSign, ok := signConf[appId]
				if !ok {
					return nil, errorV1.ErrorHwPaasAppidError("appid not exist")
				}
				signStr := appSign.SignKey + "&X-Genie-Timestamp=" + timeStamp + "&X-Genie-Nonce=" + nonce + "&X-Genie-DeviceId=" + deviceId
				verifySign := utils.MD5HashString(signStr)
				if verifySign != sign {
					return nil, errorV1.ErrorHwPaasSignError("sign error")
				}
				customContext := custom_context.SetDeviceId(ctx, deviceId)
				platformContext := custom_context.SetPlatform(customContext, platform)
				appIdContext := custom_context.SetAppId(platformContext, appId)
				versionContext := custom_context.SetVersion(appIdContext, version)
				return handler(versionContext, req)
			} else {
				return nil, errorV1.ErrorHwPaasSignError("transport.FromServerContext err")
			}
		}
	}
}
