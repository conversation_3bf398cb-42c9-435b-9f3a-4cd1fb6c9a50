package middleware

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/pkg/utils"
)

func AddTraceToRequest() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			header, ok := transport.FromServerContext(ctx)
			if ok {
				traceId := header.RequestHeader().Get("traceId")
				if traceId == "" {
					uuid, _ := utils.NewUUID()
					if uuid != "" {
						traceId = utils.MD5HashString(uuid)
					}
				}
				header.RequestHeader().Set(common.HeaderTraceId, traceId)
			}
			return handler(ctx, req)
		}
	}
}
