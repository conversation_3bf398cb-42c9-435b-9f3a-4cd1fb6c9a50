package middleware

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/spf13/cast"
	"hw-paas-service/internal/common"
	"hw-paas-service/internal/pkg/custom_context"
	"hw-paas-service/internal/pkg/logger"
)

type RequestHeader struct {
	DeviceId  string
	TraceId   string
	UserId    string
	TalIdType int
}
type option func(ctx context.Context, header *RequestHeader) context.Context

func Options(ctx context.Context, h *RequestHeader) context.Context {
	ctx = custom_context.SetDeviceId(ctx, h.DeviceId)
	ctx = custom_context.SetJwtUserId(ctx, h.UserId)
	ctx = custom_context.SetTraceId(ctx, h.TraceId)
	return custom_context.SetTalIdType(ctx, h.TalIdType)
}
func Header2Ctx(opts ...option) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			signHeader, err := Func(ctx)
			if err != nil {
				return nil, err
			}
			for _, o := range opts {
				ctx = o(ctx, signHeader)
			}
			return handler(ctx, req)
		}
	}
}
func Func(ctx context.Context) (*RequestHeader, error) {
	if header, ok := transport.FromServerContext(ctx); ok {
		talIdType := header.RequestHeader().Get("talIdType")
		if len(talIdType) == 0 {
			talIdType = "0"
		}
		reqHeader := &RequestHeader{
			DeviceId:  header.RequestHeader().Get("X-Tal-Sn"),
			UserId:    header.RequestHeader().Get("talId"),
			TraceId:   header.RequestHeader().Get(common.HeaderTraceId),
			TalIdType: cast.ToInt(talIdType),
		}
		logger.Infof(ctx, "dw_http_header: %+v", header.RequestHeader())
		return reqHeader, nil
	} else {
		return nil, nil
	}
}
