package middleware

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	errorV1 "hw-paas-service/api/ai/v1"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/pkg/custom_context"
)

func AccessCheck(authConf *conf.Auth, ucenterRepo *data.UcenterRepo) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				token := tr.RequestHeader().Get("tal_token")
				_, err := ucenterRepo.CheckLogin(ctx, &data.CheckLoginReq{Token: token})
				isLogin := false
				if err == nil {
					isLogin = true
					ctx = custom_context.SetTalToken(ctx, token)
				}

				for _, url := range authConf.ProtectedUrls {
					if tr.Operation() == url && !isLogin {
						return nil, errorV1.ErrorHwPaasUnauthorized("用户未登录或登录已过期")
					}
				}

				defer func() {
					// Do something on exiting
				}()
			}
			return handler(ctx, req)
		}
	}
}
