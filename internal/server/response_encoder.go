package server

import (
	"encoding/json"
	kjson "github.com/go-kratos/kratos/v2/encoding/json"
	"google.golang.org/protobuf/encoding/protojson"
	"hw-paas-service/internal/common"
	netHttp "net/http"
	"time"

	"github.com/go-kratos/kratos/v2/encoding"
)

func MyResponseEncoder(w netHttp.ResponseWriter, r *netHttp.Request, i interface{}) error {
	kjson.MarshalOptions = protojson.MarshalOptions{
		EmitUnpopulated: true,
		UseProtoNames:   true,
	}
	reply := Response{
		ErrorReason: "success",
		ErrorMsg:    "",
		MetaData:    nil,
		TraceId:     r.Header.Get(common.HeaderTraceId),
		ServerTime:  time.Now().Unix(),
	}
	codec := encoding.GetCodec("json")
	data, err := codec.Marshal(i)
	err = json.Unmarshal(data, &reply.Data)
	if err != nil {
		return err
	}

	data, err = codec.Marshal(reply)
	// logger.Infof(r.Context(), "dw_http_response: %s", string(data))
	w.Header().Set("Content-Type", "application/json")
	w.Write(data)
	return nil
}
