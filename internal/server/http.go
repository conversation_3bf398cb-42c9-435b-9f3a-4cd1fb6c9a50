package server

import (
	"git.100tal.com/znxx_xpp/go-libs/httpserver"
	prom "github.com/go-kratos/kratos/contrib/metrics/prometheus/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/mux"

	v1 "hw-paas-service/api/ai/v1"
	commentv1 "hw-paas-service/api/comment/v1"
	adminv1 "hw-paas-service/api/eduadmin/v1"
	fw "hw-paas-service/api/finger_words/v1"
	qw "hw-paas-service/api/query_words/v1"
	rb "hw-paas-service/api/reading_book/v1"
	resourcev1 "hw-paas-service/api/resource/v1"
	skillv1 "hw-paas-service/api/skill/v1"
	"hw-paas-service/internal/biz/biz_metrics"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/server/middleware"
	"hw-paas-service/internal/service"
	"hw-paas-service/internal/service/evaluate"
)

var errorHandle *conf.ErrorHandle

// NewHTTPServer new a HTTP server.
func NewHTTPServer(c *conf.Server,
	ai *service.AiService,
	fwSvc *service.FingerWordsService,
	skill *service.SkillService,
	qwSvc *service.QueryWordsService,
	eval *evaluate.EvalService,
	rbSvc *service.ReadingBookService,
	errorHandler *conf.ErrorHandle,
	logger log.Logger,
	authConf *conf.Auth,
	ucenterRepo *data.UcenterRepo,
	resourceSvc *service.ResourceService,
	adminSvc *service.AdminService,
	commentSvc *service.CommentService,
) *http.Server {
	errorHandle = errorHandler

	opts := []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			tracing.Server(),
			middleware.AddTraceToRequest(),
			middleware.Header2Ctx(middleware.Options),
			middleware.AccessCheck(authConf, ucenterRepo),
			logging.Server(logger),
			validate.Validator(),
			metrics.Server(
				metrics.WithSeconds(prom.NewHistogram(biz_metrics.MetricSeconds)),
				metrics.WithRequests(prom.NewCounter(biz_metrics.MetricRequests)),
			),
			// middleware.Sign(sign),
		),
		httpserver.ServerHandle,
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	opts = append(opts, http.ErrorEncoder(MyErrorEncoder))
	opts = append(opts, http.ResponseEncoder(MyResponseEncoder))

	srv := http.NewServer(opts...)

	fileSrv := NewFileUploadServer()
	fileSrv.RegisterRoutes(srv)

	router := mux.NewRouter()
	// router.Handle("/intelligence/v1/evaluate/metrics", promhttp.Handler())
	router.HandleFunc("/intelligence/v1/evaluate", eval.WsHandler) ///intelligence/v1/evaluate
	srv.HandlePrefix("/intelligence/v1/evaluate", router)

	v1.RegisterAiHTTPServer(srv, ai)
	fw.RegisterFingerWordsHTTPServer(srv, fwSvc)
	qw.RegisterQueryWordsHTTPServer(srv, qwSvc)
	skillv1.RegisterSkillHTTPServer(srv, skill)
	rb.RegisterReadingBookHTTPServer(srv, rbSvc)
	resourcev1.RegisterResourceHTTPServer(srv, resourceSvc)
	adminv1.RegisterAdminHTTPServer(srv, adminSvc)
	commentv1.RegisterCommentHTTPServer(srv, commentSvc)

	return srv
}
