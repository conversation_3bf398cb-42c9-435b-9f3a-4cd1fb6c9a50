// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.23.3
// source: api/comment/v1/comment.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationCommentListComment = "/api.comment.v1.Comment/ListComment"

type CommentHTTPServer interface {
	// ListComment rpc CreateComment (CreateCommentRequest) returns (CreateCommentReply);
	// rpc UpdateComment (UpdateCommentRequest) returns (UpdateCommentReply);
	// rpc DeleteComment (DeleteCommentRequest) returns (DeleteCommentReply);
	// rpc GetComment (GetCommentRequest) returns (GetCommentReply);
	ListComment(context.Context, *ListCommentRequest) (*ListCommentReply, error)
}

func RegisterCommentHTTPServer(s *http.Server, srv CommentHTTPServer) {
	r := s.Route("/")
	r.GET("/intelligence/api/comment/v1/comments", _Comment_ListComment0_HTTP_Handler(srv))
}

func _Comment_ListComment0_HTTP_Handler(srv CommentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListCommentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCommentListComment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListComment(ctx, req.(*ListCommentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCommentReply)
		return ctx.Result(200, reply)
	}
}

type CommentHTTPClient interface {
	ListComment(ctx context.Context, req *ListCommentRequest, opts ...http.CallOption) (rsp *ListCommentReply, err error)
}

type CommentHTTPClientImpl struct {
	cc *http.Client
}

func NewCommentHTTPClient(client *http.Client) CommentHTTPClient {
	return &CommentHTTPClientImpl{client}
}

func (c *CommentHTTPClientImpl) ListComment(ctx context.Context, in *ListCommentRequest, opts ...http.CallOption) (*ListCommentReply, error) {
	var out ListCommentReply
	pattern := "/intelligence/api/comment/v1/comments"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCommentListComment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
