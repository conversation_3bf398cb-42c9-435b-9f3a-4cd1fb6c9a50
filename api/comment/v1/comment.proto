syntax = "proto3";

package api.comment.v1;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "validate/validate.proto";

option go_package = "hw-paas-service/api/comment/v1;v1";
option java_multiple_files = true;
option java_package = "api.comment.v1";

service Comment {
	// rpc CreateComment (CreateCommentRequest) returns (CreateCommentReply);
	// rpc UpdateComment (UpdateCommentRequest) returns (UpdateCommentReply);
	// rpc DeleteComment (DeleteCommentRequest) returns (DeleteCommentReply);
	// rpc GetComment (GetCommentRequest) returns (GetCommentReply);
	rpc ListComment (ListCommentRequest) returns (ListCommentReply){
    option (google.api.http) = {
      get: "/intelligence/api/comment/v1/comments"
    };
  };
}

// message CreateCommentRequest {}
// message CreateCommentReply {}
//
// message UpdateCommentRequest {}
// message UpdateCommentReply {}
//
// message DeleteCommentRequest {}
// message DeleteCommentReply {}
//
// message GetCommentRequest {}
// message GetCommentReply {}

message ListCommentRequest {
  int64 subject_type = 1;
  string subject_id = 2[(validate.rules).string.min_len = 1];
  int64 parent_id = 3;
  int32 page = 4;
  int32 page_size = 5;
}
message ListCommentReply {
  repeated CommentInfo comments = 1;
  int32 total = 2;
}


message CommentInfo {
  int64 id = 1;
  int32 subject_type = 2;
  string subject_id = 3;
  int64 parent_id = 4;
  string user_avatar = 5;
  string user_name = 6;
  string tal_id = 7;
  string content = 8;
  string  created_at = 9;
}
