// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/comment/v1/comment.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListCommentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCommentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCommentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCommentRequestMultiError, or nil if none found.
func (m *ListCommentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCommentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubjectType

	if utf8.RuneCountInString(m.GetSubjectId()) < 1 {
		err := ListCommentRequestValidationError{
			field:  "SubjectId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ParentId

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return ListCommentRequestMultiError(errors)
	}

	return nil
}

// ListCommentRequestMultiError is an error wrapping multiple validation errors
// returned by ListCommentRequest.ValidateAll() if the designated constraints
// aren't met.
type ListCommentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCommentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCommentRequestMultiError) AllErrors() []error { return m }

// ListCommentRequestValidationError is the validation error returned by
// ListCommentRequest.Validate if the designated constraints aren't met.
type ListCommentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCommentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCommentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCommentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCommentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCommentRequestValidationError) ErrorName() string {
	return "ListCommentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCommentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCommentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCommentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCommentRequestValidationError{}

// Validate checks the field values on ListCommentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListCommentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCommentReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCommentReplyMultiError, or nil if none found.
func (m *ListCommentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCommentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCommentReplyValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCommentReplyValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCommentReplyValidationError{
					field:  fmt.Sprintf("Comments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListCommentReplyMultiError(errors)
	}

	return nil
}

// ListCommentReplyMultiError is an error wrapping multiple validation errors
// returned by ListCommentReply.ValidateAll() if the designated constraints
// aren't met.
type ListCommentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCommentReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCommentReplyMultiError) AllErrors() []error { return m }

// ListCommentReplyValidationError is the validation error returned by
// ListCommentReply.Validate if the designated constraints aren't met.
type ListCommentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCommentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCommentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCommentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCommentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCommentReplyValidationError) ErrorName() string { return "ListCommentReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListCommentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCommentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCommentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCommentReplyValidationError{}

// Validate checks the field values on CommentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommentInfoMultiError, or
// nil if none found.
func (m *CommentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CommentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for SubjectType

	// no validation rules for SubjectId

	// no validation rules for ParentId

	// no validation rules for UserAvatar

	// no validation rules for UserName

	// no validation rules for TalId

	// no validation rules for Content

	// no validation rules for CreatedAt

	if len(errors) > 0 {
		return CommentInfoMultiError(errors)
	}

	return nil
}

// CommentInfoMultiError is an error wrapping multiple validation errors
// returned by CommentInfo.ValidateAll() if the designated constraints aren't met.
type CommentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommentInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommentInfoMultiError) AllErrors() []error { return m }

// CommentInfoValidationError is the validation error returned by
// CommentInfo.Validate if the designated constraints aren't met.
type CommentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommentInfoValidationError) ErrorName() string { return "CommentInfoValidationError" }

// Error satisfies the builtin error interface
func (e CommentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommentInfoValidationError{}
