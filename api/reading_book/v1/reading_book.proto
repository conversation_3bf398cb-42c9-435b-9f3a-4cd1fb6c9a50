syntax = "proto3";

package api.reading_book.v1;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "hw-paas-service/api/reading_book/v1;v1";
option java_multiple_files = true;
option java_package = "api.reading_book.v1";

service ReadingBook {
  rpc SentenceIdentify (SentenceIdentifyRequest) returns (SentenceIdentifyReply) {
    option (google.api.http) = {
      post: "/intelligence/v1/sentence_identify"
      body: "*"
    };
  };
}
message SentenceIdentifyRequest{
  string image_base64 = 1;
  string client_trace_id = 2;
  repeated int32 finger_pos = 3[(validate.rules).repeated.min_items = 2];
}
message SentenceIdentifyReply{
  string sentence = 1;
  int32 result_code = 2;
}

