// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/reading_book/v1/reading_book.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SentenceIdentifyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SentenceIdentifyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SentenceIdentifyRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SentenceIdentifyRequestMultiError, or nil if none found.
func (m *SentenceIdentifyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SentenceIdentifyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageBase64

	// no validation rules for ClientTraceId

	if len(m.GetFingerPos()) < 2 {
		err := SentenceIdentifyRequestValidationError{
			field:  "FingerPos",
			reason: "value must contain at least 2 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SentenceIdentifyRequestMultiError(errors)
	}

	return nil
}

// SentenceIdentifyRequestMultiError is an error wrapping multiple validation
// errors returned by SentenceIdentifyRequest.ValidateAll() if the designated
// constraints aren't met.
type SentenceIdentifyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SentenceIdentifyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SentenceIdentifyRequestMultiError) AllErrors() []error { return m }

// SentenceIdentifyRequestValidationError is the validation error returned by
// SentenceIdentifyRequest.Validate if the designated constraints aren't met.
type SentenceIdentifyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SentenceIdentifyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SentenceIdentifyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SentenceIdentifyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SentenceIdentifyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SentenceIdentifyRequestValidationError) ErrorName() string {
	return "SentenceIdentifyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SentenceIdentifyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSentenceIdentifyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SentenceIdentifyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SentenceIdentifyRequestValidationError{}

// Validate checks the field values on SentenceIdentifyReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SentenceIdentifyReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SentenceIdentifyReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SentenceIdentifyReplyMultiError, or nil if none found.
func (m *SentenceIdentifyReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SentenceIdentifyReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sentence

	// no validation rules for ResultCode

	if len(errors) > 0 {
		return SentenceIdentifyReplyMultiError(errors)
	}

	return nil
}

// SentenceIdentifyReplyMultiError is an error wrapping multiple validation
// errors returned by SentenceIdentifyReply.ValidateAll() if the designated
// constraints aren't met.
type SentenceIdentifyReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SentenceIdentifyReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SentenceIdentifyReplyMultiError) AllErrors() []error { return m }

// SentenceIdentifyReplyValidationError is the validation error returned by
// SentenceIdentifyReply.Validate if the designated constraints aren't met.
type SentenceIdentifyReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SentenceIdentifyReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SentenceIdentifyReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SentenceIdentifyReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SentenceIdentifyReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SentenceIdentifyReplyValidationError) ErrorName() string {
	return "SentenceIdentifyReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SentenceIdentifyReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSentenceIdentifyReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SentenceIdentifyReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SentenceIdentifyReplyValidationError{}
