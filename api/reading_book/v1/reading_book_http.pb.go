// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.23.3
// source: api/reading_book/v1/reading_book.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationReadingBookSentenceIdentify = "/api.reading_book.v1.ReadingBook/SentenceIdentify"

type ReadingBookHTTPServer interface {
	SentenceIdentify(context.Context, *SentenceIdentifyRequest) (*SentenceIdentifyReply, error)
}

func RegisterReadingBookHTTPServer(s *http.Server, srv ReadingBookHTTPServer) {
	r := s.Route("/")
	r.POST("/intelligence/v1/sentence_identify", _ReadingBook_SentenceIdentify0_HTTP_Handler(srv))
}

func _ReadingBook_SentenceIdentify0_HTTP_Handler(srv ReadingBookHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SentenceIdentifyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationReadingBookSentenceIdentify)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SentenceIdentify(ctx, req.(*SentenceIdentifyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SentenceIdentifyReply)
		return ctx.Result(200, reply)
	}
}

type ReadingBookHTTPClient interface {
	SentenceIdentify(ctx context.Context, req *SentenceIdentifyRequest, opts ...http.CallOption) (rsp *SentenceIdentifyReply, err error)
}

type ReadingBookHTTPClientImpl struct {
	cc *http.Client
}

func NewReadingBookHTTPClient(client *http.Client) ReadingBookHTTPClient {
	return &ReadingBookHTTPClientImpl{client}
}

func (c *ReadingBookHTTPClientImpl) SentenceIdentify(ctx context.Context, in *SentenceIdentifyRequest, opts ...http.CallOption) (*SentenceIdentifyReply, error) {
	var out SentenceIdentifyReply
	pattern := "/intelligence/v1/sentence_identify"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationReadingBookSentenceIdentify))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
