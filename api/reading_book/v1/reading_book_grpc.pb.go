// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.3
// source: api/reading_book/v1/reading_book.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ReadingBook_SentenceIdentify_FullMethodName = "/api.reading_book.v1.ReadingBook/SentenceIdentify"
)

// ReadingBookClient is the client API for ReadingBook service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReadingBookClient interface {
	SentenceIdentify(ctx context.Context, in *SentenceIdentifyRequest, opts ...grpc.CallOption) (*SentenceIdentifyReply, error)
}

type readingBookClient struct {
	cc grpc.ClientConnInterface
}

func NewReadingBookClient(cc grpc.ClientConnInterface) ReadingBookClient {
	return &readingBookClient{cc}
}

func (c *readingBookClient) SentenceIdentify(ctx context.Context, in *SentenceIdentifyRequest, opts ...grpc.CallOption) (*SentenceIdentifyReply, error) {
	out := new(SentenceIdentifyReply)
	err := c.cc.Invoke(ctx, ReadingBook_SentenceIdentify_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReadingBookServer is the server API for ReadingBook service.
// All implementations must embed UnimplementedReadingBookServer
// for forward compatibility
type ReadingBookServer interface {
	SentenceIdentify(context.Context, *SentenceIdentifyRequest) (*SentenceIdentifyReply, error)
	mustEmbedUnimplementedReadingBookServer()
}

// UnimplementedReadingBookServer must be embedded to have forward compatible implementations.
type UnimplementedReadingBookServer struct {
}

func (UnimplementedReadingBookServer) SentenceIdentify(context.Context, *SentenceIdentifyRequest) (*SentenceIdentifyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SentenceIdentify not implemented")
}
func (UnimplementedReadingBookServer) mustEmbedUnimplementedReadingBookServer() {}

// UnsafeReadingBookServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReadingBookServer will
// result in compilation errors.
type UnsafeReadingBookServer interface {
	mustEmbedUnimplementedReadingBookServer()
}

func RegisterReadingBookServer(s grpc.ServiceRegistrar, srv ReadingBookServer) {
	s.RegisterService(&ReadingBook_ServiceDesc, srv)
}

func _ReadingBook_SentenceIdentify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SentenceIdentifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReadingBookServer).SentenceIdentify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ReadingBook_SentenceIdentify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReadingBookServer).SentenceIdentify(ctx, req.(*SentenceIdentifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ReadingBook_ServiceDesc is the grpc.ServiceDesc for ReadingBook service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ReadingBook_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.reading_book.v1.ReadingBook",
	HandlerType: (*ReadingBookServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SentenceIdentify",
			Handler:    _ReadingBook_SentenceIdentify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/reading_book/v1/reading_book.proto",
}
