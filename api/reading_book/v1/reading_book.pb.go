// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.3
// source: api/reading_book/v1/reading_book.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SentenceIdentifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageBase64   string  `protobuf:"bytes,1,opt,name=image_base64,json=imageBase64,proto3" json:"image_base64,omitempty"`
	ClientTraceId string  `protobuf:"bytes,2,opt,name=client_trace_id,json=clientTraceId,proto3" json:"client_trace_id,omitempty"`
	FingerPos     []int32 `protobuf:"varint,3,rep,packed,name=finger_pos,json=fingerPos,proto3" json:"finger_pos,omitempty"`
}

func (x *SentenceIdentifyRequest) Reset() {
	*x = SentenceIdentifyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_reading_book_v1_reading_book_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SentenceIdentifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SentenceIdentifyRequest) ProtoMessage() {}

func (x *SentenceIdentifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_reading_book_v1_reading_book_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SentenceIdentifyRequest.ProtoReflect.Descriptor instead.
func (*SentenceIdentifyRequest) Descriptor() ([]byte, []int) {
	return file_api_reading_book_v1_reading_book_proto_rawDescGZIP(), []int{0}
}

func (x *SentenceIdentifyRequest) GetImageBase64() string {
	if x != nil {
		return x.ImageBase64
	}
	return ""
}

func (x *SentenceIdentifyRequest) GetClientTraceId() string {
	if x != nil {
		return x.ClientTraceId
	}
	return ""
}

func (x *SentenceIdentifyRequest) GetFingerPos() []int32 {
	if x != nil {
		return x.FingerPos
	}
	return nil
}

type SentenceIdentifyReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sentence   string `protobuf:"bytes,1,opt,name=sentence,proto3" json:"sentence,omitempty"`
	ResultCode int32  `protobuf:"varint,2,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
}

func (x *SentenceIdentifyReply) Reset() {
	*x = SentenceIdentifyReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_reading_book_v1_reading_book_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SentenceIdentifyReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SentenceIdentifyReply) ProtoMessage() {}

func (x *SentenceIdentifyReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_reading_book_v1_reading_book_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SentenceIdentifyReply.ProtoReflect.Descriptor instead.
func (*SentenceIdentifyReply) Descriptor() ([]byte, []int) {
	return file_api_reading_book_v1_reading_book_proto_rawDescGZIP(), []int{1}
}

func (x *SentenceIdentifyReply) GetSentence() string {
	if x != nil {
		return x.Sentence
	}
	return ""
}

func (x *SentenceIdentifyReply) GetResultCode() int32 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

var File_api_reading_book_v1_reading_book_proto protoreflect.FileDescriptor

var file_api_reading_book_v1_reading_book_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x36, 0x34, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0a, 0x66,
	0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x02, 0x52, 0x09, 0x66, 0x69, 0x6e, 0x67, 0x65,
	0x72, 0x50, 0x6f, 0x73, 0x22, 0x54, 0x0a, 0x15, 0x53, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x32, 0xab, 0x01, 0x0a, 0x0b, 0x52,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x9b, 0x01, 0x0a, 0x10, 0x53,
	0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x12,
	0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x27, 0x22, 0x22, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x79, 0x3a, 0x01, 0x2a, 0x42, 0x3f, 0x0a, 0x13, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x26, 0x68, 0x77, 0x2d, 0x70, 0x61, 0x61, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_reading_book_v1_reading_book_proto_rawDescOnce sync.Once
	file_api_reading_book_v1_reading_book_proto_rawDescData = file_api_reading_book_v1_reading_book_proto_rawDesc
)

func file_api_reading_book_v1_reading_book_proto_rawDescGZIP() []byte {
	file_api_reading_book_v1_reading_book_proto_rawDescOnce.Do(func() {
		file_api_reading_book_v1_reading_book_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_reading_book_v1_reading_book_proto_rawDescData)
	})
	return file_api_reading_book_v1_reading_book_proto_rawDescData
}

var file_api_reading_book_v1_reading_book_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_reading_book_v1_reading_book_proto_goTypes = []interface{}{
	(*SentenceIdentifyRequest)(nil), // 0: api.reading_book.v1.SentenceIdentifyRequest
	(*SentenceIdentifyReply)(nil),   // 1: api.reading_book.v1.SentenceIdentifyReply
}
var file_api_reading_book_v1_reading_book_proto_depIdxs = []int32{
	0, // 0: api.reading_book.v1.ReadingBook.SentenceIdentify:input_type -> api.reading_book.v1.SentenceIdentifyRequest
	1, // 1: api.reading_book.v1.ReadingBook.SentenceIdentify:output_type -> api.reading_book.v1.SentenceIdentifyReply
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_reading_book_v1_reading_book_proto_init() }
func file_api_reading_book_v1_reading_book_proto_init() {
	if File_api_reading_book_v1_reading_book_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_reading_book_v1_reading_book_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SentenceIdentifyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_reading_book_v1_reading_book_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SentenceIdentifyReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_reading_book_v1_reading_book_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_reading_book_v1_reading_book_proto_goTypes,
		DependencyIndexes: file_api_reading_book_v1_reading_book_proto_depIdxs,
		MessageInfos:      file_api_reading_book_v1_reading_book_proto_msgTypes,
	}.Build()
	File_api_reading_book_v1_reading_book_proto = out.File
	file_api_reading_book_v1_reading_book_proto_rawDesc = nil
	file_api_reading_book_v1_reading_book_proto_goTypes = nil
	file_api_reading_book_v1_reading_book_proto_depIdxs = nil
}
