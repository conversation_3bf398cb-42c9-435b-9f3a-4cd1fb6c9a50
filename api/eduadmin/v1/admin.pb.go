// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.3
// source: api/eduadmin/v1/admin.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ImportEduTermsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 能过上传文件名，获取文件内容
	File string `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
}

func (x *ImportEduTermsRequest) Reset() {
	*x = ImportEduTermsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_eduadmin_v1_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportEduTermsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportEduTermsRequest) ProtoMessage() {}

func (x *ImportEduTermsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_eduadmin_v1_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportEduTermsRequest.ProtoReflect.Descriptor instead.
func (*ImportEduTermsRequest) Descriptor() ([]byte, []int) {
	return file_api_eduadmin_v1_admin_proto_rawDescGZIP(), []int{0}
}

func (x *ImportEduTermsRequest) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

type ImportEduTermsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ImportEduTermsReply) Reset() {
	*x = ImportEduTermsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_eduadmin_v1_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportEduTermsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportEduTermsReply) ProtoMessage() {}

func (x *ImportEduTermsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_eduadmin_v1_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportEduTermsReply.ProtoReflect.Descriptor instead.
func (*ImportEduTermsReply) Descriptor() ([]byte, []int) {
	return file_api_eduadmin_v1_admin_proto_rawDescGZIP(), []int{1}
}

func (x *ImportEduTermsReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ExecuteSqlFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文件内容
	File string `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
}

func (x *ExecuteSqlFileRequest) Reset() {
	*x = ExecuteSqlFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_eduadmin_v1_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteSqlFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteSqlFileRequest) ProtoMessage() {}

func (x *ExecuteSqlFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_eduadmin_v1_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteSqlFileRequest.ProtoReflect.Descriptor instead.
func (*ExecuteSqlFileRequest) Descriptor() ([]byte, []int) {
	return file_api_eduadmin_v1_admin_proto_rawDescGZIP(), []int{2}
}

func (x *ExecuteSqlFileRequest) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

type ExecuteSqlFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 执行结果
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ExecuteSqlFileReply) Reset() {
	*x = ExecuteSqlFileReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_eduadmin_v1_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteSqlFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteSqlFileReply) ProtoMessage() {}

func (x *ExecuteSqlFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_eduadmin_v1_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteSqlFileReply.ProtoReflect.Descriptor instead.
func (*ExecuteSqlFileReply) Descriptor() ([]byte, []int) {
	return file_api_eduadmin_v1_admin_proto_rawDescGZIP(), []int{3}
}

func (x *ExecuteSqlFileReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_api_eduadmin_v1_admin_proto protoreflect.FileDescriptor

var file_api_eduadmin_v1_admin_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x64, 0x75, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x64, 0x75, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x2b, 0x0a, 0x15, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x64, 0x75,
	0x54, 0x65, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65,
	0x22, 0x2f, 0x0a, 0x13, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72,
	0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0x2b, 0x0a, 0x15, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x53, 0x71, 0x6c, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x2f,
	0x0a, 0x13, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x53, 0x71, 0x6c, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32,
	0xb6, 0x02, 0x0a, 0x05, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x92, 0x01, 0x0a, 0x0e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x12, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x64, 0x75, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x64, 0x75, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x64, 0x75,
	0x54, 0x65, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2c, 0x12, 0x2a, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63,
	0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x64, 0x75, 0x2d, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2d, 0x74, 0x65, 0x72, 0x6d, 0x12, 0x97,
	0x01, 0x0a, 0x0e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x53, 0x71, 0x6c, 0x46, 0x69, 0x6c,
	0x65, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x64, 0x75, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x53, 0x71, 0x6c, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x64, 0x75, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x53, 0x71, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x12, 0x2f, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c,
	0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x64,
	0x75, 0x2d, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x2d,
	0x73, 0x71, 0x6c, 0x2d, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x37, 0x0a, 0x0f, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x64, 0x75, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x22, 0x68,
	0x77, 0x2d, 0x70, 0x61, 0x61, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x65, 0x64, 0x75, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_eduadmin_v1_admin_proto_rawDescOnce sync.Once
	file_api_eduadmin_v1_admin_proto_rawDescData = file_api_eduadmin_v1_admin_proto_rawDesc
)

func file_api_eduadmin_v1_admin_proto_rawDescGZIP() []byte {
	file_api_eduadmin_v1_admin_proto_rawDescOnce.Do(func() {
		file_api_eduadmin_v1_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_eduadmin_v1_admin_proto_rawDescData)
	})
	return file_api_eduadmin_v1_admin_proto_rawDescData
}

var file_api_eduadmin_v1_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_eduadmin_v1_admin_proto_goTypes = []interface{}{
	(*ImportEduTermsRequest)(nil), // 0: api.eduadmin.v1.ImportEduTermsRequest
	(*ImportEduTermsReply)(nil),   // 1: api.eduadmin.v1.ImportEduTermsReply
	(*ExecuteSqlFileRequest)(nil), // 2: api.eduadmin.v1.ExecuteSqlFileRequest
	(*ExecuteSqlFileReply)(nil),   // 3: api.eduadmin.v1.ExecuteSqlFileReply
}
var file_api_eduadmin_v1_admin_proto_depIdxs = []int32{
	0, // 0: api.eduadmin.v1.Admin.ImportEduTerms:input_type -> api.eduadmin.v1.ImportEduTermsRequest
	2, // 1: api.eduadmin.v1.Admin.ExecuteSqlFile:input_type -> api.eduadmin.v1.ExecuteSqlFileRequest
	1, // 2: api.eduadmin.v1.Admin.ImportEduTerms:output_type -> api.eduadmin.v1.ImportEduTermsReply
	3, // 3: api.eduadmin.v1.Admin.ExecuteSqlFile:output_type -> api.eduadmin.v1.ExecuteSqlFileReply
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_eduadmin_v1_admin_proto_init() }
func file_api_eduadmin_v1_admin_proto_init() {
	if File_api_eduadmin_v1_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_eduadmin_v1_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportEduTermsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_eduadmin_v1_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportEduTermsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_eduadmin_v1_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteSqlFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_eduadmin_v1_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteSqlFileReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_eduadmin_v1_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_eduadmin_v1_admin_proto_goTypes,
		DependencyIndexes: file_api_eduadmin_v1_admin_proto_depIdxs,
		MessageInfos:      file_api_eduadmin_v1_admin_proto_msgTypes,
	}.Build()
	File_api_eduadmin_v1_admin_proto = out.File
	file_api_eduadmin_v1_admin_proto_rawDesc = nil
	file_api_eduadmin_v1_admin_proto_goTypes = nil
	file_api_eduadmin_v1_admin_proto_depIdxs = nil
}
