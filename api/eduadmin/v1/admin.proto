syntax = "proto3";

package api.eduadmin.v1;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "validate/validate.proto";

option go_package = "hw-paas-service/api/eduadmin/v1;v1";
option java_multiple_files = true;
option java_package = "api.eduadmin.v1";

service Admin {
	rpc ImportEduTerms (ImportEduTermsRequest) returns (ImportEduTermsReply){
		option (google.api.http) = {
			get: "/intelligence/v1/api/edu-admin/import-term"
		};
	}

  rpc ExecuteSqlFile(ExecuteSqlFileRequest) returns (ExecuteSqlFileReply) {
    option (google.api.http) = {
      get: "/intelligence/v1/api/edu-admin/execute-sql-file"
    };
  }
//	 rpc CreateTerm (CreateTermRequest) returns (CreateTermReply);
//	 rpc UpdateTerm (UpdateTermRequest) returns (UpdateTermReply);
//	 rpc DeleteTerm (DeleteTermRequest) returns (DeleteTermReply);
//	 rpc GetTerm (GetTermRequest) returns (GetTermReply);
}

message ImportEduTermsRequest {
	// 能过上传文件名，获取文件内容
	string file = 1;
}

message ImportEduTermsReply {
	string message = 1;
}


message ExecuteSqlFileRequest {
  // 文件内容
  string file = 1;
}

message ExecuteSqlFileReply {
  // 执行结果
  string message = 1;
}

