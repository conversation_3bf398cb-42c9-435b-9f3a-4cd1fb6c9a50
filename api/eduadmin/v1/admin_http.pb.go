// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.23.3
// source: api/eduadmin/v1/admin.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAdminExecuteSqlFile = "/api.eduadmin.v1.Admin/ExecuteSqlFile"
const OperationAdminImportEduTerms = "/api.eduadmin.v1.Admin/ImportEduTerms"

type AdminHTTPServer interface {
	ExecuteSqlFile(context.Context, *ExecuteSqlFileRequest) (*ExecuteSqlFileReply, error)
	ImportEduTerms(context.Context, *ImportEduTermsRequest) (*ImportEduTermsReply, error)
}

func RegisterAdminHTTPServer(s *http.Server, srv AdminHTTPServer) {
	r := s.Route("/")
	r.GET("/intelligence/v1/api/edu-admin/import-term", _Admin_ImportEduTerms0_HTTP_Handler(srv))
	r.GET("/intelligence/v1/api/edu-admin/execute-sql-file", _Admin_ExecuteSqlFile0_HTTP_Handler(srv))
}

func _Admin_ImportEduTerms0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ImportEduTermsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminImportEduTerms)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ImportEduTerms(ctx, req.(*ImportEduTermsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ImportEduTermsReply)
		return ctx.Result(200, reply)
	}
}

func _Admin_ExecuteSqlFile0_HTTP_Handler(srv AdminHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExecuteSqlFileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAdminExecuteSqlFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExecuteSqlFile(ctx, req.(*ExecuteSqlFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExecuteSqlFileReply)
		return ctx.Result(200, reply)
	}
}

type AdminHTTPClient interface {
	ExecuteSqlFile(ctx context.Context, req *ExecuteSqlFileRequest, opts ...http.CallOption) (rsp *ExecuteSqlFileReply, err error)
	ImportEduTerms(ctx context.Context, req *ImportEduTermsRequest, opts ...http.CallOption) (rsp *ImportEduTermsReply, err error)
}

type AdminHTTPClientImpl struct {
	cc *http.Client
}

func NewAdminHTTPClient(client *http.Client) AdminHTTPClient {
	return &AdminHTTPClientImpl{client}
}

func (c *AdminHTTPClientImpl) ExecuteSqlFile(ctx context.Context, in *ExecuteSqlFileRequest, opts ...http.CallOption) (*ExecuteSqlFileReply, error) {
	var out ExecuteSqlFileReply
	pattern := "/intelligence/v1/api/edu-admin/execute-sql-file"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminExecuteSqlFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AdminHTTPClientImpl) ImportEduTerms(ctx context.Context, in *ImportEduTermsRequest, opts ...http.CallOption) (*ImportEduTermsReply, error) {
	var out ImportEduTermsReply
	pattern := "/intelligence/v1/api/edu-admin/import-term"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAdminImportEduTerms))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
