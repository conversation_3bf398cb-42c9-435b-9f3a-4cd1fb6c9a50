// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.3
// source: api/eduadmin/v1/admin.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Admin_ImportEduTerms_FullMethodName = "/api.eduadmin.v1.Admin/ImportEduTerms"
	Admin_ExecuteSqlFile_FullMethodName = "/api.eduadmin.v1.Admin/ExecuteSqlFile"
)

// AdminClient is the client API for Admin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdminClient interface {
	ImportEduTerms(ctx context.Context, in *ImportEduTermsRequest, opts ...grpc.CallOption) (*ImportEduTermsReply, error)
	ExecuteSqlFile(ctx context.Context, in *ExecuteSqlFileRequest, opts ...grpc.CallOption) (*ExecuteSqlFileReply, error)
}

type adminClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminClient(cc grpc.ClientConnInterface) AdminClient {
	return &adminClient{cc}
}

func (c *adminClient) ImportEduTerms(ctx context.Context, in *ImportEduTermsRequest, opts ...grpc.CallOption) (*ImportEduTermsReply, error) {
	out := new(ImportEduTermsReply)
	err := c.cc.Invoke(ctx, Admin_ImportEduTerms_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminClient) ExecuteSqlFile(ctx context.Context, in *ExecuteSqlFileRequest, opts ...grpc.CallOption) (*ExecuteSqlFileReply, error) {
	out := new(ExecuteSqlFileReply)
	err := c.cc.Invoke(ctx, Admin_ExecuteSqlFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminServer is the server API for Admin service.
// All implementations must embed UnimplementedAdminServer
// for forward compatibility
type AdminServer interface {
	ImportEduTerms(context.Context, *ImportEduTermsRequest) (*ImportEduTermsReply, error)
	ExecuteSqlFile(context.Context, *ExecuteSqlFileRequest) (*ExecuteSqlFileReply, error)
	mustEmbedUnimplementedAdminServer()
}

// UnimplementedAdminServer must be embedded to have forward compatible implementations.
type UnimplementedAdminServer struct {
}

func (UnimplementedAdminServer) ImportEduTerms(context.Context, *ImportEduTermsRequest) (*ImportEduTermsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportEduTerms not implemented")
}
func (UnimplementedAdminServer) ExecuteSqlFile(context.Context, *ExecuteSqlFileRequest) (*ExecuteSqlFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteSqlFile not implemented")
}
func (UnimplementedAdminServer) mustEmbedUnimplementedAdminServer() {}

// UnsafeAdminServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminServer will
// result in compilation errors.
type UnsafeAdminServer interface {
	mustEmbedUnimplementedAdminServer()
}

func RegisterAdminServer(s grpc.ServiceRegistrar, srv AdminServer) {
	s.RegisterService(&Admin_ServiceDesc, srv)
}

func _Admin_ImportEduTerms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportEduTermsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ImportEduTerms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ImportEduTerms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ImportEduTerms(ctx, req.(*ImportEduTermsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Admin_ExecuteSqlFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteSqlFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminServer).ExecuteSqlFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Admin_ExecuteSqlFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminServer).ExecuteSqlFile(ctx, req.(*ExecuteSqlFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Admin_ServiceDesc is the grpc.ServiceDesc for Admin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Admin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.eduadmin.v1.Admin",
	HandlerType: (*AdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ImportEduTerms",
			Handler:    _Admin_ImportEduTerms_Handler,
		},
		{
			MethodName: "ExecuteSqlFile",
			Handler:    _Admin_ExecuteSqlFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/eduadmin/v1/admin.proto",
}
