// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/eduadmin/v1/admin.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ImportEduTermsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ImportEduTermsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImportEduTermsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImportEduTermsRequestMultiError, or nil if none found.
func (m *ImportEduTermsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ImportEduTermsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for File

	if len(errors) > 0 {
		return ImportEduTermsRequestMultiError(errors)
	}

	return nil
}

// ImportEduTermsRequestMultiError is an error wrapping multiple validation
// errors returned by ImportEduTermsRequest.ValidateAll() if the designated
// constraints aren't met.
type ImportEduTermsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImportEduTermsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImportEduTermsRequestMultiError) AllErrors() []error { return m }

// ImportEduTermsRequestValidationError is the validation error returned by
// ImportEduTermsRequest.Validate if the designated constraints aren't met.
type ImportEduTermsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImportEduTermsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImportEduTermsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImportEduTermsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImportEduTermsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImportEduTermsRequestValidationError) ErrorName() string {
	return "ImportEduTermsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ImportEduTermsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImportEduTermsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImportEduTermsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImportEduTermsRequestValidationError{}

// Validate checks the field values on ImportEduTermsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ImportEduTermsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImportEduTermsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImportEduTermsReplyMultiError, or nil if none found.
func (m *ImportEduTermsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ImportEduTermsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if len(errors) > 0 {
		return ImportEduTermsReplyMultiError(errors)
	}

	return nil
}

// ImportEduTermsReplyMultiError is an error wrapping multiple validation
// errors returned by ImportEduTermsReply.ValidateAll() if the designated
// constraints aren't met.
type ImportEduTermsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImportEduTermsReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImportEduTermsReplyMultiError) AllErrors() []error { return m }

// ImportEduTermsReplyValidationError is the validation error returned by
// ImportEduTermsReply.Validate if the designated constraints aren't met.
type ImportEduTermsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImportEduTermsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImportEduTermsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImportEduTermsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImportEduTermsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImportEduTermsReplyValidationError) ErrorName() string {
	return "ImportEduTermsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ImportEduTermsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImportEduTermsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImportEduTermsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImportEduTermsReplyValidationError{}

// Validate checks the field values on ExecuteSqlFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecuteSqlFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteSqlFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteSqlFileRequestMultiError, or nil if none found.
func (m *ExecuteSqlFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSqlFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for File

	if len(errors) > 0 {
		return ExecuteSqlFileRequestMultiError(errors)
	}

	return nil
}

// ExecuteSqlFileRequestMultiError is an error wrapping multiple validation
// errors returned by ExecuteSqlFileRequest.ValidateAll() if the designated
// constraints aren't met.
type ExecuteSqlFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSqlFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSqlFileRequestMultiError) AllErrors() []error { return m }

// ExecuteSqlFileRequestValidationError is the validation error returned by
// ExecuteSqlFileRequest.Validate if the designated constraints aren't met.
type ExecuteSqlFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSqlFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteSqlFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteSqlFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteSqlFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteSqlFileRequestValidationError) ErrorName() string {
	return "ExecuteSqlFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteSqlFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSqlFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSqlFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSqlFileRequestValidationError{}

// Validate checks the field values on ExecuteSqlFileReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecuteSqlFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecuteSqlFileReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecuteSqlFileReplyMultiError, or nil if none found.
func (m *ExecuteSqlFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecuteSqlFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if len(errors) > 0 {
		return ExecuteSqlFileReplyMultiError(errors)
	}

	return nil
}

// ExecuteSqlFileReplyMultiError is an error wrapping multiple validation
// errors returned by ExecuteSqlFileReply.ValidateAll() if the designated
// constraints aren't met.
type ExecuteSqlFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecuteSqlFileReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecuteSqlFileReplyMultiError) AllErrors() []error { return m }

// ExecuteSqlFileReplyValidationError is the validation error returned by
// ExecuteSqlFileReply.Validate if the designated constraints aren't met.
type ExecuteSqlFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecuteSqlFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecuteSqlFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecuteSqlFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecuteSqlFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecuteSqlFileReplyValidationError) ErrorName() string {
	return "ExecuteSqlFileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ExecuteSqlFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecuteSqlFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecuteSqlFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecuteSqlFileReplyValidationError{}
