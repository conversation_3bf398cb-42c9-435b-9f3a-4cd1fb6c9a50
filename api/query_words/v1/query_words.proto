syntax = "proto3";

package api.query_words.v1;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

option go_package = "hw-paas-service/api/query_words/v1;v1";
option java_multiple_files = true;
option java_package = "api.query_words.v1";

service QueryWords {
	rpc QueryWordsList (QueryWordsListRequest) returns (QueryWordsListReply) {
		option (google.api.http) = {
			post: "/intelligence/v1/query_words/list"
			body: "*"
		};
	};
}

message QueryWordsListRequest {
	string word = 1;
	QueryWordsListQuery query = 2;
}
/*
	word 单词
	british 英式发音
	american 美式发音
	synonym 同义词
	antonym 反义词
	inflection 变形词
	prefix 前缀
	suffix 后缀
	phrase 短语
	meaning 释义
	sentence 例句
*/
message QueryWordsListQuery {
	string word = 1;
	string british = 2;
	string american = 3;
	string synonym = 4;
	string antonym = 5;
	string inflection = 6;
	string prefix = 7;
	string suffix = 8;
	string phrase = 9;
	string meaning = 10;
	string sentence = 11;
}
message QueryWordsListReply {
	int32 total = 1;
	repeated QueryWordsListItem list = 2;
	bool curse = 3;
}
message QueryWordsListItem {
	google.protobuf.Struct detail = 1;
	float score = 2;
}