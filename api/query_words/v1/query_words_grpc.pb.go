// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.3
// source: api/query_words/v1/query_words.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	QueryWords_QueryWordsList_FullMethodName = "/api.query_words.v1.QueryWords/QueryWordsList"
)

// QueryWordsClient is the client API for QueryWords service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type QueryWordsClient interface {
	QueryWordsList(ctx context.Context, in *QueryWordsListRequest, opts ...grpc.CallOption) (*QueryWordsListReply, error)
}

type queryWordsClient struct {
	cc grpc.ClientConnInterface
}

func NewQueryWordsClient(cc grpc.ClientConnInterface) QueryWordsClient {
	return &queryWordsClient{cc}
}

func (c *queryWordsClient) QueryWordsList(ctx context.Context, in *QueryWordsListRequest, opts ...grpc.CallOption) (*QueryWordsListReply, error) {
	out := new(QueryWordsListReply)
	err := c.cc.Invoke(ctx, QueryWords_QueryWordsList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QueryWordsServer is the server API for QueryWords service.
// All implementations must embed UnimplementedQueryWordsServer
// for forward compatibility
type QueryWordsServer interface {
	QueryWordsList(context.Context, *QueryWordsListRequest) (*QueryWordsListReply, error)
	mustEmbedUnimplementedQueryWordsServer()
}

// UnimplementedQueryWordsServer must be embedded to have forward compatible implementations.
type UnimplementedQueryWordsServer struct {
}

func (UnimplementedQueryWordsServer) QueryWordsList(context.Context, *QueryWordsListRequest) (*QueryWordsListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryWordsList not implemented")
}
func (UnimplementedQueryWordsServer) mustEmbedUnimplementedQueryWordsServer() {}

// UnsafeQueryWordsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to QueryWordsServer will
// result in compilation errors.
type UnsafeQueryWordsServer interface {
	mustEmbedUnimplementedQueryWordsServer()
}

func RegisterQueryWordsServer(s grpc.ServiceRegistrar, srv QueryWordsServer) {
	s.RegisterService(&QueryWords_ServiceDesc, srv)
}

func _QueryWords_QueryWordsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryWordsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueryWordsServer).QueryWordsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QueryWords_QueryWordsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueryWordsServer).QueryWordsList(ctx, req.(*QueryWordsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// QueryWords_ServiceDesc is the grpc.ServiceDesc for QueryWords service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var QueryWords_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.query_words.v1.QueryWords",
	HandlerType: (*QueryWordsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryWordsList",
			Handler:    _QueryWords_QueryWordsList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/query_words/v1/query_words.proto",
}
