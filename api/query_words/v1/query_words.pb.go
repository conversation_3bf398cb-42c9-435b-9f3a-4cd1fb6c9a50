// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.3
// source: api/query_words/v1/query_words.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QueryWordsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Word  string               `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"`
	Query *QueryWordsListQuery `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
}

func (x *QueryWordsListRequest) Reset() {
	*x = QueryWordsListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_query_words_v1_query_words_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryWordsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryWordsListRequest) ProtoMessage() {}

func (x *QueryWordsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_query_words_v1_query_words_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryWordsListRequest.ProtoReflect.Descriptor instead.
func (*QueryWordsListRequest) Descriptor() ([]byte, []int) {
	return file_api_query_words_v1_query_words_proto_rawDescGZIP(), []int{0}
}

func (x *QueryWordsListRequest) GetWord() string {
	if x != nil {
		return x.Word
	}
	return ""
}

func (x *QueryWordsListRequest) GetQuery() *QueryWordsListQuery {
	if x != nil {
		return x.Query
	}
	return nil
}

// word 单词
// british 英式发音
// american 美式发音
// synonym 同义词
// antonym 反义词
// inflection 变形词
// prefix 前缀
// suffix 后缀
// phrase 短语
// meaning 释义
// sentence 例句
type QueryWordsListQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Word       string `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"`
	British    string `protobuf:"bytes,2,opt,name=british,proto3" json:"british,omitempty"`
	American   string `protobuf:"bytes,3,opt,name=american,proto3" json:"american,omitempty"`
	Synonym    string `protobuf:"bytes,4,opt,name=synonym,proto3" json:"synonym,omitempty"`
	Antonym    string `protobuf:"bytes,5,opt,name=antonym,proto3" json:"antonym,omitempty"`
	Inflection string `protobuf:"bytes,6,opt,name=inflection,proto3" json:"inflection,omitempty"`
	Prefix     string `protobuf:"bytes,7,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Suffix     string `protobuf:"bytes,8,opt,name=suffix,proto3" json:"suffix,omitempty"`
	Phrase     string `protobuf:"bytes,9,opt,name=phrase,proto3" json:"phrase,omitempty"`
	Meaning    string `protobuf:"bytes,10,opt,name=meaning,proto3" json:"meaning,omitempty"`
	Sentence   string `protobuf:"bytes,11,opt,name=sentence,proto3" json:"sentence,omitempty"`
}

func (x *QueryWordsListQuery) Reset() {
	*x = QueryWordsListQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_query_words_v1_query_words_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryWordsListQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryWordsListQuery) ProtoMessage() {}

func (x *QueryWordsListQuery) ProtoReflect() protoreflect.Message {
	mi := &file_api_query_words_v1_query_words_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryWordsListQuery.ProtoReflect.Descriptor instead.
func (*QueryWordsListQuery) Descriptor() ([]byte, []int) {
	return file_api_query_words_v1_query_words_proto_rawDescGZIP(), []int{1}
}

func (x *QueryWordsListQuery) GetWord() string {
	if x != nil {
		return x.Word
	}
	return ""
}

func (x *QueryWordsListQuery) GetBritish() string {
	if x != nil {
		return x.British
	}
	return ""
}

func (x *QueryWordsListQuery) GetAmerican() string {
	if x != nil {
		return x.American
	}
	return ""
}

func (x *QueryWordsListQuery) GetSynonym() string {
	if x != nil {
		return x.Synonym
	}
	return ""
}

func (x *QueryWordsListQuery) GetAntonym() string {
	if x != nil {
		return x.Antonym
	}
	return ""
}

func (x *QueryWordsListQuery) GetInflection() string {
	if x != nil {
		return x.Inflection
	}
	return ""
}

func (x *QueryWordsListQuery) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *QueryWordsListQuery) GetSuffix() string {
	if x != nil {
		return x.Suffix
	}
	return ""
}

func (x *QueryWordsListQuery) GetPhrase() string {
	if x != nil {
		return x.Phrase
	}
	return ""
}

func (x *QueryWordsListQuery) GetMeaning() string {
	if x != nil {
		return x.Meaning
	}
	return ""
}

func (x *QueryWordsListQuery) GetSentence() string {
	if x != nil {
		return x.Sentence
	}
	return ""
}

type QueryWordsListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*QueryWordsListItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	Curse bool                  `protobuf:"varint,3,opt,name=curse,proto3" json:"curse,omitempty"`
}

func (x *QueryWordsListReply) Reset() {
	*x = QueryWordsListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_query_words_v1_query_words_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryWordsListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryWordsListReply) ProtoMessage() {}

func (x *QueryWordsListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_query_words_v1_query_words_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryWordsListReply.ProtoReflect.Descriptor instead.
func (*QueryWordsListReply) Descriptor() ([]byte, []int) {
	return file_api_query_words_v1_query_words_proto_rawDescGZIP(), []int{2}
}

func (x *QueryWordsListReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *QueryWordsListReply) GetList() []*QueryWordsListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *QueryWordsListReply) GetCurse() bool {
	if x != nil {
		return x.Curse
	}
	return false
}

type QueryWordsListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Detail *structpb.Struct `protobuf:"bytes,1,opt,name=detail,proto3" json:"detail,omitempty"`
	Score  float32          `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *QueryWordsListItem) Reset() {
	*x = QueryWordsListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_query_words_v1_query_words_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryWordsListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryWordsListItem) ProtoMessage() {}

func (x *QueryWordsListItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_query_words_v1_query_words_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryWordsListItem.ProtoReflect.Descriptor instead.
func (*QueryWordsListItem) Descriptor() ([]byte, []int) {
	return file_api_query_words_v1_query_words_proto_rawDescGZIP(), []int{3}
}

func (x *QueryWordsListItem) GetDetail() *structpb.Struct {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *QueryWordsListItem) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

var File_api_query_words_v1_query_words_proto protoreflect.FileDescriptor

var file_api_query_words_v1_query_words_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x61, 0x70, 0x69, 0x2e, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6a, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57,
	0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x77,
	0x6f, 0x72, 0x64, 0x12, 0x3d, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72,
	0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x22, 0xb1, 0x02, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x64,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x62, 0x72, 0x69, 0x74, 0x69, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x62, 0x72, 0x69, 0x74, 0x69, 0x73, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x6d, 0x65, 0x72,
	0x69, 0x63, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x6d, 0x65, 0x72,
	0x69, 0x63, 0x61, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x79, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x79, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x6e, 0x74, 0x6f, 0x6e, 0x79, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x6e, 0x74, 0x6f, 0x6e, 0x79, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x66, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e,
	0x66, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x68, 0x72, 0x61,
	0x73, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65,
	0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65,
	0x6e, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x22, 0x7d, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57,
	0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x3a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x77, 0x6f,
	0x72, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x64,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x75, 0x72, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x63, 0x75, 0x72, 0x73, 0x65, 0x22, 0x5b, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f,
	0x72, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2f, 0x0a, 0x06, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x32, 0xa1, 0x01, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x92, 0x01, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x64, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57,
	0x6f, 0x72, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x22, 0x21, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f,
	0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2f, 0x6c,
	0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x42, 0x3d, 0x0a, 0x12, 0x61, 0x70, 0x69, 0x2e, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x25,
	0x68, 0x77, 0x2d, 0x70, 0x61, 0x61, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_query_words_v1_query_words_proto_rawDescOnce sync.Once
	file_api_query_words_v1_query_words_proto_rawDescData = file_api_query_words_v1_query_words_proto_rawDesc
)

func file_api_query_words_v1_query_words_proto_rawDescGZIP() []byte {
	file_api_query_words_v1_query_words_proto_rawDescOnce.Do(func() {
		file_api_query_words_v1_query_words_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_query_words_v1_query_words_proto_rawDescData)
	})
	return file_api_query_words_v1_query_words_proto_rawDescData
}

var file_api_query_words_v1_query_words_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_query_words_v1_query_words_proto_goTypes = []interface{}{
	(*QueryWordsListRequest)(nil), // 0: api.query_words.v1.QueryWordsListRequest
	(*QueryWordsListQuery)(nil),   // 1: api.query_words.v1.QueryWordsListQuery
	(*QueryWordsListReply)(nil),   // 2: api.query_words.v1.QueryWordsListReply
	(*QueryWordsListItem)(nil),    // 3: api.query_words.v1.QueryWordsListItem
	(*structpb.Struct)(nil),       // 4: google.protobuf.Struct
}
var file_api_query_words_v1_query_words_proto_depIdxs = []int32{
	1, // 0: api.query_words.v1.QueryWordsListRequest.query:type_name -> api.query_words.v1.QueryWordsListQuery
	3, // 1: api.query_words.v1.QueryWordsListReply.list:type_name -> api.query_words.v1.QueryWordsListItem
	4, // 2: api.query_words.v1.QueryWordsListItem.detail:type_name -> google.protobuf.Struct
	0, // 3: api.query_words.v1.QueryWords.QueryWordsList:input_type -> api.query_words.v1.QueryWordsListRequest
	2, // 4: api.query_words.v1.QueryWords.QueryWordsList:output_type -> api.query_words.v1.QueryWordsListReply
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_query_words_v1_query_words_proto_init() }
func file_api_query_words_v1_query_words_proto_init() {
	if File_api_query_words_v1_query_words_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_query_words_v1_query_words_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryWordsListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_query_words_v1_query_words_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryWordsListQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_query_words_v1_query_words_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryWordsListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_query_words_v1_query_words_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryWordsListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_query_words_v1_query_words_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_query_words_v1_query_words_proto_goTypes,
		DependencyIndexes: file_api_query_words_v1_query_words_proto_depIdxs,
		MessageInfos:      file_api_query_words_v1_query_words_proto_msgTypes,
	}.Build()
	File_api_query_words_v1_query_words_proto = out.File
	file_api_query_words_v1_query_words_proto_rawDesc = nil
	file_api_query_words_v1_query_words_proto_goTypes = nil
	file_api_query_words_v1_query_words_proto_depIdxs = nil
}
