// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/query_words/v1/query_words.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on QueryWordsListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryWordsListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryWordsListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryWordsListRequestMultiError, or nil if none found.
func (m *QueryWordsListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryWordsListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Word

	if all {
		switch v := interface{}(m.GetQuery()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryWordsListRequestValidationError{
					field:  "Query",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryWordsListRequestValidationError{
					field:  "Query",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuery()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryWordsListRequestValidationError{
				field:  "Query",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QueryWordsListRequestMultiError(errors)
	}

	return nil
}

// QueryWordsListRequestMultiError is an error wrapping multiple validation
// errors returned by QueryWordsListRequest.ValidateAll() if the designated
// constraints aren't met.
type QueryWordsListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryWordsListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryWordsListRequestMultiError) AllErrors() []error { return m }

// QueryWordsListRequestValidationError is the validation error returned by
// QueryWordsListRequest.Validate if the designated constraints aren't met.
type QueryWordsListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryWordsListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryWordsListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryWordsListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryWordsListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryWordsListRequestValidationError) ErrorName() string {
	return "QueryWordsListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryWordsListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryWordsListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryWordsListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryWordsListRequestValidationError{}

// Validate checks the field values on QueryWordsListQuery with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryWordsListQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryWordsListQuery with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryWordsListQueryMultiError, or nil if none found.
func (m *QueryWordsListQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryWordsListQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Word

	// no validation rules for British

	// no validation rules for American

	// no validation rules for Synonym

	// no validation rules for Antonym

	// no validation rules for Inflection

	// no validation rules for Prefix

	// no validation rules for Suffix

	// no validation rules for Phrase

	// no validation rules for Meaning

	// no validation rules for Sentence

	if len(errors) > 0 {
		return QueryWordsListQueryMultiError(errors)
	}

	return nil
}

// QueryWordsListQueryMultiError is an error wrapping multiple validation
// errors returned by QueryWordsListQuery.ValidateAll() if the designated
// constraints aren't met.
type QueryWordsListQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryWordsListQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryWordsListQueryMultiError) AllErrors() []error { return m }

// QueryWordsListQueryValidationError is the validation error returned by
// QueryWordsListQuery.Validate if the designated constraints aren't met.
type QueryWordsListQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryWordsListQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryWordsListQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryWordsListQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryWordsListQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryWordsListQueryValidationError) ErrorName() string {
	return "QueryWordsListQueryValidationError"
}

// Error satisfies the builtin error interface
func (e QueryWordsListQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryWordsListQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryWordsListQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryWordsListQueryValidationError{}

// Validate checks the field values on QueryWordsListReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryWordsListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryWordsListReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryWordsListReplyMultiError, or nil if none found.
func (m *QueryWordsListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryWordsListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryWordsListReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryWordsListReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryWordsListReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Curse

	if len(errors) > 0 {
		return QueryWordsListReplyMultiError(errors)
	}

	return nil
}

// QueryWordsListReplyMultiError is an error wrapping multiple validation
// errors returned by QueryWordsListReply.ValidateAll() if the designated
// constraints aren't met.
type QueryWordsListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryWordsListReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryWordsListReplyMultiError) AllErrors() []error { return m }

// QueryWordsListReplyValidationError is the validation error returned by
// QueryWordsListReply.Validate if the designated constraints aren't met.
type QueryWordsListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryWordsListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryWordsListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryWordsListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryWordsListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryWordsListReplyValidationError) ErrorName() string {
	return "QueryWordsListReplyValidationError"
}

// Error satisfies the builtin error interface
func (e QueryWordsListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryWordsListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryWordsListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryWordsListReplyValidationError{}

// Validate checks the field values on QueryWordsListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryWordsListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryWordsListItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryWordsListItemMultiError, or nil if none found.
func (m *QueryWordsListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryWordsListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryWordsListItemValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryWordsListItemValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryWordsListItemValidationError{
				field:  "Detail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Score

	if len(errors) > 0 {
		return QueryWordsListItemMultiError(errors)
	}

	return nil
}

// QueryWordsListItemMultiError is an error wrapping multiple validation errors
// returned by QueryWordsListItem.ValidateAll() if the designated constraints
// aren't met.
type QueryWordsListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryWordsListItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryWordsListItemMultiError) AllErrors() []error { return m }

// QueryWordsListItemValidationError is the validation error returned by
// QueryWordsListItem.Validate if the designated constraints aren't met.
type QueryWordsListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryWordsListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryWordsListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryWordsListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryWordsListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryWordsListItemValidationError) ErrorName() string {
	return "QueryWordsListItemValidationError"
}

// Error satisfies the builtin error interface
func (e QueryWordsListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryWordsListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryWordsListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryWordsListItemValidationError{}
