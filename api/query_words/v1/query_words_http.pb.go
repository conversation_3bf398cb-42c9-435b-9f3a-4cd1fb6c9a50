// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.23.3
// source: api/query_words/v1/query_words.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationQueryWordsQueryWordsList = "/api.query_words.v1.QueryWords/QueryWordsList"

type QueryWordsHTTPServer interface {
	QueryWordsList(context.Context, *QueryWordsListRequest) (*QueryWordsListReply, error)
}

func RegisterQueryWordsHTTPServer(s *http.Server, srv QueryWordsHTTPServer) {
	r := s.Route("/")
	r.POST("/intelligence/v1/query_words/list", _QueryWords_QueryWordsList0_HTTP_Handler(srv))
}

func _QueryWords_QueryWordsList0_HTTP_Handler(srv QueryWordsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryWordsListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationQueryWordsQueryWordsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryWordsList(ctx, req.(*QueryWordsListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryWordsListReply)
		return ctx.Result(200, reply)
	}
}

type QueryWordsHTTPClient interface {
	QueryWordsList(ctx context.Context, req *QueryWordsListRequest, opts ...http.CallOption) (rsp *QueryWordsListReply, err error)
}

type QueryWordsHTTPClientImpl struct {
	cc *http.Client
}

func NewQueryWordsHTTPClient(client *http.Client) QueryWordsHTTPClient {
	return &QueryWordsHTTPClientImpl{client}
}

func (c *QueryWordsHTTPClientImpl) QueryWordsList(ctx context.Context, in *QueryWordsListRequest, opts ...http.CallOption) (*QueryWordsListReply, error) {
	var out QueryWordsListReply
	pattern := "/intelligence/v1/query_words/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationQueryWordsQueryWordsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
