syntax = "proto3";

package api.skill.v1;
import "google/api/annotations.proto";

option go_package = "hw-paas-service/api/skill/v1;v1";
option java_multiple_files = true;
option java_package = "api.skill.v1";

service Skill {
  rpc GetRnList (GetRnListRequest) returns (GetRnListReply){
    option (google.api.http) = {
      get: "/intelligence/v1/skill/rns"
    };
  };
}

message GetRnListRequest {
  string app_version = 1;
}

message GetRnListReply {
  message bundle_item {
    string bundle_name = 1;
    string bundle_version = 2;
    string level = 3;
    int32  force = 4;
    string check_sum = 5;
    string download_url = 6;
  }
  message rn {
    string skill_name = 1;
    repeated bundle_item bundle_list = 2;
  }
  repeated rn rn_list = 1;
}
