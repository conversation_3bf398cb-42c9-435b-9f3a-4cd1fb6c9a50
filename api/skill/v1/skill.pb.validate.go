// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/skill/v1/skill.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetRnListRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetRnListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRnListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRnListRequestMultiError, or nil if none found.
func (m *GetRnListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRnListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppVersion

	if len(errors) > 0 {
		return GetRnListRequestMultiError(errors)
	}

	return nil
}

// GetRnListRequestMultiError is an error wrapping multiple validation errors
// returned by GetRnListRequest.ValidateAll() if the designated constraints
// aren't met.
type GetRnListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRnListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRnListRequestMultiError) AllErrors() []error { return m }

// GetRnListRequestValidationError is the validation error returned by
// GetRnListRequest.Validate if the designated constraints aren't met.
type GetRnListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRnListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRnListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRnListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRnListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRnListRequestValidationError) ErrorName() string { return "GetRnListRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetRnListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRnListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRnListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRnListRequestValidationError{}

// Validate checks the field values on GetRnListReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetRnListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRnListReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetRnListReplyMultiError,
// or nil if none found.
func (m *GetRnListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRnListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRnList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRnListReplyValidationError{
						field:  fmt.Sprintf("RnList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRnListReplyValidationError{
						field:  fmt.Sprintf("RnList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRnListReplyValidationError{
					field:  fmt.Sprintf("RnList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRnListReplyMultiError(errors)
	}

	return nil
}

// GetRnListReplyMultiError is an error wrapping multiple validation errors
// returned by GetRnListReply.ValidateAll() if the designated constraints
// aren't met.
type GetRnListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRnListReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRnListReplyMultiError) AllErrors() []error { return m }

// GetRnListReplyValidationError is the validation error returned by
// GetRnListReply.Validate if the designated constraints aren't met.
type GetRnListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRnListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRnListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRnListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRnListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRnListReplyValidationError) ErrorName() string { return "GetRnListReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetRnListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRnListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRnListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRnListReplyValidationError{}

// Validate checks the field values on GetRnListReplyBundleItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRnListReplyBundleItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRnListReplyBundleItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRnListReplyBundleItemMultiError, or nil if none found.
func (m *GetRnListReplyBundleItem) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRnListReplyBundleItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BundleName

	// no validation rules for BundleVersion

	// no validation rules for Level

	// no validation rules for Force

	// no validation rules for CheckSum

	// no validation rules for DownloadUrl

	if len(errors) > 0 {
		return GetRnListReplyBundleItemMultiError(errors)
	}

	return nil
}

// GetRnListReplyBundleItemMultiError is an error wrapping multiple validation
// errors returned by GetRnListReplyBundleItem.ValidateAll() if the designated
// constraints aren't met.
type GetRnListReplyBundleItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRnListReplyBundleItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRnListReplyBundleItemMultiError) AllErrors() []error { return m }

// GetRnListReplyBundleItemValidationError is the validation error returned by
// GetRnListReplyBundleItem.Validate if the designated constraints aren't met.
type GetRnListReplyBundleItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRnListReplyBundleItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRnListReplyBundleItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRnListReplyBundleItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRnListReplyBundleItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRnListReplyBundleItemValidationError) ErrorName() string {
	return "GetRnListReplyBundleItemValidationError"
}

// Error satisfies the builtin error interface
func (e GetRnListReplyBundleItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRnListReplyBundleItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRnListReplyBundleItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRnListReplyBundleItemValidationError{}

// Validate checks the field values on GetRnListReplyRn with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetRnListReplyRn) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRnListReplyRn with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRnListReplyRnMultiError, or nil if none found.
func (m *GetRnListReplyRn) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRnListReplyRn) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SkillName

	for idx, item := range m.GetBundleList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRnListReplyRnValidationError{
						field:  fmt.Sprintf("BundleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRnListReplyRnValidationError{
						field:  fmt.Sprintf("BundleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRnListReplyRnValidationError{
					field:  fmt.Sprintf("BundleList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRnListReplyRnMultiError(errors)
	}

	return nil
}

// GetRnListReplyRnMultiError is an error wrapping multiple validation errors
// returned by GetRnListReplyRn.ValidateAll() if the designated constraints
// aren't met.
type GetRnListReplyRnMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRnListReplyRnMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRnListReplyRnMultiError) AllErrors() []error { return m }

// GetRnListReplyRnValidationError is the validation error returned by
// GetRnListReplyRn.Validate if the designated constraints aren't met.
type GetRnListReplyRnValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRnListReplyRnValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRnListReplyRnValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRnListReplyRnValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRnListReplyRnValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRnListReplyRnValidationError) ErrorName() string { return "GetRnListReplyRnValidationError" }

// Error satisfies the builtin error interface
func (e GetRnListReplyRnValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRnListReplyRn.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRnListReplyRnValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRnListReplyRnValidationError{}
