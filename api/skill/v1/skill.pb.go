// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.3
// source: api/skill/v1/skill.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetRnListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppVersion string `protobuf:"bytes,1,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
}

func (x *GetRnListRequest) Reset() {
	*x = GetRnListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_skill_v1_skill_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRnListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRnListRequest) ProtoMessage() {}

func (x *GetRnListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_skill_v1_skill_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRnListRequest.ProtoReflect.Descriptor instead.
func (*GetRnListRequest) Descriptor() ([]byte, []int) {
	return file_api_skill_v1_skill_proto_rawDescGZIP(), []int{0}
}

func (x *GetRnListRequest) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

type GetRnListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RnList []*GetRnListReplyRn `protobuf:"bytes,1,rep,name=rn_list,json=rnList,proto3" json:"rn_list,omitempty"`
}

func (x *GetRnListReply) Reset() {
	*x = GetRnListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_skill_v1_skill_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRnListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRnListReply) ProtoMessage() {}

func (x *GetRnListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_skill_v1_skill_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRnListReply.ProtoReflect.Descriptor instead.
func (*GetRnListReply) Descriptor() ([]byte, []int) {
	return file_api_skill_v1_skill_proto_rawDescGZIP(), []int{1}
}

func (x *GetRnListReply) GetRnList() []*GetRnListReplyRn {
	if x != nil {
		return x.RnList
	}
	return nil
}

type GetRnListReplyBundleItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BundleName    string `protobuf:"bytes,1,opt,name=bundle_name,json=bundleName,proto3" json:"bundle_name,omitempty"`
	BundleVersion string `protobuf:"bytes,2,opt,name=bundle_version,json=bundleVersion,proto3" json:"bundle_version,omitempty"`
	Level         string `protobuf:"bytes,3,opt,name=level,proto3" json:"level,omitempty"`
	Force         int32  `protobuf:"varint,4,opt,name=force,proto3" json:"force,omitempty"`
	CheckSum      string `protobuf:"bytes,5,opt,name=check_sum,json=checkSum,proto3" json:"check_sum,omitempty"`
	DownloadUrl   string `protobuf:"bytes,6,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
}

func (x *GetRnListReplyBundleItem) Reset() {
	*x = GetRnListReplyBundleItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_skill_v1_skill_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRnListReplyBundleItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRnListReplyBundleItem) ProtoMessage() {}

func (x *GetRnListReplyBundleItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_skill_v1_skill_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRnListReplyBundleItem.ProtoReflect.Descriptor instead.
func (*GetRnListReplyBundleItem) Descriptor() ([]byte, []int) {
	return file_api_skill_v1_skill_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetRnListReplyBundleItem) GetBundleName() string {
	if x != nil {
		return x.BundleName
	}
	return ""
}

func (x *GetRnListReplyBundleItem) GetBundleVersion() string {
	if x != nil {
		return x.BundleVersion
	}
	return ""
}

func (x *GetRnListReplyBundleItem) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *GetRnListReplyBundleItem) GetForce() int32 {
	if x != nil {
		return x.Force
	}
	return 0
}

func (x *GetRnListReplyBundleItem) GetCheckSum() string {
	if x != nil {
		return x.CheckSum
	}
	return ""
}

func (x *GetRnListReplyBundleItem) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

type GetRnListReplyRn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SkillName  string                      `protobuf:"bytes,1,opt,name=skill_name,json=skillName,proto3" json:"skill_name,omitempty"`
	BundleList []*GetRnListReplyBundleItem `protobuf:"bytes,2,rep,name=bundle_list,json=bundleList,proto3" json:"bundle_list,omitempty"`
}

func (x *GetRnListReplyRn) Reset() {
	*x = GetRnListReplyRn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_skill_v1_skill_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRnListReplyRn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRnListReplyRn) ProtoMessage() {}

func (x *GetRnListReplyRn) ProtoReflect() protoreflect.Message {
	mi := &file_api_skill_v1_skill_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRnListReplyRn.ProtoReflect.Descriptor instead.
func (*GetRnListReplyRn) Descriptor() ([]byte, []int) {
	return file_api_skill_v1_skill_proto_rawDescGZIP(), []int{1, 1}
}

func (x *GetRnListReplyRn) GetSkillName() string {
	if x != nil {
		return x.SkillName
	}
	return ""
}

func (x *GetRnListReplyRn) GetBundleList() []*GetRnListReplyBundleItem {
	if x != nil {
		return x.BundleList
	}
	return nil
}

var File_api_skill_v1_skill_proto protoreflect.FileDescriptor

var file_api_skill_v1_skill_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x33, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70,
	0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xfe, 0x02, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x52, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x38,
	0x0a, 0x07, 0x72, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x72, 0x6e,
	0x52, 0x06, 0x72, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xc1, 0x01, 0x0a, 0x0b, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x73, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x75, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x1a, 0x6e, 0x0a, 0x02,
	0x72, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x49, 0x0a, 0x0b, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x6b, 0x69,
	0x6c, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x2e, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x52, 0x0a, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x32, 0x76, 0x0a, 0x05,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x6d, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x52, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c,
	0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6b, 0x69, 0x6c, 0x6c,
	0x2f, 0x72, 0x6e, 0x73, 0x42, 0x31, 0x0a, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x6b, 0x69, 0x6c,
	0x6c, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x1f, 0x68, 0x77, 0x2d, 0x70, 0x61, 0x61, 0x73, 0x2d,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x6b, 0x69, 0x6c,
	0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_skill_v1_skill_proto_rawDescOnce sync.Once
	file_api_skill_v1_skill_proto_rawDescData = file_api_skill_v1_skill_proto_rawDesc
)

func file_api_skill_v1_skill_proto_rawDescGZIP() []byte {
	file_api_skill_v1_skill_proto_rawDescOnce.Do(func() {
		file_api_skill_v1_skill_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_skill_v1_skill_proto_rawDescData)
	})
	return file_api_skill_v1_skill_proto_rawDescData
}

var file_api_skill_v1_skill_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_skill_v1_skill_proto_goTypes = []interface{}{
	(*GetRnListRequest)(nil),         // 0: api.skill.v1.GetRnListRequest
	(*GetRnListReply)(nil),           // 1: api.skill.v1.GetRnListReply
	(*GetRnListReplyBundleItem)(nil), // 2: api.skill.v1.GetRnListReply.bundle_item
	(*GetRnListReplyRn)(nil),         // 3: api.skill.v1.GetRnListReply.rn
}
var file_api_skill_v1_skill_proto_depIdxs = []int32{
	3, // 0: api.skill.v1.GetRnListReply.rn_list:type_name -> api.skill.v1.GetRnListReply.rn
	2, // 1: api.skill.v1.GetRnListReply.rn.bundle_list:type_name -> api.skill.v1.GetRnListReply.bundle_item
	0, // 2: api.skill.v1.Skill.GetRnList:input_type -> api.skill.v1.GetRnListRequest
	1, // 3: api.skill.v1.Skill.GetRnList:output_type -> api.skill.v1.GetRnListReply
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_skill_v1_skill_proto_init() }
func file_api_skill_v1_skill_proto_init() {
	if File_api_skill_v1_skill_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_skill_v1_skill_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRnListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_skill_v1_skill_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRnListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_skill_v1_skill_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRnListReplyBundleItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_skill_v1_skill_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRnListReplyRn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_skill_v1_skill_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_skill_v1_skill_proto_goTypes,
		DependencyIndexes: file_api_skill_v1_skill_proto_depIdxs,
		MessageInfos:      file_api_skill_v1_skill_proto_msgTypes,
	}.Build()
	File_api_skill_v1_skill_proto = out.File
	file_api_skill_v1_skill_proto_rawDesc = nil
	file_api_skill_v1_skill_proto_goTypes = nil
	file_api_skill_v1_skill_proto_depIdxs = nil
}
