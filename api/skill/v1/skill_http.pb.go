// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.23.3
// source: api/skill/v1/skill.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSkillGetRnList = "/api.skill.v1.Skill/GetRnList"

type SkillHTTPServer interface {
	GetRnList(context.Context, *GetRnListRequest) (*GetRnListReply, error)
}

func RegisterSkillHTTPServer(s *http.Server, srv SkillHTTPServer) {
	r := s.Route("/")
	r.GET("/intelligence/v1/skill/rns", _Skill_GetRnList0_HTTP_Handler(srv))
}

func _Skill_GetRnList0_HTTP_Handler(srv SkillHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRnListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSkillGetRnList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRnList(ctx, req.(*GetRnListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRnListReply)
		return ctx.Result(200, reply)
	}
}

type SkillHTTPClient interface {
	GetRnList(ctx context.Context, req *GetRnListRequest, opts ...http.CallOption) (rsp *GetRnListReply, err error)
}

type SkillHTTPClientImpl struct {
	cc *http.Client
}

func NewSkillHTTPClient(client *http.Client) SkillHTTPClient {
	return &SkillHTTPClientImpl{client}
}

func (c *SkillHTTPClientImpl) GetRnList(ctx context.Context, in *GetRnListRequest, opts ...http.CallOption) (*GetRnListReply, error) {
	var out GetRnListReply
	pattern := "/intelligence/v1/skill/rns"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSkillGetRnList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
