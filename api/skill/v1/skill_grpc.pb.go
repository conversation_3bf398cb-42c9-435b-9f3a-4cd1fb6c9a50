// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.3
// source: api/skill/v1/skill.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Skill_GetRnList_FullMethodName = "/api.skill.v1.Skill/GetRnList"
)

// SkillClient is the client API for Skill service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SkillClient interface {
	GetRnList(ctx context.Context, in *GetRnListRequest, opts ...grpc.CallOption) (*GetRnListReply, error)
}

type skillClient struct {
	cc grpc.ClientConnInterface
}

func NewSkillClient(cc grpc.ClientConnInterface) SkillClient {
	return &skillClient{cc}
}

func (c *skillClient) GetRnList(ctx context.Context, in *GetRnListRequest, opts ...grpc.CallOption) (*GetRnListReply, error) {
	out := new(GetRnListReply)
	err := c.cc.Invoke(ctx, Skill_GetRnList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SkillServer is the server API for Skill service.
// All implementations must embed UnimplementedSkillServer
// for forward compatibility
type SkillServer interface {
	GetRnList(context.Context, *GetRnListRequest) (*GetRnListReply, error)
	mustEmbedUnimplementedSkillServer()
}

// UnimplementedSkillServer must be embedded to have forward compatible implementations.
type UnimplementedSkillServer struct {
}

func (UnimplementedSkillServer) GetRnList(context.Context, *GetRnListRequest) (*GetRnListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRnList not implemented")
}
func (UnimplementedSkillServer) mustEmbedUnimplementedSkillServer() {}

// UnsafeSkillServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SkillServer will
// result in compilation errors.
type UnsafeSkillServer interface {
	mustEmbedUnimplementedSkillServer()
}

func RegisterSkillServer(s grpc.ServiceRegistrar, srv SkillServer) {
	s.RegisterService(&Skill_ServiceDesc, srv)
}

func _Skill_GetRnList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRnListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SkillServer).GetRnList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Skill_GetRnList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SkillServer).GetRnList(ctx, req.(*GetRnListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Skill_ServiceDesc is the grpc.ServiceDesc for Skill service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Skill_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.skill.v1.Skill",
	HandlerType: (*SkillServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRnList",
			Handler:    _Skill_GetRnList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/skill/v1/skill.proto",
}
