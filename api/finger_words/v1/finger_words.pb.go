// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.3
// source: api/finger_words/v1/finger_words.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FingerWordsEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientTraceId  string  `protobuf:"bytes,1,opt,name=client_trace_id,json=clientTraceId,proto3" json:"client_trace_id,omitempty"`
	RetryStatus    int32   `protobuf:"varint,2,opt,name=retry_status,json=retryStatus,proto3" json:"retry_status,omitempty"`
	TextBmp        string  `protobuf:"bytes,3,opt,name=text_bmp,json=textBmp,proto3" json:"text_bmp,omitempty"`
	FingerPos_2Ma  []int32 `protobuf:"varint,4,rep,packed,name=finger_pos_2ma,json=fingerPos2ma,proto3" json:"finger_pos_2ma,omitempty"`
	TextPos_2Ma    []int32 `protobuf:"varint,5,rep,packed,name=text_pos_2ma,json=textPos2ma,proto3" json:"text_pos_2ma,omitempty"`
	Version        string  `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	AppVersion     string  `protobuf:"bytes,7,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	LocalModelTime string  `protobuf:"bytes,8,opt,name=local_model_time,json=localModelTime,proto3" json:"local_model_time,omitempty"`
}

func (x *FingerWordsEntryRequest) Reset() {
	*x = FingerWordsEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_finger_words_v1_finger_words_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FingerWordsEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FingerWordsEntryRequest) ProtoMessage() {}

func (x *FingerWordsEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_finger_words_v1_finger_words_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FingerWordsEntryRequest.ProtoReflect.Descriptor instead.
func (*FingerWordsEntryRequest) Descriptor() ([]byte, []int) {
	return file_api_finger_words_v1_finger_words_proto_rawDescGZIP(), []int{0}
}

func (x *FingerWordsEntryRequest) GetClientTraceId() string {
	if x != nil {
		return x.ClientTraceId
	}
	return ""
}

func (x *FingerWordsEntryRequest) GetRetryStatus() int32 {
	if x != nil {
		return x.RetryStatus
	}
	return 0
}

func (x *FingerWordsEntryRequest) GetTextBmp() string {
	if x != nil {
		return x.TextBmp
	}
	return ""
}

func (x *FingerWordsEntryRequest) GetFingerPos_2Ma() []int32 {
	if x != nil {
		return x.FingerPos_2Ma
	}
	return nil
}

func (x *FingerWordsEntryRequest) GetTextPos_2Ma() []int32 {
	if x != nil {
		return x.TextPos_2Ma
	}
	return nil
}

func (x *FingerWordsEntryRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *FingerWordsEntryRequest) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *FingerWordsEntryRequest) GetLocalModelTime() string {
	if x != nil {
		return x.LocalModelTime
	}
	return ""
}

type FingerWordsEntryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NearWord    string           `protobuf:"bytes,1,opt,name=near_word,json=nearWord,proto3" json:"near_word,omitempty"`
	LineWords   []string         `protobuf:"bytes,2,rep,name=line_words,json=lineWords,proto3" json:"line_words,omitempty"`
	NearIndex   int32            `protobuf:"varint,3,opt,name=near_index,json=nearIndex,proto3" json:"near_index,omitempty"`
	Ocr         string           `protobuf:"bytes,4,opt,name=ocr,proto3" json:"ocr,omitempty"`
	RetryStatus int32            `protobuf:"varint,5,opt,name=retry_status,json=retryStatus,proto3" json:"retry_status,omitempty"`
	Result      *structpb.Struct `protobuf:"bytes,6,opt,name=result,proto3" json:"result,omitempty"`
	Dispatch    *structpb.Struct `protobuf:"bytes,8,opt,name=dispatch,proto3" json:"dispatch,omitempty"`
}

func (x *FingerWordsEntryReply) Reset() {
	*x = FingerWordsEntryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_finger_words_v1_finger_words_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FingerWordsEntryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FingerWordsEntryReply) ProtoMessage() {}

func (x *FingerWordsEntryReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_finger_words_v1_finger_words_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FingerWordsEntryReply.ProtoReflect.Descriptor instead.
func (*FingerWordsEntryReply) Descriptor() ([]byte, []int) {
	return file_api_finger_words_v1_finger_words_proto_rawDescGZIP(), []int{1}
}

func (x *FingerWordsEntryReply) GetNearWord() string {
	if x != nil {
		return x.NearWord
	}
	return ""
}

func (x *FingerWordsEntryReply) GetLineWords() []string {
	if x != nil {
		return x.LineWords
	}
	return nil
}

func (x *FingerWordsEntryReply) GetNearIndex() int32 {
	if x != nil {
		return x.NearIndex
	}
	return 0
}

func (x *FingerWordsEntryReply) GetOcr() string {
	if x != nil {
		return x.Ocr
	}
	return ""
}

func (x *FingerWordsEntryReply) GetRetryStatus() int32 {
	if x != nil {
		return x.RetryStatus
	}
	return 0
}

func (x *FingerWordsEntryReply) GetResult() *structpb.Struct {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *FingerWordsEntryReply) GetDispatch() *structpb.Struct {
	if x != nil {
		return x.Dispatch
	}
	return nil
}

type FingerWordsQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Word       string `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"`
	AppVersion string `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	SkillName  string `protobuf:"bytes,3,opt,name=skill_name,json=skillName,proto3" json:"skill_name,omitempty"`
}

func (x *FingerWordsQueryRequest) Reset() {
	*x = FingerWordsQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_finger_words_v1_finger_words_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FingerWordsQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FingerWordsQueryRequest) ProtoMessage() {}

func (x *FingerWordsQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_finger_words_v1_finger_words_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FingerWordsQueryRequest.ProtoReflect.Descriptor instead.
func (*FingerWordsQueryRequest) Descriptor() ([]byte, []int) {
	return file_api_finger_words_v1_finger_words_proto_rawDescGZIP(), []int{2}
}

func (x *FingerWordsQueryRequest) GetWord() string {
	if x != nil {
		return x.Word
	}
	return ""
}

func (x *FingerWordsQueryRequest) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *FingerWordsQueryRequest) GetSkillName() string {
	if x != nil {
		return x.SkillName
	}
	return ""
}

type FingerWordsQueryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Detail   *structpb.Struct `protobuf:"bytes,1,opt,name=detail,proto3" json:"detail,omitempty"`
	Dispatch *structpb.Struct `protobuf:"bytes,2,opt,name=dispatch,proto3" json:"dispatch,omitempty"`
}

func (x *FingerWordsQueryReply) Reset() {
	*x = FingerWordsQueryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_finger_words_v1_finger_words_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FingerWordsQueryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FingerWordsQueryReply) ProtoMessage() {}

func (x *FingerWordsQueryReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_finger_words_v1_finger_words_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FingerWordsQueryReply.ProtoReflect.Descriptor instead.
func (*FingerWordsQueryReply) Descriptor() ([]byte, []int) {
	return file_api_finger_words_v1_finger_words_proto_rawDescGZIP(), []int{3}
}

func (x *FingerWordsQueryReply) GetDetail() *structpb.Struct {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *FingerWordsQueryReply) GetDispatch() *structpb.Struct {
	if x != nil {
		return x.Dispatch
	}
	return nil
}

var File_api_finger_words_v1_finger_words_proto protoreflect.FileDescriptor

var file_api_finger_words_v1_finger_words_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72,
	0x64, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72,
	0x64, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69,
	0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xc7, 0x02, 0x0a, 0x17, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x57, 0x6f, 0x72,
	0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f,
	0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x62, 0x6d, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x74,
	0x65, 0x78, 0x74, 0x42, 0x6d, 0x70, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72,
	0x5f, 0x70, 0x6f, 0x73, 0x5f, 0x32, 0x6d, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0c,
	0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x32, 0x6d, 0x61, 0x12, 0x20, 0x0a, 0x0c,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x5f, 0x32, 0x6d, 0x61, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x0a, 0x74, 0x65, 0x78, 0x74, 0x50, 0x6f, 0x73, 0x32, 0x6d, 0x61, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x8d, 0x02, 0x0a,
	0x15, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65, 0x61, 0x72, 0x5f, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x65, 0x61, 0x72, 0x57,
	0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x57, 0x6f, 0x72,
	0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x61, 0x72, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x65, 0x61, 0x72, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x63, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6f, 0x63, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x33, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x52, 0x08, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x22, 0x88, 0x01, 0x0a,
	0x17, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x04, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x26, 0x0a, 0x0a, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x6b,
	0x69, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x7d, 0x0a, 0x15, 0x46, 0x69, 0x6e, 0x67, 0x65,
	0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x2f, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x33, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x08, 0x64, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x32, 0xcb, 0x02, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x67, 0x65,
	0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x9c, 0x01, 0x0a, 0x10, 0x46, 0x69, 0x6e, 0x67, 0x65,
	0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x2c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x22, 0x23, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2f, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0x9c, 0x01, 0x0a, 0x10, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72,
	0x57, 0x6f, 0x72, 0x64, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66,
	0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x69, 0x6e, 0x67, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x22, 0x23, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x66,
	0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2f, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x3a, 0x01, 0x2a, 0x42, 0x3f, 0x0a, 0x13, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x6e, 0x67,
	0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x26, 0x68,
	0x77, 0x2d, 0x70, 0x61, 0x61, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_finger_words_v1_finger_words_proto_rawDescOnce sync.Once
	file_api_finger_words_v1_finger_words_proto_rawDescData = file_api_finger_words_v1_finger_words_proto_rawDesc
)

func file_api_finger_words_v1_finger_words_proto_rawDescGZIP() []byte {
	file_api_finger_words_v1_finger_words_proto_rawDescOnce.Do(func() {
		file_api_finger_words_v1_finger_words_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_finger_words_v1_finger_words_proto_rawDescData)
	})
	return file_api_finger_words_v1_finger_words_proto_rawDescData
}

var file_api_finger_words_v1_finger_words_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_finger_words_v1_finger_words_proto_goTypes = []interface{}{
	(*FingerWordsEntryRequest)(nil), // 0: api.finger_words.v1.FingerWordsEntryRequest
	(*FingerWordsEntryReply)(nil),   // 1: api.finger_words.v1.FingerWordsEntryReply
	(*FingerWordsQueryRequest)(nil), // 2: api.finger_words.v1.FingerWordsQueryRequest
	(*FingerWordsQueryReply)(nil),   // 3: api.finger_words.v1.FingerWordsQueryReply
	(*structpb.Struct)(nil),         // 4: google.protobuf.Struct
}
var file_api_finger_words_v1_finger_words_proto_depIdxs = []int32{
	4, // 0: api.finger_words.v1.FingerWordsEntryReply.result:type_name -> google.protobuf.Struct
	4, // 1: api.finger_words.v1.FingerWordsEntryReply.dispatch:type_name -> google.protobuf.Struct
	4, // 2: api.finger_words.v1.FingerWordsQueryReply.detail:type_name -> google.protobuf.Struct
	4, // 3: api.finger_words.v1.FingerWordsQueryReply.dispatch:type_name -> google.protobuf.Struct
	0, // 4: api.finger_words.v1.FingerWords.FingerWordsEntry:input_type -> api.finger_words.v1.FingerWordsEntryRequest
	2, // 5: api.finger_words.v1.FingerWords.FingerWordsQuery:input_type -> api.finger_words.v1.FingerWordsQueryRequest
	1, // 6: api.finger_words.v1.FingerWords.FingerWordsEntry:output_type -> api.finger_words.v1.FingerWordsEntryReply
	3, // 7: api.finger_words.v1.FingerWords.FingerWordsQuery:output_type -> api.finger_words.v1.FingerWordsQueryReply
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_finger_words_v1_finger_words_proto_init() }
func file_api_finger_words_v1_finger_words_proto_init() {
	if File_api_finger_words_v1_finger_words_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_finger_words_v1_finger_words_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FingerWordsEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_finger_words_v1_finger_words_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FingerWordsEntryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_finger_words_v1_finger_words_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FingerWordsQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_finger_words_v1_finger_words_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FingerWordsQueryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_finger_words_v1_finger_words_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_finger_words_v1_finger_words_proto_goTypes,
		DependencyIndexes: file_api_finger_words_v1_finger_words_proto_depIdxs,
		MessageInfos:      file_api_finger_words_v1_finger_words_proto_msgTypes,
	}.Build()
	File_api_finger_words_v1_finger_words_proto = out.File
	file_api_finger_words_v1_finger_words_proto_rawDesc = nil
	file_api_finger_words_v1_finger_words_proto_goTypes = nil
	file_api_finger_words_v1_finger_words_proto_depIdxs = nil
}
