// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.23.3
// source: api/finger_words/v1/finger_words.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationFingerWordsFingerWordsEntry = "/api.finger_words.v1.FingerWords/FingerWordsEntry"
const OperationFingerWordsFingerWordsQuery = "/api.finger_words.v1.FingerWords/FingerWordsQuery"

type FingerWordsHTTPServer interface {
	FingerWordsEntry(context.Context, *FingerWordsEntryRequest) (*FingerWordsEntryReply, error)
	FingerWordsQuery(context.Context, *FingerWordsQueryRequest) (*FingerWordsQueryReply, error)
}

func RegisterFingerWordsHTTPServer(s *http.Server, srv FingerWordsHTTPServer) {
	r := s.Route("/")
	r.POST("/intelligence/v1/finger_words/entry", _FingerWords_FingerWordsEntry0_HTTP_Handler(srv))
	r.POST("/intelligence/v1/finger_words/query", _FingerWords_FingerWordsQuery0_HTTP_Handler(srv))
}

func _FingerWords_FingerWordsEntry0_HTTP_Handler(srv FingerWordsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FingerWordsEntryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFingerWordsFingerWordsEntry)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FingerWordsEntry(ctx, req.(*FingerWordsEntryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FingerWordsEntryReply)
		return ctx.Result(200, reply)
	}
}

func _FingerWords_FingerWordsQuery0_HTTP_Handler(srv FingerWordsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FingerWordsQueryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFingerWordsFingerWordsQuery)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FingerWordsQuery(ctx, req.(*FingerWordsQueryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FingerWordsQueryReply)
		return ctx.Result(200, reply)
	}
}

type FingerWordsHTTPClient interface {
	FingerWordsEntry(ctx context.Context, req *FingerWordsEntryRequest, opts ...http.CallOption) (rsp *FingerWordsEntryReply, err error)
	FingerWordsQuery(ctx context.Context, req *FingerWordsQueryRequest, opts ...http.CallOption) (rsp *FingerWordsQueryReply, err error)
}

type FingerWordsHTTPClientImpl struct {
	cc *http.Client
}

func NewFingerWordsHTTPClient(client *http.Client) FingerWordsHTTPClient {
	return &FingerWordsHTTPClientImpl{client}
}

func (c *FingerWordsHTTPClientImpl) FingerWordsEntry(ctx context.Context, in *FingerWordsEntryRequest, opts ...http.CallOption) (*FingerWordsEntryReply, error) {
	var out FingerWordsEntryReply
	pattern := "/intelligence/v1/finger_words/entry"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFingerWordsFingerWordsEntry))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *FingerWordsHTTPClientImpl) FingerWordsQuery(ctx context.Context, in *FingerWordsQueryRequest, opts ...http.CallOption) (*FingerWordsQueryReply, error) {
	var out FingerWordsQueryReply
	pattern := "/intelligence/v1/finger_words/query"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFingerWordsFingerWordsQuery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
