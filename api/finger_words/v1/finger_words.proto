syntax = "proto3";

package api.finger_words.v1;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "validate/validate.proto";

option go_package = "hw-paas-service/api/finger_words/v1;v1";
option java_multiple_files = true;
option java_package = "api.finger_words.v1";

service FingerWords {
	rpc FingerWordsEntry (FingerWordsEntryRequest) returns (FingerWordsEntryReply) {
		option (google.api.http) = {
			post: "/intelligence/v1/finger_words/entry"
			body: "*"
		};
	};
	rpc FingerWordsQuery (FingerWordsQueryRequest) returns (FingerWordsQueryReply) {
		option (google.api.http) = {
			post: "/intelligence/v1/finger_words/query"
			body: "*"
		};
	};
}

message FingerWordsEntryRequest {
	string client_trace_id = 1[(validate.rules).string.min_len = 1];
	int32 retry_status = 2;
	string text_bmp = 3[(validate.rules).string.min_len = 1];
	repeated int32 finger_pos_2ma = 4;
	repeated int32 text_pos_2ma = 5;
	string version = 6;
	string app_version = 7[(validate.rules).string.min_len = 1];
	string local_model_time = 8;
}
message FingerWordsEntryReply {
	string near_word = 1;
	repeated string line_words = 2;
	int32 near_index = 3;
	string ocr = 4;
	int32 retry_status = 5;
	google.protobuf.Struct result = 6;
	google.protobuf.Struct dispatch = 8;
}

message FingerWordsQueryRequest {
	string word = 1[(validate.rules).string.min_len = 1];
	string app_version = 2[(validate.rules).string.min_len = 1];
	string skill_name = 3[(validate.rules).string.min_len = 1];
}
message FingerWordsQueryReply {
	google.protobuf.Struct detail = 1;
	google.protobuf.Struct dispatch = 2;
}