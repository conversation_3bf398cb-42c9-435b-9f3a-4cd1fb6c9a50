// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.3
// source: api/finger_words/v1/finger_words.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FingerWords_FingerWordsEntry_FullMethodName = "/api.finger_words.v1.FingerWords/FingerWordsEntry"
	FingerWords_FingerWordsQuery_FullMethodName = "/api.finger_words.v1.FingerWords/FingerWordsQuery"
)

// FingerWordsClient is the client API for FingerWords service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FingerWordsClient interface {
	FingerWordsEntry(ctx context.Context, in *FingerWordsEntryRequest, opts ...grpc.CallOption) (*FingerWordsEntryReply, error)
	FingerWordsQuery(ctx context.Context, in *FingerWordsQueryRequest, opts ...grpc.CallOption) (*FingerWordsQueryReply, error)
}

type fingerWordsClient struct {
	cc grpc.ClientConnInterface
}

func NewFingerWordsClient(cc grpc.ClientConnInterface) FingerWordsClient {
	return &fingerWordsClient{cc}
}

func (c *fingerWordsClient) FingerWordsEntry(ctx context.Context, in *FingerWordsEntryRequest, opts ...grpc.CallOption) (*FingerWordsEntryReply, error) {
	out := new(FingerWordsEntryReply)
	err := c.cc.Invoke(ctx, FingerWords_FingerWordsEntry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fingerWordsClient) FingerWordsQuery(ctx context.Context, in *FingerWordsQueryRequest, opts ...grpc.CallOption) (*FingerWordsQueryReply, error) {
	out := new(FingerWordsQueryReply)
	err := c.cc.Invoke(ctx, FingerWords_FingerWordsQuery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FingerWordsServer is the server API for FingerWords service.
// All implementations must embed UnimplementedFingerWordsServer
// for forward compatibility
type FingerWordsServer interface {
	FingerWordsEntry(context.Context, *FingerWordsEntryRequest) (*FingerWordsEntryReply, error)
	FingerWordsQuery(context.Context, *FingerWordsQueryRequest) (*FingerWordsQueryReply, error)
	mustEmbedUnimplementedFingerWordsServer()
}

// UnimplementedFingerWordsServer must be embedded to have forward compatible implementations.
type UnimplementedFingerWordsServer struct {
}

func (UnimplementedFingerWordsServer) FingerWordsEntry(context.Context, *FingerWordsEntryRequest) (*FingerWordsEntryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FingerWordsEntry not implemented")
}
func (UnimplementedFingerWordsServer) FingerWordsQuery(context.Context, *FingerWordsQueryRequest) (*FingerWordsQueryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FingerWordsQuery not implemented")
}
func (UnimplementedFingerWordsServer) mustEmbedUnimplementedFingerWordsServer() {}

// UnsafeFingerWordsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FingerWordsServer will
// result in compilation errors.
type UnsafeFingerWordsServer interface {
	mustEmbedUnimplementedFingerWordsServer()
}

func RegisterFingerWordsServer(s grpc.ServiceRegistrar, srv FingerWordsServer) {
	s.RegisterService(&FingerWords_ServiceDesc, srv)
}

func _FingerWords_FingerWordsEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FingerWordsEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FingerWordsServer).FingerWordsEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FingerWords_FingerWordsEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FingerWordsServer).FingerWordsEntry(ctx, req.(*FingerWordsEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FingerWords_FingerWordsQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FingerWordsQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FingerWordsServer).FingerWordsQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FingerWords_FingerWordsQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FingerWordsServer).FingerWordsQuery(ctx, req.(*FingerWordsQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FingerWords_ServiceDesc is the grpc.ServiceDesc for FingerWords service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FingerWords_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.finger_words.v1.FingerWords",
	HandlerType: (*FingerWordsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FingerWordsEntry",
			Handler:    _FingerWords_FingerWordsEntry_Handler,
		},
		{
			MethodName: "FingerWordsQuery",
			Handler:    _FingerWords_FingerWordsQuery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/finger_words/v1/finger_words.proto",
}
