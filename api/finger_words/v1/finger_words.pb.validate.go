// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/finger_words/v1/finger_words.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FingerWordsEntryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FingerWordsEntryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FingerWordsEntryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FingerWordsEntryRequestMultiError, or nil if none found.
func (m *FingerWordsEntryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FingerWordsEntryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetClientTraceId()) < 1 {
		err := FingerWordsEntryRequestValidationError{
			field:  "ClientTraceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for RetryStatus

	if utf8.RuneCountInString(m.GetTextBmp()) < 1 {
		err := FingerWordsEntryRequestValidationError{
			field:  "TextBmp",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Version

	if utf8.RuneCountInString(m.GetAppVersion()) < 1 {
		err := FingerWordsEntryRequestValidationError{
			field:  "AppVersion",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for LocalModelTime

	if len(errors) > 0 {
		return FingerWordsEntryRequestMultiError(errors)
	}

	return nil
}

// FingerWordsEntryRequestMultiError is an error wrapping multiple validation
// errors returned by FingerWordsEntryRequest.ValidateAll() if the designated
// constraints aren't met.
type FingerWordsEntryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FingerWordsEntryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FingerWordsEntryRequestMultiError) AllErrors() []error { return m }

// FingerWordsEntryRequestValidationError is the validation error returned by
// FingerWordsEntryRequest.Validate if the designated constraints aren't met.
type FingerWordsEntryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FingerWordsEntryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FingerWordsEntryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FingerWordsEntryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FingerWordsEntryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FingerWordsEntryRequestValidationError) ErrorName() string {
	return "FingerWordsEntryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FingerWordsEntryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFingerWordsEntryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FingerWordsEntryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FingerWordsEntryRequestValidationError{}

// Validate checks the field values on FingerWordsEntryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FingerWordsEntryReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FingerWordsEntryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FingerWordsEntryReplyMultiError, or nil if none found.
func (m *FingerWordsEntryReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FingerWordsEntryReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NearWord

	// no validation rules for NearIndex

	// no validation rules for Ocr

	// no validation rules for RetryStatus

	if all {
		switch v := interface{}(m.GetResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FingerWordsEntryReplyValidationError{
					field:  "Result",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FingerWordsEntryReplyValidationError{
					field:  "Result",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FingerWordsEntryReplyValidationError{
				field:  "Result",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDispatch()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FingerWordsEntryReplyValidationError{
					field:  "Dispatch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FingerWordsEntryReplyValidationError{
					field:  "Dispatch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDispatch()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FingerWordsEntryReplyValidationError{
				field:  "Dispatch",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FingerWordsEntryReplyMultiError(errors)
	}

	return nil
}

// FingerWordsEntryReplyMultiError is an error wrapping multiple validation
// errors returned by FingerWordsEntryReply.ValidateAll() if the designated
// constraints aren't met.
type FingerWordsEntryReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FingerWordsEntryReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FingerWordsEntryReplyMultiError) AllErrors() []error { return m }

// FingerWordsEntryReplyValidationError is the validation error returned by
// FingerWordsEntryReply.Validate if the designated constraints aren't met.
type FingerWordsEntryReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FingerWordsEntryReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FingerWordsEntryReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FingerWordsEntryReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FingerWordsEntryReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FingerWordsEntryReplyValidationError) ErrorName() string {
	return "FingerWordsEntryReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FingerWordsEntryReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFingerWordsEntryReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FingerWordsEntryReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FingerWordsEntryReplyValidationError{}

// Validate checks the field values on FingerWordsQueryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FingerWordsQueryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FingerWordsQueryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FingerWordsQueryRequestMultiError, or nil if none found.
func (m *FingerWordsQueryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FingerWordsQueryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetWord()) < 1 {
		err := FingerWordsQueryRequestValidationError{
			field:  "Word",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAppVersion()) < 1 {
		err := FingerWordsQueryRequestValidationError{
			field:  "AppVersion",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSkillName()) < 1 {
		err := FingerWordsQueryRequestValidationError{
			field:  "SkillName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FingerWordsQueryRequestMultiError(errors)
	}

	return nil
}

// FingerWordsQueryRequestMultiError is an error wrapping multiple validation
// errors returned by FingerWordsQueryRequest.ValidateAll() if the designated
// constraints aren't met.
type FingerWordsQueryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FingerWordsQueryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FingerWordsQueryRequestMultiError) AllErrors() []error { return m }

// FingerWordsQueryRequestValidationError is the validation error returned by
// FingerWordsQueryRequest.Validate if the designated constraints aren't met.
type FingerWordsQueryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FingerWordsQueryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FingerWordsQueryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FingerWordsQueryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FingerWordsQueryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FingerWordsQueryRequestValidationError) ErrorName() string {
	return "FingerWordsQueryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FingerWordsQueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFingerWordsQueryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FingerWordsQueryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FingerWordsQueryRequestValidationError{}

// Validate checks the field values on FingerWordsQueryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FingerWordsQueryReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FingerWordsQueryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FingerWordsQueryReplyMultiError, or nil if none found.
func (m *FingerWordsQueryReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FingerWordsQueryReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FingerWordsQueryReplyValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FingerWordsQueryReplyValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FingerWordsQueryReplyValidationError{
				field:  "Detail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDispatch()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FingerWordsQueryReplyValidationError{
					field:  "Dispatch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FingerWordsQueryReplyValidationError{
					field:  "Dispatch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDispatch()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FingerWordsQueryReplyValidationError{
				field:  "Dispatch",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FingerWordsQueryReplyMultiError(errors)
	}

	return nil
}

// FingerWordsQueryReplyMultiError is an error wrapping multiple validation
// errors returned by FingerWordsQueryReply.ValidateAll() if the designated
// constraints aren't met.
type FingerWordsQueryReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FingerWordsQueryReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FingerWordsQueryReplyMultiError) AllErrors() []error { return m }

// FingerWordsQueryReplyValidationError is the validation error returned by
// FingerWordsQueryReply.Validate if the designated constraints aren't met.
type FingerWordsQueryReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FingerWordsQueryReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FingerWordsQueryReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FingerWordsQueryReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FingerWordsQueryReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FingerWordsQueryReplyValidationError) ErrorName() string {
	return "FingerWordsQueryReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FingerWordsQueryReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFingerWordsQueryReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FingerWordsQueryReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FingerWordsQueryReplyValidationError{}
