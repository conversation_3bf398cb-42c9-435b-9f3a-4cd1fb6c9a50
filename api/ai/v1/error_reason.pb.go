// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.3
// source: api/ai/v1/error_reason.proto

package v1

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	// 命名规范 服务名称_错误  error.code 尽量使用http.code来标识且符合code码含义, 需要备注什么错误，方便定位
	ErrorReason_HW_PAAS_APPID_ERROR         ErrorReason = 0
	ErrorReason_HW_PAAS_SIGN_ERROR          ErrorReason = 1
	ErrorReason_HW_PAAS_PARAM_ERROR         ErrorReason = 2
	ErrorReason_HW_PAAS_NOT_FOUND_ERROR     ErrorReason = 3  //资源未找到
	ErrorReason_HW_PAAS_UNEXCEPT_ERROR      ErrorReason = 4  //未知错误
	ErrorReason_HW_PAAS_THIRD_PART_ERROR    ErrorReason = 5  //第三方接口报错
	ErrorReason_HW_PAAS_CORRECT_ERROR       ErrorReason = 6  //口算批改接口错误
	ErrorReason_HW_PAAS_CORRECT_FUZZY_ERROR ErrorReason = 7  //口算批改-照片不清晰接口错误
	ErrorReason_HW_PAAS_FINGER_OCR_ERROR    ErrorReason = 8  //指尖查词OCR失败
	ErrorReason_HW_PAAS_QUESTION_IS_BAND    ErrorReason = 9  //题目已被绑定
	ErrorReason_HW_PAAS_DEFAULT_ERR         ErrorReason = 10 //默认业务错误
	ErrorReason_HW_PAAS_UNAUTHORIZED        ErrorReason = 11 //鉴权失败
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0:  "HW_PAAS_APPID_ERROR",
		1:  "HW_PAAS_SIGN_ERROR",
		2:  "HW_PAAS_PARAM_ERROR",
		3:  "HW_PAAS_NOT_FOUND_ERROR",
		4:  "HW_PAAS_UNEXCEPT_ERROR",
		5:  "HW_PAAS_THIRD_PART_ERROR",
		6:  "HW_PAAS_CORRECT_ERROR",
		7:  "HW_PAAS_CORRECT_FUZZY_ERROR",
		8:  "HW_PAAS_FINGER_OCR_ERROR",
		9:  "HW_PAAS_QUESTION_IS_BAND",
		10: "HW_PAAS_DEFAULT_ERR",
		11: "HW_PAAS_UNAUTHORIZED",
	}
	ErrorReason_value = map[string]int32{
		"HW_PAAS_APPID_ERROR":         0,
		"HW_PAAS_SIGN_ERROR":          1,
		"HW_PAAS_PARAM_ERROR":         2,
		"HW_PAAS_NOT_FOUND_ERROR":     3,
		"HW_PAAS_UNEXCEPT_ERROR":      4,
		"HW_PAAS_THIRD_PART_ERROR":    5,
		"HW_PAAS_CORRECT_ERROR":       6,
		"HW_PAAS_CORRECT_FUZZY_ERROR": 7,
		"HW_PAAS_FINGER_OCR_ERROR":    8,
		"HW_PAAS_QUESTION_IS_BAND":    9,
		"HW_PAAS_DEFAULT_ERR":         10,
		"HW_PAAS_UNAUTHORIZED":        11,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_ai_v1_error_reason_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_api_ai_v1_error_reason_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_api_ai_v1_error_reason_proto_rawDescGZIP(), []int{0}
}

var File_api_ai_v1_error_reason_proto protoreflect.FileDescriptor

var file_api_ai_v1_error_reason_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x61, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x13, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2a, 0xa7, 0x03, 0x0a, 0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x1d, 0x0a, 0x13, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x49,
	0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x00, 0x1a, 0x04, 0xa8, 0x45, 0x93, 0x03, 0x12,
	0x1c, 0x0a, 0x12, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x1a, 0x04, 0xa8, 0x45, 0x93, 0x03, 0x12, 0x1d, 0x0a,
	0x13, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x02, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x21, 0x0a, 0x17,
	0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12,
	0x20, 0x0a, 0x16, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53, 0x5f, 0x55, 0x4e, 0x45, 0x58, 0x43,
	0x45, 0x50, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x04, 0x1a, 0x04, 0xa8, 0x45, 0xc8,
	0x01, 0x12, 0x22, 0x0a, 0x18, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53, 0x5f, 0x54, 0x48, 0x49,
	0x52, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x05, 0x1a,
	0x04, 0xa8, 0x45, 0xc8, 0x01, 0x12, 0x1f, 0x0a, 0x15, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53,
	0x5f, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x06,
	0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x25, 0x0a, 0x1b, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41,
	0x53, 0x5f, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x46, 0x55, 0x5a, 0x5a, 0x59, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x07, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x22, 0x0a,
	0x18, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53, 0x5f, 0x46, 0x49, 0x4e, 0x47, 0x45, 0x52, 0x5f,
	0x4f, 0x43, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x08, 0x1a, 0x04, 0xa8, 0x45, 0xc8,
	0x01, 0x12, 0x22, 0x0a, 0x18, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53, 0x5f, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x53, 0x5f, 0x42, 0x41, 0x4e, 0x44, 0x10, 0x09, 0x1a,
	0x04, 0xa8, 0x45, 0xc8, 0x01, 0x12, 0x1d, 0x0a, 0x13, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53,
	0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x0a, 0x1a, 0x04,
	0xa8, 0x45, 0xc8, 0x01, 0x12, 0x1e, 0x0a, 0x14, 0x48, 0x57, 0x5f, 0x50, 0x41, 0x41, 0x53, 0x5f,
	0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x0b, 0x1a, 0x04,
	0xa8, 0x45, 0x91, 0x03, 0x1a, 0x04, 0xa0, 0x45, 0xf4, 0x03, 0x42, 0x27, 0x0a, 0x09, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x18, 0x68, 0x77, 0x2d, 0x70, 0x61,
	0x61, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_ai_v1_error_reason_proto_rawDescOnce sync.Once
	file_api_ai_v1_error_reason_proto_rawDescData = file_api_ai_v1_error_reason_proto_rawDesc
)

func file_api_ai_v1_error_reason_proto_rawDescGZIP() []byte {
	file_api_ai_v1_error_reason_proto_rawDescOnce.Do(func() {
		file_api_ai_v1_error_reason_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_ai_v1_error_reason_proto_rawDescData)
	})
	return file_api_ai_v1_error_reason_proto_rawDescData
}

var file_api_ai_v1_error_reason_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_ai_v1_error_reason_proto_goTypes = []interface{}{
	(ErrorReason)(0), // 0: api.paas.v1.ErrorReason
}
var file_api_ai_v1_error_reason_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_ai_v1_error_reason_proto_init() }
func file_api_ai_v1_error_reason_proto_init() {
	if File_api_ai_v1_error_reason_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_ai_v1_error_reason_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_ai_v1_error_reason_proto_goTypes,
		DependencyIndexes: file_api_ai_v1_error_reason_proto_depIdxs,
		EnumInfos:         file_api_ai_v1_error_reason_proto_enumTypes,
	}.Build()
	File_api_ai_v1_error_reason_proto = out.File
	file_api_ai_v1_error_reason_proto_rawDesc = nil
	file_api_ai_v1_error_reason_proto_goTypes = nil
	file_api_ai_v1_error_reason_proto_depIdxs = nil
}
