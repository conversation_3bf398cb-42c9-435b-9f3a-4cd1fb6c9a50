// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.3
// source: api/ai/v1/ai.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckEmailExistsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *CheckEmailExistsReq) Reset() {
	*x = CheckEmailExistsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckEmailExistsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEmailExistsReq) ProtoMessage() {}

func (x *CheckEmailExistsReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEmailExistsReq.ProtoReflect.Descriptor instead.
func (*CheckEmailExistsReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{0}
}

func (x *CheckEmailExistsReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type CheckEmailExistsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exists bool `protobuf:"varint,1,opt,name=exists,proto3" json:"exists,omitempty"` // 是否存在 0: 不存在 1: 存在
}

func (x *CheckEmailExistsResp) Reset() {
	*x = CheckEmailExistsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckEmailExistsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckEmailExistsResp) ProtoMessage() {}

func (x *CheckEmailExistsResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckEmailExistsResp.ProtoReflect.Descriptor instead.
func (*CheckEmailExistsResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{1}
}

func (x *CheckEmailExistsResp) GetExists() bool {
	if x != nil {
		return x.Exists
	}
	return false
}

type CheckLoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TalToken string `protobuf:"bytes,1,opt,name=tal_token,json=talToken,proto3" json:"tal_token,omitempty"`
}

func (x *CheckLoginReq) Reset() {
	*x = CheckLoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckLoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckLoginReq) ProtoMessage() {}

func (x *CheckLoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckLoginReq.ProtoReflect.Descriptor instead.
func (*CheckLoginReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{2}
}

func (x *CheckLoginReq) GetTalToken() string {
	if x != nil {
		return x.TalToken
	}
	return ""
}

type CheckLoginResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TalId    string `protobuf:"bytes,1,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`          // 好未来用户 ID
	TalToken string `protobuf:"bytes,2,opt,name=tal_token,json=talToken,proto3" json:"tal_token,omitempty"` // 用户登录标识
	Expired  int64  `protobuf:"varint,3,opt,name=expired,proto3" json:"expired,omitempty"`                  // 登陆标识更新时间
	Life     int64  `protobuf:"varint,4,opt,name=life,proto3" json:"life,omitempty"`                        // 登陆标识有效时间
}

func (x *CheckLoginResp) Reset() {
	*x = CheckLoginResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckLoginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckLoginResp) ProtoMessage() {}

func (x *CheckLoginResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckLoginResp.ProtoReflect.Descriptor instead.
func (*CheckLoginResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{3}
}

func (x *CheckLoginResp) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *CheckLoginResp) GetTalToken() string {
	if x != nil {
		return x.TalToken
	}
	return ""
}

func (x *CheckLoginResp) GetExpired() int64 {
	if x != nil {
		return x.Expired
	}
	return 0
}

func (x *CheckLoginResp) GetLife() int64 {
	if x != nil {
		return x.Life
	}
	return 0
}

type LoginOutReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TalToken string `protobuf:"bytes,1,opt,name=tal_token,json=talToken,proto3" json:"tal_token,omitempty"`
}

func (x *LoginOutReq) Reset() {
	*x = LoginOutReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginOutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginOutReq) ProtoMessage() {}

func (x *LoginOutReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginOutReq.ProtoReflect.Descriptor instead.
func (*LoginOutReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{4}
}

func (x *LoginOutReq) GetTalToken() string {
	if x != nil {
		return x.TalToken
	}
	return ""
}

type LoginOutResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *LoginOutResp) Reset() {
	*x = LoginOutResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginOutResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginOutResp) ProtoMessage() {}

func (x *LoginOutResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginOutResp.ProtoReflect.Descriptor instead.
func (*LoginOutResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{5}
}

func (x *LoginOutResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CodeLoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *CodeLoginReq) Reset() {
	*x = CodeLoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeLoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeLoginReq) ProtoMessage() {}

func (x *CodeLoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeLoginReq.ProtoReflect.Descriptor instead.
func (*CodeLoginReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{6}
}

func (x *CodeLoginReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type CodeLoginResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TalId    string `protobuf:"bytes,1,opt,name=tal_id,json=talId,proto3" json:"tal_id,omitempty"`          // 好未来用户 ID
	TalToken string `protobuf:"bytes,2,opt,name=tal_token,json=talToken,proto3" json:"tal_token,omitempty"` // 用户登录标识
	Expired  int64  `protobuf:"varint,3,opt,name=expired,proto3" json:"expired,omitempty"`                  // 登陆标识更新时间
	Life     int64  `protobuf:"varint,4,opt,name=life,proto3" json:"life,omitempty"`                        // 登陆标识有效时间
}

func (x *CodeLoginResp) Reset() {
	*x = CodeLoginResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeLoginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeLoginResp) ProtoMessage() {}

func (x *CodeLoginResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeLoginResp.ProtoReflect.Descriptor instead.
func (*CodeLoginResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{7}
}

func (x *CodeLoginResp) GetTalId() string {
	if x != nil {
		return x.TalId
	}
	return ""
}

func (x *CodeLoginResp) GetTalToken() string {
	if x != nil {
		return x.TalToken
	}
	return ""
}

func (x *CodeLoginResp) GetExpired() int64 {
	if x != nil {
		return x.Expired
	}
	return 0
}

func (x *CodeLoginResp) GetLife() int64 {
	if x != nil {
		return x.Life
	}
	return 0
}

type TaskDriveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	App string `protobuf:"bytes,1,opt,name=app,proto3" json:"app,omitempty"`
}

func (x *TaskDriveReq) Reset() {
	*x = TaskDriveReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskDriveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskDriveReq) ProtoMessage() {}

func (x *TaskDriveReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskDriveReq.ProtoReflect.Descriptor instead.
func (*TaskDriveReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{8}
}

func (x *TaskDriveReq) GetApp() string {
	if x != nil {
		return x.App
	}
	return ""
}

type TaskDriveResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TaskDriveResp) Reset() {
	*x = TaskDriveResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskDriveResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskDriveResp) ProtoMessage() {}

func (x *TaskDriveResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskDriveResp.ProtoReflect.Descriptor instead.
func (*TaskDriveResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{9}
}

type CommonConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommonConfigReq) Reset() {
	*x = CommonConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonConfigReq) ProtoMessage() {}

func (x *CommonConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonConfigReq.ProtoReflect.Descriptor instead.
func (*CommonConfigReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{10}
}

type CommonConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ranges    []*Range `protobuf:"bytes,1,rep,name=ranges,proto3" json:"ranges,omitempty"`
	RightLine int32    `protobuf:"varint,2,opt,name=right_line,json=rightLine,proto3" json:"right_line,omitempty"`
}

func (x *CommonConfigResp) Reset() {
	*x = CommonConfigResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonConfigResp) ProtoMessage() {}

func (x *CommonConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonConfigResp.ProtoReflect.Descriptor instead.
func (*CommonConfigResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{11}
}

func (x *CommonConfigResp) GetRanges() []*Range {
	if x != nil {
		return x.Ranges
	}
	return nil
}

func (x *CommonConfigResp) GetRightLine() int32 {
	if x != nil {
		return x.RightLine
	}
	return 0
}

type Range struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Word       string `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"`
	Min        int32  `protobuf:"varint,2,opt,name=min,proto3" json:"min,omitempty"`
	Max        int32  `protobuf:"varint,3,opt,name=max,proto3" json:"max,omitempty"`
	IncludeMin bool   `protobuf:"varint,4,opt,name=include_min,json=includeMin,proto3" json:"include_min,omitempty"`
	IncludeMax bool   `protobuf:"varint,5,opt,name=include_max,json=includeMax,proto3" json:"include_max,omitempty"`
}

func (x *Range) Reset() {
	*x = Range{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Range) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Range) ProtoMessage() {}

func (x *Range) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Range.ProtoReflect.Descriptor instead.
func (*Range) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{12}
}

func (x *Range) GetWord() string {
	if x != nil {
		return x.Word
	}
	return ""
}

func (x *Range) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *Range) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *Range) GetIncludeMin() bool {
	if x != nil {
		return x.IncludeMin
	}
	return false
}

func (x *Range) GetIncludeMax() bool {
	if x != nil {
		return x.IncludeMax
	}
	return false
}

type FeedbackTraceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraceId  string `protobuf:"bytes,1,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	Feedback string `protobuf:"bytes,2,opt,name=feedback,proto3" json:"feedback,omitempty"`
	Biz      int32  `protobuf:"varint,3,opt,name=biz,proto3" json:"biz,omitempty"` //1:口算批改;2:指尖查词;3:作业批改;4:指尖查词;5:语音查词;6:绘本指读
}

func (x *FeedbackTraceRequest) Reset() {
	*x = FeedbackTraceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedbackTraceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackTraceRequest) ProtoMessage() {}

func (x *FeedbackTraceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackTraceRequest.ProtoReflect.Descriptor instead.
func (*FeedbackTraceRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{13}
}

func (x *FeedbackTraceRequest) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *FeedbackTraceRequest) GetFeedback() string {
	if x != nil {
		return x.Feedback
	}
	return ""
}

func (x *FeedbackTraceRequest) GetBiz() int32 {
	if x != nil {
		return x.Biz
	}
	return 0
}

type FeedbackTraceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FeedbackTraceReply) Reset() {
	*x = FeedbackTraceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedbackTraceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackTraceReply) ProtoMessage() {}

func (x *FeedbackTraceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackTraceReply.ProtoReflect.Descriptor instead.
func (*FeedbackTraceReply) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{14}
}

type QueryCorrectRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl string `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
}

func (x *QueryCorrectRequest) Reset() {
	*x = QueryCorrectRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCorrectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCorrectRequest) ProtoMessage() {}

func (x *QueryCorrectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCorrectRequest.ProtoReflect.Descriptor instead.
func (*QueryCorrectRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{15}
}

func (x *QueryCorrectRequest) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{16}
}

type ListQuestionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	QType       int32  `protobuf:"varint,2,opt,name=q_type,json=qType,proto3" json:"q_type,omitempty"`
	Difficulty  int32  `protobuf:"varint,3,opt,name=difficulty,proto3" json:"difficulty,omitempty"`
	Status      int32  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	Grade       string `protobuf:"bytes,5,opt,name=grade,proto3" json:"grade,omitempty"`
	Subject     string `protobuf:"bytes,6,opt,name=subject,proto3" json:"subject,omitempty"`
	Knowledge   string `protobuf:"bytes,7,opt,name=knowledge,proto3" json:"knowledge,omitempty"`
	KnowledgeNo string `protobuf:"bytes,8,opt,name=knowledge_no,json=knowledgeNo,proto3" json:"knowledge_no,omitempty"`
	Content     string `protobuf:"bytes,9,opt,name=content,proto3" json:"content,omitempty"`
	Page        int32  `protobuf:"varint,10,opt,name=page,proto3" json:"page,omitempty"`
	PageSize    int32  `protobuf:"varint,11,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListQuestionsRequest) Reset() {
	*x = ListQuestionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListQuestionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQuestionsRequest) ProtoMessage() {}

func (x *ListQuestionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQuestionsRequest.ProtoReflect.Descriptor instead.
func (*ListQuestionsRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{17}
}

func (x *ListQuestionsRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListQuestionsRequest) GetQType() int32 {
	if x != nil {
		return x.QType
	}
	return 0
}

func (x *ListQuestionsRequest) GetDifficulty() int32 {
	if x != nil {
		return x.Difficulty
	}
	return 0
}

func (x *ListQuestionsRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListQuestionsRequest) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *ListQuestionsRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *ListQuestionsRequest) GetKnowledge() string {
	if x != nil {
		return x.Knowledge
	}
	return ""
}

func (x *ListQuestionsRequest) GetKnowledgeNo() string {
	if x != nil {
		return x.KnowledgeNo
	}
	return ""
}

func (x *ListQuestionsRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ListQuestionsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListQuestionsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListQuestionsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Questions []*Question `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	Total     int32       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListQuestionsReply) Reset() {
	*x = ListQuestionsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListQuestionsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQuestionsReply) ProtoMessage() {}

func (x *ListQuestionsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQuestionsReply.ProtoReflect.Descriptor instead.
func (*ListQuestionsReply) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{18}
}

func (x *ListQuestionsReply) GetQuestions() []*Question {
	if x != nil {
		return x.Questions
	}
	return nil
}

func (x *ListQuestionsReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetQuestionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetQuestionRequest) Reset() {
	*x = GetQuestionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuestionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionRequest) ProtoMessage() {}

func (x *GetQuestionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionRequest.ProtoReflect.Descriptor instead.
func (*GetQuestionRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{19}
}

func (x *GetQuestionRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetQuestionBatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetQuestionBatchRequest) Reset() {
	*x = GetQuestionBatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuestionBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionBatchRequest) ProtoMessage() {}

func (x *GetQuestionBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionBatchRequest.ProtoReflect.Descriptor instead.
func (*GetQuestionBatchRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{20}
}

func (x *GetQuestionBatchRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetQuestionBatchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Questions []*Question `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
}

func (x *GetQuestionBatchReply) Reset() {
	*x = GetQuestionBatchReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuestionBatchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionBatchReply) ProtoMessage() {}

func (x *GetQuestionBatchReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionBatchReply.ProtoReflect.Descriptor instead.
func (*GetQuestionBatchReply) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{21}
}

func (x *GetQuestionBatchReply) GetQuestions() []*Question {
	if x != nil {
		return x.Questions
	}
	return nil
}

type UpsertQuestionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	QType       int32  `protobuf:"varint,2,opt,name=q_type,json=qType,proto3" json:"q_type,omitempty"`
	Grade       string `protobuf:"bytes,3,opt,name=grade,proto3" json:"grade,omitempty"`
	Subject     string `protobuf:"bytes,4,opt,name=subject,proto3" json:"subject,omitempty"`
	Difficulty  int32  `protobuf:"varint,5,opt,name=difficulty,proto3" json:"difficulty,omitempty"`
	Question    string `protobuf:"bytes,6,opt,name=question,proto3" json:"question,omitempty"`
	Answer      string `protobuf:"bytes,7,opt,name=answer,proto3" json:"answer,omitempty"`
	Solution    string `protobuf:"bytes,8,opt,name=solution,proto3" json:"solution,omitempty"`
	Knowledge   string `protobuf:"bytes,9,opt,name=knowledge,proto3" json:"knowledge,omitempty"`
	KnowledgeNo string `protobuf:"bytes,10,opt,name=knowledge_no,json=knowledgeNo,proto3" json:"knowledge_no,omitempty"`
	Note        string `protobuf:"bytes,11,opt,name=note,proto3" json:"note,omitempty"`
	Status      int32  `protobuf:"varint,12,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpsertQuestionRequest) Reset() {
	*x = UpsertQuestionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertQuestionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertQuestionRequest) ProtoMessage() {}

func (x *UpsertQuestionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertQuestionRequest.ProtoReflect.Descriptor instead.
func (*UpsertQuestionRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{22}
}

func (x *UpsertQuestionRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpsertQuestionRequest) GetQType() int32 {
	if x != nil {
		return x.QType
	}
	return 0
}

func (x *UpsertQuestionRequest) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *UpsertQuestionRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *UpsertQuestionRequest) GetDifficulty() int32 {
	if x != nil {
		return x.Difficulty
	}
	return 0
}

func (x *UpsertQuestionRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *UpsertQuestionRequest) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *UpsertQuestionRequest) GetSolution() string {
	if x != nil {
		return x.Solution
	}
	return ""
}

func (x *UpsertQuestionRequest) GetKnowledge() string {
	if x != nil {
		return x.Knowledge
	}
	return ""
}

func (x *UpsertQuestionRequest) GetKnowledgeNo() string {
	if x != nil {
		return x.KnowledgeNo
	}
	return ""
}

func (x *UpsertQuestionRequest) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *UpsertQuestionRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type UpdateQuestionWithKeyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Fields map[string]string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateQuestionWithKeyRequest) Reset() {
	*x = UpdateQuestionWithKeyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateQuestionWithKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateQuestionWithKeyRequest) ProtoMessage() {}

func (x *UpdateQuestionWithKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateQuestionWithKeyRequest.ProtoReflect.Descriptor instead.
func (*UpdateQuestionWithKeyRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateQuestionWithKeyRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateQuestionWithKeyRequest) GetFields() map[string]string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type Question struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	QType       int32  `protobuf:"varint,2,opt,name=q_type,json=qType,proto3" json:"q_type,omitempty"`
	Grade       string `protobuf:"bytes,3,opt,name=grade,proto3" json:"grade,omitempty"`
	Subject     string `protobuf:"bytes,4,opt,name=subject,proto3" json:"subject,omitempty"`
	Difficulty  int32  `protobuf:"varint,5,opt,name=difficulty,proto3" json:"difficulty,omitempty"`
	Question    string `protobuf:"bytes,6,opt,name=question,proto3" json:"question,omitempty"`
	Answer      string `protobuf:"bytes,7,opt,name=answer,proto3" json:"answer,omitempty"`
	Solution    string `protobuf:"bytes,8,opt,name=solution,proto3" json:"solution,omitempty"`
	Knowledge   string `protobuf:"bytes,9,opt,name=knowledge,proto3" json:"knowledge,omitempty"`
	KnowledgeNo string `protobuf:"bytes,10,opt,name=knowledge_no,json=knowledgeNo,proto3" json:"knowledge_no,omitempty"`
	Note        string `protobuf:"bytes,11,opt,name=note,proto3" json:"note,omitempty"`
	Status      int32  `protobuf:"varint,12,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *Question) Reset() {
	*x = Question{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Question) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Question) ProtoMessage() {}

func (x *Question) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Question.ProtoReflect.Descriptor instead.
func (*Question) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{24}
}

func (x *Question) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Question) GetQType() int32 {
	if x != nil {
		return x.QType
	}
	return 0
}

func (x *Question) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *Question) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *Question) GetDifficulty() int32 {
	if x != nil {
		return x.Difficulty
	}
	return 0
}

func (x *Question) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *Question) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *Question) GetSolution() string {
	if x != nil {
		return x.Solution
	}
	return ""
}

func (x *Question) GetKnowledge() string {
	if x != nil {
		return x.Knowledge
	}
	return ""
}

func (x *Question) GetKnowledgeNo() string {
	if x != nil {
		return x.KnowledgeNo
	}
	return ""
}

func (x *Question) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *Question) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type GetQuestionStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids string `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids,omitempty"`
}

func (x *GetQuestionStatusRequest) Reset() {
	*x = GetQuestionStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuestionStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionStatusRequest) ProtoMessage() {}

func (x *GetQuestionStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionStatusRequest.ProtoReflect.Descriptor instead.
func (*GetQuestionStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{25}
}

func (x *GetQuestionStatusRequest) GetIds() string {
	if x != nil {
		return x.Ids
	}
	return ""
}

type GetQuestionStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Questions []*GetQuestionStatusReply_QuestionStatus `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
}

func (x *GetQuestionStatusReply) Reset() {
	*x = GetQuestionStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuestionStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionStatusReply) ProtoMessage() {}

func (x *GetQuestionStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionStatusReply.ProtoReflect.Descriptor instead.
func (*GetQuestionStatusReply) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{26}
}

func (x *GetQuestionStatusReply) GetQuestions() []*GetQuestionStatusReply_QuestionStatus {
	if x != nil {
		return x.Questions
	}
	return nil
}

type UploadFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	File   string `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
	Suffix string `protobuf:"bytes,2,opt,name=suffix,proto3" json:"suffix,omitempty"`
}

func (x *UploadFileRequest) Reset() {
	*x = UploadFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileRequest) ProtoMessage() {}

func (x *UploadFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileRequest.ProtoReflect.Descriptor instead.
func (*UploadFileRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{27}
}

func (x *UploadFileRequest) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *UploadFileRequest) GetSuffix() string {
	if x != nil {
		return x.Suffix
	}
	return ""
}

type UploadFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *UploadFileReply) Reset() {
	*x = UploadFileReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileReply) ProtoMessage() {}

func (x *UploadFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileReply.ProtoReflect.Descriptor instead.
func (*UploadFileReply) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{28}
}

func (x *UploadFileReply) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type CreateQuestionImportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileUrl  string `protobuf:"bytes,2,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	Subject  string `protobuf:"bytes,3,opt,name=subject,proto3" json:"subject,omitempty"`
}

func (x *CreateQuestionImportRequest) Reset() {
	*x = CreateQuestionImportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateQuestionImportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateQuestionImportRequest) ProtoMessage() {}

func (x *CreateQuestionImportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateQuestionImportRequest.ProtoReflect.Descriptor instead.
func (*CreateQuestionImportRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{29}
}

func (x *CreateQuestionImportRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *CreateQuestionImportRequest) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

func (x *CreateQuestionImportRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

type ListQuestionImportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListQuestionImportRequest) Reset() {
	*x = ListQuestionImportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListQuestionImportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQuestionImportRequest) ProtoMessage() {}

func (x *ListQuestionImportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQuestionImportRequest.ProtoReflect.Descriptor instead.
func (*ListQuestionImportRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{30}
}

func (x *ListQuestionImportRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListQuestionImportRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type QuestionImportData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FileName   string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`        // 文件名
	FileUrl    string `protobuf:"bytes,3,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`           // 文件地址
	Subject    string `protobuf:"bytes,4,opt,name=subject,proto3" json:"subject,omitempty"`                          // 学科
	ImportTime string `protobuf:"bytes,5,opt,name=import_time,json=importTime,proto3" json:"import_time,omitempty"`  // 导入时间
	Status     int32  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`                           // 1创建中 2导入失败 3待确认 4已完成 5已取消
	NumSuccess int32  `protobuf:"varint,7,opt,name=num_success,json=numSuccess,proto3" json:"num_success,omitempty"` // 成功导入的题目数量
	NumError   int32  `protobuf:"varint,8,opt,name=num_error,json=numError,proto3" json:"num_error,omitempty"`       // 导入失败的题目数量
	NumRepeat  int32  `protobuf:"varint,9,opt,name=num_repeat,json=numRepeat,proto3" json:"num_repeat,omitempty"`    // 重复的题目数量
}

func (x *QuestionImportData) Reset() {
	*x = QuestionImportData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionImportData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionImportData) ProtoMessage() {}

func (x *QuestionImportData) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionImportData.ProtoReflect.Descriptor instead.
func (*QuestionImportData) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{31}
}

func (x *QuestionImportData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *QuestionImportData) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *QuestionImportData) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

func (x *QuestionImportData) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *QuestionImportData) GetImportTime() string {
	if x != nil {
		return x.ImportTime
	}
	return ""
}

func (x *QuestionImportData) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *QuestionImportData) GetNumSuccess() int32 {
	if x != nil {
		return x.NumSuccess
	}
	return 0
}

func (x *QuestionImportData) GetNumError() int32 {
	if x != nil {
		return x.NumError
	}
	return 0
}

func (x *QuestionImportData) GetNumRepeat() int32 {
	if x != nil {
		return x.NumRepeat
	}
	return 0
}

type ListQuestionImportReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*QuestionImportData `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ListQuestionImportReply) Reset() {
	*x = ListQuestionImportReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListQuestionImportReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQuestionImportReply) ProtoMessage() {}

func (x *ListQuestionImportReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQuestionImportReply.ProtoReflect.Descriptor instead.
func (*ListQuestionImportReply) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{32}
}

func (x *ListQuestionImportReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListQuestionImportReply) GetList() []*QuestionImportData {
	if x != nil {
		return x.List
	}
	return nil
}

type QuestionImportActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Action string `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *QuestionImportActionRequest) Reset() {
	*x = QuestionImportActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionImportActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionImportActionRequest) ProtoMessage() {}

func (x *QuestionImportActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionImportActionRequest.ProtoReflect.Descriptor instead.
func (*QuestionImportActionRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{33}
}

func (x *QuestionImportActionRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *QuestionImportActionRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type GetQuestionImportDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Page     int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GetQuestionImportDetailRequest) Reset() {
	*x = GetQuestionImportDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuestionImportDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionImportDetailRequest) ProtoMessage() {}

func (x *GetQuestionImportDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionImportDetailRequest.ProtoReflect.Descriptor instead.
func (*GetQuestionImportDetailRequest) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{34}
}

func (x *GetQuestionImportDetailRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetQuestionImportDetailRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetQuestionImportDetailRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ImportErrorReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionId  int32  `protobuf:"varint,1,opt,name=question_id,json=questionId,proto3" json:"question_id,omitempty"`   // 题目ID
	ErrorReason string `protobuf:"bytes,2,opt,name=error_reason,json=errorReason,proto3" json:"error_reason,omitempty"` // 错误原因
}

func (x *ImportErrorReason) Reset() {
	*x = ImportErrorReason{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportErrorReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportErrorReason) ProtoMessage() {}

func (x *ImportErrorReason) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportErrorReason.ProtoReflect.Descriptor instead.
func (*ImportErrorReason) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{35}
}

func (x *ImportErrorReason) GetQuestionId() int32 {
	if x != nil {
		return x.QuestionId
	}
	return 0
}

func (x *ImportErrorReason) GetErrorReason() string {
	if x != nil {
		return x.ErrorReason
	}
	return ""
}

type GetQuestionImportDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           int32                `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`                                                      // 1创建中 2导入失败 3待确认 4已完成 5已取消
	FailedReason     string               `protobuf:"bytes,2,opt,name=failed_reason,json=failedReason,proto3" json:"failed_reason,omitempty"`                       // 导入失败原因
	TotalError       int32                `protobuf:"varint,3,opt,name=total_error,json=totalError,proto3" json:"total_error,omitempty"`                            // 导入失败的题目数量
	RepeatQuestionId []int32              `protobuf:"varint,4,rep,packed,name=repeat_question_id,json=repeatQuestionId,proto3" json:"repeat_question_id,omitempty"` // 重复的题目ID列表
	ErrorReasons     []*ImportErrorReason `protobuf:"bytes,5,rep,name=error_reasons,json=errorReasons,proto3" json:"error_reasons,omitempty"`                       // 导入失败的题目列表
}

func (x *GetQuestionImportDetailReply) Reset() {
	*x = GetQuestionImportDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuestionImportDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionImportDetailReply) ProtoMessage() {}

func (x *GetQuestionImportDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionImportDetailReply.ProtoReflect.Descriptor instead.
func (*GetQuestionImportDetailReply) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{36}
}

func (x *GetQuestionImportDetailReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetQuestionImportDetailReply) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

func (x *GetQuestionImportDetailReply) GetTotalError() int32 {
	if x != nil {
		return x.TotalError
	}
	return 0
}

func (x *GetQuestionImportDetailReply) GetRepeatQuestionId() []int32 {
	if x != nil {
		return x.RepeatQuestionId
	}
	return nil
}

func (x *GetQuestionImportDetailReply) GetErrorReasons() []*ImportErrorReason {
	if x != nil {
		return x.ErrorReasons
	}
	return nil
}

type BlogListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Category string `protobuf:"bytes,3,opt,name=category,proto3" json:"category,omitempty"`
}

func (x *BlogListReq) Reset() {
	*x = BlogListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlogListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlogListReq) ProtoMessage() {}

func (x *BlogListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlogListReq.ProtoReflect.Descriptor instead.
func (*BlogListReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{37}
}

func (x *BlogListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *BlogListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *BlogListReq) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

type BlogListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*BlogArticle `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total int32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *BlogListResp) Reset() {
	*x = BlogListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlogListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlogListResp) ProtoMessage() {}

func (x *BlogListResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlogListResp.ProtoReflect.Descriptor instead.
func (*BlogListResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{38}
}

func (x *BlogListResp) GetList() []*BlogArticle {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *BlogListResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type BlogDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Id   int32  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *BlogDetailReq) Reset() {
	*x = BlogDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlogDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlogDetailReq) ProtoMessage() {}

func (x *BlogDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlogDetailReq.ProtoReflect.Descriptor instead.
func (*BlogDetailReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{39}
}

func (x *BlogDetailReq) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *BlogDetailReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type BlogDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Article *BlogArticle `protobuf:"bytes,1,opt,name=article,proto3" json:"article,omitempty"`
}

func (x *BlogDetailResp) Reset() {
	*x = BlogDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlogDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlogDetailResp) ProtoMessage() {}

func (x *BlogDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlogDetailResp.ProtoReflect.Descriptor instead.
func (*BlogDetailResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{40}
}

func (x *BlogDetailResp) GetArticle() *BlogArticle {
	if x != nil {
		return x.Article
	}
	return nil
}

type BlogArticle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Path            string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	Category        string `protobuf:"bytes,3,opt,name=category,proto3" json:"category,omitempty"`
	ArticleTitle    string `protobuf:"bytes,4,opt,name=article_title,json=articleTitle,proto3" json:"article_title,omitempty"`
	ShortContent    string `protobuf:"bytes,5,opt,name=short_content,json=shortContent,proto3" json:"short_content,omitempty"`
	CoverImg        string `protobuf:"bytes,6,opt,name=cover_img,json=coverImg,proto3" json:"cover_img,omitempty"`
	AuthorAvatar    string `protobuf:"bytes,7,opt,name=author_avatar,json=authorAvatar,proto3" json:"author_avatar,omitempty"`
	AuthorName      string `protobuf:"bytes,8,opt,name=author_name,json=authorName,proto3" json:"author_name,omitempty"`
	ArticleContent  string `protobuf:"bytes,9,opt,name=article_content,json=articleContent,proto3" json:"article_content,omitempty"`
	PageTitle       string `protobuf:"bytes,10,opt,name=page_title,json=pageTitle,proto3" json:"page_title,omitempty"`
	MetaKeywords    string `protobuf:"bytes,11,opt,name=meta_keywords,json=metaKeywords,proto3" json:"meta_keywords,omitempty"`
	MetaDescription string `protobuf:"bytes,12,opt,name=meta_description,json=metaDescription,proto3" json:"meta_description,omitempty"`
	CreatedAt       string `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       string `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *BlogArticle) Reset() {
	*x = BlogArticle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlogArticle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlogArticle) ProtoMessage() {}

func (x *BlogArticle) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlogArticle.ProtoReflect.Descriptor instead.
func (*BlogArticle) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{41}
}

func (x *BlogArticle) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BlogArticle) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *BlogArticle) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *BlogArticle) GetArticleTitle() string {
	if x != nil {
		return x.ArticleTitle
	}
	return ""
}

func (x *BlogArticle) GetShortContent() string {
	if x != nil {
		return x.ShortContent
	}
	return ""
}

func (x *BlogArticle) GetCoverImg() string {
	if x != nil {
		return x.CoverImg
	}
	return ""
}

func (x *BlogArticle) GetAuthorAvatar() string {
	if x != nil {
		return x.AuthorAvatar
	}
	return ""
}

func (x *BlogArticle) GetAuthorName() string {
	if x != nil {
		return x.AuthorName
	}
	return ""
}

func (x *BlogArticle) GetArticleContent() string {
	if x != nil {
		return x.ArticleContent
	}
	return ""
}

func (x *BlogArticle) GetPageTitle() string {
	if x != nil {
		return x.PageTitle
	}
	return ""
}

func (x *BlogArticle) GetMetaKeywords() string {
	if x != nil {
		return x.MetaKeywords
	}
	return ""
}

func (x *BlogArticle) GetMetaDescription() string {
	if x != nil {
		return x.MetaDescription
	}
	return ""
}

func (x *BlogArticle) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *BlogArticle) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type BlogFeedbackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FeedbackType string `protobuf:"bytes,2,opt,name=feedback_type,json=feedbackType,proto3" json:"feedback_type,omitempty"`
}

func (x *BlogFeedbackReq) Reset() {
	*x = BlogFeedbackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlogFeedbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlogFeedbackReq) ProtoMessage() {}

func (x *BlogFeedbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlogFeedbackReq.ProtoReflect.Descriptor instead.
func (*BlogFeedbackReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{42}
}

func (x *BlogFeedbackReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BlogFeedbackReq) GetFeedbackType() string {
	if x != nil {
		return x.FeedbackType
	}
	return ""
}

type BlogCategoryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BlogCategoryReq) Reset() {
	*x = BlogCategoryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlogCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlogCategoryReq) ProtoMessage() {}

func (x *BlogCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlogCategoryReq.ProtoReflect.Descriptor instead.
func (*BlogCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{43}
}

type BlogCategoryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []string `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *BlogCategoryResp) Reset() {
	*x = BlogCategoryResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlogCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlogCategoryResp) ProtoMessage() {}

func (x *BlogCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlogCategoryResp.ProtoReflect.Descriptor instead.
func (*BlogCategoryResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{44}
}

func (x *BlogCategoryResp) GetList() []string {
	if x != nil {
		return x.List
	}
	return nil
}

type EduTermInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Term        string   `protobuf:"bytes,2,opt,name=term,proto3" json:"term,omitempty"`
	Knowledge_1 string   `protobuf:"bytes,3,opt,name=knowledge_1,json=knowledge1,proto3" json:"knowledge_1,omitempty"`
	Knowledge_2 string   `protobuf:"bytes,4,opt,name=knowledge_2,json=knowledge2,proto3" json:"knowledge_2,omitempty"`
	Title       string   `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Tag         []string `protobuf:"bytes,6,rep,name=tag,proto3" json:"tag,omitempty"`
	Path        string   `protobuf:"bytes,7,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *EduTermInfo) Reset() {
	*x = EduTermInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduTermInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduTermInfo) ProtoMessage() {}

func (x *EduTermInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduTermInfo.ProtoReflect.Descriptor instead.
func (*EduTermInfo) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{45}
}

func (x *EduTermInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EduTermInfo) GetTerm() string {
	if x != nil {
		return x.Term
	}
	return ""
}

func (x *EduTermInfo) GetKnowledge_1() string {
	if x != nil {
		return x.Knowledge_1
	}
	return ""
}

func (x *EduTermInfo) GetKnowledge_2() string {
	if x != nil {
		return x.Knowledge_2
	}
	return ""
}

func (x *EduTermInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *EduTermInfo) GetTag() []string {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *EduTermInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type EduTermListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject string `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
}

func (x *EduTermListReq) Reset() {
	*x = EduTermListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduTermListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduTermListReq) ProtoMessage() {}

func (x *EduTermListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduTermListReq.ProtoReflect.Descriptor instead.
func (*EduTermListReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{46}
}

func (x *EduTermListReq) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

type EduTermListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32          `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*EduTermInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *EduTermListResp) Reset() {
	*x = EduTermListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduTermListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduTermListResp) ProtoMessage() {}

func (x *EduTermListResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduTermListResp.ProtoReflect.Descriptor instead.
func (*EduTermListResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{47}
}

func (x *EduTermListResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *EduTermListResp) GetList() []*EduTermInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type EduTermDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *EduTermDetailReq) Reset() {
	*x = EduTermDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduTermDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduTermDetailReq) ProtoMessage() {}

func (x *EduTermDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduTermDetailReq.ProtoReflect.Descriptor instead.
func (*EduTermDetailReq) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{48}
}

func (x *EduTermDetailReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EduTermDetailReq) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type EduTermBlock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleType   string `protobuf:"bytes,1,opt,name=module_type,json=moduleType,proto3" json:"module_type,omitempty"`
	ModuleIndex  int32  `protobuf:"varint,2,opt,name=module_index,json=moduleIndex,proto3" json:"module_index,omitempty"`
	BlockType    string `protobuf:"bytes,3,opt,name=block_type,json=blockType,proto3" json:"block_type,omitempty"`
	BlockOrder   int32  `protobuf:"varint,4,opt,name=block_order,json=blockOrder,proto3" json:"block_order,omitempty"`
	BlockContent string `protobuf:"bytes,5,opt,name=block_content,json=blockContent,proto3" json:"block_content,omitempty"`
}

func (x *EduTermBlock) Reset() {
	*x = EduTermBlock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduTermBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduTermBlock) ProtoMessage() {}

func (x *EduTermBlock) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduTermBlock.ProtoReflect.Descriptor instead.
func (*EduTermBlock) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{49}
}

func (x *EduTermBlock) GetModuleType() string {
	if x != nil {
		return x.ModuleType
	}
	return ""
}

func (x *EduTermBlock) GetModuleIndex() int32 {
	if x != nil {
		return x.ModuleIndex
	}
	return 0
}

func (x *EduTermBlock) GetBlockType() string {
	if x != nil {
		return x.BlockType
	}
	return ""
}

func (x *EduTermBlock) GetBlockOrder() int32 {
	if x != nil {
		return x.BlockOrder
	}
	return 0
}

func (x *EduTermBlock) GetBlockContent() string {
	if x != nil {
		return x.BlockContent
	}
	return ""
}

type EduTermDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int32           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Term            string          `protobuf:"bytes,2,opt,name=term,proto3" json:"term,omitempty"`
	Knowledge_1     string          `protobuf:"bytes,3,opt,name=knowledge_1,json=knowledge1,proto3" json:"knowledge_1,omitempty"`
	Knowledge_2     string          `protobuf:"bytes,4,opt,name=knowledge_2,json=knowledge2,proto3" json:"knowledge_2,omitempty"`
	Title           string          `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Tag             []string        `protobuf:"bytes,6,rep,name=tag,proto3" json:"tag,omitempty"`
	MetaDescription string          `protobuf:"bytes,7,opt,name=meta_description,json=metaDescription,proto3" json:"meta_description,omitempty"`
	BlockList       []*EduTermBlock `protobuf:"bytes,8,rep,name=block_list,json=blockList,proto3" json:"block_list,omitempty"`
	CreatedAt       string          `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       string          `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *EduTermDetailResp) Reset() {
	*x = EduTermDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduTermDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduTermDetailResp) ProtoMessage() {}

func (x *EduTermDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduTermDetailResp.ProtoReflect.Descriptor instead.
func (*EduTermDetailResp) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{50}
}

func (x *EduTermDetailResp) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EduTermDetailResp) GetTerm() string {
	if x != nil {
		return x.Term
	}
	return ""
}

func (x *EduTermDetailResp) GetKnowledge_1() string {
	if x != nil {
		return x.Knowledge_1
	}
	return ""
}

func (x *EduTermDetailResp) GetKnowledge_2() string {
	if x != nil {
		return x.Knowledge_2
	}
	return ""
}

func (x *EduTermDetailResp) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *EduTermDetailResp) GetTag() []string {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *EduTermDetailResp) GetMetaDescription() string {
	if x != nil {
		return x.MetaDescription
	}
	return ""
}

func (x *EduTermDetailResp) GetBlockList() []*EduTermBlock {
	if x != nil {
		return x.BlockList
	}
	return nil
}

func (x *EduTermDetailResp) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *EduTermDetailResp) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type GetQuestionStatusReply_QuestionStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Status int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *GetQuestionStatusReply_QuestionStatus) Reset() {
	*x = GetQuestionStatusReply_QuestionStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_ai_v1_ai_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuestionStatusReply_QuestionStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuestionStatusReply_QuestionStatus) ProtoMessage() {}

func (x *GetQuestionStatusReply_QuestionStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_ai_v1_ai_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuestionStatusReply_QuestionStatus.ProtoReflect.Descriptor instead.
func (*GetQuestionStatusReply_QuestionStatus) Descriptor() ([]byte, []int) {
	return file_api_ai_v1_ai_proto_rawDescGZIP(), []int{26, 0}
}

func (x *GetQuestionStatusReply_QuestionStatus) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetQuestionStatusReply_QuestionStatus) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

var File_api_ai_v1_ai_proto protoreflect.FileDescriptor

var file_api_ai_v1_ai_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x34, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x2e, 0x0a, 0x14, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x22, 0x35, 0x0a, 0x0d, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x74,
	0x61, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x72, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61,
	0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x66, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x6c, 0x69, 0x66, 0x65, 0x22, 0x33, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4f, 0x75,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x09, 0x74, 0x61, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x08, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x20, 0x0a, 0x0c, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x2b, 0x0a, 0x0c,
	0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x71, 0x0a, 0x0d, 0x43, 0x6f, 0x64,
	0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x6c, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x6c, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x66, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x6c, 0x69, 0x66, 0x65, 0x22, 0x29, 0x0a, 0x0c,
	0x54, 0x61, 0x73, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x03,
	0x61, 0x70, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x03, 0x61, 0x70, 0x70, 0x22, 0x0f, 0x0a, 0x0d, 0x54, 0x61, 0x73, 0x6b, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x11, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x22, 0x5b, 0x0a, 0x10, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x28, 0x0a, 0x06, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x06, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x05, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4d, 0x69, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6d, 0x61, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4d, 0x61, 0x78, 0x22, 0x5f, 0x0a, 0x14,
	0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x62,
	0x69, 0x7a, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x62, 0x69, 0x7a, 0x22, 0x14, 0x0a,
	0x12, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x47, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x09, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0xfa,
	0x42, 0x10, 0x72, 0x0e, 0x32, 0x0c, 0x5e, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3f, 0x3a, 0x2f, 0x2f,
	0x2e, 0x2a, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x07, 0x0a, 0x05,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb1, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x71, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x71, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x66, 0x66, 0x69, 0x63, 0x75,
	0x6c, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x69, 0x66, 0x66, 0x69,
	0x63, 0x75, 0x6c, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4e, 0x6f, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x5d, 0x0a, 0x12, 0x4c, 0x69, 0x73,
	0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x31, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x24, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x29,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4a, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x31, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xcb, 0x02, 0x0a, 0x15, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x71, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x71, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x66, 0x66, 0x69, 0x63,
	0x75, 0x6c, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x69, 0x66, 0x66,
	0x69, 0x63, 0x75, 0x6c, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f,
	0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f,
	0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xb6, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x4b, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x57,
	0x69, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbe, 0x02, 0x0a,
	0x08, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x71, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x71, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x66, 0x66, 0x69, 0x63, 0x75, 0x6c, 0x74, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x69, 0x66, 0x66, 0x69, 0x63, 0x75, 0x6c, 0x74, 0x79,
	0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4e,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x6f, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x2c, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4e, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x38, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x51, 0x0a, 0x11, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x66, 0x69,
	0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x06, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x75, 0x66,
	0x66, 0x69, 0x78, 0x22, 0x23, 0x0a, 0x0f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x96, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x13, 0xfa, 0x42, 0x10, 0x72, 0x0e, 0x32, 0x0c, 0x5e, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3f,
	0x3a, 0x2f, 0x2f, 0x2e, 0x2a, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x21,
	0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x22, 0x4c, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22,
	0x8c, 0x02, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6e, 0x75, 0x6d, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x75, 0x6d, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6e, 0x75, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x1d, 0x0a, 0x0a, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x22, 0x62,
	0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0x57, 0x0a, 0x1b, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6a, 0x0a, 0x1e, 0x47,
	0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x57, 0x0a, 0x11, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x22, 0xed, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x2c, 0x0a, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x10, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x41, 0x0a,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73,
	0x22, 0x5a, 0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x50, 0x0a, 0x0c,
	0x42, 0x6c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2a, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x67, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x33,
	0x0a, 0x0d, 0x42, 0x6c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x42, 0x0a, 0x0e, 0x42, 0x6c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x07, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x67, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x07,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x22, 0xd0, 0x03, 0x0a, 0x0b, 0x42, 0x6c, 0x6f, 0x67,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x65, 0x74, 0x61, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x61, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x29, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x74,
	0x61, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x46, 0x0a, 0x0f, 0x42, 0x6c,
	0x6f, 0x67, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x11, 0x0a, 0x0f, 0x42, 0x6c, 0x6f, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x22, 0x26, 0x0a, 0x10, 0x42, 0x6c, 0x6f, 0x67, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xaf, 0x01,
	0x0a, 0x0b, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x65, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x72,
	0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x31,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f,
	0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22,
	0x2a, 0x0a, 0x0e, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x22, 0x53, 0x0a, 0x0f, 0x45,
	0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x36, 0x0a, 0x10, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0xb7, 0x01, 0x0a, 0x0c, 0x45, 0x64, 0x75,
	0x54, 0x65, 0x72, 0x6d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1d, 0x0a,
	0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a,
	0x0d, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x22, 0xc2, 0x02, 0x0a, 0x11, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x72, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x72, 0x6d, 0x12, 0x1f, 0x0a, 0x0b,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x31, 0x12, 0x1f, 0x0a,
	0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x32, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x32, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x29, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x36, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x09,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x32, 0xb3, 0x19, 0x0a, 0x02, 0x41, 0x69, 0x12, 0x79,
	0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x12, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x22,
	0x25, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x82, 0x01, 0x0a, 0x0d, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x63, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63,
	0x6b, 0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2b, 0x22, 0x26, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e,
	0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x63, 0x6b, 0x2f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x7a,
	0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c,
	0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x73, 0x0a, 0x0b, 0x47, 0x65,
	0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x30, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x12, 0x28, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67,
	0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x90, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x30, 0x12, 0x2e, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x12, 0x80, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x22, 0x28, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c,
	0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x92, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x4b, 0x65,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x22, 0x2c, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0x8d, 0x01, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a,
	0x12, 0x28, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x93, 0x01, 0x0a, 0x14, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x22, 0x2f, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a,
	0x12, 0x95, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2f, 0x12, 0x2d, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x93, 0x01, 0x0a, 0x14, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x22, 0x2f, 0x2f, 0x69, 0x6e, 0x74,
	0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x69, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0xa6,
	0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x12, 0x2f, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69,
	0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x76, 0x0a, 0x0a, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x22, 0x23, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c,
	0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x3a, 0x01, 0x2a, 0x12,
	0x73, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24,
	0x12, 0x22, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x6a, 0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x12, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x3a, 0x01, 0x2a,
	0x12, 0x60, 0x0a, 0x08, 0x42, 0x6c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x67, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x6c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x23, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67,
	0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x6c, 0x6f, 0x67, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x68, 0x0a, 0x0a, 0x42, 0x6c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x62, 0x6c, 0x6f, 0x67, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x70, 0x0a, 0x0c,
	0x42, 0x6c, 0x6f, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x67, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x62, 0x6c, 0x6f, 0x67, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x6f,
	0x0a, 0x0c, 0x42, 0x6c, 0x6f, 0x67, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x67, 0x46,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62,
	0x6c, 0x6f, 0x67, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x3a, 0x01, 0x2a, 0x12,
	0x61, 0x0a, 0x07, 0x53, 0x65, 0x74, 0x42, 0x6c, 0x6f, 0x67, 0x12, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x67, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x1a, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x22, 0x25, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1f, 0x22, 0x1a, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e,
	0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x6c, 0x6f, 0x67, 0x2f, 0x73, 0x65, 0x74, 0x3a,
	0x01, 0x2a, 0x12, 0x6d, 0x0a, 0x0b, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64,
	0x75, 0x54, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21,
	0x12, 0x1f, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x65, 0x64, 0x75, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x2f, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x75, 0x0a, 0x0d, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x64, 0x75, 0x54, 0x65, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x54,
	0x65, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x29, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67,
	0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x64, 0x75, 0x5f, 0x74, 0x65, 0x72,
	0x6d, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x6a, 0x0a, 0x09, 0x43, 0x6f, 0x64, 0x65,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24,
	0x22, 0x1f, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x6f, 0x67, 0x69,
	0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x63, 0x0a, 0x08, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4f, 0x75, 0x74,
	0x12, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x12, 0x6b, 0x0a, 0x0a, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x28, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65,
	0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x84, 0x01, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2f, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x29, 0x12, 0x27, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65,
	0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x42, 0x1e, 0x5a,
	0x1c, 0x68, 0x77, 0x2d, 0x70, 0x61, 0x61, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_ai_v1_ai_proto_rawDescOnce sync.Once
	file_api_ai_v1_ai_proto_rawDescData = file_api_ai_v1_ai_proto_rawDesc
)

func file_api_ai_v1_ai_proto_rawDescGZIP() []byte {
	file_api_ai_v1_ai_proto_rawDescOnce.Do(func() {
		file_api_ai_v1_ai_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_ai_v1_ai_proto_rawDescData)
	})
	return file_api_ai_v1_ai_proto_rawDescData
}

var file_api_ai_v1_ai_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_api_ai_v1_ai_proto_goTypes = []interface{}{
	(*CheckEmailExistsReq)(nil),                   // 0: api.ai.v1.CheckEmailExistsReq
	(*CheckEmailExistsResp)(nil),                  // 1: api.ai.v1.CheckEmailExistsResp
	(*CheckLoginReq)(nil),                         // 2: api.ai.v1.CheckLoginReq
	(*CheckLoginResp)(nil),                        // 3: api.ai.v1.CheckLoginResp
	(*LoginOutReq)(nil),                           // 4: api.ai.v1.LoginOutReq
	(*LoginOutResp)(nil),                          // 5: api.ai.v1.LoginOutResp
	(*CodeLoginReq)(nil),                          // 6: api.ai.v1.CodeLoginReq
	(*CodeLoginResp)(nil),                         // 7: api.ai.v1.CodeLoginResp
	(*TaskDriveReq)(nil),                          // 8: api.ai.v1.TaskDriveReq
	(*TaskDriveResp)(nil),                         // 9: api.ai.v1.TaskDriveResp
	(*CommonConfigReq)(nil),                       // 10: api.ai.v1.CommonConfigReq
	(*CommonConfigResp)(nil),                      // 11: api.ai.v1.CommonConfigResp
	(*Range)(nil),                                 // 12: api.ai.v1.Range
	(*FeedbackTraceRequest)(nil),                  // 13: api.ai.v1.FeedbackTraceRequest
	(*FeedbackTraceReply)(nil),                    // 14: api.ai.v1.FeedbackTraceReply
	(*QueryCorrectRequest)(nil),                   // 15: api.ai.v1.QueryCorrectRequest
	(*Empty)(nil),                                 // 16: api.ai.v1.Empty
	(*ListQuestionsRequest)(nil),                  // 17: api.ai.v1.ListQuestionsRequest
	(*ListQuestionsReply)(nil),                    // 18: api.ai.v1.ListQuestionsReply
	(*GetQuestionRequest)(nil),                    // 19: api.ai.v1.GetQuestionRequest
	(*GetQuestionBatchRequest)(nil),               // 20: api.ai.v1.GetQuestionBatchRequest
	(*GetQuestionBatchReply)(nil),                 // 21: api.ai.v1.GetQuestionBatchReply
	(*UpsertQuestionRequest)(nil),                 // 22: api.ai.v1.UpsertQuestionRequest
	(*UpdateQuestionWithKeyRequest)(nil),          // 23: api.ai.v1.UpdateQuestionWithKeyRequest
	(*Question)(nil),                              // 24: api.ai.v1.Question
	(*GetQuestionStatusRequest)(nil),              // 25: api.ai.v1.GetQuestionStatusRequest
	(*GetQuestionStatusReply)(nil),                // 26: api.ai.v1.GetQuestionStatusReply
	(*UploadFileRequest)(nil),                     // 27: api.ai.v1.UploadFileRequest
	(*UploadFileReply)(nil),                       // 28: api.ai.v1.UploadFileReply
	(*CreateQuestionImportRequest)(nil),           // 29: api.ai.v1.CreateQuestionImportRequest
	(*ListQuestionImportRequest)(nil),             // 30: api.ai.v1.ListQuestionImportRequest
	(*QuestionImportData)(nil),                    // 31: api.ai.v1.QuestionImportData
	(*ListQuestionImportReply)(nil),               // 32: api.ai.v1.ListQuestionImportReply
	(*QuestionImportActionRequest)(nil),           // 33: api.ai.v1.QuestionImportActionRequest
	(*GetQuestionImportDetailRequest)(nil),        // 34: api.ai.v1.GetQuestionImportDetailRequest
	(*ImportErrorReason)(nil),                     // 35: api.ai.v1.ImportErrorReason
	(*GetQuestionImportDetailReply)(nil),          // 36: api.ai.v1.GetQuestionImportDetailReply
	(*BlogListReq)(nil),                           // 37: api.ai.v1.BlogListReq
	(*BlogListResp)(nil),                          // 38: api.ai.v1.BlogListResp
	(*BlogDetailReq)(nil),                         // 39: api.ai.v1.BlogDetailReq
	(*BlogDetailResp)(nil),                        // 40: api.ai.v1.BlogDetailResp
	(*BlogArticle)(nil),                           // 41: api.ai.v1.BlogArticle
	(*BlogFeedbackReq)(nil),                       // 42: api.ai.v1.BlogFeedbackReq
	(*BlogCategoryReq)(nil),                       // 43: api.ai.v1.BlogCategoryReq
	(*BlogCategoryResp)(nil),                      // 44: api.ai.v1.BlogCategoryResp
	(*EduTermInfo)(nil),                           // 45: api.ai.v1.EduTermInfo
	(*EduTermListReq)(nil),                        // 46: api.ai.v1.EduTermListReq
	(*EduTermListResp)(nil),                       // 47: api.ai.v1.EduTermListResp
	(*EduTermDetailReq)(nil),                      // 48: api.ai.v1.EduTermDetailReq
	(*EduTermBlock)(nil),                          // 49: api.ai.v1.EduTermBlock
	(*EduTermDetailResp)(nil),                     // 50: api.ai.v1.EduTermDetailResp
	nil,                                           // 51: api.ai.v1.UpdateQuestionWithKeyRequest.FieldsEntry
	(*GetQuestionStatusReply_QuestionStatus)(nil), // 52: api.ai.v1.GetQuestionStatusReply.QuestionStatus
	(*structpb.Struct)(nil),                       // 53: google.protobuf.Struct
}
var file_api_ai_v1_ai_proto_depIdxs = []int32{
	12, // 0: api.ai.v1.CommonConfigResp.ranges:type_name -> api.ai.v1.Range
	24, // 1: api.ai.v1.ListQuestionsReply.questions:type_name -> api.ai.v1.Question
	24, // 2: api.ai.v1.GetQuestionBatchReply.questions:type_name -> api.ai.v1.Question
	51, // 3: api.ai.v1.UpdateQuestionWithKeyRequest.fields:type_name -> api.ai.v1.UpdateQuestionWithKeyRequest.FieldsEntry
	52, // 4: api.ai.v1.GetQuestionStatusReply.questions:type_name -> api.ai.v1.GetQuestionStatusReply.QuestionStatus
	31, // 5: api.ai.v1.ListQuestionImportReply.list:type_name -> api.ai.v1.QuestionImportData
	35, // 6: api.ai.v1.GetQuestionImportDetailReply.error_reasons:type_name -> api.ai.v1.ImportErrorReason
	41, // 7: api.ai.v1.BlogListResp.list:type_name -> api.ai.v1.BlogArticle
	41, // 8: api.ai.v1.BlogDetailResp.article:type_name -> api.ai.v1.BlogArticle
	45, // 9: api.ai.v1.EduTermListResp.list:type_name -> api.ai.v1.EduTermInfo
	49, // 10: api.ai.v1.EduTermDetailResp.block_list:type_name -> api.ai.v1.EduTermBlock
	15, // 11: api.ai.v1.Ai.QueryCorrect:input_type -> api.ai.v1.QueryCorrectRequest
	13, // 12: api.ai.v1.Ai.FeedbackTrace:input_type -> api.ai.v1.FeedbackTraceRequest
	17, // 13: api.ai.v1.Ai.ListQuestions:input_type -> api.ai.v1.ListQuestionsRequest
	19, // 14: api.ai.v1.Ai.GetQuestion:input_type -> api.ai.v1.GetQuestionRequest
	20, // 15: api.ai.v1.Ai.GetQuestionBatch:input_type -> api.ai.v1.GetQuestionBatchRequest
	22, // 16: api.ai.v1.Ai.UpsertQuestion:input_type -> api.ai.v1.UpsertQuestionRequest
	23, // 17: api.ai.v1.Ai.UpdateQuestionWithKey:input_type -> api.ai.v1.UpdateQuestionWithKeyRequest
	25, // 18: api.ai.v1.Ai.GetQuestionStatus:input_type -> api.ai.v1.GetQuestionStatusRequest
	29, // 19: api.ai.v1.Ai.CreateQuestionImport:input_type -> api.ai.v1.CreateQuestionImportRequest
	30, // 20: api.ai.v1.Ai.ListQuestionImport:input_type -> api.ai.v1.ListQuestionImportRequest
	33, // 21: api.ai.v1.Ai.QuestionImportAction:input_type -> api.ai.v1.QuestionImportActionRequest
	34, // 22: api.ai.v1.Ai.GetQuestionImportDetail:input_type -> api.ai.v1.GetQuestionImportDetailRequest
	27, // 23: api.ai.v1.Ai.UploadFile:input_type -> api.ai.v1.UploadFileRequest
	10, // 24: api.ai.v1.Ai.CommonConfig:input_type -> api.ai.v1.CommonConfigReq
	8,  // 25: api.ai.v1.Ai.TaskDrive:input_type -> api.ai.v1.TaskDriveReq
	37, // 26: api.ai.v1.Ai.BlogList:input_type -> api.ai.v1.BlogListReq
	39, // 27: api.ai.v1.Ai.BlogDetail:input_type -> api.ai.v1.BlogDetailReq
	43, // 28: api.ai.v1.Ai.BlogCategory:input_type -> api.ai.v1.BlogCategoryReq
	42, // 29: api.ai.v1.Ai.BlogFeedback:input_type -> api.ai.v1.BlogFeedbackReq
	41, // 30: api.ai.v1.Ai.SetBlog:input_type -> api.ai.v1.BlogArticle
	46, // 31: api.ai.v1.Ai.EduTermList:input_type -> api.ai.v1.EduTermListReq
	48, // 32: api.ai.v1.Ai.EduTermDetail:input_type -> api.ai.v1.EduTermDetailReq
	6,  // 33: api.ai.v1.Ai.CodeLogin:input_type -> api.ai.v1.CodeLoginReq
	4,  // 34: api.ai.v1.Ai.LoginOut:input_type -> api.ai.v1.LoginOutReq
	2,  // 35: api.ai.v1.Ai.CheckLogin:input_type -> api.ai.v1.CheckLoginReq
	0,  // 36: api.ai.v1.Ai.CheckEmailExists:input_type -> api.ai.v1.CheckEmailExistsReq
	53, // 37: api.ai.v1.Ai.QueryCorrect:output_type -> google.protobuf.Struct
	14, // 38: api.ai.v1.Ai.FeedbackTrace:output_type -> api.ai.v1.FeedbackTraceReply
	18, // 39: api.ai.v1.Ai.ListQuestions:output_type -> api.ai.v1.ListQuestionsReply
	24, // 40: api.ai.v1.Ai.GetQuestion:output_type -> api.ai.v1.Question
	21, // 41: api.ai.v1.Ai.GetQuestionBatch:output_type -> api.ai.v1.GetQuestionBatchReply
	53, // 42: api.ai.v1.Ai.UpsertQuestion:output_type -> google.protobuf.Struct
	53, // 43: api.ai.v1.Ai.UpdateQuestionWithKey:output_type -> google.protobuf.Struct
	26, // 44: api.ai.v1.Ai.GetQuestionStatus:output_type -> api.ai.v1.GetQuestionStatusReply
	53, // 45: api.ai.v1.Ai.CreateQuestionImport:output_type -> google.protobuf.Struct
	32, // 46: api.ai.v1.Ai.ListQuestionImport:output_type -> api.ai.v1.ListQuestionImportReply
	53, // 47: api.ai.v1.Ai.QuestionImportAction:output_type -> google.protobuf.Struct
	36, // 48: api.ai.v1.Ai.GetQuestionImportDetail:output_type -> api.ai.v1.GetQuestionImportDetailReply
	28, // 49: api.ai.v1.Ai.UploadFile:output_type -> api.ai.v1.UploadFileReply
	11, // 50: api.ai.v1.Ai.CommonConfig:output_type -> api.ai.v1.CommonConfigResp
	9,  // 51: api.ai.v1.Ai.TaskDrive:output_type -> api.ai.v1.TaskDriveResp
	38, // 52: api.ai.v1.Ai.BlogList:output_type -> api.ai.v1.BlogListResp
	40, // 53: api.ai.v1.Ai.BlogDetail:output_type -> api.ai.v1.BlogDetailResp
	44, // 54: api.ai.v1.Ai.BlogCategory:output_type -> api.ai.v1.BlogCategoryResp
	53, // 55: api.ai.v1.Ai.BlogFeedback:output_type -> google.protobuf.Struct
	53, // 56: api.ai.v1.Ai.SetBlog:output_type -> google.protobuf.Struct
	47, // 57: api.ai.v1.Ai.EduTermList:output_type -> api.ai.v1.EduTermListResp
	50, // 58: api.ai.v1.Ai.EduTermDetail:output_type -> api.ai.v1.EduTermDetailResp
	7,  // 59: api.ai.v1.Ai.CodeLogin:output_type -> api.ai.v1.CodeLoginResp
	5,  // 60: api.ai.v1.Ai.LoginOut:output_type -> api.ai.v1.LoginOutResp
	3,  // 61: api.ai.v1.Ai.CheckLogin:output_type -> api.ai.v1.CheckLoginResp
	1,  // 62: api.ai.v1.Ai.CheckEmailExists:output_type -> api.ai.v1.CheckEmailExistsResp
	37, // [37:63] is the sub-list for method output_type
	11, // [11:37] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_ai_v1_ai_proto_init() }
func file_api_ai_v1_ai_proto_init() {
	if File_api_ai_v1_ai_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_ai_v1_ai_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckEmailExistsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckEmailExistsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckLoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckLoginResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginOutReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginOutResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeLoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeLoginResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskDriveReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskDriveResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonConfigResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Range); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedbackTraceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedbackTraceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCorrectRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListQuestionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListQuestionsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuestionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuestionBatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuestionBatchReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertQuestionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateQuestionWithKeyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Question); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuestionStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuestionStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadFileReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateQuestionImportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListQuestionImportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionImportData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListQuestionImportReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionImportActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuestionImportDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportErrorReason); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuestionImportDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlogListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlogListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlogDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlogDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlogArticle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlogFeedbackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlogCategoryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlogCategoryResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduTermInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduTermListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduTermListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduTermDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduTermBlock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduTermDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_ai_v1_ai_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuestionStatusReply_QuestionStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_ai_v1_ai_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_ai_v1_ai_proto_goTypes,
		DependencyIndexes: file_api_ai_v1_ai_proto_depIdxs,
		MessageInfos:      file_api_ai_v1_ai_proto_msgTypes,
	}.Build()
	File_api_ai_v1_ai_proto = out.File
	file_api_ai_v1_ai_proto_rawDesc = nil
	file_api_ai_v1_ai_proto_goTypes = nil
	file_api_ai_v1_ai_proto_depIdxs = nil
}
