// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.23.3
// source: api/ai/v1/ai.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAiBlogCategory = "/api.ai.v1.Ai/BlogCategory"
const OperationAiBlogDetail = "/api.ai.v1.Ai/BlogDetail"
const OperationAiBlogFeedback = "/api.ai.v1.Ai/BlogFeedback"
const OperationAiBlogList = "/api.ai.v1.Ai/BlogList"
const OperationAiCheckEmailExists = "/api.ai.v1.Ai/CheckEmailExists"
const OperationAiCheckLogin = "/api.ai.v1.Ai/CheckLogin"
const OperationAiCodeLogin = "/api.ai.v1.Ai/CodeLogin"
const OperationAiCommonConfig = "/api.ai.v1.Ai/CommonConfig"
const OperationAiCreateQuestionImport = "/api.ai.v1.Ai/CreateQuestionImport"
const OperationAiEduTermDetail = "/api.ai.v1.Ai/EduTermDetail"
const OperationAiEduTermList = "/api.ai.v1.Ai/EduTermList"
const OperationAiFeedbackTrace = "/api.ai.v1.Ai/FeedbackTrace"
const OperationAiGetQuestion = "/api.ai.v1.Ai/GetQuestion"
const OperationAiGetQuestionBatch = "/api.ai.v1.Ai/GetQuestionBatch"
const OperationAiGetQuestionImportDetail = "/api.ai.v1.Ai/GetQuestionImportDetail"
const OperationAiGetQuestionStatus = "/api.ai.v1.Ai/GetQuestionStatus"
const OperationAiListQuestionImport = "/api.ai.v1.Ai/ListQuestionImport"
const OperationAiListQuestions = "/api.ai.v1.Ai/ListQuestions"
const OperationAiLoginOut = "/api.ai.v1.Ai/LoginOut"
const OperationAiQueryCorrect = "/api.ai.v1.Ai/QueryCorrect"
const OperationAiQuestionImportAction = "/api.ai.v1.Ai/QuestionImportAction"
const OperationAiSetBlog = "/api.ai.v1.Ai/SetBlog"
const OperationAiTaskDrive = "/api.ai.v1.Ai/TaskDrive"
const OperationAiUpdateQuestionWithKey = "/api.ai.v1.Ai/UpdateQuestionWithKey"
const OperationAiUploadFile = "/api.ai.v1.Ai/UploadFile"
const OperationAiUpsertQuestion = "/api.ai.v1.Ai/UpsertQuestion"

type AiHTTPServer interface {
	BlogCategory(context.Context, *BlogCategoryReq) (*BlogCategoryResp, error)
	BlogDetail(context.Context, *BlogDetailReq) (*BlogDetailResp, error)
	BlogFeedback(context.Context, *BlogFeedbackReq) (*structpb.Struct, error)
	BlogList(context.Context, *BlogListReq) (*BlogListResp, error)
	// CheckEmailExists CheckEmailExists
	CheckEmailExists(context.Context, *CheckEmailExistsReq) (*CheckEmailExistsResp, error)
	CheckLogin(context.Context, *CheckLoginReq) (*CheckLoginResp, error)
	CodeLogin(context.Context, *CodeLoginReq) (*CodeLoginResp, error)
	CommonConfig(context.Context, *CommonConfigReq) (*CommonConfigResp, error)
	// CreateQuestionImportquestion_import create
	CreateQuestionImport(context.Context, *CreateQuestionImportRequest) (*structpb.Struct, error)
	EduTermDetail(context.Context, *EduTermDetailReq) (*EduTermDetailResp, error)
	EduTermList(context.Context, *EduTermListReq) (*EduTermListResp, error)
	FeedbackTrace(context.Context, *FeedbackTraceRequest) (*FeedbackTraceReply, error)
	GetQuestion(context.Context, *GetQuestionRequest) (*Question, error)
	GetQuestionBatch(context.Context, *GetQuestionBatchRequest) (*GetQuestionBatchReply, error)
	// GetQuestionImportDetailquestion import detail
	GetQuestionImportDetail(context.Context, *GetQuestionImportDetailRequest) (*GetQuestionImportDetailReply, error)
	GetQuestionStatus(context.Context, *GetQuestionStatusRequest) (*GetQuestionStatusReply, error)
	// ListQuestionImportquestion_import list
	ListQuestionImport(context.Context, *ListQuestionImportRequest) (*ListQuestionImportReply, error)
	ListQuestions(context.Context, *ListQuestionsRequest) (*ListQuestionsReply, error)
	LoginOut(context.Context, *LoginOutReq) (*LoginOutResp, error)
	QueryCorrect(context.Context, *QueryCorrectRequest) (*structpb.Struct, error)
	// QuestionImportActionquestion import action
	QuestionImportAction(context.Context, *QuestionImportActionRequest) (*structpb.Struct, error)
	SetBlog(context.Context, *BlogArticle) (*structpb.Struct, error)
	TaskDrive(context.Context, *TaskDriveReq) (*TaskDriveResp, error)
	UpdateQuestionWithKey(context.Context, *UpdateQuestionWithKeyRequest) (*structpb.Struct, error)
	UploadFile(context.Context, *UploadFileRequest) (*UploadFileReply, error)
	UpsertQuestion(context.Context, *UpsertQuestionRequest) (*structpb.Struct, error)
}

func RegisterAiHTTPServer(s *http.Server, srv AiHTTPServer) {
	r := s.Route("/")
	r.POST("/intelligence/api/ai/v1/query_correct", _Ai_QueryCorrect0_HTTP_Handler(srv))
	r.POST("/intelligence/api/ai/v2/feedback/trace", _Ai_FeedbackTrace0_HTTP_Handler(srv))
	r.GET("/intelligence/api/ai/v1/questions", _Ai_ListQuestions0_HTTP_Handler(srv))
	r.GET("/intelligence/api/ai/v1/questions/detail", _Ai_GetQuestion0_HTTP_Handler(srv))
	r.GET("/intelligence/api/ai/v1/questions/detail_batch", _Ai_GetQuestionBatch0_HTTP_Handler(srv))
	r.POST("/intelligence/api/ai/v1/questions/update", _Ai_UpsertQuestion0_HTTP_Handler(srv))
	r.POST("/intelligence/api/ai/v1/questions/update_key", _Ai_UpdateQuestionWithKey0_HTTP_Handler(srv))
	r.GET("/intelligence/api/ai/v1/questions/status", _Ai_GetQuestionStatus0_HTTP_Handler(srv))
	r.POST("/intelligence/api/ai/v1/questions_import/create", _Ai_CreateQuestionImport0_HTTP_Handler(srv))
	r.GET("/intelligence/api/ai/v1/questions_import/list", _Ai_ListQuestionImport0_HTTP_Handler(srv))
	r.POST("/intelligence/api/ai/v1/questions_import/action", _Ai_QuestionImportAction0_HTTP_Handler(srv))
	r.GET("/intelligence/api/ai/v1/questions_import/detail", _Ai_GetQuestionImportDetail0_HTTP_Handler(srv))
	r.POST("/intelligence/api/ai/v1/upload_file", _Ai_UploadFile0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/common/config", _Ai_CommonConfig0_HTTP_Handler(srv))
	r.POST("/intelligence/api/v1/task/drive", _Ai_TaskDrive0_HTTP_Handler(srv))
	r.GET("/intelligence/api/blog/list", _Ai_BlogList0_HTTP_Handler(srv))
	r.GET("/intelligence/api/blog/detail", _Ai_BlogDetail0_HTTP_Handler(srv))
	r.GET("/intelligence/api/blog/category", _Ai_BlogCategory0_HTTP_Handler(srv))
	r.POST("/intelligence/api/blog/feedback", _Ai_BlogFeedback0_HTTP_Handler(srv))
	r.POST("/intelligence/api/blog/set", _Ai_SetBlog0_HTTP_Handler(srv))
	r.GET("/intelligence/api/edu_term/list", _Ai_EduTermList0_HTTP_Handler(srv))
	r.GET("/intelligence/api/edu_term/detail", _Ai_EduTermDetail0_HTTP_Handler(srv))
	r.POST("/intelligence/api/v1/code_login", _Ai_CodeLogin0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/login_out", _Ai_LoginOut0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/check_login", _Ai_CheckLogin0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/check_email_exists", _Ai_CheckEmailExists0_HTTP_Handler(srv))
}

func _Ai_QueryCorrect0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryCorrectRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiQueryCorrect)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryCorrect(ctx, req.(*QueryCorrectRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _Ai_FeedbackTrace0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FeedbackTraceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiFeedbackTrace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FeedbackTrace(ctx, req.(*FeedbackTraceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FeedbackTraceReply)
		return ctx.Result(200, reply)
	}
}

func _Ai_ListQuestions0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListQuestionsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiListQuestions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListQuestions(ctx, req.(*ListQuestionsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListQuestionsReply)
		return ctx.Result(200, reply)
	}
}

func _Ai_GetQuestion0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetQuestionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiGetQuestion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetQuestion(ctx, req.(*GetQuestionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Question)
		return ctx.Result(200, reply)
	}
}

func _Ai_GetQuestionBatch0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetQuestionBatchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiGetQuestionBatch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetQuestionBatch(ctx, req.(*GetQuestionBatchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetQuestionBatchReply)
		return ctx.Result(200, reply)
	}
}

func _Ai_UpsertQuestion0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpsertQuestionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiUpsertQuestion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpsertQuestion(ctx, req.(*UpsertQuestionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _Ai_UpdateQuestionWithKey0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateQuestionWithKeyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiUpdateQuestionWithKey)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateQuestionWithKey(ctx, req.(*UpdateQuestionWithKeyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _Ai_GetQuestionStatus0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetQuestionStatusRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiGetQuestionStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetQuestionStatus(ctx, req.(*GetQuestionStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetQuestionStatusReply)
		return ctx.Result(200, reply)
	}
}

func _Ai_CreateQuestionImport0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateQuestionImportRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiCreateQuestionImport)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateQuestionImport(ctx, req.(*CreateQuestionImportRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _Ai_ListQuestionImport0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListQuestionImportRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiListQuestionImport)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListQuestionImport(ctx, req.(*ListQuestionImportRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListQuestionImportReply)
		return ctx.Result(200, reply)
	}
}

func _Ai_QuestionImportAction0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QuestionImportActionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiQuestionImportAction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QuestionImportAction(ctx, req.(*QuestionImportActionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _Ai_GetQuestionImportDetail0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetQuestionImportDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiGetQuestionImportDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetQuestionImportDetail(ctx, req.(*GetQuestionImportDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetQuestionImportDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Ai_UploadFile0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadFileRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiUploadFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadFile(ctx, req.(*UploadFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UploadFileReply)
		return ctx.Result(200, reply)
	}
}

func _Ai_CommonConfig0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonConfigReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiCommonConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CommonConfig(ctx, req.(*CommonConfigReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonConfigResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_TaskDrive0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TaskDriveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiTaskDrive)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TaskDrive(ctx, req.(*TaskDriveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TaskDriveResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_BlogList0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BlogListReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiBlogList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BlogList(ctx, req.(*BlogListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BlogListResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_BlogDetail0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BlogDetailReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiBlogDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BlogDetail(ctx, req.(*BlogDetailReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BlogDetailResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_BlogCategory0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BlogCategoryReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiBlogCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BlogCategory(ctx, req.(*BlogCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BlogCategoryResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_BlogFeedback0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BlogFeedbackReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiBlogFeedback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BlogFeedback(ctx, req.(*BlogFeedbackReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _Ai_SetBlog0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BlogArticle
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiSetBlog)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetBlog(ctx, req.(*BlogArticle))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*structpb.Struct)
		return ctx.Result(200, reply)
	}
}

func _Ai_EduTermList0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EduTermListReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiEduTermList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.EduTermList(ctx, req.(*EduTermListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EduTermListResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_EduTermDetail0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EduTermDetailReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiEduTermDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.EduTermDetail(ctx, req.(*EduTermDetailReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EduTermDetailResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_CodeLogin0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CodeLoginReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiCodeLogin)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CodeLogin(ctx, req.(*CodeLoginReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CodeLoginResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_LoginOut0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LoginOutReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiLoginOut)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LoginOut(ctx, req.(*LoginOutReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LoginOutResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_CheckLogin0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckLoginReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiCheckLogin)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckLogin(ctx, req.(*CheckLoginReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckLoginResp)
		return ctx.Result(200, reply)
	}
}

func _Ai_CheckEmailExists0_HTTP_Handler(srv AiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckEmailExistsReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAiCheckEmailExists)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckEmailExists(ctx, req.(*CheckEmailExistsReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckEmailExistsResp)
		return ctx.Result(200, reply)
	}
}

type AiHTTPClient interface {
	BlogCategory(ctx context.Context, req *BlogCategoryReq, opts ...http.CallOption) (rsp *BlogCategoryResp, err error)
	BlogDetail(ctx context.Context, req *BlogDetailReq, opts ...http.CallOption) (rsp *BlogDetailResp, err error)
	BlogFeedback(ctx context.Context, req *BlogFeedbackReq, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	BlogList(ctx context.Context, req *BlogListReq, opts ...http.CallOption) (rsp *BlogListResp, err error)
	CheckEmailExists(ctx context.Context, req *CheckEmailExistsReq, opts ...http.CallOption) (rsp *CheckEmailExistsResp, err error)
	CheckLogin(ctx context.Context, req *CheckLoginReq, opts ...http.CallOption) (rsp *CheckLoginResp, err error)
	CodeLogin(ctx context.Context, req *CodeLoginReq, opts ...http.CallOption) (rsp *CodeLoginResp, err error)
	CommonConfig(ctx context.Context, req *CommonConfigReq, opts ...http.CallOption) (rsp *CommonConfigResp, err error)
	CreateQuestionImport(ctx context.Context, req *CreateQuestionImportRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	EduTermDetail(ctx context.Context, req *EduTermDetailReq, opts ...http.CallOption) (rsp *EduTermDetailResp, err error)
	EduTermList(ctx context.Context, req *EduTermListReq, opts ...http.CallOption) (rsp *EduTermListResp, err error)
	FeedbackTrace(ctx context.Context, req *FeedbackTraceRequest, opts ...http.CallOption) (rsp *FeedbackTraceReply, err error)
	GetQuestion(ctx context.Context, req *GetQuestionRequest, opts ...http.CallOption) (rsp *Question, err error)
	GetQuestionBatch(ctx context.Context, req *GetQuestionBatchRequest, opts ...http.CallOption) (rsp *GetQuestionBatchReply, err error)
	GetQuestionImportDetail(ctx context.Context, req *GetQuestionImportDetailRequest, opts ...http.CallOption) (rsp *GetQuestionImportDetailReply, err error)
	GetQuestionStatus(ctx context.Context, req *GetQuestionStatusRequest, opts ...http.CallOption) (rsp *GetQuestionStatusReply, err error)
	ListQuestionImport(ctx context.Context, req *ListQuestionImportRequest, opts ...http.CallOption) (rsp *ListQuestionImportReply, err error)
	ListQuestions(ctx context.Context, req *ListQuestionsRequest, opts ...http.CallOption) (rsp *ListQuestionsReply, err error)
	LoginOut(ctx context.Context, req *LoginOutReq, opts ...http.CallOption) (rsp *LoginOutResp, err error)
	QueryCorrect(ctx context.Context, req *QueryCorrectRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	QuestionImportAction(ctx context.Context, req *QuestionImportActionRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	SetBlog(ctx context.Context, req *BlogArticle, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	TaskDrive(ctx context.Context, req *TaskDriveReq, opts ...http.CallOption) (rsp *TaskDriveResp, err error)
	UpdateQuestionWithKey(ctx context.Context, req *UpdateQuestionWithKeyRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
	UploadFile(ctx context.Context, req *UploadFileRequest, opts ...http.CallOption) (rsp *UploadFileReply, err error)
	UpsertQuestion(ctx context.Context, req *UpsertQuestionRequest, opts ...http.CallOption) (rsp *structpb.Struct, err error)
}

type AiHTTPClientImpl struct {
	cc *http.Client
}

func NewAiHTTPClient(client *http.Client) AiHTTPClient {
	return &AiHTTPClientImpl{client}
}

func (c *AiHTTPClientImpl) BlogCategory(ctx context.Context, in *BlogCategoryReq, opts ...http.CallOption) (*BlogCategoryResp, error) {
	var out BlogCategoryResp
	pattern := "/intelligence/api/blog/category"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiBlogCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) BlogDetail(ctx context.Context, in *BlogDetailReq, opts ...http.CallOption) (*BlogDetailResp, error) {
	var out BlogDetailResp
	pattern := "/intelligence/api/blog/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiBlogDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) BlogFeedback(ctx context.Context, in *BlogFeedbackReq, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/intelligence/api/blog/feedback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiBlogFeedback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) BlogList(ctx context.Context, in *BlogListReq, opts ...http.CallOption) (*BlogListResp, error) {
	var out BlogListResp
	pattern := "/intelligence/api/blog/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiBlogList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) CheckEmailExists(ctx context.Context, in *CheckEmailExistsReq, opts ...http.CallOption) (*CheckEmailExistsResp, error) {
	var out CheckEmailExistsResp
	pattern := "/intelligence/api/v1/check_email_exists"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiCheckEmailExists))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) CheckLogin(ctx context.Context, in *CheckLoginReq, opts ...http.CallOption) (*CheckLoginResp, error) {
	var out CheckLoginResp
	pattern := "/intelligence/api/v1/check_login"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiCheckLogin))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) CodeLogin(ctx context.Context, in *CodeLoginReq, opts ...http.CallOption) (*CodeLoginResp, error) {
	var out CodeLoginResp
	pattern := "/intelligence/api/v1/code_login"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiCodeLogin))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) CommonConfig(ctx context.Context, in *CommonConfigReq, opts ...http.CallOption) (*CommonConfigResp, error) {
	var out CommonConfigResp
	pattern := "/intelligence/api/v1/common/config"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiCommonConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) CreateQuestionImport(ctx context.Context, in *CreateQuestionImportRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/intelligence/api/ai/v1/questions_import/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiCreateQuestionImport))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) EduTermDetail(ctx context.Context, in *EduTermDetailReq, opts ...http.CallOption) (*EduTermDetailResp, error) {
	var out EduTermDetailResp
	pattern := "/intelligence/api/edu_term/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiEduTermDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) EduTermList(ctx context.Context, in *EduTermListReq, opts ...http.CallOption) (*EduTermListResp, error) {
	var out EduTermListResp
	pattern := "/intelligence/api/edu_term/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiEduTermList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) FeedbackTrace(ctx context.Context, in *FeedbackTraceRequest, opts ...http.CallOption) (*FeedbackTraceReply, error) {
	var out FeedbackTraceReply
	pattern := "/intelligence/api/ai/v2/feedback/trace"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiFeedbackTrace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) GetQuestion(ctx context.Context, in *GetQuestionRequest, opts ...http.CallOption) (*Question, error) {
	var out Question
	pattern := "/intelligence/api/ai/v1/questions/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiGetQuestion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) GetQuestionBatch(ctx context.Context, in *GetQuestionBatchRequest, opts ...http.CallOption) (*GetQuestionBatchReply, error) {
	var out GetQuestionBatchReply
	pattern := "/intelligence/api/ai/v1/questions/detail_batch"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiGetQuestionBatch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) GetQuestionImportDetail(ctx context.Context, in *GetQuestionImportDetailRequest, opts ...http.CallOption) (*GetQuestionImportDetailReply, error) {
	var out GetQuestionImportDetailReply
	pattern := "/intelligence/api/ai/v1/questions_import/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiGetQuestionImportDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) GetQuestionStatus(ctx context.Context, in *GetQuestionStatusRequest, opts ...http.CallOption) (*GetQuestionStatusReply, error) {
	var out GetQuestionStatusReply
	pattern := "/intelligence/api/ai/v1/questions/status"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiGetQuestionStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) ListQuestionImport(ctx context.Context, in *ListQuestionImportRequest, opts ...http.CallOption) (*ListQuestionImportReply, error) {
	var out ListQuestionImportReply
	pattern := "/intelligence/api/ai/v1/questions_import/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiListQuestionImport))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) ListQuestions(ctx context.Context, in *ListQuestionsRequest, opts ...http.CallOption) (*ListQuestionsReply, error) {
	var out ListQuestionsReply
	pattern := "/intelligence/api/ai/v1/questions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiListQuestions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) LoginOut(ctx context.Context, in *LoginOutReq, opts ...http.CallOption) (*LoginOutResp, error) {
	var out LoginOutResp
	pattern := "/intelligence/api/v1/login_out"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAiLoginOut))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) QueryCorrect(ctx context.Context, in *QueryCorrectRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/intelligence/api/ai/v1/query_correct"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiQueryCorrect))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) QuestionImportAction(ctx context.Context, in *QuestionImportActionRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/intelligence/api/ai/v1/questions_import/action"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiQuestionImportAction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) SetBlog(ctx context.Context, in *BlogArticle, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/intelligence/api/blog/set"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiSetBlog))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) TaskDrive(ctx context.Context, in *TaskDriveReq, opts ...http.CallOption) (*TaskDriveResp, error) {
	var out TaskDriveResp
	pattern := "/intelligence/api/v1/task/drive"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiTaskDrive))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) UpdateQuestionWithKey(ctx context.Context, in *UpdateQuestionWithKeyRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/intelligence/api/ai/v1/questions/update_key"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiUpdateQuestionWithKey))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) UploadFile(ctx context.Context, in *UploadFileRequest, opts ...http.CallOption) (*UploadFileReply, error) {
	var out UploadFileReply
	pattern := "/intelligence/api/ai/v1/upload_file"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiUploadFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AiHTTPClientImpl) UpsertQuestion(ctx context.Context, in *UpsertQuestionRequest, opts ...http.CallOption) (*structpb.Struct, error) {
	var out structpb.Struct
	pattern := "/intelligence/api/ai/v1/questions/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAiUpsertQuestion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
