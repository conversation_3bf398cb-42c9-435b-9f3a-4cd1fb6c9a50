// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.3
// source: api/ai/v1/ai.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Ai_QueryCorrect_FullMethodName            = "/api.ai.v1.Ai/QueryCorrect"
	Ai_FeedbackTrace_FullMethodName           = "/api.ai.v1.Ai/FeedbackTrace"
	Ai_ListQuestions_FullMethodName           = "/api.ai.v1.Ai/ListQuestions"
	Ai_GetQuestion_FullMethodName             = "/api.ai.v1.Ai/GetQuestion"
	Ai_GetQuestionBatch_FullMethodName        = "/api.ai.v1.Ai/GetQuestionBatch"
	Ai_UpsertQuestion_FullMethodName          = "/api.ai.v1.Ai/UpsertQuestion"
	Ai_UpdateQuestionWithKey_FullMethodName   = "/api.ai.v1.Ai/UpdateQuestionWithKey"
	Ai_GetQuestionStatus_FullMethodName       = "/api.ai.v1.Ai/GetQuestionStatus"
	Ai_CreateQuestionImport_FullMethodName    = "/api.ai.v1.Ai/CreateQuestionImport"
	Ai_ListQuestionImport_FullMethodName      = "/api.ai.v1.Ai/ListQuestionImport"
	Ai_QuestionImportAction_FullMethodName    = "/api.ai.v1.Ai/QuestionImportAction"
	Ai_GetQuestionImportDetail_FullMethodName = "/api.ai.v1.Ai/GetQuestionImportDetail"
	Ai_UploadFile_FullMethodName              = "/api.ai.v1.Ai/UploadFile"
	Ai_CommonConfig_FullMethodName            = "/api.ai.v1.Ai/CommonConfig"
	Ai_TaskDrive_FullMethodName               = "/api.ai.v1.Ai/TaskDrive"
	Ai_BlogList_FullMethodName                = "/api.ai.v1.Ai/BlogList"
	Ai_BlogDetail_FullMethodName              = "/api.ai.v1.Ai/BlogDetail"
	Ai_BlogCategory_FullMethodName            = "/api.ai.v1.Ai/BlogCategory"
	Ai_BlogFeedback_FullMethodName            = "/api.ai.v1.Ai/BlogFeedback"
	Ai_SetBlog_FullMethodName                 = "/api.ai.v1.Ai/SetBlog"
	Ai_EduTermList_FullMethodName             = "/api.ai.v1.Ai/EduTermList"
	Ai_EduTermDetail_FullMethodName           = "/api.ai.v1.Ai/EduTermDetail"
	Ai_CodeLogin_FullMethodName               = "/api.ai.v1.Ai/CodeLogin"
	Ai_LoginOut_FullMethodName                = "/api.ai.v1.Ai/LoginOut"
	Ai_CheckLogin_FullMethodName              = "/api.ai.v1.Ai/CheckLogin"
	Ai_CheckEmailExists_FullMethodName        = "/api.ai.v1.Ai/CheckEmailExists"
)

// AiClient is the client API for Ai service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AiClient interface {
	QueryCorrect(ctx context.Context, in *QueryCorrectRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	FeedbackTrace(ctx context.Context, in *FeedbackTraceRequest, opts ...grpc.CallOption) (*FeedbackTraceReply, error)
	ListQuestions(ctx context.Context, in *ListQuestionsRequest, opts ...grpc.CallOption) (*ListQuestionsReply, error)
	GetQuestion(ctx context.Context, in *GetQuestionRequest, opts ...grpc.CallOption) (*Question, error)
	GetQuestionBatch(ctx context.Context, in *GetQuestionBatchRequest, opts ...grpc.CallOption) (*GetQuestionBatchReply, error)
	UpsertQuestion(ctx context.Context, in *UpsertQuestionRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	UpdateQuestionWithKey(ctx context.Context, in *UpdateQuestionWithKeyRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	GetQuestionStatus(ctx context.Context, in *GetQuestionStatusRequest, opts ...grpc.CallOption) (*GetQuestionStatusReply, error)
	// question_import create
	CreateQuestionImport(ctx context.Context, in *CreateQuestionImportRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// question_import list
	ListQuestionImport(ctx context.Context, in *ListQuestionImportRequest, opts ...grpc.CallOption) (*ListQuestionImportReply, error)
	// question import action
	QuestionImportAction(ctx context.Context, in *QuestionImportActionRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// question import detail
	GetQuestionImportDetail(ctx context.Context, in *GetQuestionImportDetailRequest, opts ...grpc.CallOption) (*GetQuestionImportDetailReply, error)
	UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileReply, error)
	CommonConfig(ctx context.Context, in *CommonConfigReq, opts ...grpc.CallOption) (*CommonConfigResp, error)
	TaskDrive(ctx context.Context, in *TaskDriveReq, opts ...grpc.CallOption) (*TaskDriveResp, error)
	BlogList(ctx context.Context, in *BlogListReq, opts ...grpc.CallOption) (*BlogListResp, error)
	BlogDetail(ctx context.Context, in *BlogDetailReq, opts ...grpc.CallOption) (*BlogDetailResp, error)
	BlogCategory(ctx context.Context, in *BlogCategoryReq, opts ...grpc.CallOption) (*BlogCategoryResp, error)
	BlogFeedback(ctx context.Context, in *BlogFeedbackReq, opts ...grpc.CallOption) (*structpb.Struct, error)
	SetBlog(ctx context.Context, in *BlogArticle, opts ...grpc.CallOption) (*structpb.Struct, error)
	EduTermList(ctx context.Context, in *EduTermListReq, opts ...grpc.CallOption) (*EduTermListResp, error)
	EduTermDetail(ctx context.Context, in *EduTermDetailReq, opts ...grpc.CallOption) (*EduTermDetailResp, error)
	CodeLogin(ctx context.Context, in *CodeLoginReq, opts ...grpc.CallOption) (*CodeLoginResp, error)
	LoginOut(ctx context.Context, in *LoginOutReq, opts ...grpc.CallOption) (*LoginOutResp, error)
	CheckLogin(ctx context.Context, in *CheckLoginReq, opts ...grpc.CallOption) (*CheckLoginResp, error)
	// CheckEmailExists
	CheckEmailExists(ctx context.Context, in *CheckEmailExistsReq, opts ...grpc.CallOption) (*CheckEmailExistsResp, error)
}

type aiClient struct {
	cc grpc.ClientConnInterface
}

func NewAiClient(cc grpc.ClientConnInterface) AiClient {
	return &aiClient{cc}
}

func (c *aiClient) QueryCorrect(ctx context.Context, in *QueryCorrectRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, Ai_QueryCorrect_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) FeedbackTrace(ctx context.Context, in *FeedbackTraceRequest, opts ...grpc.CallOption) (*FeedbackTraceReply, error) {
	out := new(FeedbackTraceReply)
	err := c.cc.Invoke(ctx, Ai_FeedbackTrace_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) ListQuestions(ctx context.Context, in *ListQuestionsRequest, opts ...grpc.CallOption) (*ListQuestionsReply, error) {
	out := new(ListQuestionsReply)
	err := c.cc.Invoke(ctx, Ai_ListQuestions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) GetQuestion(ctx context.Context, in *GetQuestionRequest, opts ...grpc.CallOption) (*Question, error) {
	out := new(Question)
	err := c.cc.Invoke(ctx, Ai_GetQuestion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) GetQuestionBatch(ctx context.Context, in *GetQuestionBatchRequest, opts ...grpc.CallOption) (*GetQuestionBatchReply, error) {
	out := new(GetQuestionBatchReply)
	err := c.cc.Invoke(ctx, Ai_GetQuestionBatch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) UpsertQuestion(ctx context.Context, in *UpsertQuestionRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, Ai_UpsertQuestion_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) UpdateQuestionWithKey(ctx context.Context, in *UpdateQuestionWithKeyRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, Ai_UpdateQuestionWithKey_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) GetQuestionStatus(ctx context.Context, in *GetQuestionStatusRequest, opts ...grpc.CallOption) (*GetQuestionStatusReply, error) {
	out := new(GetQuestionStatusReply)
	err := c.cc.Invoke(ctx, Ai_GetQuestionStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) CreateQuestionImport(ctx context.Context, in *CreateQuestionImportRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, Ai_CreateQuestionImport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) ListQuestionImport(ctx context.Context, in *ListQuestionImportRequest, opts ...grpc.CallOption) (*ListQuestionImportReply, error) {
	out := new(ListQuestionImportReply)
	err := c.cc.Invoke(ctx, Ai_ListQuestionImport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) QuestionImportAction(ctx context.Context, in *QuestionImportActionRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, Ai_QuestionImportAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) GetQuestionImportDetail(ctx context.Context, in *GetQuestionImportDetailRequest, opts ...grpc.CallOption) (*GetQuestionImportDetailReply, error) {
	out := new(GetQuestionImportDetailReply)
	err := c.cc.Invoke(ctx, Ai_GetQuestionImportDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileReply, error) {
	out := new(UploadFileReply)
	err := c.cc.Invoke(ctx, Ai_UploadFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) CommonConfig(ctx context.Context, in *CommonConfigReq, opts ...grpc.CallOption) (*CommonConfigResp, error) {
	out := new(CommonConfigResp)
	err := c.cc.Invoke(ctx, Ai_CommonConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) TaskDrive(ctx context.Context, in *TaskDriveReq, opts ...grpc.CallOption) (*TaskDriveResp, error) {
	out := new(TaskDriveResp)
	err := c.cc.Invoke(ctx, Ai_TaskDrive_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) BlogList(ctx context.Context, in *BlogListReq, opts ...grpc.CallOption) (*BlogListResp, error) {
	out := new(BlogListResp)
	err := c.cc.Invoke(ctx, Ai_BlogList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) BlogDetail(ctx context.Context, in *BlogDetailReq, opts ...grpc.CallOption) (*BlogDetailResp, error) {
	out := new(BlogDetailResp)
	err := c.cc.Invoke(ctx, Ai_BlogDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) BlogCategory(ctx context.Context, in *BlogCategoryReq, opts ...grpc.CallOption) (*BlogCategoryResp, error) {
	out := new(BlogCategoryResp)
	err := c.cc.Invoke(ctx, Ai_BlogCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) BlogFeedback(ctx context.Context, in *BlogFeedbackReq, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, Ai_BlogFeedback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) SetBlog(ctx context.Context, in *BlogArticle, opts ...grpc.CallOption) (*structpb.Struct, error) {
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, Ai_SetBlog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) EduTermList(ctx context.Context, in *EduTermListReq, opts ...grpc.CallOption) (*EduTermListResp, error) {
	out := new(EduTermListResp)
	err := c.cc.Invoke(ctx, Ai_EduTermList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) EduTermDetail(ctx context.Context, in *EduTermDetailReq, opts ...grpc.CallOption) (*EduTermDetailResp, error) {
	out := new(EduTermDetailResp)
	err := c.cc.Invoke(ctx, Ai_EduTermDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) CodeLogin(ctx context.Context, in *CodeLoginReq, opts ...grpc.CallOption) (*CodeLoginResp, error) {
	out := new(CodeLoginResp)
	err := c.cc.Invoke(ctx, Ai_CodeLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) LoginOut(ctx context.Context, in *LoginOutReq, opts ...grpc.CallOption) (*LoginOutResp, error) {
	out := new(LoginOutResp)
	err := c.cc.Invoke(ctx, Ai_LoginOut_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) CheckLogin(ctx context.Context, in *CheckLoginReq, opts ...grpc.CallOption) (*CheckLoginResp, error) {
	out := new(CheckLoginResp)
	err := c.cc.Invoke(ctx, Ai_CheckLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aiClient) CheckEmailExists(ctx context.Context, in *CheckEmailExistsReq, opts ...grpc.CallOption) (*CheckEmailExistsResp, error) {
	out := new(CheckEmailExistsResp)
	err := c.cc.Invoke(ctx, Ai_CheckEmailExists_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AiServer is the server API for Ai service.
// All implementations must embed UnimplementedAiServer
// for forward compatibility
type AiServer interface {
	QueryCorrect(context.Context, *QueryCorrectRequest) (*structpb.Struct, error)
	FeedbackTrace(context.Context, *FeedbackTraceRequest) (*FeedbackTraceReply, error)
	ListQuestions(context.Context, *ListQuestionsRequest) (*ListQuestionsReply, error)
	GetQuestion(context.Context, *GetQuestionRequest) (*Question, error)
	GetQuestionBatch(context.Context, *GetQuestionBatchRequest) (*GetQuestionBatchReply, error)
	UpsertQuestion(context.Context, *UpsertQuestionRequest) (*structpb.Struct, error)
	UpdateQuestionWithKey(context.Context, *UpdateQuestionWithKeyRequest) (*structpb.Struct, error)
	GetQuestionStatus(context.Context, *GetQuestionStatusRequest) (*GetQuestionStatusReply, error)
	// question_import create
	CreateQuestionImport(context.Context, *CreateQuestionImportRequest) (*structpb.Struct, error)
	// question_import list
	ListQuestionImport(context.Context, *ListQuestionImportRequest) (*ListQuestionImportReply, error)
	// question import action
	QuestionImportAction(context.Context, *QuestionImportActionRequest) (*structpb.Struct, error)
	// question import detail
	GetQuestionImportDetail(context.Context, *GetQuestionImportDetailRequest) (*GetQuestionImportDetailReply, error)
	UploadFile(context.Context, *UploadFileRequest) (*UploadFileReply, error)
	CommonConfig(context.Context, *CommonConfigReq) (*CommonConfigResp, error)
	TaskDrive(context.Context, *TaskDriveReq) (*TaskDriveResp, error)
	BlogList(context.Context, *BlogListReq) (*BlogListResp, error)
	BlogDetail(context.Context, *BlogDetailReq) (*BlogDetailResp, error)
	BlogCategory(context.Context, *BlogCategoryReq) (*BlogCategoryResp, error)
	BlogFeedback(context.Context, *BlogFeedbackReq) (*structpb.Struct, error)
	SetBlog(context.Context, *BlogArticle) (*structpb.Struct, error)
	EduTermList(context.Context, *EduTermListReq) (*EduTermListResp, error)
	EduTermDetail(context.Context, *EduTermDetailReq) (*EduTermDetailResp, error)
	CodeLogin(context.Context, *CodeLoginReq) (*CodeLoginResp, error)
	LoginOut(context.Context, *LoginOutReq) (*LoginOutResp, error)
	CheckLogin(context.Context, *CheckLoginReq) (*CheckLoginResp, error)
	// CheckEmailExists
	CheckEmailExists(context.Context, *CheckEmailExistsReq) (*CheckEmailExistsResp, error)
	mustEmbedUnimplementedAiServer()
}

// UnimplementedAiServer must be embedded to have forward compatible implementations.
type UnimplementedAiServer struct {
}

func (UnimplementedAiServer) QueryCorrect(context.Context, *QueryCorrectRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCorrect not implemented")
}
func (UnimplementedAiServer) FeedbackTrace(context.Context, *FeedbackTraceRequest) (*FeedbackTraceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FeedbackTrace not implemented")
}
func (UnimplementedAiServer) ListQuestions(context.Context, *ListQuestionsRequest) (*ListQuestionsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListQuestions not implemented")
}
func (UnimplementedAiServer) GetQuestion(context.Context, *GetQuestionRequest) (*Question, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQuestion not implemented")
}
func (UnimplementedAiServer) GetQuestionBatch(context.Context, *GetQuestionBatchRequest) (*GetQuestionBatchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQuestionBatch not implemented")
}
func (UnimplementedAiServer) UpsertQuestion(context.Context, *UpsertQuestionRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertQuestion not implemented")
}
func (UnimplementedAiServer) UpdateQuestionWithKey(context.Context, *UpdateQuestionWithKeyRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateQuestionWithKey not implemented")
}
func (UnimplementedAiServer) GetQuestionStatus(context.Context, *GetQuestionStatusRequest) (*GetQuestionStatusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQuestionStatus not implemented")
}
func (UnimplementedAiServer) CreateQuestionImport(context.Context, *CreateQuestionImportRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateQuestionImport not implemented")
}
func (UnimplementedAiServer) ListQuestionImport(context.Context, *ListQuestionImportRequest) (*ListQuestionImportReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListQuestionImport not implemented")
}
func (UnimplementedAiServer) QuestionImportAction(context.Context, *QuestionImportActionRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuestionImportAction not implemented")
}
func (UnimplementedAiServer) GetQuestionImportDetail(context.Context, *GetQuestionImportDetailRequest) (*GetQuestionImportDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQuestionImportDetail not implemented")
}
func (UnimplementedAiServer) UploadFile(context.Context, *UploadFileRequest) (*UploadFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadFile not implemented")
}
func (UnimplementedAiServer) CommonConfig(context.Context, *CommonConfigReq) (*CommonConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommonConfig not implemented")
}
func (UnimplementedAiServer) TaskDrive(context.Context, *TaskDriveReq) (*TaskDriveResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskDrive not implemented")
}
func (UnimplementedAiServer) BlogList(context.Context, *BlogListReq) (*BlogListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlogList not implemented")
}
func (UnimplementedAiServer) BlogDetail(context.Context, *BlogDetailReq) (*BlogDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlogDetail not implemented")
}
func (UnimplementedAiServer) BlogCategory(context.Context, *BlogCategoryReq) (*BlogCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlogCategory not implemented")
}
func (UnimplementedAiServer) BlogFeedback(context.Context, *BlogFeedbackReq) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlogFeedback not implemented")
}
func (UnimplementedAiServer) SetBlog(context.Context, *BlogArticle) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetBlog not implemented")
}
func (UnimplementedAiServer) EduTermList(context.Context, *EduTermListReq) (*EduTermListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EduTermList not implemented")
}
func (UnimplementedAiServer) EduTermDetail(context.Context, *EduTermDetailReq) (*EduTermDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EduTermDetail not implemented")
}
func (UnimplementedAiServer) CodeLogin(context.Context, *CodeLoginReq) (*CodeLoginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CodeLogin not implemented")
}
func (UnimplementedAiServer) LoginOut(context.Context, *LoginOutReq) (*LoginOutResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoginOut not implemented")
}
func (UnimplementedAiServer) CheckLogin(context.Context, *CheckLoginReq) (*CheckLoginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLogin not implemented")
}
func (UnimplementedAiServer) CheckEmailExists(context.Context, *CheckEmailExistsReq) (*CheckEmailExistsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckEmailExists not implemented")
}
func (UnimplementedAiServer) mustEmbedUnimplementedAiServer() {}

// UnsafeAiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AiServer will
// result in compilation errors.
type UnsafeAiServer interface {
	mustEmbedUnimplementedAiServer()
}

func RegisterAiServer(s grpc.ServiceRegistrar, srv AiServer) {
	s.RegisterService(&Ai_ServiceDesc, srv)
}

func _Ai_QueryCorrect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCorrectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).QueryCorrect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_QueryCorrect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).QueryCorrect(ctx, req.(*QueryCorrectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_FeedbackTrace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FeedbackTraceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).FeedbackTrace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_FeedbackTrace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).FeedbackTrace(ctx, req.(*FeedbackTraceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_ListQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListQuestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).ListQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_ListQuestions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).ListQuestions(ctx, req.(*ListQuestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_GetQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuestionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).GetQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_GetQuestion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).GetQuestion(ctx, req.(*GetQuestionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_GetQuestionBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuestionBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).GetQuestionBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_GetQuestionBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).GetQuestionBatch(ctx, req.(*GetQuestionBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_UpsertQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertQuestionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).UpsertQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_UpsertQuestion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).UpsertQuestion(ctx, req.(*UpsertQuestionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_UpdateQuestionWithKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateQuestionWithKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).UpdateQuestionWithKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_UpdateQuestionWithKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).UpdateQuestionWithKey(ctx, req.(*UpdateQuestionWithKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_GetQuestionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuestionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).GetQuestionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_GetQuestionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).GetQuestionStatus(ctx, req.(*GetQuestionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_CreateQuestionImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateQuestionImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).CreateQuestionImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_CreateQuestionImport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).CreateQuestionImport(ctx, req.(*CreateQuestionImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_ListQuestionImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListQuestionImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).ListQuestionImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_ListQuestionImport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).ListQuestionImport(ctx, req.(*ListQuestionImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_QuestionImportAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuestionImportActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).QuestionImportAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_QuestionImportAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).QuestionImportAction(ctx, req.(*QuestionImportActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_GetQuestionImportDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuestionImportDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).GetQuestionImportDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_GetQuestionImportDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).GetQuestionImportDetail(ctx, req.(*GetQuestionImportDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_UploadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).UploadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_UploadFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).UploadFile(ctx, req.(*UploadFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_CommonConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).CommonConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_CommonConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).CommonConfig(ctx, req.(*CommonConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_TaskDrive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskDriveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).TaskDrive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_TaskDrive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).TaskDrive(ctx, req.(*TaskDriveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_BlogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlogListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).BlogList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_BlogList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).BlogList(ctx, req.(*BlogListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_BlogDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlogDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).BlogDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_BlogDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).BlogDetail(ctx, req.(*BlogDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_BlogCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlogCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).BlogCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_BlogCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).BlogCategory(ctx, req.(*BlogCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_BlogFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlogFeedbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).BlogFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_BlogFeedback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).BlogFeedback(ctx, req.(*BlogFeedbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_SetBlog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlogArticle)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).SetBlog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_SetBlog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).SetBlog(ctx, req.(*BlogArticle))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_EduTermList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EduTermListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).EduTermList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_EduTermList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).EduTermList(ctx, req.(*EduTermListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_EduTermDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EduTermDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).EduTermDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_EduTermDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).EduTermDetail(ctx, req.(*EduTermDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_CodeLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CodeLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).CodeLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_CodeLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).CodeLogin(ctx, req.(*CodeLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_LoginOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginOutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).LoginOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_LoginOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).LoginOut(ctx, req.(*LoginOutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_CheckLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).CheckLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_CheckLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).CheckLogin(ctx, req.(*CheckLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ai_CheckEmailExists_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckEmailExistsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AiServer).CheckEmailExists(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ai_CheckEmailExists_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AiServer).CheckEmailExists(ctx, req.(*CheckEmailExistsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Ai_ServiceDesc is the grpc.ServiceDesc for Ai service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Ai_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.ai.v1.Ai",
	HandlerType: (*AiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryCorrect",
			Handler:    _Ai_QueryCorrect_Handler,
		},
		{
			MethodName: "FeedbackTrace",
			Handler:    _Ai_FeedbackTrace_Handler,
		},
		{
			MethodName: "ListQuestions",
			Handler:    _Ai_ListQuestions_Handler,
		},
		{
			MethodName: "GetQuestion",
			Handler:    _Ai_GetQuestion_Handler,
		},
		{
			MethodName: "GetQuestionBatch",
			Handler:    _Ai_GetQuestionBatch_Handler,
		},
		{
			MethodName: "UpsertQuestion",
			Handler:    _Ai_UpsertQuestion_Handler,
		},
		{
			MethodName: "UpdateQuestionWithKey",
			Handler:    _Ai_UpdateQuestionWithKey_Handler,
		},
		{
			MethodName: "GetQuestionStatus",
			Handler:    _Ai_GetQuestionStatus_Handler,
		},
		{
			MethodName: "CreateQuestionImport",
			Handler:    _Ai_CreateQuestionImport_Handler,
		},
		{
			MethodName: "ListQuestionImport",
			Handler:    _Ai_ListQuestionImport_Handler,
		},
		{
			MethodName: "QuestionImportAction",
			Handler:    _Ai_QuestionImportAction_Handler,
		},
		{
			MethodName: "GetQuestionImportDetail",
			Handler:    _Ai_GetQuestionImportDetail_Handler,
		},
		{
			MethodName: "UploadFile",
			Handler:    _Ai_UploadFile_Handler,
		},
		{
			MethodName: "CommonConfig",
			Handler:    _Ai_CommonConfig_Handler,
		},
		{
			MethodName: "TaskDrive",
			Handler:    _Ai_TaskDrive_Handler,
		},
		{
			MethodName: "BlogList",
			Handler:    _Ai_BlogList_Handler,
		},
		{
			MethodName: "BlogDetail",
			Handler:    _Ai_BlogDetail_Handler,
		},
		{
			MethodName: "BlogCategory",
			Handler:    _Ai_BlogCategory_Handler,
		},
		{
			MethodName: "BlogFeedback",
			Handler:    _Ai_BlogFeedback_Handler,
		},
		{
			MethodName: "SetBlog",
			Handler:    _Ai_SetBlog_Handler,
		},
		{
			MethodName: "EduTermList",
			Handler:    _Ai_EduTermList_Handler,
		},
		{
			MethodName: "EduTermDetail",
			Handler:    _Ai_EduTermDetail_Handler,
		},
		{
			MethodName: "CodeLogin",
			Handler:    _Ai_CodeLogin_Handler,
		},
		{
			MethodName: "LoginOut",
			Handler:    _Ai_LoginOut_Handler,
		},
		{
			MethodName: "CheckLogin",
			Handler:    _Ai_CheckLogin_Handler,
		},
		{
			MethodName: "CheckEmailExists",
			Handler:    _Ai_CheckEmailExists_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/ai/v1/ai.proto",
}
