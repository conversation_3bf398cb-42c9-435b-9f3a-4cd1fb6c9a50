syntax = "proto3";

package api.paas.v1;
import "errors/errors.proto";

option go_package = "hw-paas-service/ai/v1;v1";
option java_multiple_files = true;
option java_package = "api.ai.v1";


enum ErrorReason {
  option (errors.default_code) = 500;
  // 命名规范 服务名称_错误  error.code 尽量使用http.code来标识且符合code码含义, 需要备注什么错误，方便定位
  HW_PAAS_APPID_ERROR = 0 [(errors.code) = 403];
  HW_PAAS_SIGN_ERROR = 1 [(errors.code) = 403];
  HW_PAAS_PARAM_ERROR = 2 [(errors.code) = 400];
  HW_PAAS_NOT_FOUND_ERROR = 3 [(errors.code) = 404]; //资源未找到
  HW_PAAS_UNEXCEPT_ERROR = 4 [(errors.code) = 200]; //未知错误
  HW_PAAS_THIRD_PART_ERROR = 5 [(errors.code) = 200]; //第三方接口报错
  HW_PAAS_CORRECT_ERROR = 6 [(errors.code) = 400];//口算批改接口错误
  HW_PAAS_CORRECT_FUZZY_ERROR = 7 [(errors.code) = 400];//口算批改-照片不清晰接口错误
  HW_PAAS_FINGER_OCR_ERROR = 8 [(errors.code) = 200];//指尖查词OCR失败
  HW_PAAS_QUESTION_IS_BAND = 9 [(errors.code) = 200];//题目已被绑定
  HW_PAAS_DEFAULT_ERR = 10 [(errors.code) = 200];//默认业务错误
  HW_PAAS_UNAUTHORIZED = 11 [(errors.code) = 401]; //鉴权失败
}
