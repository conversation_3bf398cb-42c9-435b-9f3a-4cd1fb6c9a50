// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/ai/v1/ai.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CheckEmailExistsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckEmailExistsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckEmailExistsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckEmailExistsReqMultiError, or nil if none found.
func (m *CheckEmailExistsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEmailExistsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetEmail()) < 1 {
		err := CheckEmailExistsReqValidationError{
			field:  "Email",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckEmailExistsReqMultiError(errors)
	}

	return nil
}

// CheckEmailExistsReqMultiError is an error wrapping multiple validation
// errors returned by CheckEmailExistsReq.ValidateAll() if the designated
// constraints aren't met.
type CheckEmailExistsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEmailExistsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEmailExistsReqMultiError) AllErrors() []error { return m }

// CheckEmailExistsReqValidationError is the validation error returned by
// CheckEmailExistsReq.Validate if the designated constraints aren't met.
type CheckEmailExistsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEmailExistsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEmailExistsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEmailExistsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEmailExistsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEmailExistsReqValidationError) ErrorName() string {
	return "CheckEmailExistsReqValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEmailExistsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEmailExistsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEmailExistsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEmailExistsReqValidationError{}

// Validate checks the field values on CheckEmailExistsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckEmailExistsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckEmailExistsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckEmailExistsRespMultiError, or nil if none found.
func (m *CheckEmailExistsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckEmailExistsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Exists

	if len(errors) > 0 {
		return CheckEmailExistsRespMultiError(errors)
	}

	return nil
}

// CheckEmailExistsRespMultiError is an error wrapping multiple validation
// errors returned by CheckEmailExistsResp.ValidateAll() if the designated
// constraints aren't met.
type CheckEmailExistsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckEmailExistsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckEmailExistsRespMultiError) AllErrors() []error { return m }

// CheckEmailExistsRespValidationError is the validation error returned by
// CheckEmailExistsResp.Validate if the designated constraints aren't met.
type CheckEmailExistsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckEmailExistsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckEmailExistsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckEmailExistsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckEmailExistsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckEmailExistsRespValidationError) ErrorName() string {
	return "CheckEmailExistsRespValidationError"
}

// Error satisfies the builtin error interface
func (e CheckEmailExistsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckEmailExistsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckEmailExistsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckEmailExistsRespValidationError{}

// Validate checks the field values on CheckLoginReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckLoginReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckLoginReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CheckLoginReqMultiError, or
// nil if none found.
func (m *CheckLoginReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckLoginReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTalToken()) < 1 {
		err := CheckLoginReqValidationError{
			field:  "TalToken",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckLoginReqMultiError(errors)
	}

	return nil
}

// CheckLoginReqMultiError is an error wrapping multiple validation errors
// returned by CheckLoginReq.ValidateAll() if the designated constraints
// aren't met.
type CheckLoginReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckLoginReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckLoginReqMultiError) AllErrors() []error { return m }

// CheckLoginReqValidationError is the validation error returned by
// CheckLoginReq.Validate if the designated constraints aren't met.
type CheckLoginReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckLoginReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckLoginReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckLoginReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckLoginReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckLoginReqValidationError) ErrorName() string { return "CheckLoginReqValidationError" }

// Error satisfies the builtin error interface
func (e CheckLoginReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckLoginReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckLoginReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckLoginReqValidationError{}

// Validate checks the field values on CheckLoginResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckLoginResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckLoginResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CheckLoginRespMultiError,
// or nil if none found.
func (m *CheckLoginResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckLoginResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TalId

	// no validation rules for TalToken

	// no validation rules for Expired

	// no validation rules for Life

	if len(errors) > 0 {
		return CheckLoginRespMultiError(errors)
	}

	return nil
}

// CheckLoginRespMultiError is an error wrapping multiple validation errors
// returned by CheckLoginResp.ValidateAll() if the designated constraints
// aren't met.
type CheckLoginRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckLoginRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckLoginRespMultiError) AllErrors() []error { return m }

// CheckLoginRespValidationError is the validation error returned by
// CheckLoginResp.Validate if the designated constraints aren't met.
type CheckLoginRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckLoginRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckLoginRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckLoginRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckLoginRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckLoginRespValidationError) ErrorName() string { return "CheckLoginRespValidationError" }

// Error satisfies the builtin error interface
func (e CheckLoginRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckLoginResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckLoginRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckLoginRespValidationError{}

// Validate checks the field values on LoginOutReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginOutReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginOutReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginOutReqMultiError, or
// nil if none found.
func (m *LoginOutReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginOutReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTalToken()) < 1 {
		err := LoginOutReqValidationError{
			field:  "TalToken",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return LoginOutReqMultiError(errors)
	}

	return nil
}

// LoginOutReqMultiError is an error wrapping multiple validation errors
// returned by LoginOutReq.ValidateAll() if the designated constraints aren't met.
type LoginOutReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginOutReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginOutReqMultiError) AllErrors() []error { return m }

// LoginOutReqValidationError is the validation error returned by
// LoginOutReq.Validate if the designated constraints aren't met.
type LoginOutReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginOutReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginOutReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginOutReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginOutReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginOutReqValidationError) ErrorName() string { return "LoginOutReqValidationError" }

// Error satisfies the builtin error interface
func (e LoginOutReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginOutReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginOutReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginOutReqValidationError{}

// Validate checks the field values on LoginOutResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginOutResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginOutResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginOutRespMultiError, or
// nil if none found.
func (m *LoginOutResp) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginOutResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Msg

	if len(errors) > 0 {
		return LoginOutRespMultiError(errors)
	}

	return nil
}

// LoginOutRespMultiError is an error wrapping multiple validation errors
// returned by LoginOutResp.ValidateAll() if the designated constraints aren't met.
type LoginOutRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginOutRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginOutRespMultiError) AllErrors() []error { return m }

// LoginOutRespValidationError is the validation error returned by
// LoginOutResp.Validate if the designated constraints aren't met.
type LoginOutRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginOutRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginOutRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginOutRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginOutRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginOutRespValidationError) ErrorName() string { return "LoginOutRespValidationError" }

// Error satisfies the builtin error interface
func (e LoginOutRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginOutResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginOutRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginOutRespValidationError{}

// Validate checks the field values on CodeLoginReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CodeLoginReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CodeLoginReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CodeLoginReqMultiError, or
// nil if none found.
func (m *CodeLoginReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CodeLoginReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCode()) < 1 {
		err := CodeLoginReqValidationError{
			field:  "Code",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CodeLoginReqMultiError(errors)
	}

	return nil
}

// CodeLoginReqMultiError is an error wrapping multiple validation errors
// returned by CodeLoginReq.ValidateAll() if the designated constraints aren't met.
type CodeLoginReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CodeLoginReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CodeLoginReqMultiError) AllErrors() []error { return m }

// CodeLoginReqValidationError is the validation error returned by
// CodeLoginReq.Validate if the designated constraints aren't met.
type CodeLoginReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CodeLoginReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CodeLoginReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CodeLoginReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CodeLoginReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CodeLoginReqValidationError) ErrorName() string { return "CodeLoginReqValidationError" }

// Error satisfies the builtin error interface
func (e CodeLoginReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCodeLoginReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CodeLoginReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CodeLoginReqValidationError{}

// Validate checks the field values on CodeLoginResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CodeLoginResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CodeLoginResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CodeLoginRespMultiError, or
// nil if none found.
func (m *CodeLoginResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CodeLoginResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TalId

	// no validation rules for TalToken

	// no validation rules for Expired

	// no validation rules for Life

	if len(errors) > 0 {
		return CodeLoginRespMultiError(errors)
	}

	return nil
}

// CodeLoginRespMultiError is an error wrapping multiple validation errors
// returned by CodeLoginResp.ValidateAll() if the designated constraints
// aren't met.
type CodeLoginRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CodeLoginRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CodeLoginRespMultiError) AllErrors() []error { return m }

// CodeLoginRespValidationError is the validation error returned by
// CodeLoginResp.Validate if the designated constraints aren't met.
type CodeLoginRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CodeLoginRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CodeLoginRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CodeLoginRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CodeLoginRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CodeLoginRespValidationError) ErrorName() string { return "CodeLoginRespValidationError" }

// Error satisfies the builtin error interface
func (e CodeLoginRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCodeLoginResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CodeLoginRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CodeLoginRespValidationError{}

// Validate checks the field values on TaskDriveReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskDriveReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskDriveReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskDriveReqMultiError, or
// nil if none found.
func (m *TaskDriveReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskDriveReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetApp()) < 1 {
		err := TaskDriveReqValidationError{
			field:  "App",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return TaskDriveReqMultiError(errors)
	}

	return nil
}

// TaskDriveReqMultiError is an error wrapping multiple validation errors
// returned by TaskDriveReq.ValidateAll() if the designated constraints aren't met.
type TaskDriveReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskDriveReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskDriveReqMultiError) AllErrors() []error { return m }

// TaskDriveReqValidationError is the validation error returned by
// TaskDriveReq.Validate if the designated constraints aren't met.
type TaskDriveReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskDriveReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskDriveReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskDriveReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskDriveReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskDriveReqValidationError) ErrorName() string { return "TaskDriveReqValidationError" }

// Error satisfies the builtin error interface
func (e TaskDriveReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskDriveReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskDriveReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskDriveReqValidationError{}

// Validate checks the field values on TaskDriveResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TaskDriveResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TaskDriveResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TaskDriveRespMultiError, or
// nil if none found.
func (m *TaskDriveResp) ValidateAll() error {
	return m.validate(true)
}

func (m *TaskDriveResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TaskDriveRespMultiError(errors)
	}

	return nil
}

// TaskDriveRespMultiError is an error wrapping multiple validation errors
// returned by TaskDriveResp.ValidateAll() if the designated constraints
// aren't met.
type TaskDriveRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TaskDriveRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TaskDriveRespMultiError) AllErrors() []error { return m }

// TaskDriveRespValidationError is the validation error returned by
// TaskDriveResp.Validate if the designated constraints aren't met.
type TaskDriveRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TaskDriveRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TaskDriveRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TaskDriveRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TaskDriveRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TaskDriveRespValidationError) ErrorName() string { return "TaskDriveRespValidationError" }

// Error satisfies the builtin error interface
func (e TaskDriveRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTaskDriveResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TaskDriveRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TaskDriveRespValidationError{}

// Validate checks the field values on CommonConfigReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CommonConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonConfigReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CommonConfigReqMultiError, or nil if none found.
func (m *CommonConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CommonConfigReqMultiError(errors)
	}

	return nil
}

// CommonConfigReqMultiError is an error wrapping multiple validation errors
// returned by CommonConfigReq.ValidateAll() if the designated constraints
// aren't met.
type CommonConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonConfigReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonConfigReqMultiError) AllErrors() []error { return m }

// CommonConfigReqValidationError is the validation error returned by
// CommonConfigReq.Validate if the designated constraints aren't met.
type CommonConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonConfigReqValidationError) ErrorName() string { return "CommonConfigReqValidationError" }

// Error satisfies the builtin error interface
func (e CommonConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonConfigReqValidationError{}

// Validate checks the field values on CommonConfigResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CommonConfigResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonConfigResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CommonConfigRespMultiError, or nil if none found.
func (m *CommonConfigResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonConfigResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRanges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CommonConfigRespValidationError{
						field:  fmt.Sprintf("Ranges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CommonConfigRespValidationError{
						field:  fmt.Sprintf("Ranges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CommonConfigRespValidationError{
					field:  fmt.Sprintf("Ranges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RightLine

	if len(errors) > 0 {
		return CommonConfigRespMultiError(errors)
	}

	return nil
}

// CommonConfigRespMultiError is an error wrapping multiple validation errors
// returned by CommonConfigResp.ValidateAll() if the designated constraints
// aren't met.
type CommonConfigRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonConfigRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonConfigRespMultiError) AllErrors() []error { return m }

// CommonConfigRespValidationError is the validation error returned by
// CommonConfigResp.Validate if the designated constraints aren't met.
type CommonConfigRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonConfigRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonConfigRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonConfigRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonConfigRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonConfigRespValidationError) ErrorName() string { return "CommonConfigRespValidationError" }

// Error satisfies the builtin error interface
func (e CommonConfigRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonConfigResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonConfigRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonConfigRespValidationError{}

// Validate checks the field values on Range with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Range) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Range with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RangeMultiError, or nil if none found.
func (m *Range) ValidateAll() error {
	return m.validate(true)
}

func (m *Range) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Word

	// no validation rules for Min

	// no validation rules for Max

	// no validation rules for IncludeMin

	// no validation rules for IncludeMax

	if len(errors) > 0 {
		return RangeMultiError(errors)
	}

	return nil
}

// RangeMultiError is an error wrapping multiple validation errors returned by
// Range.ValidateAll() if the designated constraints aren't met.
type RangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RangeMultiError) AllErrors() []error { return m }

// RangeValidationError is the validation error returned by Range.Validate if
// the designated constraints aren't met.
type RangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RangeValidationError) ErrorName() string { return "RangeValidationError" }

// Error satisfies the builtin error interface
func (e RangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RangeValidationError{}

// Validate checks the field values on FeedbackTraceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FeedbackTraceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeedbackTraceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeedbackTraceRequestMultiError, or nil if none found.
func (m *FeedbackTraceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FeedbackTraceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TraceId

	// no validation rules for Feedback

	// no validation rules for Biz

	if len(errors) > 0 {
		return FeedbackTraceRequestMultiError(errors)
	}

	return nil
}

// FeedbackTraceRequestMultiError is an error wrapping multiple validation
// errors returned by FeedbackTraceRequest.ValidateAll() if the designated
// constraints aren't met.
type FeedbackTraceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeedbackTraceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeedbackTraceRequestMultiError) AllErrors() []error { return m }

// FeedbackTraceRequestValidationError is the validation error returned by
// FeedbackTraceRequest.Validate if the designated constraints aren't met.
type FeedbackTraceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeedbackTraceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeedbackTraceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeedbackTraceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeedbackTraceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeedbackTraceRequestValidationError) ErrorName() string {
	return "FeedbackTraceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FeedbackTraceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeedbackTraceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeedbackTraceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeedbackTraceRequestValidationError{}

// Validate checks the field values on FeedbackTraceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FeedbackTraceReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeedbackTraceReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeedbackTraceReplyMultiError, or nil if none found.
func (m *FeedbackTraceReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FeedbackTraceReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FeedbackTraceReplyMultiError(errors)
	}

	return nil
}

// FeedbackTraceReplyMultiError is an error wrapping multiple validation errors
// returned by FeedbackTraceReply.ValidateAll() if the designated constraints
// aren't met.
type FeedbackTraceReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeedbackTraceReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeedbackTraceReplyMultiError) AllErrors() []error { return m }

// FeedbackTraceReplyValidationError is the validation error returned by
// FeedbackTraceReply.Validate if the designated constraints aren't met.
type FeedbackTraceReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeedbackTraceReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeedbackTraceReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeedbackTraceReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeedbackTraceReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeedbackTraceReplyValidationError) ErrorName() string {
	return "FeedbackTraceReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FeedbackTraceReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeedbackTraceReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeedbackTraceReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeedbackTraceReplyValidationError{}

// Validate checks the field values on QueryCorrectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryCorrectRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryCorrectRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryCorrectRequestMultiError, or nil if none found.
func (m *QueryCorrectRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryCorrectRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_QueryCorrectRequest_ImageUrl_Pattern.MatchString(m.GetImageUrl()) {
		err := QueryCorrectRequestValidationError{
			field:  "ImageUrl",
			reason: "value does not match regex pattern \"^https?://.*\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return QueryCorrectRequestMultiError(errors)
	}

	return nil
}

// QueryCorrectRequestMultiError is an error wrapping multiple validation
// errors returned by QueryCorrectRequest.ValidateAll() if the designated
// constraints aren't met.
type QueryCorrectRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryCorrectRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryCorrectRequestMultiError) AllErrors() []error { return m }

// QueryCorrectRequestValidationError is the validation error returned by
// QueryCorrectRequest.Validate if the designated constraints aren't met.
type QueryCorrectRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryCorrectRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryCorrectRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryCorrectRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryCorrectRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryCorrectRequestValidationError) ErrorName() string {
	return "QueryCorrectRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryCorrectRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryCorrectRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryCorrectRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryCorrectRequestValidationError{}

var _QueryCorrectRequest_ImageUrl_Pattern = regexp.MustCompile("^https?://.*")

// Validate checks the field values on Empty with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Empty) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Empty with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EmptyMultiError, or nil if none found.
func (m *Empty) ValidateAll() error {
	return m.validate(true)
}

func (m *Empty) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EmptyMultiError(errors)
	}

	return nil
}

// EmptyMultiError is an error wrapping multiple validation errors returned by
// Empty.ValidateAll() if the designated constraints aren't met.
type EmptyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmptyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmptyMultiError) AllErrors() []error { return m }

// EmptyValidationError is the validation error returned by Empty.Validate if
// the designated constraints aren't met.
type EmptyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmptyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmptyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmptyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmptyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmptyValidationError) ErrorName() string { return "EmptyValidationError" }

// Error satisfies the builtin error interface
func (e EmptyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmpty.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmptyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmptyValidationError{}

// Validate checks the field values on ListQuestionsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListQuestionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListQuestionsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListQuestionsRequestMultiError, or nil if none found.
func (m *ListQuestionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListQuestionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for QType

	// no validation rules for Difficulty

	// no validation rules for Status

	// no validation rules for Grade

	// no validation rules for Subject

	// no validation rules for Knowledge

	// no validation rules for KnowledgeNo

	// no validation rules for Content

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return ListQuestionsRequestMultiError(errors)
	}

	return nil
}

// ListQuestionsRequestMultiError is an error wrapping multiple validation
// errors returned by ListQuestionsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListQuestionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListQuestionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListQuestionsRequestMultiError) AllErrors() []error { return m }

// ListQuestionsRequestValidationError is the validation error returned by
// ListQuestionsRequest.Validate if the designated constraints aren't met.
type ListQuestionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListQuestionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListQuestionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListQuestionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListQuestionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListQuestionsRequestValidationError) ErrorName() string {
	return "ListQuestionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListQuestionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListQuestionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListQuestionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListQuestionsRequestValidationError{}

// Validate checks the field values on ListQuestionsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListQuestionsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListQuestionsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListQuestionsReplyMultiError, or nil if none found.
func (m *ListQuestionsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListQuestionsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListQuestionsReplyValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListQuestionsReplyValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListQuestionsReplyValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return ListQuestionsReplyMultiError(errors)
	}

	return nil
}

// ListQuestionsReplyMultiError is an error wrapping multiple validation errors
// returned by ListQuestionsReply.ValidateAll() if the designated constraints
// aren't met.
type ListQuestionsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListQuestionsReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListQuestionsReplyMultiError) AllErrors() []error { return m }

// ListQuestionsReplyValidationError is the validation error returned by
// ListQuestionsReply.Validate if the designated constraints aren't met.
type ListQuestionsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListQuestionsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListQuestionsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListQuestionsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListQuestionsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListQuestionsReplyValidationError) ErrorName() string {
	return "ListQuestionsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListQuestionsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListQuestionsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListQuestionsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListQuestionsReplyValidationError{}

// Validate checks the field values on GetQuestionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQuestionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQuestionRequestMultiError, or nil if none found.
func (m *GetQuestionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetQuestionRequestMultiError(errors)
	}

	return nil
}

// GetQuestionRequestMultiError is an error wrapping multiple validation errors
// returned by GetQuestionRequest.ValidateAll() if the designated constraints
// aren't met.
type GetQuestionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionRequestMultiError) AllErrors() []error { return m }

// GetQuestionRequestValidationError is the validation error returned by
// GetQuestionRequest.Validate if the designated constraints aren't met.
type GetQuestionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionRequestValidationError) ErrorName() string {
	return "GetQuestionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionRequestValidationError{}

// Validate checks the field values on GetQuestionBatchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQuestionBatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionBatchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQuestionBatchRequestMultiError, or nil if none found.
func (m *GetQuestionBatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionBatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetQuestionBatchRequestMultiError(errors)
	}

	return nil
}

// GetQuestionBatchRequestMultiError is an error wrapping multiple validation
// errors returned by GetQuestionBatchRequest.ValidateAll() if the designated
// constraints aren't met.
type GetQuestionBatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionBatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionBatchRequestMultiError) AllErrors() []error { return m }

// GetQuestionBatchRequestValidationError is the validation error returned by
// GetQuestionBatchRequest.Validate if the designated constraints aren't met.
type GetQuestionBatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionBatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionBatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionBatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionBatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionBatchRequestValidationError) ErrorName() string {
	return "GetQuestionBatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionBatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionBatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionBatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionBatchRequestValidationError{}

// Validate checks the field values on GetQuestionBatchReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQuestionBatchReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionBatchReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQuestionBatchReplyMultiError, or nil if none found.
func (m *GetQuestionBatchReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionBatchReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetQuestionBatchReplyValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetQuestionBatchReplyValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetQuestionBatchReplyValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetQuestionBatchReplyMultiError(errors)
	}

	return nil
}

// GetQuestionBatchReplyMultiError is an error wrapping multiple validation
// errors returned by GetQuestionBatchReply.ValidateAll() if the designated
// constraints aren't met.
type GetQuestionBatchReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionBatchReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionBatchReplyMultiError) AllErrors() []error { return m }

// GetQuestionBatchReplyValidationError is the validation error returned by
// GetQuestionBatchReply.Validate if the designated constraints aren't met.
type GetQuestionBatchReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionBatchReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionBatchReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionBatchReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionBatchReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionBatchReplyValidationError) ErrorName() string {
	return "GetQuestionBatchReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionBatchReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionBatchReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionBatchReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionBatchReplyValidationError{}

// Validate checks the field values on UpsertQuestionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpsertQuestionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertQuestionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpsertQuestionRequestMultiError, or nil if none found.
func (m *UpsertQuestionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertQuestionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for QType

	// no validation rules for Grade

	// no validation rules for Subject

	// no validation rules for Difficulty

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for Solution

	// no validation rules for Knowledge

	// no validation rules for KnowledgeNo

	// no validation rules for Note

	// no validation rules for Status

	if len(errors) > 0 {
		return UpsertQuestionRequestMultiError(errors)
	}

	return nil
}

// UpsertQuestionRequestMultiError is an error wrapping multiple validation
// errors returned by UpsertQuestionRequest.ValidateAll() if the designated
// constraints aren't met.
type UpsertQuestionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertQuestionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertQuestionRequestMultiError) AllErrors() []error { return m }

// UpsertQuestionRequestValidationError is the validation error returned by
// UpsertQuestionRequest.Validate if the designated constraints aren't met.
type UpsertQuestionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertQuestionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertQuestionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertQuestionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertQuestionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertQuestionRequestValidationError) ErrorName() string {
	return "UpsertQuestionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertQuestionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertQuestionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertQuestionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertQuestionRequestValidationError{}

// Validate checks the field values on UpdateQuestionWithKeyRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateQuestionWithKeyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateQuestionWithKeyRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateQuestionWithKeyRequestMultiError, or nil if none found.
func (m *UpdateQuestionWithKeyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateQuestionWithKeyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Fields

	if len(errors) > 0 {
		return UpdateQuestionWithKeyRequestMultiError(errors)
	}

	return nil
}

// UpdateQuestionWithKeyRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateQuestionWithKeyRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateQuestionWithKeyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateQuestionWithKeyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateQuestionWithKeyRequestMultiError) AllErrors() []error { return m }

// UpdateQuestionWithKeyRequestValidationError is the validation error returned
// by UpdateQuestionWithKeyRequest.Validate if the designated constraints
// aren't met.
type UpdateQuestionWithKeyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateQuestionWithKeyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateQuestionWithKeyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateQuestionWithKeyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateQuestionWithKeyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateQuestionWithKeyRequestValidationError) ErrorName() string {
	return "UpdateQuestionWithKeyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateQuestionWithKeyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateQuestionWithKeyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateQuestionWithKeyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateQuestionWithKeyRequestValidationError{}

// Validate checks the field values on Question with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Question) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Question with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QuestionMultiError, or nil
// if none found.
func (m *Question) ValidateAll() error {
	return m.validate(true)
}

func (m *Question) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for QType

	// no validation rules for Grade

	// no validation rules for Subject

	// no validation rules for Difficulty

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for Solution

	// no validation rules for Knowledge

	// no validation rules for KnowledgeNo

	// no validation rules for Note

	// no validation rules for Status

	if len(errors) > 0 {
		return QuestionMultiError(errors)
	}

	return nil
}

// QuestionMultiError is an error wrapping multiple validation errors returned
// by Question.ValidateAll() if the designated constraints aren't met.
type QuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionMultiError) AllErrors() []error { return m }

// QuestionValidationError is the validation error returned by
// Question.Validate if the designated constraints aren't met.
type QuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionValidationError) ErrorName() string { return "QuestionValidationError" }

// Error satisfies the builtin error interface
func (e QuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionValidationError{}

// Validate checks the field values on GetQuestionStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQuestionStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQuestionStatusRequestMultiError, or nil if none found.
func (m *GetQuestionStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ids

	if len(errors) > 0 {
		return GetQuestionStatusRequestMultiError(errors)
	}

	return nil
}

// GetQuestionStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetQuestionStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetQuestionStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionStatusRequestMultiError) AllErrors() []error { return m }

// GetQuestionStatusRequestValidationError is the validation error returned by
// GetQuestionStatusRequest.Validate if the designated constraints aren't met.
type GetQuestionStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionStatusRequestValidationError) ErrorName() string {
	return "GetQuestionStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionStatusRequestValidationError{}

// Validate checks the field values on GetQuestionStatusReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQuestionStatusReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionStatusReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQuestionStatusReplyMultiError, or nil if none found.
func (m *GetQuestionStatusReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionStatusReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetQuestionStatusReplyValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetQuestionStatusReplyValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetQuestionStatusReplyValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetQuestionStatusReplyMultiError(errors)
	}

	return nil
}

// GetQuestionStatusReplyMultiError is an error wrapping multiple validation
// errors returned by GetQuestionStatusReply.ValidateAll() if the designated
// constraints aren't met.
type GetQuestionStatusReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionStatusReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionStatusReplyMultiError) AllErrors() []error { return m }

// GetQuestionStatusReplyValidationError is the validation error returned by
// GetQuestionStatusReply.Validate if the designated constraints aren't met.
type GetQuestionStatusReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionStatusReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionStatusReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionStatusReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionStatusReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionStatusReplyValidationError) ErrorName() string {
	return "GetQuestionStatusReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionStatusReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionStatusReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionStatusReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionStatusReplyValidationError{}

// Validate checks the field values on UploadFileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UploadFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadFileRequestMultiError, or nil if none found.
func (m *UploadFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetFile()) < 1 {
		err := UploadFileRequestValidationError{
			field:  "File",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSuffix()) < 1 {
		err := UploadFileRequestValidationError{
			field:  "Suffix",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UploadFileRequestMultiError(errors)
	}

	return nil
}

// UploadFileRequestMultiError is an error wrapping multiple validation errors
// returned by UploadFileRequest.ValidateAll() if the designated constraints
// aren't met.
type UploadFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileRequestMultiError) AllErrors() []error { return m }

// UploadFileRequestValidationError is the validation error returned by
// UploadFileRequest.Validate if the designated constraints aren't met.
type UploadFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileRequestValidationError) ErrorName() string {
	return "UploadFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileRequestValidationError{}

// Validate checks the field values on UploadFileReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UploadFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFileReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadFileReplyMultiError, or nil if none found.
func (m *UploadFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return UploadFileReplyMultiError(errors)
	}

	return nil
}

// UploadFileReplyMultiError is an error wrapping multiple validation errors
// returned by UploadFileReply.ValidateAll() if the designated constraints
// aren't met.
type UploadFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileReplyMultiError) AllErrors() []error { return m }

// UploadFileReplyValidationError is the validation error returned by
// UploadFileReply.Validate if the designated constraints aren't met.
type UploadFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileReplyValidationError) ErrorName() string { return "UploadFileReplyValidationError" }

// Error satisfies the builtin error interface
func (e UploadFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileReplyValidationError{}

// Validate checks the field values on CreateQuestionImportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateQuestionImportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateQuestionImportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateQuestionImportRequestMultiError, or nil if none found.
func (m *CreateQuestionImportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateQuestionImportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetFileName()) < 1 {
		err := CreateQuestionImportRequestValidationError{
			field:  "FileName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateQuestionImportRequest_FileUrl_Pattern.MatchString(m.GetFileUrl()) {
		err := CreateQuestionImportRequestValidationError{
			field:  "FileUrl",
			reason: "value does not match regex pattern \"^https?://.*\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSubject()) < 1 {
		err := CreateQuestionImportRequestValidationError{
			field:  "Subject",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateQuestionImportRequestMultiError(errors)
	}

	return nil
}

// CreateQuestionImportRequestMultiError is an error wrapping multiple
// validation errors returned by CreateQuestionImportRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateQuestionImportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateQuestionImportRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateQuestionImportRequestMultiError) AllErrors() []error { return m }

// CreateQuestionImportRequestValidationError is the validation error returned
// by CreateQuestionImportRequest.Validate if the designated constraints
// aren't met.
type CreateQuestionImportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateQuestionImportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateQuestionImportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateQuestionImportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateQuestionImportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateQuestionImportRequestValidationError) ErrorName() string {
	return "CreateQuestionImportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateQuestionImportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateQuestionImportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateQuestionImportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateQuestionImportRequestValidationError{}

var _CreateQuestionImportRequest_FileUrl_Pattern = regexp.MustCompile("^https?://.*")

// Validate checks the field values on ListQuestionImportRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListQuestionImportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListQuestionImportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListQuestionImportRequestMultiError, or nil if none found.
func (m *ListQuestionImportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListQuestionImportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return ListQuestionImportRequestMultiError(errors)
	}

	return nil
}

// ListQuestionImportRequestMultiError is an error wrapping multiple validation
// errors returned by ListQuestionImportRequest.ValidateAll() if the
// designated constraints aren't met.
type ListQuestionImportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListQuestionImportRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListQuestionImportRequestMultiError) AllErrors() []error { return m }

// ListQuestionImportRequestValidationError is the validation error returned by
// ListQuestionImportRequest.Validate if the designated constraints aren't met.
type ListQuestionImportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListQuestionImportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListQuestionImportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListQuestionImportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListQuestionImportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListQuestionImportRequestValidationError) ErrorName() string {
	return "ListQuestionImportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListQuestionImportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListQuestionImportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListQuestionImportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListQuestionImportRequestValidationError{}

// Validate checks the field values on QuestionImportData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionImportData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionImportData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionImportDataMultiError, or nil if none found.
func (m *QuestionImportData) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionImportData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for FileName

	// no validation rules for FileUrl

	// no validation rules for Subject

	// no validation rules for ImportTime

	// no validation rules for Status

	// no validation rules for NumSuccess

	// no validation rules for NumError

	// no validation rules for NumRepeat

	if len(errors) > 0 {
		return QuestionImportDataMultiError(errors)
	}

	return nil
}

// QuestionImportDataMultiError is an error wrapping multiple validation errors
// returned by QuestionImportData.ValidateAll() if the designated constraints
// aren't met.
type QuestionImportDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionImportDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionImportDataMultiError) AllErrors() []error { return m }

// QuestionImportDataValidationError is the validation error returned by
// QuestionImportData.Validate if the designated constraints aren't met.
type QuestionImportDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionImportDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionImportDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionImportDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionImportDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionImportDataValidationError) ErrorName() string {
	return "QuestionImportDataValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionImportDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionImportData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionImportDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionImportDataValidationError{}

// Validate checks the field values on ListQuestionImportReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListQuestionImportReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListQuestionImportReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListQuestionImportReplyMultiError, or nil if none found.
func (m *ListQuestionImportReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListQuestionImportReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListQuestionImportReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListQuestionImportReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListQuestionImportReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListQuestionImportReplyMultiError(errors)
	}

	return nil
}

// ListQuestionImportReplyMultiError is an error wrapping multiple validation
// errors returned by ListQuestionImportReply.ValidateAll() if the designated
// constraints aren't met.
type ListQuestionImportReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListQuestionImportReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListQuestionImportReplyMultiError) AllErrors() []error { return m }

// ListQuestionImportReplyValidationError is the validation error returned by
// ListQuestionImportReply.Validate if the designated constraints aren't met.
type ListQuestionImportReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListQuestionImportReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListQuestionImportReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListQuestionImportReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListQuestionImportReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListQuestionImportReplyValidationError) ErrorName() string {
	return "ListQuestionImportReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListQuestionImportReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListQuestionImportReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListQuestionImportReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListQuestionImportReplyValidationError{}

// Validate checks the field values on QuestionImportActionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionImportActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionImportActionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionImportActionRequestMultiError, or nil if none found.
func (m *QuestionImportActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionImportActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := QuestionImportActionRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAction()) < 1 {
		err := QuestionImportActionRequestValidationError{
			field:  "Action",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return QuestionImportActionRequestMultiError(errors)
	}

	return nil
}

// QuestionImportActionRequestMultiError is an error wrapping multiple
// validation errors returned by QuestionImportActionRequest.ValidateAll() if
// the designated constraints aren't met.
type QuestionImportActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionImportActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionImportActionRequestMultiError) AllErrors() []error { return m }

// QuestionImportActionRequestValidationError is the validation error returned
// by QuestionImportActionRequest.Validate if the designated constraints
// aren't met.
type QuestionImportActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionImportActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionImportActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionImportActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionImportActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionImportActionRequestValidationError) ErrorName() string {
	return "QuestionImportActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionImportActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionImportActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionImportActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionImportActionRequestValidationError{}

// Validate checks the field values on GetQuestionImportDetailRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQuestionImportDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionImportDetailRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetQuestionImportDetailRequestMultiError, or nil if none found.
func (m *GetQuestionImportDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionImportDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := GetQuestionImportDetailRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetQuestionImportDetailRequestMultiError(errors)
	}

	return nil
}

// GetQuestionImportDetailRequestMultiError is an error wrapping multiple
// validation errors returned by GetQuestionImportDetailRequest.ValidateAll()
// if the designated constraints aren't met.
type GetQuestionImportDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionImportDetailRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionImportDetailRequestMultiError) AllErrors() []error { return m }

// GetQuestionImportDetailRequestValidationError is the validation error
// returned by GetQuestionImportDetailRequest.Validate if the designated
// constraints aren't met.
type GetQuestionImportDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionImportDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionImportDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionImportDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionImportDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionImportDetailRequestValidationError) ErrorName() string {
	return "GetQuestionImportDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionImportDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionImportDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionImportDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionImportDetailRequestValidationError{}

// Validate checks the field values on ImportErrorReason with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ImportErrorReason) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImportErrorReason with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImportErrorReasonMultiError, or nil if none found.
func (m *ImportErrorReason) ValidateAll() error {
	return m.validate(true)
}

func (m *ImportErrorReason) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QuestionId

	// no validation rules for ErrorReason

	if len(errors) > 0 {
		return ImportErrorReasonMultiError(errors)
	}

	return nil
}

// ImportErrorReasonMultiError is an error wrapping multiple validation errors
// returned by ImportErrorReason.ValidateAll() if the designated constraints
// aren't met.
type ImportErrorReasonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImportErrorReasonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImportErrorReasonMultiError) AllErrors() []error { return m }

// ImportErrorReasonValidationError is the validation error returned by
// ImportErrorReason.Validate if the designated constraints aren't met.
type ImportErrorReasonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImportErrorReasonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImportErrorReasonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImportErrorReasonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImportErrorReasonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImportErrorReasonValidationError) ErrorName() string {
	return "ImportErrorReasonValidationError"
}

// Error satisfies the builtin error interface
func (e ImportErrorReasonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImportErrorReason.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImportErrorReasonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImportErrorReasonValidationError{}

// Validate checks the field values on GetQuestionImportDetailReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQuestionImportDetailReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionImportDetailReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQuestionImportDetailReplyMultiError, or nil if none found.
func (m *GetQuestionImportDetailReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionImportDetailReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for FailedReason

	// no validation rules for TotalError

	for idx, item := range m.GetErrorReasons() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetQuestionImportDetailReplyValidationError{
						field:  fmt.Sprintf("ErrorReasons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetQuestionImportDetailReplyValidationError{
						field:  fmt.Sprintf("ErrorReasons[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetQuestionImportDetailReplyValidationError{
					field:  fmt.Sprintf("ErrorReasons[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetQuestionImportDetailReplyMultiError(errors)
	}

	return nil
}

// GetQuestionImportDetailReplyMultiError is an error wrapping multiple
// validation errors returned by GetQuestionImportDetailReply.ValidateAll() if
// the designated constraints aren't met.
type GetQuestionImportDetailReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionImportDetailReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionImportDetailReplyMultiError) AllErrors() []error { return m }

// GetQuestionImportDetailReplyValidationError is the validation error returned
// by GetQuestionImportDetailReply.Validate if the designated constraints
// aren't met.
type GetQuestionImportDetailReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionImportDetailReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionImportDetailReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionImportDetailReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionImportDetailReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionImportDetailReplyValidationError) ErrorName() string {
	return "GetQuestionImportDetailReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionImportDetailReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionImportDetailReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionImportDetailReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionImportDetailReplyValidationError{}

// Validate checks the field values on BlogListReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BlogListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlogListReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BlogListReqMultiError, or
// nil if none found.
func (m *BlogListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BlogListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for Category

	if len(errors) > 0 {
		return BlogListReqMultiError(errors)
	}

	return nil
}

// BlogListReqMultiError is an error wrapping multiple validation errors
// returned by BlogListReq.ValidateAll() if the designated constraints aren't met.
type BlogListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlogListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlogListReqMultiError) AllErrors() []error { return m }

// BlogListReqValidationError is the validation error returned by
// BlogListReq.Validate if the designated constraints aren't met.
type BlogListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlogListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlogListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlogListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlogListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlogListReqValidationError) ErrorName() string { return "BlogListReqValidationError" }

// Error satisfies the builtin error interface
func (e BlogListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlogListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlogListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlogListReqValidationError{}

// Validate checks the field values on BlogListResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BlogListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlogListResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BlogListRespMultiError, or
// nil if none found.
func (m *BlogListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BlogListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BlogListRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BlogListRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BlogListRespValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return BlogListRespMultiError(errors)
	}

	return nil
}

// BlogListRespMultiError is an error wrapping multiple validation errors
// returned by BlogListResp.ValidateAll() if the designated constraints aren't met.
type BlogListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlogListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlogListRespMultiError) AllErrors() []error { return m }

// BlogListRespValidationError is the validation error returned by
// BlogListResp.Validate if the designated constraints aren't met.
type BlogListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlogListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlogListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlogListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlogListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlogListRespValidationError) ErrorName() string { return "BlogListRespValidationError" }

// Error satisfies the builtin error interface
func (e BlogListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlogListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlogListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlogListRespValidationError{}

// Validate checks the field values on BlogDetailReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BlogDetailReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlogDetailReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BlogDetailReqMultiError, or
// nil if none found.
func (m *BlogDetailReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BlogDetailReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Path

	// no validation rules for Id

	if len(errors) > 0 {
		return BlogDetailReqMultiError(errors)
	}

	return nil
}

// BlogDetailReqMultiError is an error wrapping multiple validation errors
// returned by BlogDetailReq.ValidateAll() if the designated constraints
// aren't met.
type BlogDetailReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlogDetailReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlogDetailReqMultiError) AllErrors() []error { return m }

// BlogDetailReqValidationError is the validation error returned by
// BlogDetailReq.Validate if the designated constraints aren't met.
type BlogDetailReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlogDetailReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlogDetailReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlogDetailReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlogDetailReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlogDetailReqValidationError) ErrorName() string { return "BlogDetailReqValidationError" }

// Error satisfies the builtin error interface
func (e BlogDetailReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlogDetailReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlogDetailReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlogDetailReqValidationError{}

// Validate checks the field values on BlogDetailResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BlogDetailResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlogDetailResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BlogDetailRespMultiError,
// or nil if none found.
func (m *BlogDetailResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BlogDetailResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetArticle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BlogDetailRespValidationError{
					field:  "Article",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BlogDetailRespValidationError{
					field:  "Article",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetArticle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BlogDetailRespValidationError{
				field:  "Article",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BlogDetailRespMultiError(errors)
	}

	return nil
}

// BlogDetailRespMultiError is an error wrapping multiple validation errors
// returned by BlogDetailResp.ValidateAll() if the designated constraints
// aren't met.
type BlogDetailRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlogDetailRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlogDetailRespMultiError) AllErrors() []error { return m }

// BlogDetailRespValidationError is the validation error returned by
// BlogDetailResp.Validate if the designated constraints aren't met.
type BlogDetailRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlogDetailRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlogDetailRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlogDetailRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlogDetailRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlogDetailRespValidationError) ErrorName() string { return "BlogDetailRespValidationError" }

// Error satisfies the builtin error interface
func (e BlogDetailRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlogDetailResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlogDetailRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlogDetailRespValidationError{}

// Validate checks the field values on BlogArticle with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BlogArticle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlogArticle with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BlogArticleMultiError, or
// nil if none found.
func (m *BlogArticle) ValidateAll() error {
	return m.validate(true)
}

func (m *BlogArticle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Path

	// no validation rules for Category

	// no validation rules for ArticleTitle

	// no validation rules for ShortContent

	// no validation rules for CoverImg

	// no validation rules for AuthorAvatar

	// no validation rules for AuthorName

	// no validation rules for ArticleContent

	// no validation rules for PageTitle

	// no validation rules for MetaKeywords

	// no validation rules for MetaDescription

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return BlogArticleMultiError(errors)
	}

	return nil
}

// BlogArticleMultiError is an error wrapping multiple validation errors
// returned by BlogArticle.ValidateAll() if the designated constraints aren't met.
type BlogArticleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlogArticleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlogArticleMultiError) AllErrors() []error { return m }

// BlogArticleValidationError is the validation error returned by
// BlogArticle.Validate if the designated constraints aren't met.
type BlogArticleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlogArticleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlogArticleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlogArticleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlogArticleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlogArticleValidationError) ErrorName() string { return "BlogArticleValidationError" }

// Error satisfies the builtin error interface
func (e BlogArticleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlogArticle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlogArticleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlogArticleValidationError{}

// Validate checks the field values on BlogFeedbackReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BlogFeedbackReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlogFeedbackReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlogFeedbackReqMultiError, or nil if none found.
func (m *BlogFeedbackReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BlogFeedbackReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for FeedbackType

	if len(errors) > 0 {
		return BlogFeedbackReqMultiError(errors)
	}

	return nil
}

// BlogFeedbackReqMultiError is an error wrapping multiple validation errors
// returned by BlogFeedbackReq.ValidateAll() if the designated constraints
// aren't met.
type BlogFeedbackReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlogFeedbackReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlogFeedbackReqMultiError) AllErrors() []error { return m }

// BlogFeedbackReqValidationError is the validation error returned by
// BlogFeedbackReq.Validate if the designated constraints aren't met.
type BlogFeedbackReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlogFeedbackReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlogFeedbackReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlogFeedbackReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlogFeedbackReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlogFeedbackReqValidationError) ErrorName() string { return "BlogFeedbackReqValidationError" }

// Error satisfies the builtin error interface
func (e BlogFeedbackReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlogFeedbackReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlogFeedbackReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlogFeedbackReqValidationError{}

// Validate checks the field values on BlogCategoryReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BlogCategoryReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlogCategoryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlogCategoryReqMultiError, or nil if none found.
func (m *BlogCategoryReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BlogCategoryReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BlogCategoryReqMultiError(errors)
	}

	return nil
}

// BlogCategoryReqMultiError is an error wrapping multiple validation errors
// returned by BlogCategoryReq.ValidateAll() if the designated constraints
// aren't met.
type BlogCategoryReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlogCategoryReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlogCategoryReqMultiError) AllErrors() []error { return m }

// BlogCategoryReqValidationError is the validation error returned by
// BlogCategoryReq.Validate if the designated constraints aren't met.
type BlogCategoryReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlogCategoryReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlogCategoryReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlogCategoryReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlogCategoryReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlogCategoryReqValidationError) ErrorName() string { return "BlogCategoryReqValidationError" }

// Error satisfies the builtin error interface
func (e BlogCategoryReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlogCategoryReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlogCategoryReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlogCategoryReqValidationError{}

// Validate checks the field values on BlogCategoryResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BlogCategoryResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlogCategoryResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlogCategoryRespMultiError, or nil if none found.
func (m *BlogCategoryResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BlogCategoryResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BlogCategoryRespMultiError(errors)
	}

	return nil
}

// BlogCategoryRespMultiError is an error wrapping multiple validation errors
// returned by BlogCategoryResp.ValidateAll() if the designated constraints
// aren't met.
type BlogCategoryRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlogCategoryRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlogCategoryRespMultiError) AllErrors() []error { return m }

// BlogCategoryRespValidationError is the validation error returned by
// BlogCategoryResp.Validate if the designated constraints aren't met.
type BlogCategoryRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlogCategoryRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlogCategoryRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlogCategoryRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlogCategoryRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlogCategoryRespValidationError) ErrorName() string { return "BlogCategoryRespValidationError" }

// Error satisfies the builtin error interface
func (e BlogCategoryRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlogCategoryResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlogCategoryRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlogCategoryRespValidationError{}

// Validate checks the field values on EduTermInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EduTermInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EduTermInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EduTermInfoMultiError, or
// nil if none found.
func (m *EduTermInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EduTermInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Term

	// no validation rules for Knowledge_1

	// no validation rules for Knowledge_2

	// no validation rules for Title

	// no validation rules for Path

	if len(errors) > 0 {
		return EduTermInfoMultiError(errors)
	}

	return nil
}

// EduTermInfoMultiError is an error wrapping multiple validation errors
// returned by EduTermInfo.ValidateAll() if the designated constraints aren't met.
type EduTermInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EduTermInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EduTermInfoMultiError) AllErrors() []error { return m }

// EduTermInfoValidationError is the validation error returned by
// EduTermInfo.Validate if the designated constraints aren't met.
type EduTermInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EduTermInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EduTermInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EduTermInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EduTermInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EduTermInfoValidationError) ErrorName() string { return "EduTermInfoValidationError" }

// Error satisfies the builtin error interface
func (e EduTermInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEduTermInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EduTermInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EduTermInfoValidationError{}

// Validate checks the field values on EduTermListReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EduTermListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EduTermListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EduTermListReqMultiError,
// or nil if none found.
func (m *EduTermListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *EduTermListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Subject

	if len(errors) > 0 {
		return EduTermListReqMultiError(errors)
	}

	return nil
}

// EduTermListReqMultiError is an error wrapping multiple validation errors
// returned by EduTermListReq.ValidateAll() if the designated constraints
// aren't met.
type EduTermListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EduTermListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EduTermListReqMultiError) AllErrors() []error { return m }

// EduTermListReqValidationError is the validation error returned by
// EduTermListReq.Validate if the designated constraints aren't met.
type EduTermListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EduTermListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EduTermListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EduTermListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EduTermListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EduTermListReqValidationError) ErrorName() string { return "EduTermListReqValidationError" }

// Error satisfies the builtin error interface
func (e EduTermListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEduTermListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EduTermListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EduTermListReqValidationError{}

// Validate checks the field values on EduTermListResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EduTermListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EduTermListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EduTermListRespMultiError, or nil if none found.
func (m *EduTermListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *EduTermListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EduTermListRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EduTermListRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EduTermListRespValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EduTermListRespMultiError(errors)
	}

	return nil
}

// EduTermListRespMultiError is an error wrapping multiple validation errors
// returned by EduTermListResp.ValidateAll() if the designated constraints
// aren't met.
type EduTermListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EduTermListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EduTermListRespMultiError) AllErrors() []error { return m }

// EduTermListRespValidationError is the validation error returned by
// EduTermListResp.Validate if the designated constraints aren't met.
type EduTermListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EduTermListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EduTermListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EduTermListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EduTermListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EduTermListRespValidationError) ErrorName() string { return "EduTermListRespValidationError" }

// Error satisfies the builtin error interface
func (e EduTermListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEduTermListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EduTermListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EduTermListRespValidationError{}

// Validate checks the field values on EduTermDetailReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EduTermDetailReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EduTermDetailReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EduTermDetailReqMultiError, or nil if none found.
func (m *EduTermDetailReq) ValidateAll() error {
	return m.validate(true)
}

func (m *EduTermDetailReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Path

	if len(errors) > 0 {
		return EduTermDetailReqMultiError(errors)
	}

	return nil
}

// EduTermDetailReqMultiError is an error wrapping multiple validation errors
// returned by EduTermDetailReq.ValidateAll() if the designated constraints
// aren't met.
type EduTermDetailReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EduTermDetailReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EduTermDetailReqMultiError) AllErrors() []error { return m }

// EduTermDetailReqValidationError is the validation error returned by
// EduTermDetailReq.Validate if the designated constraints aren't met.
type EduTermDetailReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EduTermDetailReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EduTermDetailReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EduTermDetailReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EduTermDetailReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EduTermDetailReqValidationError) ErrorName() string { return "EduTermDetailReqValidationError" }

// Error satisfies the builtin error interface
func (e EduTermDetailReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEduTermDetailReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EduTermDetailReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EduTermDetailReqValidationError{}

// Validate checks the field values on EduTermBlock with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EduTermBlock) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EduTermBlock with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EduTermBlockMultiError, or
// nil if none found.
func (m *EduTermBlock) ValidateAll() error {
	return m.validate(true)
}

func (m *EduTermBlock) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModuleType

	// no validation rules for ModuleIndex

	// no validation rules for BlockType

	// no validation rules for BlockOrder

	// no validation rules for BlockContent

	if len(errors) > 0 {
		return EduTermBlockMultiError(errors)
	}

	return nil
}

// EduTermBlockMultiError is an error wrapping multiple validation errors
// returned by EduTermBlock.ValidateAll() if the designated constraints aren't met.
type EduTermBlockMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EduTermBlockMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EduTermBlockMultiError) AllErrors() []error { return m }

// EduTermBlockValidationError is the validation error returned by
// EduTermBlock.Validate if the designated constraints aren't met.
type EduTermBlockValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EduTermBlockValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EduTermBlockValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EduTermBlockValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EduTermBlockValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EduTermBlockValidationError) ErrorName() string { return "EduTermBlockValidationError" }

// Error satisfies the builtin error interface
func (e EduTermBlockValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEduTermBlock.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EduTermBlockValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EduTermBlockValidationError{}

// Validate checks the field values on EduTermDetailResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EduTermDetailResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EduTermDetailResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EduTermDetailRespMultiError, or nil if none found.
func (m *EduTermDetailResp) ValidateAll() error {
	return m.validate(true)
}

func (m *EduTermDetailResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Term

	// no validation rules for Knowledge_1

	// no validation rules for Knowledge_2

	// no validation rules for Title

	// no validation rules for MetaDescription

	for idx, item := range m.GetBlockList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EduTermDetailRespValidationError{
						field:  fmt.Sprintf("BlockList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EduTermDetailRespValidationError{
						field:  fmt.Sprintf("BlockList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EduTermDetailRespValidationError{
					field:  fmt.Sprintf("BlockList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return EduTermDetailRespMultiError(errors)
	}

	return nil
}

// EduTermDetailRespMultiError is an error wrapping multiple validation errors
// returned by EduTermDetailResp.ValidateAll() if the designated constraints
// aren't met.
type EduTermDetailRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EduTermDetailRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EduTermDetailRespMultiError) AllErrors() []error { return m }

// EduTermDetailRespValidationError is the validation error returned by
// EduTermDetailResp.Validate if the designated constraints aren't met.
type EduTermDetailRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EduTermDetailRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EduTermDetailRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EduTermDetailRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EduTermDetailRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EduTermDetailRespValidationError) ErrorName() string {
	return "EduTermDetailRespValidationError"
}

// Error satisfies the builtin error interface
func (e EduTermDetailRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEduTermDetailResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EduTermDetailRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EduTermDetailRespValidationError{}

// Validate checks the field values on GetQuestionStatusReply_QuestionStatus
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetQuestionStatusReply_QuestionStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQuestionStatusReply_QuestionStatus
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetQuestionStatusReply_QuestionStatusMultiError, or nil if none found.
func (m *GetQuestionStatusReply_QuestionStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQuestionStatusReply_QuestionStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	if len(errors) > 0 {
		return GetQuestionStatusReply_QuestionStatusMultiError(errors)
	}

	return nil
}

// GetQuestionStatusReply_QuestionStatusMultiError is an error wrapping
// multiple validation errors returned by
// GetQuestionStatusReply_QuestionStatus.ValidateAll() if the designated
// constraints aren't met.
type GetQuestionStatusReply_QuestionStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQuestionStatusReply_QuestionStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQuestionStatusReply_QuestionStatusMultiError) AllErrors() []error { return m }

// GetQuestionStatusReply_QuestionStatusValidationError is the validation error
// returned by GetQuestionStatusReply_QuestionStatus.Validate if the
// designated constraints aren't met.
type GetQuestionStatusReply_QuestionStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQuestionStatusReply_QuestionStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQuestionStatusReply_QuestionStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQuestionStatusReply_QuestionStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQuestionStatusReply_QuestionStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQuestionStatusReply_QuestionStatusValidationError) ErrorName() string {
	return "GetQuestionStatusReply_QuestionStatusValidationError"
}

// Error satisfies the builtin error interface
func (e GetQuestionStatusReply_QuestionStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQuestionStatusReply_QuestionStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQuestionStatusReply_QuestionStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQuestionStatusReply_QuestionStatusValidationError{}
