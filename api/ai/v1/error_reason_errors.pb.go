// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package v1

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 命名规范 服务名称_错误  error.code 尽量使用http.code来标识且符合code码含义, 需要备注什么错误，方便定位
func IsHwPaasAppidError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_APPID_ERROR.String() && e.Code == 403
}

// 命名规范 服务名称_错误  error.code 尽量使用http.code来标识且符合code码含义, 需要备注什么错误，方便定位
func ErrorHwPaasAppidError(format string, args ...interface{}) *errors.Error {
	return errors.New(403, ErrorReason_HW_PAAS_APPID_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsHwPaasSignError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_SIGN_ERROR.String() && e.Code == 403
}

func ErrorHwPaasSignError(format string, args ...interface{}) *errors.Error {
	return errors.New(403, ErrorReason_HW_PAAS_SIGN_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsHwPaasParamError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_PARAM_ERROR.String() && e.Code == 400
}

func ErrorHwPaasParamError(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_HW_PAAS_PARAM_ERROR.String(), fmt.Sprintf(format, args...))
}

// 资源未找到
func IsHwPaasNotFoundError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_NOT_FOUND_ERROR.String() && e.Code == 404
}

// 资源未找到
func ErrorHwPaasNotFoundError(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_HW_PAAS_NOT_FOUND_ERROR.String(), fmt.Sprintf(format, args...))
}

// 未知错误
func IsHwPaasUnexceptError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_UNEXCEPT_ERROR.String() && e.Code == 200
}

// 未知错误
func ErrorHwPaasUnexceptError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_HW_PAAS_UNEXCEPT_ERROR.String(), fmt.Sprintf(format, args...))
}

// 第三方接口报错
func IsHwPaasThirdPartError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_THIRD_PART_ERROR.String() && e.Code == 200
}

// 第三方接口报错
func ErrorHwPaasThirdPartError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_HW_PAAS_THIRD_PART_ERROR.String(), fmt.Sprintf(format, args...))
}

// 口算批改接口错误
func IsHwPaasCorrectError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_CORRECT_ERROR.String() && e.Code == 400
}

// 口算批改接口错误
func ErrorHwPaasCorrectError(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_HW_PAAS_CORRECT_ERROR.String(), fmt.Sprintf(format, args...))
}

// 口算批改-照片不清晰接口错误
func IsHwPaasCorrectFuzzyError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_CORRECT_FUZZY_ERROR.String() && e.Code == 400
}

// 口算批改-照片不清晰接口错误
func ErrorHwPaasCorrectFuzzyError(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_HW_PAAS_CORRECT_FUZZY_ERROR.String(), fmt.Sprintf(format, args...))
}

// 指尖查词OCR失败
func IsHwPaasFingerOcrError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_FINGER_OCR_ERROR.String() && e.Code == 200
}

// 指尖查词OCR失败
func ErrorHwPaasFingerOcrError(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_HW_PAAS_FINGER_OCR_ERROR.String(), fmt.Sprintf(format, args...))
}

// 题目已被绑定
func IsHwPaasQuestionIsBand(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_QUESTION_IS_BAND.String() && e.Code == 200
}

// 题目已被绑定
func ErrorHwPaasQuestionIsBand(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_HW_PAAS_QUESTION_IS_BAND.String(), fmt.Sprintf(format, args...))
}

// 默认业务错误
func IsHwPaasDefaultErr(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_DEFAULT_ERR.String() && e.Code == 200
}

// 默认业务错误
func ErrorHwPaasDefaultErr(format string, args ...interface{}) *errors.Error {
	return errors.New(200, ErrorReason_HW_PAAS_DEFAULT_ERR.String(), fmt.Sprintf(format, args...))
}

// 鉴权失败
func IsHwPaasUnauthorized(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_HW_PAAS_UNAUTHORIZED.String() && e.Code == 401
}

// 鉴权失败
func ErrorHwPaasUnauthorized(format string, args ...interface{}) *errors.Error {
	return errors.New(401, ErrorReason_HW_PAAS_UNAUTHORIZED.String(), fmt.Sprintf(format, args...))
}
