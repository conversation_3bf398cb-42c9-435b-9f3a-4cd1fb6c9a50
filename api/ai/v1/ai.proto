syntax = "proto3";

package api.ai.v1;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "validate/validate.proto";

option go_package = "hw-paas-service/api/ai/v1;v1";


service Ai {
  rpc QueryCorrect (QueryCorrectRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/intelligence/api/ai/v1/query_correct"
      body: "*"
    };
  };

  rpc FeedbackTrace (FeedbackTraceRequest) returns (FeedbackTraceReply) {
    option (google.api.http) = {
      post: "/intelligence/api/ai/v2/feedback/trace"
      body: "*"
    };
  };

  rpc ListQuestions (ListQuestionsRequest) returns (ListQuestionsReply) {
    option (google.api.http) = {
      get: "/intelligence/api/ai/v1/questions"
    };
  };

  rpc GetQuestion (GetQuestionRequest) returns (Question) {
    option (google.api.http) = {
      get: "/intelligence/api/ai/v1/questions/detail"
    };
  };

  rpc GetQuestionBatch (GetQuestionBatchRequest) returns (GetQuestionBatchReply) {
    option (google.api.http) = {
      get: "/intelligence/api/ai/v1/questions/detail_batch"
    };
  };

  rpc UpsertQuestion (UpsertQuestionRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/intelligence/api/ai/v1/questions/update"
      body: "*"
    };
  };

  rpc UpdateQuestionWithKey(UpdateQuestionWithKeyRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/intelligence/api/ai/v1/questions/update_key"
      body: "*"
    };
  }

  rpc GetQuestionStatus (GetQuestionStatusRequest) returns (GetQuestionStatusReply) {
    option (google.api.http) = {
      get: "/intelligence/api/ai/v1/questions/status"
    };
  }

  //question_import create
  rpc CreateQuestionImport (CreateQuestionImportRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/intelligence/api/ai/v1/questions_import/create"
      body: "*"
    };
  };

  //question_import list
  rpc ListQuestionImport (ListQuestionImportRequest) returns (ListQuestionImportReply) {
    option (google.api.http) = {
      get: "/intelligence/api/ai/v1/questions_import/list"
    };
  };

  //question import action
  rpc QuestionImportAction (QuestionImportActionRequest) returns (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/intelligence/api/ai/v1/questions_import/action"
      body: "*"
    };
  };

  //question import detail
  rpc GetQuestionImportDetail (GetQuestionImportDetailRequest) returns (GetQuestionImportDetailReply) {
    option (google.api.http) = {
      get: "/intelligence/api/ai/v1/questions_import/detail"
    };
  };

  rpc UploadFile (UploadFileRequest) returns (UploadFileReply) {
    option (google.api.http) = {
      post: "/intelligence/api/ai/v1/upload_file"
      body: "*"
    };
  };
  rpc CommonConfig (CommonConfigReq) returns (CommonConfigResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/common/config"
    };
  };
  rpc TaskDrive (TaskDriveReq) returns (TaskDriveResp) {
    option (google.api.http) = {
      post: "/intelligence/api/v1/task/drive"
      body: "*"
    };
  };

  rpc BlogList (BlogListReq) returns (BlogListResp) {
    option (google.api.http) = {
      get: "/intelligence/api/blog/list"
    };
  };

  rpc BlogDetail (BlogDetailReq) returns (BlogDetailResp) {
    option (google.api.http) = {
      get: "/intelligence/api/blog/detail"
    };
  };

  rpc BlogCategory (BlogCategoryReq) returns (BlogCategoryResp) {
    option (google.api.http) = {
      get: "/intelligence/api/blog/category"
    };
  };

  rpc BlogFeedback (BlogFeedbackReq) returns (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/intelligence/api/blog/feedback"
      body: "*"
    };
  };

  rpc SetBlog (BlogArticle) returns  (google.protobuf.Struct) {
    option (google.api.http) = {
      post: "/intelligence/api/blog/set"
      body: "*"
    };
  };

  rpc EduTermList (EduTermListReq) returns (EduTermListResp) {
    option (google.api.http) = {
      get: "/intelligence/api/edu_term/list"
    };
  };

  rpc EduTermDetail (EduTermDetailReq) returns (EduTermDetailResp) {
    option (google.api.http) = {
      get: "/intelligence/api/edu_term/detail"
    };
  };

  rpc CodeLogin (CodeLoginReq) returns (CodeLoginResp) {
    option (google.api.http) = {
      post: "/intelligence/api/v1/code_login"
      body: "*"
    };
  }

  rpc LoginOut (LoginOutReq) returns (LoginOutResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/login_out"
    };
  };

  rpc CheckLogin (CheckLoginReq) returns (CheckLoginResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/check_login"
    };
  };

  // CheckEmailExists
  rpc CheckEmailExists (CheckEmailExistsReq) returns (CheckEmailExistsResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/check_email_exists"
    };
  };

}

message CheckEmailExistsReq{
  string email = 1 [(validate.rules).string = {min_len: 1}];
}
message CheckEmailExistsResp {
  bool exists = 1; // 是否存在 0: 不存在 1: 存在
}
message CheckLoginReq {
  string tal_token = 1 [(validate.rules).string = {min_len: 1}];
}
message CheckLoginResp {
  string tal_id = 1;          // 好未来用户 ID
  string tal_token = 2;       // 用户登录标识
  int64  expired = 3;       // 登陆标识更新时间
  int64 life = 4;         // 登陆标识有效时间
}

message LoginOutReq{
  string tal_token = 1 [(validate.rules).string = {min_len: 1}];
}

message LoginOutResp {
  string msg = 1;
}

message CodeLoginReq {
  string code = 1 [(validate.rules).string = {min_len: 1}];
}

message CodeLoginResp {
  string tal_id = 1;          // 好未来用户 ID
  string tal_token = 2;       // 用户登录标识
  int64  expired = 3;       // 登陆标识更新时间
  int64 life = 4;         // 登陆标识有效时间
}

message TaskDriveReq{
  string app = 1 [(validate.rules).string = {min_len: 1}];
}
message TaskDriveResp{
}
message CommonConfigReq{}
message CommonConfigResp{
  repeated Range ranges = 1;
  int32 right_line = 2;
}
message Range {
  string word = 1;
  int32 min = 2;
  int32 max = 3;
  bool include_min = 4;
  bool include_max = 5;
}

message FeedbackTraceRequest{
  string trace_id = 1;
  string feedback = 2;
  int32 biz = 3;//1:口算批改;2:指尖查词;3:作业批改;4:指尖查词;5:语音查词;6:绘本指读
}
message FeedbackTraceReply{}

message QueryCorrectRequest {
  string image_url = 1[(validate.rules).string = {
    pattern: "^https?://.*"
  }];
}

message Empty {}

message ListQuestionsRequest {
  int32 id = 1;
  int32 q_type = 2;
  int32 difficulty = 3;
  int32 status = 4;
  string grade = 5;
  string subject = 6;
  string knowledge = 7;
  string knowledge_no = 8;
  string content = 9;
  int32 page = 10;
  int32 page_size = 11;
}

message ListQuestionsReply {
  repeated Question questions = 1;
  int32 total = 2;
}

message GetQuestionRequest {
  int32 id = 1;
}

message GetQuestionBatchRequest {
  string id = 1;
}

message GetQuestionBatchReply {
  repeated Question questions = 1;
}

message UpsertQuestionRequest {
  int32 id = 1;
  int32 q_type = 2;
  string grade = 3;
  string subject = 4;
  int32 difficulty = 5;
  string question = 6;
  string answer = 7;
  string solution = 8;
  string knowledge = 9;
  string knowledge_no = 10;
  string note = 11;
  int32 status = 12;
}

message UpdateQuestionWithKeyRequest {
  int32 id = 1;
  map<string, string> fields = 2;
}

message Question {
  int32 id = 1;
  int32 q_type = 2;
  string grade = 3;
  string subject = 4;
  int32 difficulty = 5;
  string question = 6;
  string answer = 7;
  string solution = 8;
  string knowledge = 9;
  string knowledge_no = 10;
  string note = 11;
  int32 status = 12;
}

message GetQuestionStatusRequest {
  string ids = 1;
}

message GetQuestionStatusReply {
  message QuestionStatus {
    int32 id = 1;
    int32 status = 2;
  }
  repeated QuestionStatus questions = 1;
}

message UploadFileRequest {
  string file = 1[(validate.rules).string.min_len = 1];
  string suffix = 2[(validate.rules).string.min_len = 1];
}

message UploadFileReply {
  string url = 1;
}

message CreateQuestionImportRequest {
  string file_name = 1[(validate.rules).string.min_len = 1];
  string file_url = 2[(validate.rules).string = {
    pattern: "^https?://.*"
  }];
  string subject = 3[(validate.rules).string.min_len = 1];
}

message ListQuestionImportRequest {
  int32 page = 1;
  int32 page_size = 2;
}

message QuestionImportData {
  int32 id = 1;
  string file_name = 2; // 文件名
  string file_url = 3; // 文件地址
  string subject = 4; // 学科
  string import_time = 5; // 导入时间
  int32 status = 6; // 1创建中 2导入失败 3待确认 4已完成 5已取消
  int32 num_success = 7; // 成功导入的题目数量
  int32 num_error = 8; // 导入失败的题目数量
  int32 num_repeat = 9; // 重复的题目数量
}

message ListQuestionImportReply {
  int32 total = 1;
  repeated QuestionImportData list = 2;
}

message QuestionImportActionRequest {
  int32 id = 1[(validate.rules).int32.gt = 0];
  string action = 2[(validate.rules).string.min_len = 1];
}

message  GetQuestionImportDetailRequest {
  int32 id = 1[(validate.rules).int32.gt = 0];
  int32 page = 2; // 页码
  int32 page_size = 3; // 每页数量
}

message ImportErrorReason {
  int32 question_id = 1; // 题目ID
  string error_reason = 2; // 错误原因
}

message  GetQuestionImportDetailReply {
  int32 status = 1; // 1创建中 2导入失败 3待确认 4已完成 5已取消
  string failed_reason = 2; // 导入失败原因
  int32 total_error = 3; // 导入失败的题目数量
  repeated int32 repeat_question_id = 4; // 重复的题目ID列表
  repeated ImportErrorReason error_reasons = 5; // 导入失败的题目列表
}

message BlogListReq {
  int32 page = 1;
  int32 page_size = 2;
  string category = 3;
}

message BlogListResp {
  repeated BlogArticle list = 1;
  int32 total = 2;
}

message BlogDetailReq {
  string path = 1;
  int32 id = 2;
}

message BlogDetailResp {
  BlogArticle article = 1;
}

message BlogArticle {
  int32 id = 1;
  string path = 2;
  string category = 3;
  string article_title = 4;
  string short_content = 5;
  string cover_img = 6;
  string author_avatar = 7;
  string author_name = 8;
  string article_content = 9;
  string page_title = 10;
  string meta_keywords = 11;
  string meta_description = 12;
  string created_at = 13;
  string updated_at = 14;
}

message BlogFeedbackReq {
  int32 id = 1;
  string feedback_type = 2;
}

message BlogCategoryReq {}
message BlogCategoryResp {
  repeated string list = 1;
}

message EduTermInfo {
  int32 id = 1;
  string term = 2;
  string knowledge_1 = 3;
  string knowledge_2 = 4;
  string title = 5;
  repeated string tag = 6;
  string path = 7;
}

message EduTermListReq {
  string subject = 1;
}

message EduTermListResp {
  int32 total = 1;
  repeated EduTermInfo list = 2;
}

message EduTermDetailReq {
  int32 id = 1;
  string path = 2;
}

message EduTermBlock {
  string module_type = 1;
  int32 module_index = 2;
  string block_type = 3;
  int32 block_order = 4;
  string block_content = 5;
}

message EduTermDetailResp {
  int32 id = 1;
  string term = 2;
  string knowledge_1 = 3;
  string knowledge_2 = 4;
  string title = 5;
  repeated string tag = 6;
  string meta_description = 7;
  repeated EduTermBlock block_list = 8;
  string created_at = 9;
  string updated_at = 10;
}
