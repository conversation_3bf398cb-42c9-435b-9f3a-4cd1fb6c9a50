// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/resource/v1/resource.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EduResource with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EduResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EduResource with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EduResourceMultiError, or
// nil if none found.
func (m *EduResource) ValidateAll() error {
	return m.validate(true)
}

func (m *EduResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ResourceType

	// no validation rules for Subject

	// no validation rules for Grade

	// no validation rules for Title

	// no validation rules for LearningTopic

	// no validation rules for Resource

	// no validation rules for ResourceDetail

	// no validation rules for Standards

	// no validation rules for ResourceDescription

	if all {
		switch v := interface{}(m.GetExtraData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EduResourceValidationError{
					field:  "ExtraData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EduResourceValidationError{
					field:  "ExtraData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtraData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EduResourceValidationError{
				field:  "ExtraData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EduResourceMultiError(errors)
	}

	return nil
}

// EduResourceMultiError is an error wrapping multiple validation errors
// returned by EduResource.ValidateAll() if the designated constraints aren't met.
type EduResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EduResourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EduResourceMultiError) AllErrors() []error { return m }

// EduResourceValidationError is the validation error returned by
// EduResource.Validate if the designated constraints aren't met.
type EduResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EduResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EduResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EduResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EduResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EduResourceValidationError) ErrorName() string { return "EduResourceValidationError" }

// Error satisfies the builtin error interface
func (e EduResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEduResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EduResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EduResourceValidationError{}

// Validate checks the field values on EduResourceGroup with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EduResourceGroup) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EduResourceGroup with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EduResourceGroupMultiError, or nil if none found.
func (m *EduResourceGroup) ValidateAll() error {
	return m.validate(true)
}

func (m *EduResourceGroup) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EduResourceGroupValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EduResourceGroupValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EduResourceGroupValidationError{
					field:  fmt.Sprintf("Resources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EduResourceGroupMultiError(errors)
	}

	return nil
}

// EduResourceGroupMultiError is an error wrapping multiple validation errors
// returned by EduResourceGroup.ValidateAll() if the designated constraints
// aren't met.
type EduResourceGroupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EduResourceGroupMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EduResourceGroupMultiError) AllErrors() []error { return m }

// EduResourceGroupValidationError is the validation error returned by
// EduResourceGroup.Validate if the designated constraints aren't met.
type EduResourceGroupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EduResourceGroupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EduResourceGroupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EduResourceGroupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EduResourceGroupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EduResourceGroupValidationError) ErrorName() string { return "EduResourceGroupValidationError" }

// Error satisfies the builtin error interface
func (e EduResourceGroupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEduResourceGroup.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EduResourceGroupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EduResourceGroupValidationError{}

// Validate checks the field values on GetResourceGroupsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetResourceGroupsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetResourceGroupsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetResourceGroupsRespMultiError, or nil if none found.
func (m *GetResourceGroupsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetResourceGroupsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetGroups() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetResourceGroupsRespValidationError{
						field:  fmt.Sprintf("Groups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetResourceGroupsRespValidationError{
						field:  fmt.Sprintf("Groups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetResourceGroupsRespValidationError{
					field:  fmt.Sprintf("Groups[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetResourceGroupsRespMultiError(errors)
	}

	return nil
}

// GetResourceGroupsRespMultiError is an error wrapping multiple validation
// errors returned by GetResourceGroupsResp.ValidateAll() if the designated
// constraints aren't met.
type GetResourceGroupsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetResourceGroupsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetResourceGroupsRespMultiError) AllErrors() []error { return m }

// GetResourceGroupsRespValidationError is the validation error returned by
// GetResourceGroupsResp.Validate if the designated constraints aren't met.
type GetResourceGroupsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetResourceGroupsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetResourceGroupsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetResourceGroupsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetResourceGroupsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetResourceGroupsRespValidationError) ErrorName() string {
	return "GetResourceGroupsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetResourceGroupsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetResourceGroupsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetResourceGroupsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetResourceGroupsRespValidationError{}

// Validate checks the field values on GetResourceListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetResourceListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetResourceListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetResourceListRespMultiError, or nil if none found.
func (m *GetResourceListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetResourceListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetResourceListRespValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetResourceListRespValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetResourceListRespValidationError{
					field:  fmt.Sprintf("Resources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return GetResourceListRespMultiError(errors)
	}

	return nil
}

// GetResourceListRespMultiError is an error wrapping multiple validation
// errors returned by GetResourceListResp.ValidateAll() if the designated
// constraints aren't met.
type GetResourceListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetResourceListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetResourceListRespMultiError) AllErrors() []error { return m }

// GetResourceListRespValidationError is the validation error returned by
// GetResourceListResp.Validate if the designated constraints aren't met.
type GetResourceListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetResourceListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetResourceListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetResourceListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetResourceListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetResourceListRespValidationError) ErrorName() string {
	return "GetResourceListRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetResourceListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetResourceListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetResourceListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetResourceListRespValidationError{}

// Validate checks the field values on GetResourceGroupsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetResourceGroupsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetResourceGroupsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetResourceGroupsReqMultiError, or nil if none found.
func (m *GetResourceGroupsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetResourceGroupsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResourceType

	// no validation rules for Subject

	// no validation rules for Grade

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for Topic

	if len(errors) > 0 {
		return GetResourceGroupsReqMultiError(errors)
	}

	return nil
}

// GetResourceGroupsReqMultiError is an error wrapping multiple validation
// errors returned by GetResourceGroupsReq.ValidateAll() if the designated
// constraints aren't met.
type GetResourceGroupsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetResourceGroupsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetResourceGroupsReqMultiError) AllErrors() []error { return m }

// GetResourceGroupsReqValidationError is the validation error returned by
// GetResourceGroupsReq.Validate if the designated constraints aren't met.
type GetResourceGroupsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetResourceGroupsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetResourceGroupsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetResourceGroupsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetResourceGroupsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetResourceGroupsReqValidationError) ErrorName() string {
	return "GetResourceGroupsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetResourceGroupsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetResourceGroupsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetResourceGroupsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetResourceGroupsReqValidationError{}

// Validate checks the field values on GetResourceListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetResourceListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetResourceListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetResourceListReqMultiError, or nil if none found.
func (m *GetResourceListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetResourceListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResourceType

	// no validation rules for Subject

	// no validation rules for Grade

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for Topic

	if len(errors) > 0 {
		return GetResourceListReqMultiError(errors)
	}

	return nil
}

// GetResourceListReqMultiError is an error wrapping multiple validation errors
// returned by GetResourceListReq.ValidateAll() if the designated constraints
// aren't met.
type GetResourceListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetResourceListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetResourceListReqMultiError) AllErrors() []error { return m }

// GetResourceListReqValidationError is the validation error returned by
// GetResourceListReq.Validate if the designated constraints aren't met.
type GetResourceListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetResourceListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetResourceListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetResourceListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetResourceListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetResourceListReqValidationError) ErrorName() string {
	return "GetResourceListReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetResourceListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetResourceListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetResourceListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetResourceListReqValidationError{}

// Validate checks the field values on ResourceType with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ResourceType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceType with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourceTypeMultiError, or
// nil if none found.
func (m *ResourceType) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return ResourceTypeMultiError(errors)
	}

	return nil
}

// ResourceTypeMultiError is an error wrapping multiple validation errors
// returned by ResourceType.ValidateAll() if the designated constraints aren't met.
type ResourceTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceTypeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceTypeMultiError) AllErrors() []error { return m }

// ResourceTypeValidationError is the validation error returned by
// ResourceType.Validate if the designated constraints aren't met.
type ResourceTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceTypeValidationError) ErrorName() string { return "ResourceTypeValidationError" }

// Error satisfies the builtin error interface
func (e ResourceTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceTypeValidationError{}

// Validate checks the field values on ResourceTypeResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResourceTypeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceTypeResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResourceTypeRespMultiError, or nil if none found.
func (m *ResourceTypeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceTypeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTypes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResourceTypeRespValidationError{
						field:  fmt.Sprintf("Types[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResourceTypeRespValidationError{
						field:  fmt.Sprintf("Types[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResourceTypeRespValidationError{
					field:  fmt.Sprintf("Types[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ResourceTypeRespMultiError(errors)
	}

	return nil
}

// ResourceTypeRespMultiError is an error wrapping multiple validation errors
// returned by ResourceTypeResp.ValidateAll() if the designated constraints
// aren't met.
type ResourceTypeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceTypeRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceTypeRespMultiError) AllErrors() []error { return m }

// ResourceTypeRespValidationError is the validation error returned by
// ResourceTypeResp.Validate if the designated constraints aren't met.
type ResourceTypeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceTypeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceTypeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceTypeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceTypeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceTypeRespValidationError) ErrorName() string { return "ResourceTypeRespValidationError" }

// Error satisfies the builtin error interface
func (e ResourceTypeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceTypeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceTypeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceTypeRespValidationError{}

// Validate checks the field values on EduGrade with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EduGrade) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EduGrade with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EduGradeMultiError, or nil
// if none found.
func (m *EduGrade) ValidateAll() error {
	return m.validate(true)
}

func (m *EduGrade) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	if len(errors) > 0 {
		return EduGradeMultiError(errors)
	}

	return nil
}

// EduGradeMultiError is an error wrapping multiple validation errors returned
// by EduGrade.ValidateAll() if the designated constraints aren't met.
type EduGradeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EduGradeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EduGradeMultiError) AllErrors() []error { return m }

// EduGradeValidationError is the validation error returned by
// EduGrade.Validate if the designated constraints aren't met.
type EduGradeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EduGradeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EduGradeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EduGradeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EduGradeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EduGradeValidationError) ErrorName() string { return "EduGradeValidationError" }

// Error satisfies the builtin error interface
func (e EduGradeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEduGrade.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EduGradeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EduGradeValidationError{}

// Validate checks the field values on GradesResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GradesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GradesResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GradesRespMultiError, or
// nil if none found.
func (m *GradesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GradesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetGrades() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GradesRespValidationError{
						field:  fmt.Sprintf("Grades[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GradesRespValidationError{
						field:  fmt.Sprintf("Grades[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GradesRespValidationError{
					field:  fmt.Sprintf("Grades[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GradesRespMultiError(errors)
	}

	return nil
}

// GradesRespMultiError is an error wrapping multiple validation errors
// returned by GradesResp.ValidateAll() if the designated constraints aren't met.
type GradesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GradesRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GradesRespMultiError) AllErrors() []error { return m }

// GradesRespValidationError is the validation error returned by
// GradesResp.Validate if the designated constraints aren't met.
type GradesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GradesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GradesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GradesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GradesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GradesRespValidationError) ErrorName() string { return "GradesRespValidationError" }

// Error satisfies the builtin error interface
func (e GradesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGradesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GradesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GradesRespValidationError{}

// Validate checks the field values on Subject with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Subject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Subject with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SubjectMultiError, or nil if none found.
func (m *Subject) ValidateAll() error {
	return m.validate(true)
}

func (m *Subject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return SubjectMultiError(errors)
	}

	return nil
}

// SubjectMultiError is an error wrapping multiple validation errors returned
// by Subject.ValidateAll() if the designated constraints aren't met.
type SubjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubjectMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubjectMultiError) AllErrors() []error { return m }

// SubjectValidationError is the validation error returned by Subject.Validate
// if the designated constraints aren't met.
type SubjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubjectValidationError) ErrorName() string { return "SubjectValidationError" }

// Error satisfies the builtin error interface
func (e SubjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubjectValidationError{}

// Validate checks the field values on SubjectsResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SubjectsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubjectsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SubjectsRespMultiError, or
// nil if none found.
func (m *SubjectsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SubjectsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSubjects() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SubjectsRespValidationError{
						field:  fmt.Sprintf("Subjects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SubjectsRespValidationError{
						field:  fmt.Sprintf("Subjects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SubjectsRespValidationError{
					field:  fmt.Sprintf("Subjects[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SubjectsRespMultiError(errors)
	}

	return nil
}

// SubjectsRespMultiError is an error wrapping multiple validation errors
// returned by SubjectsResp.ValidateAll() if the designated constraints aren't met.
type SubjectsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubjectsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubjectsRespMultiError) AllErrors() []error { return m }

// SubjectsRespValidationError is the validation error returned by
// SubjectsResp.Validate if the designated constraints aren't met.
type SubjectsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubjectsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubjectsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubjectsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubjectsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubjectsRespValidationError) ErrorName() string { return "SubjectsRespValidationError" }

// Error satisfies the builtin error interface
func (e SubjectsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubjectsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubjectsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubjectsRespValidationError{}
