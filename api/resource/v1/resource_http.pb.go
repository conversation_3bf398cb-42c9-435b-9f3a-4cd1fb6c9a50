// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.23.3
// source: api/resource/v1/resource.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationResourceGetGrades = "/api.resource.v1.Resource/GetGrades"
const OperationResourceGetResourceDetail = "/api.resource.v1.Resource/GetResourceDetail"
const OperationResourceGetResourceGroups = "/api.resource.v1.Resource/GetResourceGroups"
const OperationResourceGetResourceList = "/api.resource.v1.Resource/GetResourceList"
const OperationResourceGetResourceMeta = "/api.resource.v1.Resource/GetResourceMeta"
const OperationResourceGetResourceType = "/api.resource.v1.Resource/GetResourceType"
const OperationResourceGetSubjects = "/api.resource.v1.Resource/GetSubjects"
const OperationResourceListRandResource = "/api.resource.v1.Resource/ListRandResource"
const OperationResourceRefreshAllCache = "/api.resource.v1.Resource/RefreshAllCache"

type ResourceHTTPServer interface {
	GetGrades(context.Context, *emptypb.Empty) (*GradesResp, error)
	GetResourceDetail(context.Context, *GetResourceDetailReq) (*ResourceDetail, error)
	GetResourceGroups(context.Context, *GetResourceGroupsReq) (*GetResourceGroupsResp, error)
	GetResourceList(context.Context, *GetResourceListReq) (*GetResourceListResp, error)
	GetResourceMeta(context.Context, *emptypb.Empty) (*ResourceMetaResp, error)
	GetResourceType(context.Context, *emptypb.Empty) (*ResourceTypeResp, error)
	GetSubjects(context.Context, *emptypb.Empty) (*SubjectsResp, error)
	ListRandResource(context.Context, *ListRandResourceReq) (*ListRandResourceResp, error)
	RefreshAllCache(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
}

func RegisterResourceHTTPServer(s *http.Server, srv ResourceHTTPServer) {
	r := s.Route("/")
	r.GET("/intelligence/api/v1/resource/grades", _Resource_GetGrades0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/resource/subjects", _Resource_GetSubjects0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/resource/types", _Resource_GetResourceType0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/resource/groups", _Resource_GetResourceGroups0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/resource/list", _Resource_GetResourceList0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/resource/meta", _Resource_GetResourceMeta0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/resource/detail", _Resource_GetResourceDetail0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/resource/rand", _Resource_ListRandResource0_HTTP_Handler(srv))
	r.GET("/intelligence/api/v1/resource/refresh-cache", _Resource_RefreshAllCache0_HTTP_Handler(srv))
}

func _Resource_GetGrades0_HTTP_Handler(srv ResourceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGetGrades)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGrades(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GradesResp)
		return ctx.Result(200, reply)
	}
}

func _Resource_GetSubjects0_HTTP_Handler(srv ResourceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGetSubjects)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSubjects(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SubjectsResp)
		return ctx.Result(200, reply)
	}
}

func _Resource_GetResourceType0_HTTP_Handler(srv ResourceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGetResourceType)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetResourceType(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResourceTypeResp)
		return ctx.Result(200, reply)
	}
}

func _Resource_GetResourceGroups0_HTTP_Handler(srv ResourceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetResourceGroupsReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGetResourceGroups)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetResourceGroups(ctx, req.(*GetResourceGroupsReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetResourceGroupsResp)
		return ctx.Result(200, reply)
	}
}

func _Resource_GetResourceList0_HTTP_Handler(srv ResourceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetResourceListReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGetResourceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetResourceList(ctx, req.(*GetResourceListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetResourceListResp)
		return ctx.Result(200, reply)
	}
}

func _Resource_GetResourceMeta0_HTTP_Handler(srv ResourceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGetResourceMeta)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetResourceMeta(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResourceMetaResp)
		return ctx.Result(200, reply)
	}
}

func _Resource_GetResourceDetail0_HTTP_Handler(srv ResourceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetResourceDetailReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceGetResourceDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetResourceDetail(ctx, req.(*GetResourceDetailReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResourceDetail)
		return ctx.Result(200, reply)
	}
}

func _Resource_ListRandResource0_HTTP_Handler(srv ResourceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRandResourceReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceListRandResource)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListRandResource(ctx, req.(*ListRandResourceReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListRandResourceResp)
		return ctx.Result(200, reply)
	}
}

func _Resource_RefreshAllCache0_HTTP_Handler(srv ResourceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in emptypb.Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResourceRefreshAllCache)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RefreshAllCache(ctx, req.(*emptypb.Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type ResourceHTTPClient interface {
	GetGrades(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *GradesResp, err error)
	GetResourceDetail(ctx context.Context, req *GetResourceDetailReq, opts ...http.CallOption) (rsp *ResourceDetail, err error)
	GetResourceGroups(ctx context.Context, req *GetResourceGroupsReq, opts ...http.CallOption) (rsp *GetResourceGroupsResp, err error)
	GetResourceList(ctx context.Context, req *GetResourceListReq, opts ...http.CallOption) (rsp *GetResourceListResp, err error)
	GetResourceMeta(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *ResourceMetaResp, err error)
	GetResourceType(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *ResourceTypeResp, err error)
	GetSubjects(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *SubjectsResp, err error)
	ListRandResource(ctx context.Context, req *ListRandResourceReq, opts ...http.CallOption) (rsp *ListRandResourceResp, err error)
	RefreshAllCache(ctx context.Context, req *emptypb.Empty, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type ResourceHTTPClientImpl struct {
	cc *http.Client
}

func NewResourceHTTPClient(client *http.Client) ResourceHTTPClient {
	return &ResourceHTTPClientImpl{client}
}

func (c *ResourceHTTPClientImpl) GetGrades(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*GradesResp, error) {
	var out GradesResp
	pattern := "/intelligence/api/v1/resource/grades"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGetGrades))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResourceHTTPClientImpl) GetResourceDetail(ctx context.Context, in *GetResourceDetailReq, opts ...http.CallOption) (*ResourceDetail, error) {
	var out ResourceDetail
	pattern := "/intelligence/api/v1/resource/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGetResourceDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResourceHTTPClientImpl) GetResourceGroups(ctx context.Context, in *GetResourceGroupsReq, opts ...http.CallOption) (*GetResourceGroupsResp, error) {
	var out GetResourceGroupsResp
	pattern := "/intelligence/api/v1/resource/groups"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGetResourceGroups))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResourceHTTPClientImpl) GetResourceList(ctx context.Context, in *GetResourceListReq, opts ...http.CallOption) (*GetResourceListResp, error) {
	var out GetResourceListResp
	pattern := "/intelligence/api/v1/resource/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGetResourceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResourceHTTPClientImpl) GetResourceMeta(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*ResourceMetaResp, error) {
	var out ResourceMetaResp
	pattern := "/intelligence/api/v1/resource/meta"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGetResourceMeta))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResourceHTTPClientImpl) GetResourceType(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*ResourceTypeResp, error) {
	var out ResourceTypeResp
	pattern := "/intelligence/api/v1/resource/types"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGetResourceType))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResourceHTTPClientImpl) GetSubjects(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*SubjectsResp, error) {
	var out SubjectsResp
	pattern := "/intelligence/api/v1/resource/subjects"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceGetSubjects))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResourceHTTPClientImpl) ListRandResource(ctx context.Context, in *ListRandResourceReq, opts ...http.CallOption) (*ListRandResourceResp, error) {
	var out ListRandResourceResp
	pattern := "/intelligence/api/v1/resource/rand"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceListRandResource))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResourceHTTPClientImpl) RefreshAllCache(ctx context.Context, in *emptypb.Empty, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/intelligence/api/v1/resource/refresh-cache"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResourceRefreshAllCache))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
