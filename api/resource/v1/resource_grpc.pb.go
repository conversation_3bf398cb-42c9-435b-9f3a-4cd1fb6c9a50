// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.3
// source: api/resource/v1/resource.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Resource_GetGrades_FullMethodName         = "/api.resource.v1.Resource/GetGrades"
	Resource_GetSubjects_FullMethodName       = "/api.resource.v1.Resource/GetSubjects"
	Resource_GetResourceType_FullMethodName   = "/api.resource.v1.Resource/GetResourceType"
	Resource_GetResourceGroups_FullMethodName = "/api.resource.v1.Resource/GetResourceGroups"
	Resource_GetResourceList_FullMethodName   = "/api.resource.v1.Resource/GetResourceList"
	Resource_GetResourceMeta_FullMethodName   = "/api.resource.v1.Resource/GetResourceMeta"
	Resource_GetResourceDetail_FullMethodName = "/api.resource.v1.Resource/GetResourceDetail"
	Resource_ListRandResource_FullMethodName  = "/api.resource.v1.Resource/ListRandResource"
	Resource_RefreshAllCache_FullMethodName   = "/api.resource.v1.Resource/RefreshAllCache"
)

// ResourceClient is the client API for Resource service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ResourceClient interface {
	GetGrades(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GradesResp, error)
	GetSubjects(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SubjectsResp, error)
	GetResourceType(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ResourceTypeResp, error)
	GetResourceGroups(ctx context.Context, in *GetResourceGroupsReq, opts ...grpc.CallOption) (*GetResourceGroupsResp, error)
	GetResourceList(ctx context.Context, in *GetResourceListReq, opts ...grpc.CallOption) (*GetResourceListResp, error)
	GetResourceMeta(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ResourceMetaResp, error)
	GetResourceDetail(ctx context.Context, in *GetResourceDetailReq, opts ...grpc.CallOption) (*ResourceDetail, error)
	ListRandResource(ctx context.Context, in *ListRandResourceReq, opts ...grpc.CallOption) (*ListRandResourceResp, error)
	RefreshAllCache(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type resourceClient struct {
	cc grpc.ClientConnInterface
}

func NewResourceClient(cc grpc.ClientConnInterface) ResourceClient {
	return &resourceClient{cc}
}

func (c *resourceClient) GetGrades(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GradesResp, error) {
	out := new(GradesResp)
	err := c.cc.Invoke(ctx, Resource_GetGrades_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceClient) GetSubjects(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SubjectsResp, error) {
	out := new(SubjectsResp)
	err := c.cc.Invoke(ctx, Resource_GetSubjects_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceClient) GetResourceType(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ResourceTypeResp, error) {
	out := new(ResourceTypeResp)
	err := c.cc.Invoke(ctx, Resource_GetResourceType_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceClient) GetResourceGroups(ctx context.Context, in *GetResourceGroupsReq, opts ...grpc.CallOption) (*GetResourceGroupsResp, error) {
	out := new(GetResourceGroupsResp)
	err := c.cc.Invoke(ctx, Resource_GetResourceGroups_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceClient) GetResourceList(ctx context.Context, in *GetResourceListReq, opts ...grpc.CallOption) (*GetResourceListResp, error) {
	out := new(GetResourceListResp)
	err := c.cc.Invoke(ctx, Resource_GetResourceList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceClient) GetResourceMeta(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ResourceMetaResp, error) {
	out := new(ResourceMetaResp)
	err := c.cc.Invoke(ctx, Resource_GetResourceMeta_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceClient) GetResourceDetail(ctx context.Context, in *GetResourceDetailReq, opts ...grpc.CallOption) (*ResourceDetail, error) {
	out := new(ResourceDetail)
	err := c.cc.Invoke(ctx, Resource_GetResourceDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceClient) ListRandResource(ctx context.Context, in *ListRandResourceReq, opts ...grpc.CallOption) (*ListRandResourceResp, error) {
	out := new(ListRandResourceResp)
	err := c.cc.Invoke(ctx, Resource_ListRandResource_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceClient) RefreshAllCache(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Resource_RefreshAllCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ResourceServer is the server API for Resource service.
// All implementations must embed UnimplementedResourceServer
// for forward compatibility
type ResourceServer interface {
	GetGrades(context.Context, *emptypb.Empty) (*GradesResp, error)
	GetSubjects(context.Context, *emptypb.Empty) (*SubjectsResp, error)
	GetResourceType(context.Context, *emptypb.Empty) (*ResourceTypeResp, error)
	GetResourceGroups(context.Context, *GetResourceGroupsReq) (*GetResourceGroupsResp, error)
	GetResourceList(context.Context, *GetResourceListReq) (*GetResourceListResp, error)
	GetResourceMeta(context.Context, *emptypb.Empty) (*ResourceMetaResp, error)
	GetResourceDetail(context.Context, *GetResourceDetailReq) (*ResourceDetail, error)
	ListRandResource(context.Context, *ListRandResourceReq) (*ListRandResourceResp, error)
	RefreshAllCache(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	mustEmbedUnimplementedResourceServer()
}

// UnimplementedResourceServer must be embedded to have forward compatible implementations.
type UnimplementedResourceServer struct {
}

func (UnimplementedResourceServer) GetGrades(context.Context, *emptypb.Empty) (*GradesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGrades not implemented")
}
func (UnimplementedResourceServer) GetSubjects(context.Context, *emptypb.Empty) (*SubjectsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubjects not implemented")
}
func (UnimplementedResourceServer) GetResourceType(context.Context, *emptypb.Empty) (*ResourceTypeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceType not implemented")
}
func (UnimplementedResourceServer) GetResourceGroups(context.Context, *GetResourceGroupsReq) (*GetResourceGroupsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceGroups not implemented")
}
func (UnimplementedResourceServer) GetResourceList(context.Context, *GetResourceListReq) (*GetResourceListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceList not implemented")
}
func (UnimplementedResourceServer) GetResourceMeta(context.Context, *emptypb.Empty) (*ResourceMetaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceMeta not implemented")
}
func (UnimplementedResourceServer) GetResourceDetail(context.Context, *GetResourceDetailReq) (*ResourceDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceDetail not implemented")
}
func (UnimplementedResourceServer) ListRandResource(context.Context, *ListRandResourceReq) (*ListRandResourceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRandResource not implemented")
}
func (UnimplementedResourceServer) RefreshAllCache(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshAllCache not implemented")
}
func (UnimplementedResourceServer) mustEmbedUnimplementedResourceServer() {}

// UnsafeResourceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ResourceServer will
// result in compilation errors.
type UnsafeResourceServer interface {
	mustEmbedUnimplementedResourceServer()
}

func RegisterResourceServer(s grpc.ServiceRegistrar, srv ResourceServer) {
	s.RegisterService(&Resource_ServiceDesc, srv)
}

func _Resource_GetGrades_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceServer).GetGrades(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Resource_GetGrades_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceServer).GetGrades(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Resource_GetSubjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceServer).GetSubjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Resource_GetSubjects_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceServer).GetSubjects(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Resource_GetResourceType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceServer).GetResourceType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Resource_GetResourceType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceServer).GetResourceType(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Resource_GetResourceGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceGroupsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceServer).GetResourceGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Resource_GetResourceGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceServer).GetResourceGroups(ctx, req.(*GetResourceGroupsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Resource_GetResourceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceServer).GetResourceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Resource_GetResourceList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceServer).GetResourceList(ctx, req.(*GetResourceListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Resource_GetResourceMeta_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceServer).GetResourceMeta(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Resource_GetResourceMeta_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceServer).GetResourceMeta(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Resource_GetResourceDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceServer).GetResourceDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Resource_GetResourceDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceServer).GetResourceDetail(ctx, req.(*GetResourceDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Resource_ListRandResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRandResourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceServer).ListRandResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Resource_ListRandResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceServer).ListRandResource(ctx, req.(*ListRandResourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Resource_RefreshAllCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceServer).RefreshAllCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Resource_RefreshAllCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceServer).RefreshAllCache(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Resource_ServiceDesc is the grpc.ServiceDesc for Resource service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Resource_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.resource.v1.Resource",
	HandlerType: (*ResourceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGrades",
			Handler:    _Resource_GetGrades_Handler,
		},
		{
			MethodName: "GetSubjects",
			Handler:    _Resource_GetSubjects_Handler,
		},
		{
			MethodName: "GetResourceType",
			Handler:    _Resource_GetResourceType_Handler,
		},
		{
			MethodName: "GetResourceGroups",
			Handler:    _Resource_GetResourceGroups_Handler,
		},
		{
			MethodName: "GetResourceList",
			Handler:    _Resource_GetResourceList_Handler,
		},
		{
			MethodName: "GetResourceMeta",
			Handler:    _Resource_GetResourceMeta_Handler,
		},
		{
			MethodName: "GetResourceDetail",
			Handler:    _Resource_GetResourceDetail_Handler,
		},
		{
			MethodName: "ListRandResource",
			Handler:    _Resource_ListRandResource_Handler,
		},
		{
			MethodName: "RefreshAllCache",
			Handler:    _Resource_RefreshAllCache_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/resource/v1/resource.proto",
}
