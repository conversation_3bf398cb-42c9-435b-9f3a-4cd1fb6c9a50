// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.23.3
// source: api/resource/v1/resource.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListRandResourceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject       string `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`                                  // 学科
	Grade         string `protobuf:"bytes,2,opt,name=grade,proto3" json:"grade,omitempty"`                                      // 年级
	ActivityTopic string `protobuf:"bytes,3,opt,name=activity_topic,json=activityTopic,proto3" json:"activity_topic,omitempty"` // 活动主题
	ExcludeUrls   string `protobuf:"bytes,4,opt,name=exclude_urls,json=excludeUrls,proto3" json:"exclude_urls,omitempty"`       // 排除的url
	PageSize      int32  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`               // 每页数量
}

func (x *ListRandResourceReq) Reset() {
	*x = ListRandResourceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRandResourceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRandResourceReq) ProtoMessage() {}

func (x *ListRandResourceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRandResourceReq.ProtoReflect.Descriptor instead.
func (*ListRandResourceReq) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{0}
}

func (x *ListRandResourceReq) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *ListRandResourceReq) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *ListRandResourceReq) GetActivityTopic() string {
	if x != nil {
		return x.ActivityTopic
	}
	return ""
}

func (x *ListRandResourceReq) GetExcludeUrls() string {
	if x != nil {
		return x.ExcludeUrls
	}
	return ""
}

func (x *ListRandResourceReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type EduTermInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Term        string   `protobuf:"bytes,2,opt,name=term,proto3" json:"term,omitempty"`
	Knowledge_1 string   `protobuf:"bytes,3,opt,name=knowledge_1,json=knowledge1,proto3" json:"knowledge_1,omitempty"`
	Knowledge_2 string   `protobuf:"bytes,4,opt,name=knowledge_2,json=knowledge2,proto3" json:"knowledge_2,omitempty"`
	Title       string   `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Tag         []string `protobuf:"bytes,6,rep,name=tag,proto3" json:"tag,omitempty"`
	Path        string   `protobuf:"bytes,7,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *EduTermInfo) Reset() {
	*x = EduTermInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduTermInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduTermInfo) ProtoMessage() {}

func (x *EduTermInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduTermInfo.ProtoReflect.Descriptor instead.
func (*EduTermInfo) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{1}
}

func (x *EduTermInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EduTermInfo) GetTerm() string {
	if x != nil {
		return x.Term
	}
	return ""
}

func (x *EduTermInfo) GetKnowledge_1() string {
	if x != nil {
		return x.Knowledge_1
	}
	return ""
}

func (x *EduTermInfo) GetKnowledge_2() string {
	if x != nil {
		return x.Knowledge_2
	}
	return ""
}

func (x *EduTermInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *EduTermInfo) GetTag() []string {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *EduTermInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type ListRandResourceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Worksheets    []*EduResource `protobuf:"bytes,1,rep,name=worksheets,proto3" json:"worksheets,omitempty"`                            // 随机资源列表
	ColoringPages []*EduResource `protobuf:"bytes,2,rep,name=coloring_pages,json=coloringPages,proto3" json:"coloring_pages,omitempty"` // 随机资源列表
	Terms         []*EduTermInfo `protobuf:"bytes,3,rep,name=terms,proto3" json:"terms,omitempty"`                                      // 随机资源列表
}

func (x *ListRandResourceResp) Reset() {
	*x = ListRandResourceResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRandResourceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRandResourceResp) ProtoMessage() {}

func (x *ListRandResourceResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRandResourceResp.ProtoReflect.Descriptor instead.
func (*ListRandResourceResp) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{2}
}

func (x *ListRandResourceResp) GetWorksheets() []*EduResource {
	if x != nil {
		return x.Worksheets
	}
	return nil
}

func (x *ListRandResourceResp) GetColoringPages() []*EduResource {
	if x != nil {
		return x.ColoringPages
	}
	return nil
}

func (x *ListRandResourceResp) GetTerms() []*EduTermInfo {
	if x != nil {
		return x.Terms
	}
	return nil
}

type GetResourceDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url          string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`                                       // 资源链接
	ResourceType string `protobuf:"bytes,2,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"` // 资源类型
	Subject      string `protobuf:"bytes,3,opt,name=subject,proto3" json:"subject,omitempty"`                               // 学科
	Grade        string `protobuf:"bytes,4,opt,name=grade,proto3" json:"grade,omitempty"`                                   // 年级
}

func (x *GetResourceDetailReq) Reset() {
	*x = GetResourceDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceDetailReq) ProtoMessage() {}

func (x *GetResourceDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceDetailReq.ProtoReflect.Descriptor instead.
func (*GetResourceDetailReq) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{3}
}

func (x *GetResourceDetailReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GetResourceDetailReq) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *GetResourceDetailReq) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *GetResourceDetailReq) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

type ResourceDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resource *EduResource `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`              // 资源详情
	PreUrl   string       `protobuf:"bytes,2,opt,name=pre_url,json=preUrl,proto3" json:"pre_url,omitempty"`    // 资源链接
	NextUrl  string       `protobuf:"bytes,3,opt,name=next_url,json=nextUrl,proto3" json:"next_url,omitempty"` // 下一个资源链接
}

func (x *ResourceDetail) Reset() {
	*x = ResourceDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceDetail) ProtoMessage() {}

func (x *ResourceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceDetail.ProtoReflect.Descriptor instead.
func (*ResourceDetail) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{4}
}

func (x *ResourceDetail) GetResource() *EduResource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *ResourceDetail) GetPreUrl() string {
	if x != nil {
		return x.PreUrl
	}
	return ""
}

func (x *ResourceDetail) GetNextUrl() string {
	if x != nil {
		return x.NextUrl
	}
	return ""
}

type EduResourceTitle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url             string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	H1Title         string `protobuf:"bytes,2,opt,name=h1_title,json=h1Title,proto3" json:"h1_title,omitempty"`
	Description     string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	MetaTitle       string `protobuf:"bytes,4,opt,name=meta_title,json=metaTitle,proto3" json:"meta_title,omitempty"`
	MetaDescription string `protobuf:"bytes,5,opt,name=meta_description,json=metaDescription,proto3" json:"meta_description,omitempty"`
	MetaKeywords    string `protobuf:"bytes,6,opt,name=meta_keywords,json=metaKeywords,proto3" json:"meta_keywords,omitempty"`
	Schemas         string `protobuf:"bytes,7,opt,name=schemas,proto3" json:"schemas,omitempty"`
	Filter          string `protobuf:"bytes,8,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *EduResourceTitle) Reset() {
	*x = EduResourceTitle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduResourceTitle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduResourceTitle) ProtoMessage() {}

func (x *EduResourceTitle) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduResourceTitle.ProtoReflect.Descriptor instead.
func (*EduResourceTitle) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{5}
}

func (x *EduResourceTitle) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *EduResourceTitle) GetH1Title() string {
	if x != nil {
		return x.H1Title
	}
	return ""
}

func (x *EduResourceTitle) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EduResourceTitle) GetMetaTitle() string {
	if x != nil {
		return x.MetaTitle
	}
	return ""
}

func (x *EduResourceTitle) GetMetaDescription() string {
	if x != nil {
		return x.MetaDescription
	}
	return ""
}

func (x *EduResourceTitle) GetMetaKeywords() string {
	if x != nil {
		return x.MetaKeywords
	}
	return ""
}

func (x *EduResourceTitle) GetSchemas() string {
	if x != nil {
		return x.Schemas
	}
	return ""
}

func (x *EduResourceTitle) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

type EduResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主键ID
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 资源类型 worksheet video coloringpage
	ResourceType string `protobuf:"bytes,2,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// 学科
	Subject string `protobuf:"bytes,3,opt,name=subject,proto3" json:"subject,omitempty"`
	// 年级 0 代表所有年级
	Grade string `protobuf:"bytes,4,opt,name=grade,proto3" json:"grade,omitempty"`
	// 标题
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// 二知识点 activity_topic
	LearningTopic string `protobuf:"bytes,6,opt,name=learning_topic,json=learningTopic,proto3" json:"learning_topic,omitempty"`
	// 三知识点
	Resource string `protobuf:"bytes,7,opt,name=resource,proto3" json:"resource,omitempty"`
	// 四级知识点
	ResourceDetail string `protobuf:"bytes,8,opt,name=resource_detail,json=resourceDetail,proto3" json:"resource_detail,omitempty"`
	// 最细粒度知识点
	Standards string `protobuf:"bytes,9,opt,name=standards,proto3" json:"standards,omitempty"`
	// 简介
	ResourceDescription string `protobuf:"bytes,10,opt,name=resource_description,json=resourceDescription,proto3" json:"resource_description,omitempty"`
	// 内容 以json格式存储
	ExtraData       *structpb.Struct `protobuf:"bytes,11,opt,name=extra_data,json=extraData,proto3" json:"extra_data,omitempty"` // Protobuf 的 `text` 类型不存在，通常使用 `string` 来存储JSON
	Url             string           `protobuf:"bytes,12,opt,name=url,proto3" json:"url,omitempty"`
	MetaTitle       string           `protobuf:"bytes,13,opt,name=meta_title,json=metaTitle,proto3" json:"meta_title,omitempty"`
	MetaDescription string           `protobuf:"bytes,14,opt,name=meta_description,json=metaDescription,proto3" json:"meta_description,omitempty"`
	MetaKeywords    string           `protobuf:"bytes,15,opt,name=meta_keywords,json=metaKeywords,proto3" json:"meta_keywords,omitempty"`
	Schemas         string           `protobuf:"bytes,16,opt,name=schemas,proto3" json:"schemas,omitempty"`
	LearningModule  string           `protobuf:"bytes,17,opt,name=learning_module,json=learningModule,proto3" json:"learning_module,omitempty"`
	UpdatedAt       string           `protobuf:"bytes,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *EduResource) Reset() {
	*x = EduResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduResource) ProtoMessage() {}

func (x *EduResource) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduResource.ProtoReflect.Descriptor instead.
func (*EduResource) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{6}
}

func (x *EduResource) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EduResource) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *EduResource) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *EduResource) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *EduResource) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *EduResource) GetLearningTopic() string {
	if x != nil {
		return x.LearningTopic
	}
	return ""
}

func (x *EduResource) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *EduResource) GetResourceDetail() string {
	if x != nil {
		return x.ResourceDetail
	}
	return ""
}

func (x *EduResource) GetStandards() string {
	if x != nil {
		return x.Standards
	}
	return ""
}

func (x *EduResource) GetResourceDescription() string {
	if x != nil {
		return x.ResourceDescription
	}
	return ""
}

func (x *EduResource) GetExtraData() *structpb.Struct {
	if x != nil {
		return x.ExtraData
	}
	return nil
}

func (x *EduResource) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *EduResource) GetMetaTitle() string {
	if x != nil {
		return x.MetaTitle
	}
	return ""
}

func (x *EduResource) GetMetaDescription() string {
	if x != nil {
		return x.MetaDescription
	}
	return ""
}

func (x *EduResource) GetMetaKeywords() string {
	if x != nil {
		return x.MetaKeywords
	}
	return ""
}

func (x *EduResource) GetSchemas() string {
	if x != nil {
		return x.Schemas
	}
	return ""
}

func (x *EduResource) GetLearningModule() string {
	if x != nil {
		return x.LearningModule
	}
	return ""
}

func (x *EduResource) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type EduResourceGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title     string         `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`         // 资源组标题
	Resources []*EduResource `protobuf:"bytes,2,rep,name=resources,proto3" json:"resources,omitempty"` // 资源列表
	Total     int32          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`        // 总数
}

func (x *EduResourceGroup) Reset() {
	*x = EduResourceGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduResourceGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduResourceGroup) ProtoMessage() {}

func (x *EduResourceGroup) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduResourceGroup.ProtoReflect.Descriptor instead.
func (*EduResourceGroup) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{7}
}

func (x *EduResourceGroup) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *EduResourceGroup) GetResources() []*EduResource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *EduResourceGroup) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetResourceGroupsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Groups []*EduResourceGroup `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"` // 资源组列表
	Title  *EduResourceTitle   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
}

func (x *GetResourceGroupsResp) Reset() {
	*x = GetResourceGroupsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceGroupsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceGroupsResp) ProtoMessage() {}

func (x *GetResourceGroupsResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceGroupsResp.ProtoReflect.Descriptor instead.
func (*GetResourceGroupsResp) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{8}
}

func (x *GetResourceGroupsResp) GetGroups() []*EduResourceGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *GetResourceGroupsResp) GetTitle() *EduResourceTitle {
	if x != nil {
		return x.Title
	}
	return nil
}

type GetResourceListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resources []*EduResource    `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"` // 资源列表
	Title     *EduResourceTitle `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Total     int32             `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"` // 总数
}

func (x *GetResourceListResp) Reset() {
	*x = GetResourceListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceListResp) ProtoMessage() {}

func (x *GetResourceListResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceListResp.ProtoReflect.Descriptor instead.
func (*GetResourceListResp) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{9}
}

func (x *GetResourceListResp) GetResources() []*EduResource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *GetResourceListResp) GetTitle() *EduResourceTitle {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *GetResourceListResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetResourceGroupsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceType string `protobuf:"bytes,1,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"` // 资源分类
	Subject      string `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`                               // 学科
	Grade        string `protobuf:"bytes,3,opt,name=grade,proto3" json:"grade,omitempty"`                                   // 年级
	Page         int32  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`                                    // 页码
	PageSize     int32  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`            // 每页数量
	Path         string `protobuf:"bytes,6,opt,name=path,proto3" json:"path,omitempty"`                                     // 资源路径
}

func (x *GetResourceGroupsReq) Reset() {
	*x = GetResourceGroupsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceGroupsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceGroupsReq) ProtoMessage() {}

func (x *GetResourceGroupsReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceGroupsReq.ProtoReflect.Descriptor instead.
func (*GetResourceGroupsReq) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{10}
}

func (x *GetResourceGroupsReq) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *GetResourceGroupsReq) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *GetResourceGroupsReq) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *GetResourceGroupsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetResourceGroupsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetResourceGroupsReq) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type GetResourceListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceType string `protobuf:"bytes,1,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"` // 资源分类
	Subject      string `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`                               // 学科
	Grade        string `protobuf:"bytes,3,opt,name=grade,proto3" json:"grade,omitempty"`                                   // 年级
	Page         int32  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`                                    // 页码
	PageSize     int32  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`            // 每页数量
	Path         string `protobuf:"bytes,6,opt,name=path,proto3" json:"path,omitempty"`                                     // 资源路径
}

func (x *GetResourceListReq) Reset() {
	*x = GetResourceListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetResourceListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceListReq) ProtoMessage() {}

func (x *GetResourceListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceListReq.ProtoReflect.Descriptor instead.
func (*GetResourceListReq) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{11}
}

func (x *GetResourceListReq) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *GetResourceListReq) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *GetResourceListReq) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *GetResourceListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetResourceListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetResourceListReq) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type ResourceType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`   // 资源分类名称
	Nodes []*ResourceType `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"` // 子分类
}

func (x *ResourceType) Reset() {
	*x = ResourceType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceType) ProtoMessage() {}

func (x *ResourceType) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceType.ProtoReflect.Descriptor instead.
func (*ResourceType) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{12}
}

func (x *ResourceType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceType) GetNodes() []*ResourceType {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type ResourceTypeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Types []*ResourceType `protobuf:"bytes,1,rep,name=types,proto3" json:"types,omitempty"` // 资源分类
}

func (x *ResourceTypeResp) Reset() {
	*x = ResourceTypeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceTypeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceTypeResp) ProtoMessage() {}

func (x *ResourceTypeResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceTypeResp.ProtoReflect.Descriptor instead.
func (*ResourceTypeResp) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{13}
}

func (x *ResourceTypeResp) GetTypes() []*ResourceType {
	if x != nil {
		return x.Types
	}
	return nil
}

type EduGrade struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`   // 年级名称
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"` // 年级值
}

func (x *EduGrade) Reset() {
	*x = EduGrade{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduGrade) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduGrade) ProtoMessage() {}

func (x *EduGrade) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduGrade.ProtoReflect.Descriptor instead.
func (*EduGrade) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{14}
}

func (x *EduGrade) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EduGrade) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type GradesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Grades []*EduGrade `protobuf:"bytes,1,rep,name=grades,proto3" json:"grades,omitempty"` // 年级
}

func (x *GradesResp) Reset() {
	*x = GradesResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GradesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GradesResp) ProtoMessage() {}

func (x *GradesResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GradesResp.ProtoReflect.Descriptor instead.
func (*GradesResp) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{15}
}

func (x *GradesResp) GetGrades() []*EduGrade {
	if x != nil {
		return x.Grades
	}
	return nil
}

type EduSubjectNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Nodes []*EduSubjectNode `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"` // 值
}

func (x *EduSubjectNode) Reset() {
	*x = EduSubjectNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EduSubjectNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EduSubjectNode) ProtoMessage() {}

func (x *EduSubjectNode) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EduSubjectNode.ProtoReflect.Descriptor instead.
func (*EduSubjectNode) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{16}
}

func (x *EduSubjectNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EduSubjectNode) GetNodes() []*EduSubjectNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type Subject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string           `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Topics []*ResourceTopic `protobuf:"bytes,2,rep,name=topics,proto3" json:"topics,omitempty"` // 值
}

func (x *Subject) Reset() {
	*x = Subject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Subject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subject) ProtoMessage() {}

func (x *Subject) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subject.ProtoReflect.Descriptor instead.
func (*Subject) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{17}
}

func (x *Subject) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Subject) GetTopics() []*ResourceTopic {
	if x != nil {
		return x.Topics
	}
	return nil
}

type ResourceTopic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Modules []string `protobuf:"bytes,2,rep,name=modules,proto3" json:"modules,omitempty"` // 值
}

func (x *ResourceTopic) Reset() {
	*x = ResourceTopic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceTopic) ProtoMessage() {}

func (x *ResourceTopic) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceTopic.ProtoReflect.Descriptor instead.
func (*ResourceTopic) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{18}
}

func (x *ResourceTopic) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceTopic) GetModules() []string {
	if x != nil {
		return x.Modules
	}
	return nil
}

type SubjectsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subjects []*EduSubjectNode `protobuf:"bytes,1,rep,name=subjects,proto3" json:"subjects,omitempty"`
}

func (x *SubjectsResp) Reset() {
	*x = SubjectsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubjectsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubjectsResp) ProtoMessage() {}

func (x *SubjectsResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubjectsResp.ProtoReflect.Descriptor instead.
func (*SubjectsResp) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{19}
}

func (x *SubjectsResp) GetSubjects() []*EduSubjectNode {
	if x != nil {
		return x.Subjects
	}
	return nil
}

type ResourceMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`         // 资源名称
	Children *ResourceMeta_Children `protobuf:"bytes,2,opt,name=children,proto3" json:"children,omitempty"` // 子资源
}

func (x *ResourceMeta) Reset() {
	*x = ResourceMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceMeta) ProtoMessage() {}

func (x *ResourceMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceMeta.ProtoReflect.Descriptor instead.
func (*ResourceMeta) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{20}
}

func (x *ResourceMeta) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceMeta) GetChildren() *ResourceMeta_Children {
	if x != nil {
		return x.Children
	}
	return nil
}

type ResourceMetaResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Metas []*ResourceMeta `protobuf:"bytes,1,rep,name=metas,proto3" json:"metas,omitempty"` // 资源元数据
}

func (x *ResourceMetaResp) Reset() {
	*x = ResourceMetaResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceMetaResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceMetaResp) ProtoMessage() {}

func (x *ResourceMetaResp) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceMetaResp.ProtoReflect.Descriptor instead.
func (*ResourceMetaResp) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{21}
}

func (x *ResourceMetaResp) GetMetas() []*ResourceMeta {
	if x != nil {
		return x.Metas
	}
	return nil
}

type ResourceMeta_Children struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Grades   []*EduGrade       `protobuf:"bytes,1,rep,name=grades,proto3" json:"grades,omitempty"`     // 年级
	Subjects []*EduSubjectNode `protobuf:"bytes,2,rep,name=subjects,proto3" json:"subjects,omitempty"` // 学科
	Types    []*ResourceType   `protobuf:"bytes,3,rep,name=types,proto3" json:"types,omitempty"`       // 资源类型
}

func (x *ResourceMeta_Children) Reset() {
	*x = ResourceMeta_Children{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_resource_v1_resource_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceMeta_Children) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceMeta_Children) ProtoMessage() {}

func (x *ResourceMeta_Children) ProtoReflect() protoreflect.Message {
	mi := &file_api_resource_v1_resource_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceMeta_Children.ProtoReflect.Descriptor instead.
func (*ResourceMeta_Children) Descriptor() ([]byte, []int) {
	return file_api_resource_v1_resource_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ResourceMeta_Children) GetGrades() []*EduGrade {
	if x != nil {
		return x.Grades
	}
	return nil
}

func (x *ResourceMeta_Children) GetSubjects() []*EduSubjectNode {
	if x != nil {
		return x.Subjects
	}
	return nil
}

func (x *ResourceMeta_Children) GetTypes() []*ResourceType {
	if x != nil {
		return x.Types
	}
	return nil
}

var File_api_resource_v1_resource_proto protoreflect.FileDescriptor

var file_api_resource_v1_resource_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0f, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xac, 0x01, 0x0a, 0x13, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xaf, 0x01, 0x0a, 0x0b, 0x45, 0x64,
	0x75, 0x54, 0x65, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x72,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x72, 0x6d, 0x12, 0x1f, 0x0a,
	0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x31, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x31, 0x12, 0x1f,
	0x0a, 0x0b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x32, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x32, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0xcd, 0x01, 0x0a, 0x14,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x68, 0x65, 0x65,
	0x74, 0x73, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x74, 0x65, 0x72, 0x6d, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x54, 0x65, 0x72, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x22, 0x7d, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x22, 0x7e, 0x0a, 0x0e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x38, 0x0a, 0x08,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x64, 0x75, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x72, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x19, 0x0a, 0x08, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6e, 0x65, 0x78, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x82, 0x02, 0x0a, 0x10, 0x45,
	0x64, 0x75, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x31, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x31, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x61,
	0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6d, 0x65, 0x74, 0x61, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22,
	0xe0, 0x04, 0x0a, 0x0b, 0x45, 0x64, 0x75, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x65,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6c, 0x65, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x72, 0x64, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x6e, 0x64,
	0x61, 0x72, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x14, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x29, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x74, 0x61,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x65, 0x74, 0x61, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x61, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x65,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x65, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x7a, 0x0a, 0x10, 0x45, 0x64, 0x75, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x09,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x64, 0x75, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x8b,
	0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0xa0, 0x01, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x64, 0x75, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0xb0, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x22, 0xae, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x22, 0x57, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x47, 0x0a, 0x10,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x33, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x34, 0x0a, 0x08, 0x45, 0x64, 0x75, 0x47, 0x72, 0x61, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x3f, 0x0a, 0x0a, 0x47,
	0x72, 0x61, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x06, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x47,
	0x72, 0x61, 0x64, 0x65, 0x52, 0x06, 0x67, 0x72, 0x61, 0x64, 0x65, 0x73, 0x22, 0x5b, 0x0a, 0x0e,
	0x45, 0x64, 0x75, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x35, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x55, 0x0a, 0x07, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69,
	0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73,
	0x22, 0x3d, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x22,
	0x4b, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x3b, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x08, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x22, 0x98, 0x02, 0x0a,
	0x0c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x42, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x2e, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x52, 0x08, 0x63, 0x68, 0x69,
	0x6c, 0x64, 0x72, 0x65, 0x6e, 0x1a, 0xaf, 0x01, 0x0a, 0x08, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72,
	0x65, 0x6e, 0x12, 0x31, 0x0a, 0x06, 0x67, 0x72, 0x61, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x47, 0x72, 0x61, 0x64, 0x65, 0x52, 0x06, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x75, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x12, 0x33, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x47, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x05, 0x6d,
	0x65, 0x74, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x05, 0x6d, 0x65, 0x74, 0x61, 0x73,
	0x32, 0x95, 0x09, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x6e, 0x0a,
	0x09, 0x47, 0x65, 0x74, 0x47, 0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x61, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x12, 0x24, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c,
	0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x67, 0x72, 0x61, 0x64, 0x65, 0x73, 0x12, 0x74, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x12, 0x26, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x75, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x12, 0x79, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x90,
	0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x12, 0x24, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x12, 0x88, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c,
	0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x78, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x24, 0x12, 0x22, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x12, 0x89, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x12, 0x24, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x8b, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x61, 0x6e, 0x64, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x61,
	0x6e, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x61, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x61, 0x6e, 0x64,
	0x12, 0x76, 0x0a, 0x0f, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x6c, 0x6c, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x12, 0x2b, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x2d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x42, 0x37, 0x0a, 0x0f, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x22, 0x68,
	0x77, 0x2d, 0x70, 0x61, 0x61, 0x73, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_resource_v1_resource_proto_rawDescOnce sync.Once
	file_api_resource_v1_resource_proto_rawDescData = file_api_resource_v1_resource_proto_rawDesc
)

func file_api_resource_v1_resource_proto_rawDescGZIP() []byte {
	file_api_resource_v1_resource_proto_rawDescOnce.Do(func() {
		file_api_resource_v1_resource_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_resource_v1_resource_proto_rawDescData)
	})
	return file_api_resource_v1_resource_proto_rawDescData
}

var file_api_resource_v1_resource_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_api_resource_v1_resource_proto_goTypes = []interface{}{
	(*ListRandResourceReq)(nil),   // 0: api.resource.v1.ListRandResourceReq
	(*EduTermInfo)(nil),           // 1: api.resource.v1.EduTermInfo
	(*ListRandResourceResp)(nil),  // 2: api.resource.v1.ListRandResourceResp
	(*GetResourceDetailReq)(nil),  // 3: api.resource.v1.GetResourceDetailReq
	(*ResourceDetail)(nil),        // 4: api.resource.v1.ResourceDetail
	(*EduResourceTitle)(nil),      // 5: api.resource.v1.EduResourceTitle
	(*EduResource)(nil),           // 6: api.resource.v1.EduResource
	(*EduResourceGroup)(nil),      // 7: api.resource.v1.EduResourceGroup
	(*GetResourceGroupsResp)(nil), // 8: api.resource.v1.GetResourceGroupsResp
	(*GetResourceListResp)(nil),   // 9: api.resource.v1.GetResourceListResp
	(*GetResourceGroupsReq)(nil),  // 10: api.resource.v1.GetResourceGroupsReq
	(*GetResourceListReq)(nil),    // 11: api.resource.v1.GetResourceListReq
	(*ResourceType)(nil),          // 12: api.resource.v1.ResourceType
	(*ResourceTypeResp)(nil),      // 13: api.resource.v1.ResourceTypeResp
	(*EduGrade)(nil),              // 14: api.resource.v1.EduGrade
	(*GradesResp)(nil),            // 15: api.resource.v1.GradesResp
	(*EduSubjectNode)(nil),        // 16: api.resource.v1.EduSubjectNode
	(*Subject)(nil),               // 17: api.resource.v1.Subject
	(*ResourceTopic)(nil),         // 18: api.resource.v1.ResourceTopic
	(*SubjectsResp)(nil),          // 19: api.resource.v1.SubjectsResp
	(*ResourceMeta)(nil),          // 20: api.resource.v1.ResourceMeta
	(*ResourceMetaResp)(nil),      // 21: api.resource.v1.ResourceMetaResp
	(*ResourceMeta_Children)(nil), // 22: api.resource.v1.ResourceMeta.Children
	(*structpb.Struct)(nil),       // 23: google.protobuf.Struct
	(*emptypb.Empty)(nil),         // 24: google.protobuf.Empty
}
var file_api_resource_v1_resource_proto_depIdxs = []int32{
	6,  // 0: api.resource.v1.ListRandResourceResp.worksheets:type_name -> api.resource.v1.EduResource
	6,  // 1: api.resource.v1.ListRandResourceResp.coloring_pages:type_name -> api.resource.v1.EduResource
	1,  // 2: api.resource.v1.ListRandResourceResp.terms:type_name -> api.resource.v1.EduTermInfo
	6,  // 3: api.resource.v1.ResourceDetail.resource:type_name -> api.resource.v1.EduResource
	23, // 4: api.resource.v1.EduResource.extra_data:type_name -> google.protobuf.Struct
	6,  // 5: api.resource.v1.EduResourceGroup.resources:type_name -> api.resource.v1.EduResource
	7,  // 6: api.resource.v1.GetResourceGroupsResp.groups:type_name -> api.resource.v1.EduResourceGroup
	5,  // 7: api.resource.v1.GetResourceGroupsResp.title:type_name -> api.resource.v1.EduResourceTitle
	6,  // 8: api.resource.v1.GetResourceListResp.resources:type_name -> api.resource.v1.EduResource
	5,  // 9: api.resource.v1.GetResourceListResp.title:type_name -> api.resource.v1.EduResourceTitle
	12, // 10: api.resource.v1.ResourceType.nodes:type_name -> api.resource.v1.ResourceType
	12, // 11: api.resource.v1.ResourceTypeResp.types:type_name -> api.resource.v1.ResourceType
	14, // 12: api.resource.v1.GradesResp.grades:type_name -> api.resource.v1.EduGrade
	16, // 13: api.resource.v1.EduSubjectNode.nodes:type_name -> api.resource.v1.EduSubjectNode
	18, // 14: api.resource.v1.Subject.topics:type_name -> api.resource.v1.ResourceTopic
	16, // 15: api.resource.v1.SubjectsResp.subjects:type_name -> api.resource.v1.EduSubjectNode
	22, // 16: api.resource.v1.ResourceMeta.children:type_name -> api.resource.v1.ResourceMeta.Children
	20, // 17: api.resource.v1.ResourceMetaResp.metas:type_name -> api.resource.v1.ResourceMeta
	14, // 18: api.resource.v1.ResourceMeta.Children.grades:type_name -> api.resource.v1.EduGrade
	16, // 19: api.resource.v1.ResourceMeta.Children.subjects:type_name -> api.resource.v1.EduSubjectNode
	12, // 20: api.resource.v1.ResourceMeta.Children.types:type_name -> api.resource.v1.ResourceType
	24, // 21: api.resource.v1.Resource.GetGrades:input_type -> google.protobuf.Empty
	24, // 22: api.resource.v1.Resource.GetSubjects:input_type -> google.protobuf.Empty
	24, // 23: api.resource.v1.Resource.GetResourceType:input_type -> google.protobuf.Empty
	10, // 24: api.resource.v1.Resource.GetResourceGroups:input_type -> api.resource.v1.GetResourceGroupsReq
	11, // 25: api.resource.v1.Resource.GetResourceList:input_type -> api.resource.v1.GetResourceListReq
	24, // 26: api.resource.v1.Resource.GetResourceMeta:input_type -> google.protobuf.Empty
	3,  // 27: api.resource.v1.Resource.GetResourceDetail:input_type -> api.resource.v1.GetResourceDetailReq
	0,  // 28: api.resource.v1.Resource.ListRandResource:input_type -> api.resource.v1.ListRandResourceReq
	24, // 29: api.resource.v1.Resource.RefreshAllCache:input_type -> google.protobuf.Empty
	15, // 30: api.resource.v1.Resource.GetGrades:output_type -> api.resource.v1.GradesResp
	19, // 31: api.resource.v1.Resource.GetSubjects:output_type -> api.resource.v1.SubjectsResp
	13, // 32: api.resource.v1.Resource.GetResourceType:output_type -> api.resource.v1.ResourceTypeResp
	8,  // 33: api.resource.v1.Resource.GetResourceGroups:output_type -> api.resource.v1.GetResourceGroupsResp
	9,  // 34: api.resource.v1.Resource.GetResourceList:output_type -> api.resource.v1.GetResourceListResp
	21, // 35: api.resource.v1.Resource.GetResourceMeta:output_type -> api.resource.v1.ResourceMetaResp
	4,  // 36: api.resource.v1.Resource.GetResourceDetail:output_type -> api.resource.v1.ResourceDetail
	2,  // 37: api.resource.v1.Resource.ListRandResource:output_type -> api.resource.v1.ListRandResourceResp
	24, // 38: api.resource.v1.Resource.RefreshAllCache:output_type -> google.protobuf.Empty
	30, // [30:39] is the sub-list for method output_type
	21, // [21:30] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_api_resource_v1_resource_proto_init() }
func file_api_resource_v1_resource_proto_init() {
	if File_api_resource_v1_resource_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_resource_v1_resource_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRandResourceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduTermInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRandResourceResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduResourceTitle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduResourceGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceGroupsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceGroupsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetResourceListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceTypeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduGrade); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GradesResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EduSubjectNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Subject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceTopic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubjectsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceMetaResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_resource_v1_resource_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceMeta_Children); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_resource_v1_resource_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_resource_v1_resource_proto_goTypes,
		DependencyIndexes: file_api_resource_v1_resource_proto_depIdxs,
		MessageInfos:      file_api_resource_v1_resource_proto_msgTypes,
	}.Build()
	File_api_resource_v1_resource_proto = out.File
	file_api_resource_v1_resource_proto_rawDesc = nil
	file_api_resource_v1_resource_proto_goTypes = nil
	file_api_resource_v1_resource_proto_depIdxs = nil
}
