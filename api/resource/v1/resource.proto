syntax = "proto3";

package api.resource.v1;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";

option go_package = "hw-paas-service/api/resource/v1;v1";
option java_multiple_files = true;
option java_package = "api.resource.v1";

service Resource {
 rpc GetGrades(google.protobuf.Empty) returns (GradesResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/grades"
    };
  };


  rpc GetSubjects(google.protobuf.Empty) returns (SubjectsResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/subjects"
    };
  };


  rpc GetResourceType(google.protobuf.Empty) returns (ResourceTypeResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/types"
    };
  };

  rpc GetResourceGroups(GetResourceGroupsReq) returns (GetResourceGroupsResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/groups"
    };
  };

  rpc GetResourceList(GetResourceListReq) returns (GetResourceListResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/list"
    };
  };

  rpc GetResourceMeta(google.protobuf.Empty) returns (ResourceMetaResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/meta"
    };
  }

  rpc GetResourceDetail(GetResourceDetailReq) returns (ResourceDetail) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/detail"
    };
  }

  rpc ListRandResource(ListRandResourceReq) returns (ListRandResourceResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/rand"
    };
  }
  
  rpc RefreshAllCache(google.protobuf.Empty) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/refresh-cache"
    };
  }
}

message ListRandResourceReq {
  string subject = 1; // 学科
  string grade = 2; // 年级
  string activity_topic = 3; // 活动主题
  string exclude_urls = 4; // 排除的url
  int32 page_size = 5; // 每页数量
}

message EduTermInfo {
  int32 id = 1;
  string term = 2;
  string knowledge_1 = 3;
  string knowledge_2 = 4;
  string title = 5;
  repeated string tag = 6;
  string path = 7;
}

message ListRandResourceResp{
  repeated EduResource worksheets = 1; // 随机资源列表
  repeated EduResource coloring_pages = 2; // 随机资源列表
  repeated  EduTermInfo terms = 3; // 随机资源列表
}

message GetResourceDetailReq {
  string url = 1; // 资源链接
  string resource_type = 2; // 资源类型
  string subject = 3; // 学科
  string grade = 4; // 年级
}

message ResourceDetail {
  EduResource resource = 1; // 资源详情
  string pre_url = 2;  // 资源链接
  string next_url = 3; // 下一个资源链接
}

message EduResourceTitle {
  string url = 1;
  string h1_title = 2;
  string description = 3;
  string meta_title = 4;
  string meta_description = 5;
  string meta_keywords = 6;
  string schemas = 7;
  string filter = 8;
}



message EduResource {
  // 主键ID
  uint32 id = 1;
  // 资源类型 worksheet video coloringpage
  string resource_type = 2;
  // 学科
  string subject = 3;
  // 年级 0 代表所有年级
  string grade = 4;
  // 标题
  string title = 5;
  // 二知识点 activity_topic
  string learning_topic = 6;
  // 三知识点
  string resource = 7; 
  // 四级知识点
  string resource_detail = 8;
  // 最细粒度知识点
  string standards = 9;
  // 简介
  string resource_description = 10;
  // 内容 以json格式存储
  google.protobuf.Struct extra_data = 11; // Protobuf 的 `text` 类型不存在，通常使用 `string` 来存储JSON
  
  string url = 12;
  string meta_title = 13;
  string meta_description = 14;
  string meta_keywords = 15;
  string schemas = 16;
  string learning_module = 17;
  string updated_at = 18;
}


message EduResourceGroup {
  string title = 1; // 资源组标题
  repeated EduResource resources = 2; // 资源列表
  int32 total = 3; // 总数
}

message GetResourceGroupsResp {
  repeated EduResourceGroup groups = 1; // 资源组列表
  EduResourceTitle title = 2; 
} 

message GetResourceListResp {
  repeated EduResource resources = 1; // 资源列表
  EduResourceTitle title = 2; 
  int32 total = 3; // 总数
}


message GetResourceGroupsReq {
  string resource_type = 1; // 资源分类
  string subject = 2; // 学科
  string grade = 3; // 年级
  int32 page = 4; // 页码
  int32 page_size = 5; // 每页数量
  string path = 6; // 资源路径
}

message GetResourceListReq {
  string resource_type = 1; // 资源分类
  string subject = 2; // 学科
  string grade = 3; // 年级
  int32 page = 4; // 页码
  int32 page_size = 5; // 每页数量
  string path = 6; // 资源路径
}

message ResourceType {
  string name = 1; // 资源分类名称
  repeated ResourceType nodes = 2; // 子分类
}

message ResourceTypeResp {
  repeated ResourceType types = 1; // 资源分类
}

message EduGrade {
    string name = 1; // 年级名称
    string value = 2; // 年级值
}

message GradesResp {
  repeated EduGrade grades = 1; // 年级
}

message EduSubjectNode {
  string name = 1;
  repeated EduSubjectNode nodes = 2; // 值
}


message Subject {
  string name = 1;
  repeated ResourceTopic topics = 2; // 值
}

message ResourceTopic {
  string name = 1;
  repeated string modules = 2; // 值
}

message SubjectsResp {
  repeated EduSubjectNode subjects = 1;
}

message ResourceMeta {
  string name = 1; // 资源名称
  message Children{
    repeated EduGrade grades = 1; // 年级
    repeated EduSubjectNode subjects = 2; // 学科
    repeated ResourceType types = 3; // 资源类型
  }
  Children children = 2; // 子资源
}


message ResourceMetaResp {
    repeated  ResourceMeta metas = 1; // 资源元数据
}