# 中间件详解

## 1. 中间件概念

中间件是在HTTP请求处理过程中的拦截器，可以在请求到达业务逻辑之前或响应返回客户端之前执行特定的逻辑。

## 2. 项目中使用的中间件

### 2.1 中间件执行顺序
**位置**: `internal/server/http.go:47-60`
```go
var opts = []http.ServerOption{
    http.Middleware(
        recovery.Recovery(),                    // 1. panic恢复中间件
        tracing.Server(),                      // 2. 链路追踪中间件
        middleware.AddTraceToRequest(),        // 3. 自定义追踪中间件
        middleware.Header2Ctx(middleware.Options), // 4. 请求头转上下文中间件
        middleware.AccessCheck(authConf, ucenterRepo), // 5. 访问控制中间件
        logging.Server(logger),                // 6. 日志中间件
        validate.Validator(),                  // 7. 参数验证中间件
        metrics.Server(                        // 8. 指标监控中间件
            metrics.WithSeconds(prom.NewHistogram(biz_metrics.MetricSeconds)),
            metrics.WithRequests(prom.NewCounter(biz_metrics.MetricRequests)),
        ),
    ),
}
```

## 3. 内置中间件详解

### 3.1 Recovery中间件
**作用**: 捕获panic，防止服务崩溃
**来源**: `github.com/go-kratos/kratos/v2/middleware/recovery`
```go
recovery.Recovery()
```
**功能**:
- 捕获处理过程中的panic
- 将panic转换为HTTP 500错误
- 记录错误日志
- 保证服务稳定运行

### 3.2 Tracing中间件
**作用**: 分布式链路追踪
**来源**: `github.com/go-kratos/kratos/v2/middleware/tracing`
```go
tracing.Server()
```
**功能**:
- 生成和传播trace ID
- 记录请求的调用链路
- 支持OpenTelemetry标准
- 便于问题排查和性能分析

### 3.3 Logging中间件
**作用**: 请求日志记录
**来源**: `github.com/go-kratos/kratos/v2/middleware/logging`
```go
logging.Server(logger)
```
**功能**:
- 记录请求开始和结束时间
- 记录请求方法、路径、状态码
- 记录请求耗时
- 支持结构化日志

### 3.4 Validate中间件
**作用**: 参数验证
**来源**: `github.com/go-kratos/kratos/v2/middleware/validate`
```go
validate.Validator()
```
**功能**:
- 基于protobuf的validate规则验证
- 自动验证请求参数
- 返回详细的验证错误信息

### 3.5 Metrics中间件
**作用**: 指标收集
**来源**: `github.com/go-kratos/kratos/v2/middleware/metrics`
```go
metrics.Server(
    metrics.WithSeconds(prom.NewHistogram(biz_metrics.MetricSeconds)),
    metrics.WithRequests(prom.NewCounter(biz_metrics.MetricRequests)),
)
```
**功能**:
- 收集请求数量指标
- 收集请求耗时指标
- 支持Prometheus格式
- 便于监控和告警

## 4. 自定义中间件详解

### 4.1 追踪中间件
**位置**: `internal/server/middleware/request_trace.go:10-27`
```go
func AddTraceToRequest() middleware.Middleware {
    return func(handler middleware.Handler) middleware.Handler {
        return func(ctx context.Context, req interface{}) (interface{}, error) {
            header, ok := transport.FromServerContext(ctx)
            if ok {
                traceId := header.RequestHeader().Get("traceId")
                if traceId == "" {
                    uuid, _ := utils.NewUUID()
                    if uuid != "" {
                        traceId = utils.MD5HashString(uuid)
                    }
                }
                header.RequestHeader().Set(common.HeaderTraceId, traceId)
            }
            return handler(ctx, req)
        }
    }
}
```
**功能**:
- 从请求头获取traceId
- 如果没有traceId，自动生成一个UUID并MD5加密
- 将traceId设置到请求头中
- 便于请求链路追踪

### 4.2 请求头转上下文中间件
**位置**: `internal/server/middleware/header2ctx.go:26-57`
```go
func Header2Ctx(opts ...option) middleware.Middleware {
    return func(handler middleware.Handler) middleware.Handler {
        return func(ctx context.Context, req interface{}) (interface{}, error) {
            signHeader, err := Func(ctx)
            if err != nil {
                return nil, err
            }
            for _, o := range opts {
                ctx = o(ctx, signHeader)
            }
            return handler(ctx, req)
        }
    }
}

func Func(ctx context.Context) (*RequestHeader, error) {
    if header, ok := transport.FromServerContext(ctx); ok {
        talIdType := header.RequestHeader().Get("talIdType")
        if len(talIdType) == 0 {
            talIdType = "0"
        }
        reqHeader := &RequestHeader{
            DeviceId:  header.RequestHeader().Get("X-Tal-Sn"),    // 设备ID
            UserId:    header.RequestHeader().Get("talId"),       // 用户ID
            TraceId:   header.RequestHeader().Get(common.HeaderTraceId), // 追踪ID
            TalIdType: cast.ToInt(talIdType),                     // 用户类型
        }
        return reqHeader, nil
    }
    return nil, nil
}
```
**功能**:
- 提取HTTP请求头中的关键信息
- 将请求头信息转换为上下文变量
- 支持设备ID、用户ID、追踪ID等信息传递
- 便于后续业务逻辑获取请求信息

### 4.3 访问控制中间件
**位置**: `internal/server/middleware/access.go:12-37`
```go
func AccessCheck(authConf *conf.Auth, ucenterRepo *data.UcenterRepo) middleware.Middleware {
    return func(handler middleware.Handler) middleware.Handler {
        return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
            if tr, ok := transport.FromServerContext(ctx); ok {
                // 检查当前请求是否需要鉴权
                for _, url := range authConf.ProtectedUrls {
                    if tr.Operation() == url {
                        // 获取token
                        token := tr.RequestHeader().Get("tal_token")
                        // 验证登录状态
                        rlt, err := ucenterRepo.CheckLogin(ctx, &data.CheckLoginReq{Token: token})
                        if err != nil {
                            return nil, errorV1.ErrorHwPaasUnauthorized("用户未登录或登录已过期")
                        }
                        // 检查用户类型
                        if rlt.TalType != ucenter_go.USER_FORMAL {
                            return nil, errorV1.ErrorHwPaasUnauthorized("用户未登录或登录已过期")
                        }
                        break
                    }
                }
            }
            return handler(ctx, req)
        }
    }
}
```
**功能**:
- 检查请求URL是否需要鉴权
- 验证用户token的有效性
- 检查用户类型是否为正式用户
- 返回统一的鉴权错误信息

## 5. 中间件配置

### 5.1 鉴权配置
**位置**: `configs/config.yaml:24-26`
```yaml
auth:
  protected_urls:
    - "/api.ai.v1.Ai/demo01"  # 需要鉴权的API路径
```

### 5.2 中间件执行流程
```
请求 -> Recovery -> Tracing -> AddTrace -> Header2Ctx -> AccessCheck -> Logging -> Validate -> Metrics -> 业务逻辑
```

## 6. 自定义中间件开发指南

### 6.1 中间件结构
```go
func CustomMiddleware() middleware.Middleware {
    return func(handler middleware.Handler) middleware.Handler {
        return func(ctx context.Context, req interface{}) (interface{}, error) {
            // 前置处理逻辑
            
            // 调用下一个中间件或业务逻辑
            reply, err := handler(ctx, req)
            
            // 后置处理逻辑
            
            return reply, err
        }
    }
}
```

### 6.2 中间件最佳实践
1. **错误处理**: 中间件应该正确处理和传播错误
2. **性能考虑**: 避免在中间件中执行耗时操作
3. **上下文传递**: 使用context传递中间件间的数据
4. **日志记录**: 记录关键的中间件执行信息
5. **配置化**: 支持通过配置文件控制中间件行为

## 7. 中间件的作用总结

1. **安全性**: 通过访问控制、参数验证等保证API安全
2. **可观测性**: 通过日志、指标、链路追踪提供系统可观测性
3. **稳定性**: 通过panic恢复保证服务稳定性
4. **标准化**: 统一处理请求头、错误格式等
5. **解耦**: 将横切关注点从业务逻辑中分离出来