# Redis操作详解

## 1. Redis技术栈

### 1.1 使用的Redis客户端
- **go-redis**: Go语言最流行的Redis客户端
- **版本**: v9.5.1
- **特性**: 支持集群、哨兵、管道、发布订阅等

### 1.2 相关依赖
**位置**: `go.mod:32-33`
```go
github.com/redis/go-redis/extra/redisotel/v9 v9.0.5  // OpenTelemetry集成
github.com/redis/go-redis/v9 v9.5.1                 // Redis客户端
```

## 2. Redis连接配置

### 2.1 配置文件
**位置**: `configs/config.yaml:15-22`
```yaml
redis:
  network: tcp                    # 网络类型
  addr: 127.0.0.1:6379          # Redis地址
  db: 0                          # 数据库编号
  dial_timeout: 2s               # 连接超时
  read_timeout: 2s               # 读取超时
  write_timeout: 2s              # 写入超时
  pool_size: 20                  # 连接池大小
```

### 2.2 配置结构定义
**位置**: `internal/conf/conf.proto:117-128`
```protobuf
message Redis {
    string network = 1;                           // 网络类型
    string addr = 2;                             // Redis地址
    string password = 3;                         // 密码
    int32 db = 4;                               // 数据库编号
    google.protobuf.Duration dial_timeout = 5;   // 连接超时
    google.protobuf.Duration read_timeout = 6;   // 读取超时
    google.protobuf.Duration write_timeout = 7;  // 写入超时
    int32 pool_size = 8;                        // 连接池大小
}
```

## 3. Redis连接初始化

### 3.1 Redis客户端创建
**位置**: `internal/data/data.go:37-57`
```go
func NewData(conf *conf.Data, logger log.Logger) (*Data, func(), error) {
    // 创建Redis客户端
    rdb := redis.NewClient(&redis.Options{
        Addr:         conf.Redis.Addr,                    // Redis地址
        Password:     conf.Redis.Password,                // 密码
        DB:           int(conf.Redis.Db),                // 数据库编号
        DialTimeout:  conf.Redis.DialTimeout.AsDuration(),  // 连接超时
        WriteTimeout: conf.Redis.WriteTimeout.AsDuration(), // 写入超时
        ReadTimeout:  conf.Redis.ReadTimeout.AsDuration(),  // 读取超时
        PoolSize:     int(conf.Redis.PoolSize),           // 连接池大小
    })

    // 测试连接
    _, err = rdb.Ping(context.Background()).Result()
    if err != nil {
        return nil, nil, err
    }

    // 添加OpenTelemetry链路追踪
    if err := redisotel.InstrumentTracing(rdb); err != nil {
        return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentTracing error")
    }
    
    // 添加指标监控
    if err := redisotel.InstrumentMetrics(rdb); err != nil {
        return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentMetrics error")
    }
    
    // 启动连接池监控
    metricRedisPoolStats(rdb)
    
    // 返回Data结构
    d := &Data{
        DB:  db,
        Rdb: rdb,
    }
    return d, cleanup, nil
}
```

## 4. Redis连接池监控

### 4.1 连接池状态监控
**位置**: `internal/data/data.go:78-91`
```go
func metricRedisPoolStats(rdb *redis.Client) {
    go func() {
        defer func() {
            if r := recover(); r != nil {
                log.Errorf("metricRedisPoolStats panic: %v", r)
            }
        }()
        
        // 每5秒收集一次连接池统计信息
        for range time.Tick(time.Second * 5) {
            stats := rdb.PoolStats()
            biz_metrics.RedisPoolStats(
                stats.Hits,       // 命中次数
                stats.Misses,     // 未命中次数
                stats.Timeouts,   // 超时次数
                stats.TotalConns, // 总连接数
                stats.IdleConns,  // 空闲连接数
                stats.StaleConns, // 过期连接数
            )
        }
    }()
}
```

## 5. Redis基础操作

### 5.1 字符串操作

#### 5.1.1 设置键值
```go
func (d *Data) SetString(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
    return d.Rdb.Set(ctx, key, value, expiration).Err()
}
```

#### 5.1.2 获取值
```go
func (d *Data) GetString(ctx context.Context, key string) (string, error) {
    return d.Rdb.Get(ctx, key).Result()
}
```

#### 5.1.3 检查键是否存在
```go
func (d *Data) Exists(ctx context.Context, key string) (bool, error) {
    count, err := d.Rdb.Exists(ctx, key).Result()
    return count > 0, err
}
```

#### 5.1.4 删除键
```go
func (d *Data) Delete(ctx context.Context, keys ...string) error {
    return d.Rdb.Del(ctx, keys...).Err()
}
```

### 5.2 哈希操作

#### 5.2.1 设置哈希字段
```go
func (d *Data) HSet(ctx context.Context, key string, values ...interface{}) error {
    return d.Rdb.HSet(ctx, key, values...).Err()
}
```

#### 5.2.2 获取哈希字段
```go
func (d *Data) HGet(ctx context.Context, key, field string) (string, error) {
    return d.Rdb.HGet(ctx, key, field).Result()
}
```

#### 5.2.3 获取所有哈希字段
```go
func (d *Data) HGetAll(ctx context.Context, key string) (map[string]string, error) {
    return d.Rdb.HGetAll(ctx, key).Result()
}
```

### 5.3 列表操作

#### 5.3.1 左推入
```go
func (d *Data) LPush(ctx context.Context, key string, values ...interface{}) error {
    return d.Rdb.LPush(ctx, key, values...).Err()
}
```

#### 5.3.2 右弹出
```go
func (d *Data) RPop(ctx context.Context, key string) (string, error) {
    return d.Rdb.RPop(ctx, key).Result()
}
```

#### 5.3.3 获取列表范围
```go
func (d *Data) LRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
    return d.Rdb.LRange(ctx, key, start, stop).Result()
}
```

### 5.4 集合操作

#### 5.4.1 添加成员
```go
func (d *Data) SAdd(ctx context.Context, key string, members ...interface{}) error {
    return d.Rdb.SAdd(ctx, key, members...).Err()
}
```

#### 5.4.2 获取所有成员
```go
func (d *Data) SMembers(ctx context.Context, key string) ([]string, error) {
    return d.Rdb.SMembers(ctx, key).Result()
}
```

### 5.5 有序集合操作

#### 5.5.1 添加成员
```go
func (d *Data) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
    return d.Rdb.ZAdd(ctx, key, members...).Err()
}
```

#### 5.5.2 按分数范围获取
```go
func (d *Data) ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
    return d.Rdb.ZRangeByScore(ctx, key, opt).Result()
}
```

## 6. 高级Redis操作

### 6.1 管道操作
```go
func (d *Data) PipelineExample(ctx context.Context) error {
    pipe := d.Rdb.Pipeline()
    
    // 批量操作
    pipe.Set(ctx, "key1", "value1", 0)
    pipe.Set(ctx, "key2", "value2", 0)
    pipe.Get(ctx, "key1")
    
    // 执行管道
    _, err := pipe.Exec(ctx)
    return err
}
```

### 6.2 事务操作
```go
func (d *Data) TransactionExample(ctx context.Context, key string) error {
    return d.Rdb.Watch(ctx, func(tx *redis.Tx) error {
        // 在事务中执行操作
        _, err := tx.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
            pipe.Set(ctx, key, "new_value", 0)
            pipe.Expire(ctx, key, time.Hour)
            return nil
        })
        return err
    }, key)
}
```

### 6.3 发布订阅
```go
func (d *Data) PublishMessage(ctx context.Context, channel string, message interface{}) error {
    return d.Rdb.Publish(ctx, channel, message).Err()
}

func (d *Data) SubscribeChannel(ctx context.Context, channel string) *redis.PubSub {
    return d.Rdb.Subscribe(ctx, channel)
}
```

## 7. 缓存模式实现

### 7.1 缓存穿透防护
```go
func (d *Data) GetWithCache(ctx context.Context, key string, fetch func() (interface{}, error)) (interface{}, error) {
    // 先从缓存获取
    cached, err := d.Rdb.Get(ctx, key).Result()
    if err == nil {
        return cached, nil
    }
    
    if err != redis.Nil {
        return nil, err
    }
    
    // 缓存未命中，从数据源获取
    data, err := fetch()
    if err != nil {
        return nil, err
    }
    
    // 设置缓存
    d.Rdb.Set(ctx, key, data, time.Hour)
    return data, nil
}
```

### 7.2 分布式锁
```go
func (d *Data) AcquireLock(ctx context.Context, key string, expiration time.Duration) (bool, error) {
    result, err := d.Rdb.SetNX(ctx, key, "locked", expiration).Result()
    return result, err
}

func (d *Data) ReleaseLock(ctx context.Context, key string) error {
    return d.Rdb.Del(ctx, key).Err()
}
```

### 7.3 限流器
```go
func (d *Data) RateLimit(ctx context.Context, key string, limit int, window time.Duration) (bool, error) {
    pipe := d.Rdb.Pipeline()
    
    // 增加计数
    pipe.Incr(ctx, key)
    pipe.Expire(ctx, key, window)
    
    results, err := pipe.Exec(ctx)
    if err != nil {
        return false, err
    }
    
    count := results[0].(*redis.IntCmd).Val()
    return count <= int64(limit), nil
}
```

## 8. JSON数据操作

### 8.1 存储JSON对象
```go
func (d *Data) SetJSON(ctx context.Context, key string, obj interface{}, expiration time.Duration) error {
    data, err := json.Marshal(obj)
    if err != nil {
        return err
    }
    return d.Rdb.Set(ctx, key, data, expiration).Err()
}
```

### 8.2 获取JSON对象
```go
func (d *Data) GetJSON(ctx context.Context, key string, obj interface{}) error {
    data, err := d.Rdb.Get(ctx, key).Result()
    if err != nil {
        return err
    }
    return json.Unmarshal([]byte(data), obj)
}
```

## 9. 错误处理

### 9.1 常见错误处理
```go
func (d *Data) SafeGet(ctx context.Context, key string) (string, bool, error) {
    result, err := d.Rdb.Get(ctx, key).Result()
    if err != nil {
        if err == redis.Nil {
            return "", false, nil // 键不存在
        }
        return "", false, err // 其他错误
    }
    return result, true, nil
}
```

## 10. 性能优化

### 10.1 批量操作
```go
func (d *Data) BatchSet(ctx context.Context, pairs map[string]interface{}) error {
    pipe := d.Rdb.Pipeline()
    
    for key, value := range pairs {
        pipe.Set(ctx, key, value, 0)
    }
    
    _, err := pipe.Exec(ctx)
    return err
}
```

### 10.2 连接池配置优化
```go
// 在配置中调整连接池参数
redis.Options{
    PoolSize:     20,              // 连接池大小
    MinIdleConns: 5,               // 最小空闲连接
    MaxConnAge:   time.Hour,       // 连接最大存活时间
    PoolTimeout:  time.Second * 30, // 获取连接超时
    IdleTimeout:  time.Minute * 5,  // 空闲连接超时
}
```

## 11. 监控和调试

### 11.1 连接池状态监控
```go
func (d *Data) GetPoolStats() *redis.PoolStats {
    return d.Rdb.PoolStats()
}
```

### 11.2 慢查询监控
```go
func (d *Data) MonitorSlowLog(ctx context.Context) {
    slowLogs := d.Rdb.SlowLogGet(ctx, 10)
    for _, log := range slowLogs.Val() {
        fmt.Printf("Slow query: %+v\n", log)
    }
}
```

## 12. 最佳实践

### 12.1 键命名规范
- 使用有意义的前缀：`user:profile:123`
- 避免过长的键名
- 使用分隔符组织层级结构

### 12.2 过期时间设置
- 为所有缓存数据设置合理的过期时间
- 避免大量键同时过期
- 使用随机过期时间防止缓存雪崩

### 12.3 内存优化
- 选择合适的数据结构
- 压缩大对象
- 定期清理过期数据

### 12.4 安全考虑
- 使用密码保护Redis
- 限制网络访问
- 避免在键中存储敏感信息