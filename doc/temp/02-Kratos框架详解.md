# Kratos框架详解

## 1. Kratos框架简介

Kratos是一个轻量级的Go微服务框架，包含大量微服务相关框架及工具。本项目使用的是Kratos v2版本。

### 1.1 框架特性
- 支持HTTP和gRPC协议
- 内置中间件支持
- 依赖注入(Wire)
- 配置管理
- 链路追踪
- 指标监控

## 2. 项目结构分析

### 2.1 标准的Kratos项目结构
```
hw-paas-service/
├── api/                    # API定义(protobuf)
├── cmd/                    # 应用入口
├── configs/                # 配置文件
├── internal/               # 内部代码
│   ├── biz/               # 业务逻辑层
│   ├── data/              # 数据访问层
│   ├── server/            # 服务器配置
│   └── service/           # 服务层
└── pkg/                   # 公共库
```

## 3. 应用启动流程

### 3.1 主函数入口
**位置**: `cmd/hw-paas-service/main.go:70-127`
```go
func main() {
    // 1. 配置加载
    var c config.Config
    if nacos {
        // 使用Nacos配置中心
        nacosClient := NewConfigClient(...)
        configSources := make([]config.Source, 0)
        for _, dataId := range dataIds {
            configSources = append(configSources, nacosConfig.NewConfigSource(...))
        }
        c = config.New(config.WithSource(configSources...))
    } else {
        // 使用本地文件配置
        c = config.New(config.WithSource(file.NewSource(flagconf)))
    }
    defer c.Close()

    // 2. 加载配置
    if err := c.Load(); err != nil {
        panic(err)
    }

    // 3. 配置解析到结构体
    var bc conf.Bootstrap
    if err := c.Scan(&bc); err != nil {
        panic(err)
    }

    // 4. 初始化日志
    zlog.Init(Name, bc.Log.Filename, ...)
    logger := log.With(zlog.NewZapLogger(zlog.STDInstance()), ...)

    // 5. 配置热更新监听
    c.Watch("biz", func(key string, value config.Value) {
        value.Scan(&bc.Biz)
        fmt.Printf("biz changed: %+v", bc.Biz)
    })

    // 6. 依赖注入初始化应用
    app, cleanup, err := initApp(bc.Server, bc.Sign, bc.Error, bc.Biz, bc.Nacos, bc.Data, bc.Services, logger, bc.Auth)
    if err != nil {
        panic(err)
    }
    defer cleanup()

    // 7. 启动应用
    if err := app.Run(); err != nil {
        panic(err)
    }
}
```

### 3.2 应用创建
**位置**: `cmd/hw-paas-service/main.go:162-173`
```go
func newApp(logger log.Logger, hs *http.Server) *kratos.App {
    return kratos.New(
        kratos.ID(id),                    // 服务ID
        kratos.Name(Name),                // 服务名称
        kratos.Version(Version),          // 服务版本
        kratos.Metadata(map[string]string{}), // 元数据
        kratos.Logger(logger),            // 日志器
        kratos.Server(hs),               // HTTP服务器
    )
}
```

## 4. 依赖注入(Wire)

### 4.1 Wire配置
**位置**: `cmd/hw-paas-service/wire.go:20-23`
```go
// initApp init kratos application.
func initApp(*conf.Server, map[string]*conf.SignConf, *conf.ErrorHandle, *conf.Biz, *conf.Nacos, *conf.Data, *conf.Services, log.Logger, *conf.Auth) (*kratos.App, func(), error) {
    panic(wire.Build(
        server.ProviderSet,    // 服务器层
        data.ProviderSet,      // 数据层
        dao.ProviderSet,       // DAO层
        biz.ProviderSet,       // 业务层
        service.ProviderSet,   // 服务层
        services.ProviderSet,  // 外部服务层
        newApp                 // 应用构造函数
    ))
}
```

### 4.2 各层ProviderSet定义

#### 服务器层
**位置**: `internal/server/server.go:6-7`
```go
var ProviderSet = wire.NewSet(NewHTTPServer)
```

#### 数据层
**位置**: `internal/data/data.go:18-19`
```go
var ProviderSet = wire.NewSet(NewData, NewUcenterRepo)
```

#### 业务层
**位置**: `internal/biz/biz.go:6-8`
```go
var ProviderSet = wire.NewSet(
    NewCorrectUseCase, 
    NewFingerWordsUseCase, 
    NewSkillUsecase, 
    NewQueryWordsUseCase, 
    NewReadingBookUseCase, 
    NewJzxQuestionUseCase,
    NewEduUseCase, 
    NewCodeLoginUseCase, 
    NewResourceUseCase
)
```

#### 服务层
**位置**: `internal/service/service.go:8-10`
```go
var ProviderSet = wire.NewSet(
    NewAiService, 
    NewFingerWordsService, 
    NewSkillService, 
    NewQueryWordsService, 
    NewReadingBookService,
    evaluate.NewEvaluateService, 
    NewResourceService
)
```

## 5. HTTP服务器配置

### 5.1 HTTP服务器创建
**位置**: `internal/server/http.go:30-88`
```go
func NewHTTPServer(c *conf.Server,
    ai *service.AiService,
    fwSvc *service.FingerWordsService,
    skill *service.SkillService,
    qwSvc *service.QueryWordsService,
    eval *evaluate.EvalService,
    rbSvc *service.ReadingBookService,
    errorHandler *conf.ErrorHandle,
    logger log.Logger,
    authConf *conf.Auth,
    ucenterRepo *data.UcenterRepo,
    resourceSvc *service.ResourceService,
) *http.Server {
    errorHandle = errorHandler

    // 中间件配置
    var opts = []http.ServerOption{
        http.Middleware(
            recovery.Recovery(),                    // 恢复中间件
            tracing.Server(),                      // 链路追踪
            middleware.AddTraceToRequest(),        // 添加追踪ID
            middleware.Header2Ctx(middleware.Options), // 请求头转上下文
            middleware.AccessCheck(authConf, ucenterRepo), // 访问检查
            logging.Server(logger),                // 日志中间件
            validate.Validator(),                  // 参数验证
            metrics.Server(                        // 指标监控
                metrics.WithSeconds(prom.NewHistogram(biz_metrics.MetricSeconds)),
                metrics.WithRequests(prom.NewCounter(biz_metrics.MetricRequests)),
            ),
        ),
        httpserver.ServerHandle,
    }

    // 服务器配置
    if c.Http.Network != "" {
        opts = append(opts, http.Network(c.Http.Network))
    }
    if c.Http.Addr != "" {
        opts = append(opts, http.Address(c.Http.Addr))
    }
    if c.Http.Timeout != nil {
        opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
    }
    
    // 自定义编码器
    opts = append(opts, http.ErrorEncoder(MyErrorEncoder))
    opts = append(opts, http.ResponseEncoder(MyResponseEncoder))

    srv := http.NewServer(opts...)

    // 路由注册
    router := mux.NewRouter()
    router.HandleFunc("/intelligence/v1/evaluate", eval.WsHandler)
    srv.HandlePrefix("/intelligence/v1/evaluate", router)
    
    // gRPC-Gateway路由注册
    v1.RegisterAiHTTPServer(srv, ai)
    fw.RegisterFingerWordsHTTPServer(srv, fwSvc)
    qw.RegisterQueryWordsHTTPServer(srv, qwSvc)
    skillv1.RegisterSkillHTTPServer(srv, skill)
    rb.RegisterReadingBookHTTPServer(srv, rbSvc)
    resourcev1.RegisterResourceHTTPServer(srv, resourceSvc)
    
    return srv
}
```

## 6. 配置管理

### 6.1 配置结构定义
**位置**: `internal/conf/conf.proto:7-17`
```protobuf
message Bootstrap {
  Server server = 1;        // 服务器配置
  Data data = 2;           // 数据源配置
  Nacos nacos = 3;         // Nacos配置
  ErrorHandle error = 4;    // 错误处理配置
  Log log = 5;             // 日志配置
  map<string, SignConf> sign = 6; // 签名配置
  Biz biz = 7;             // 业务配置
  Services services = 8;    // 外部服务配置
  Auth auth = 9;           // 鉴权配置
}
```

### 6.2 配置热更新
**位置**: `cmd/hw-paas-service/main.go:113-116`
```go
c.Watch("biz", func(key string, value config.Value) {
    value.Scan(&bc.Biz)
    fmt.Printf("biz changed: %+v", bc.Biz)
})
```

## 7. 中间件系统

### 7.1 中间件链
Kratos使用中间件链模式，按顺序执行：
1. `recovery.Recovery()` - panic恢复
2. `tracing.Server()` - 链路追踪
3. `middleware.AddTraceToRequest()` - 自定义追踪
4. `middleware.Header2Ctx()` - 请求头处理
5. `middleware.AccessCheck()` - 访问控制
6. `logging.Server()` - 日志记录
7. `validate.Validator()` - 参数验证
8. `metrics.Server()` - 指标收集

## 8. 服务注册

### 8.1 gRPC-Gateway自动注册
通过protobuf生成的代码自动注册HTTP路由：
```go
v1.RegisterAiHTTPServer(srv, ai)           // AI服务
fw.RegisterFingerWordsHTTPServer(srv, fwSvc) // 指尖查词服务
qw.RegisterQueryWordsHTTPServer(srv, qwSvc)  // 查词服务
```

### 8.2 自定义路由注册
```go
router := mux.NewRouter()
router.HandleFunc("/intelligence/v1/evaluate", eval.WsHandler)
srv.HandlePrefix("/intelligence/v1/evaluate", router)
```

## 9. 错误处理

### 9.1 自定义错误编码器
**位置**: `internal/server/http.go:72`
```go
opts = append(opts, http.ErrorEncoder(MyErrorEncoder))
```

### 9.2 自定义响应编码器
**位置**: `internal/server/http.go:73`
```go
opts = append(opts, http.ResponseEncoder(MyResponseEncoder))
```

## 10. 日志系统

### 10.1 日志初始化
**位置**: `cmd/hw-paas-service/main.go:99-111`
```go
zlog.Init(Name, bc.Log.Filename, int(bc.Log.MaxSize), int(bc.Log.MaxBackup), int(bc.Log.MaxAge), bc.Log.Compress)
defer zlog.Sync()

logger := log.With(zlog.NewZapLogger(zlog.STDInstance()),
    "ts", log.DefaultTimestamp,
    "caller", log.DefaultCaller,
    "service.id", id,
    "service.name", Name,
    "service.version", Version,
    "trace_id", tracing.TraceID(),
    "span_id", tracing.SpanID(),
    "dayu_trace_id", dayu_trace.TraceID(),
)
```

## 11. 指标监控

### 11.1 Prometheus集成
**位置**: `internal/server/http.go:55-58`
```go
metrics.Server(
    metrics.WithSeconds(prom.NewHistogram(biz_metrics.MetricSeconds)),
    metrics.WithRequests(prom.NewCounter(biz_metrics.MetricRequests)),
),
```

Kratos框架提供了完整的微服务开发框架，包括HTTP/gRPC服务、中间件、配置管理、依赖注入等功能，大大简化了Go微服务的开发复杂度。