# 数据库操作详解

## 1. 数据库技术栈

### 1.1 使用的ORM框架
- **GORM**: Go语言最流行的ORM框架
- **版本**: v1.25.8
- **驱动**: MySQL驱动 `gorm.io/driver/mysql`

### 1.2 相关依赖
**位置**: `go.mod:46-47`
```go
gorm.io/driver/mysql v1.5.6
gorm.io/gorm v1.25.8
```

## 2. 数据库连接配置

### 2.1 配置文件
**位置**: `configs/config.yaml:7-10`
```yaml
data:
  database:
    driver: mysql
    source: ai_tools_rw:jJ9yO_uD7lS4dK0q@tcp(sea-pad-mysql-test.mysql.database.chinacloudapi.cn:3306)/ai_tools?timeout=1s&readTimeout=1s&writeTimeout=1s&parseTime=true&loc=Local&charset=utf8mb4,utf8
```

### 2.2 配置结构定义
**位置**: `internal/conf/conf.proto:112-116`
```protobuf
message Database {
    string driver = 1;  // 数据库驱动
    string source = 2;  // 数据库连接字符串
}
```

## 3. 数据库初始化

### 3.1 数据库连接创建
**位置**: `internal/data/data.go:28-35`
```go
func NewData(conf *conf.Data, logger log.Logger) (*Data, func(), error) {
    // 创建数据库连接
    db, err := gorm.Open(mysql.Open(conf.Database.Source), &gorm.Config{})
    if err != nil {
        return nil, nil, err
    }
    
    // 添加OpenTelemetry插件用于链路追踪
    if err := db.Use(otelgorm.NewPlugin()); err != nil {
        return nil, nil, errors.Wrap(err, "data: db.Use error")
    }
    
    // ... 其他初始化代码
}
```

### 3.2 数据结构定义
**位置**: `internal/data/data.go:21-25`
```go
type Data struct {
    DB  *gorm.DB      // GORM数据库实例
    Rdb *redis.Client // Redis客户端实例
}
```

### 3.3 资源清理
**位置**: `internal/data/data.go:65-76`
```go
return d, func() {
    // 获取底层数据库连接
    _db, err := d.DB.DB()
    if err != nil {
        log.NewHelper(logger).Errorf("database close err:%+v", err)
    }
    // 关闭数据库连接
    _ = _db.Close()
    log.NewHelper(logger).Info("closing the mysql")
    
    // 关闭Redis连接
    d.Rdb.Close()
    log.NewHelper(logger).Info("closing the redis")
}, nil
```

## 4. 数据模型定义

### 4.1 模型示例 - 英文单词表
**位置**: `internal/data/model/hw_en_word.go`
```go
type HwEnWord struct {
    ID           int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
    Word         string    `gorm:"column:word;type:varchar(255);not null" json:"word"`
    Phonetic     string    `gorm:"column:phonetic;type:varchar(255)" json:"phonetic"`
    Translation  string    `gorm:"column:translation;type:text" json:"translation"`
    Example      string    `gorm:"column:example;type:text" json:"example"`
    AudioUrl     string    `gorm:"column:audio_url;type:varchar(500)" json:"audio_url"`
    CreatedAt    time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
    UpdatedAt    time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// 指定表名
func (HwEnWord) TableName() string {
    return "hw_en_word"
}
```

### 4.2 GORM标签说明
- `gorm:"column:id"`: 指定数据库列名
- `gorm:"primaryKey"`: 主键
- `gorm:"autoIncrement"`: 自增
- `gorm:"type:varchar(255)"`: 指定列类型
- `gorm:"not null"`: 非空约束
- `gorm:"autoCreateTime"`: 自动设置创建时间
- `gorm:"autoUpdateTime"`: 自动设置更新时间

## 5. DAO层实现

### 5.1 DAO结构定义
```go
type HwEnWordDao struct {
    data *data.Data
    log  *log.Helper
}

func NewHwEnWordDao(data *data.Data, logger log.Logger) *HwEnWordDao {
    return &HwEnWordDao{
        data: data,
        log:  log.NewHelper(logger),
    }
}
```

### 5.2 基础CRUD操作

#### 5.2.1 创建记录
```go
func (d *HwEnWordDao) Create(ctx context.Context, word *model.HwEnWord) error {
    return d.data.DB.WithContext(ctx).Create(word).Error
}
```

#### 5.2.2 查询单条记录
```go
func (d *HwEnWordDao) GetByID(ctx context.Context, id int64) (*model.HwEnWord, error) {
    var word model.HwEnWord
    err := d.data.DB.WithContext(ctx).Where("id = ?", id).First(&word).Error
    if err != nil {
        return nil, err
    }
    return &word, nil
}
```

#### 5.2.3 查询多条记录
```go
func (d *HwEnWordDao) List(ctx context.Context, offset, limit int) ([]*model.HwEnWord, error) {
    var words []*model.HwEnWord
    err := d.data.DB.WithContext(ctx).
        Offset(offset).
        Limit(limit).
        Find(&words).Error
    return words, err
}
```

#### 5.2.4 更新记录
```go
func (d *HwEnWordDao) Update(ctx context.Context, word *model.HwEnWord) error {
    return d.data.DB.WithContext(ctx).Save(word).Error
}
```

#### 5.2.5 删除记录
```go
func (d *HwEnWordDao) Delete(ctx context.Context, id int64) error {
    return d.data.DB.WithContext(ctx).Delete(&model.HwEnWord{}, id).Error
}
```

## 6. 高级查询操作

### 6.1 条件查询
```go
func (d *HwEnWordDao) GetByWord(ctx context.Context, word string) (*model.HwEnWord, error) {
    var result model.HwEnWord
    err := d.data.DB.WithContext(ctx).
        Where("word = ?", word).
        First(&result).Error
    return &result, err
}
```

### 6.2 模糊查询
```go
func (d *HwEnWordDao) SearchByWord(ctx context.Context, keyword string) ([]*model.HwEnWord, error) {
    var words []*model.HwEnWord
    err := d.data.DB.WithContext(ctx).
        Where("word LIKE ?", "%"+keyword+"%").
        Find(&words).Error
    return words, err
}
```

### 6.3 分页查询
```go
func (d *HwEnWordDao) GetWithPage(ctx context.Context, page, pageSize int) ([]*model.HwEnWord, int64, error) {
    var words []*model.HwEnWord
    var total int64
    
    // 计算总数
    err := d.data.DB.WithContext(ctx).Model(&model.HwEnWord{}).Count(&total).Error
    if err != nil {
        return nil, 0, err
    }
    
    // 分页查询
    offset := (page - 1) * pageSize
    err = d.data.DB.WithContext(ctx).
        Offset(offset).
        Limit(pageSize).
        Find(&words).Error
    
    return words, total, err
}
```

### 6.4 复杂查询
```go
func (d *HwEnWordDao) GetByConditions(ctx context.Context, conditions map[string]interface{}) ([]*model.HwEnWord, error) {
    var words []*model.HwEnWord
    query := d.data.DB.WithContext(ctx)
    
    for key, value := range conditions {
        query = query.Where(key+" = ?", value)
    }
    
    err := query.Find(&words).Error
    return words, err
}
```

## 7. 事务处理

### 7.1 手动事务
```go
func (d *HwEnWordDao) BatchCreate(ctx context.Context, words []*model.HwEnWord) error {
    return d.data.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        for _, word := range words {
            if err := tx.Create(word).Error; err != nil {
                return err // 自动回滚
            }
        }
        return nil // 自动提交
    })
}
```

### 7.2 批量操作
```go
func (d *HwEnWordDao) BatchInsert(ctx context.Context, words []*model.HwEnWord) error {
    // GORM批量插入
    return d.data.DB.WithContext(ctx).CreateInBatches(words, 100).Error
}
```

## 8. 错误处理

### 8.1 常见错误处理
```go
func (d *HwEnWordDao) GetByID(ctx context.Context, id int64) (*model.HwEnWord, error) {
    var word model.HwEnWord
    err := d.data.DB.WithContext(ctx).Where("id = ?", id).First(&word).Error
    
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, fmt.Errorf("word not found with id: %d", id)
        }
        return nil, err
    }
    
    return &word, nil
}
```

## 9. 性能优化

### 9.1 预加载关联
```go
func (d *HwEnWordDao) GetWithRelations(ctx context.Context, id int64) (*model.HwEnWord, error) {
    var word model.HwEnWord
    err := d.data.DB.WithContext(ctx).
        Preload("Examples").    // 预加载例句
        Preload("Synonyms").    // 预加载同义词
        Where("id = ?", id).
        First(&word).Error
    return &word, err
}
```

### 9.2 选择特定字段
```go
func (d *HwEnWordDao) GetWordList(ctx context.Context) ([]string, error) {
    var words []string
    err := d.data.DB.WithContext(ctx).
        Model(&model.HwEnWord{}).
        Select("word").
        Pluck("word", &words).Error
    return words, err
}
```

## 10. 数据库迁移

### 10.1 自动迁移
```go
func (d *Data) AutoMigrate() error {
    return d.DB.AutoMigrate(
        &model.HwEnWord{},
        &model.BlogArticle{},
        &model.EduTerm{},
        // 添加更多模型...
    )
}
```

## 11. 最佳实践

### 11.1 上下文使用
- 始终使用`WithContext(ctx)`传递上下文
- 支持请求取消和超时控制
- 便于链路追踪

### 11.2 错误处理
- 区分不同类型的错误（记录不存在、数据库连接错误等）
- 使用`errors.Wrap`包装错误信息
- 记录详细的错误日志

### 11.3 性能考虑
- 使用索引优化查询性能
- 避免N+1查询问题
- 合理使用批量操作
- 使用连接池管理数据库连接

### 11.4 安全性
- 使用参数化查询防止SQL注入
- 验证输入数据
- 控制查询结果集大小