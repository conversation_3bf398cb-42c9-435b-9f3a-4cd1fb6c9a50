# 中间件系统详解

## 1. 中间件概述

中间件（Middleware）是位于应用程序和底层系统之间的软件层，它提供了通用的服务和功能，如认证、日志记录、错误处理、链路追踪等。在 Go 微服务架构中，中间件采用洋葱模型，请求和响应都会经过中间件层。

### 1.1 中间件模式

```
Request → Middleware1 → Middleware2 → Handler → Middleware2 → Middleware1 → Response
```

### 1.2 核心特性

- **可组合性**: 多个中间件可以组合使用
- **顺序性**: 中间件按照注册顺序执行
- **透明性**: 对业务逻辑透明
- **可重用性**: 中间件可以在多个服务中复用

## 2. 项目中的中间件架构

### 2.1 中间件注册

**文件位置**: `internal/server/http.go`

```go
func NewHTTPServer(c *conf.Server,
	ai *service.AiService,
	fwSvc *service.FingerWordsService,
	skill *service.SkillService,
	qwSvc *service.QueryWordsService,
	eval *evaluate.EvalService,
	rbSvc *service.ReadingBookService,
	errorHandler *conf.ErrorHandle,
	logger log.Logger,
	authConf *conf.Auth,
	ucenterRepo *data.UcenterRepo,
	resourceSvc *service.ResourceService,
) *http.Server {
	errorHandle = errorHandler

	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			tracing.Server(),
			middleware.AddTraceToRequest(),
			middleware.Header2Ctx(middleware.Options),
			middleware.AccessCheck(authConf, ucenterRepo),
			logging.Server(logger),
			validate.Validator(),
			metrics.Server(
				metrics.WithSeconds(prom.NewHistogram(biz_metrics.MetricSeconds)),
				metrics.WithRequests(prom.NewCounter(biz_metrics.MetricRequests)),
			),
		),
		httpserver.ServerHandle,
	}
	
	srv := http.NewServer(opts...)
	return srv
}
```

#### 中间件执行顺序分析：

1. **recovery.Recovery()** - 异常恢复（最外层）
2. **tracing.Server()** - 链路追踪
3. **middleware.AddTraceToRequest()** - 添加追踪信息
4. **middleware.Header2Ctx()** - 请求头转上下文
5. **middleware.AccessCheck()** - 访问控制
6. **logging.Server()** - 日志记录
7. **validate.Validator()** - 参数验证
8. **metrics.Server()** - 指标收集（最内层）

## 3. 自定义中间件实现

### 3.1 请求追踪中间件

**文件位置**: `internal/server/middleware/request_trace.go`

```go
package middleware

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"hw-paas-service/internal/pkg/trace_context"
)

func AddTraceToRequest() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				// 从请求头获取追踪信息
				traceId := tr.RequestHeader().Get("X-Trace-Id")
				if traceId == "" {
					// 如果没有追踪ID，生成一个新的
					traceId = trace_context.GenerateTraceId()
				}
				
				// 将追踪ID添加到上下文
				ctx = trace_context.WithTraceId(ctx, traceId)
				
				// 设置响应头
				tr.ReplyHeader().Set("X-Trace-Id", traceId)
			}
			
			return handler(ctx, req)
		}
	}
}
```

#### 追踪中间件分析：

1. **追踪ID获取**:
   ```go
   traceId := tr.RequestHeader().Get("X-Trace-Id")
   if traceId == "" {
       traceId = trace_context.GenerateTraceId()
   }
   ```
   - 优先使用客户端传递的追踪ID
   - 如果没有则生成新的追踪ID

2. **上下文传递**:
   ```go
   ctx = trace_context.WithTraceId(ctx, traceId)
   ```
   - 将追踪ID存储到上下文中
   - 后续处理可以获取追踪信息

3. **响应头设置**:
   ```go
   tr.ReplyHeader().Set("X-Trace-Id", traceId)
   ```
   - 将追踪ID返回给客户端
   - 便于客户端进行问题排查

### 3.2 请求头转上下文中间件

**文件位置**: `internal/server/middleware/header2ctx.go`

```go
package middleware

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"hw-paas-service/internal/pkg/custom_context"
)

var Options = []string{
	"X-Genie-DeviceId",
	"X-Genie-AppId", 
	"X-Genie-Version",
	"X-Genie-Platform",
	"X-Tal-Sn",
	"talId",
	"User-Agent",
}

func Header2Ctx(headers []string) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				// 将指定的请求头添加到上下文
				for _, header := range headers {
					value := tr.RequestHeader().Get(header)
					if value != "" {
						ctx = custom_context.WithValue(ctx, header, value)
					}
				}
			}
			return handler(ctx, req)
		}
	}
}
```

#### 请求头中间件分析：

1. **配置化头部**:
   ```go
   var Options = []string{
       "X-Genie-DeviceId",
       "X-Genie-AppId", 
       // ...
   }
   ```
   - 定义需要传递的请求头
   - 支持业务相关的自定义头部

2. **头部提取**:
   ```go
   for _, header := range headers {
       value := tr.RequestHeader().Get(header)
       if value != "" {
           ctx = custom_context.WithValue(ctx, header, value)
       }
   }
   ```
   - 遍历配置的头部列表
   - 将非空值存储到上下文

### 3.3 访问控制中间件

**文件位置**: `internal/server/middleware/access.go`

```go
package middleware

import (
	"context"
	"git.100tal.com/tal_ucenter_sdk/ucenter_go"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	errorV1 "hw-paas-service/api/ai/v1"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data"
)

func AccessCheck(authConf *conf.Auth, ucenterRepo *data.UcenterRepo) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				// 检查当前请求是否需要认证
				for _, url := range authConf.ProtectedUrls {
					if tr.Operation() == url {
						// 获取认证token
						token := tr.RequestHeader().Get("tal_token")
						
						// 验证token
						rlt, err := ucenterRepo.CheckLogin(ctx, &data.CheckLoginReq{Token: token})
						if err != nil {
							return nil, errorV1.ErrorHwPaasUnauthorized("用户未登录或登录已过期")
						}
						
						// 检查用户类型
						if rlt.TalType != ucenter_go.USER_FORMAL {
							return nil, errorV1.ErrorHwPaasUnauthorized("用户未登录或登录已过期")
						}
						break
					}
				}

				defer func() {
					// 请求完成后的清理工作
				}()
			}
			return handler(ctx, req)
		}
	}
}
```

#### 访问控制分析：

1. **保护URL配置**:
   ```go
   for _, url := range authConf.ProtectedUrls {
       if tr.Operation() == url {
           // 需要认证的接口
       }
   }
   ```
   - 配置化的保护接口列表
   - 只对指定接口进行认证检查

2. **Token验证**:
   ```go
   token := tr.RequestHeader().Get("tal_token")
   rlt, err := ucenterRepo.CheckLogin(ctx, &data.CheckLoginReq{Token: token})
   ```
   - 从请求头获取认证token
   - 调用用户中心验证token有效性

3. **用户类型检查**:
   ```go
   if rlt.TalType != ucenter_go.USER_FORMAL {
       return nil, errorV1.ErrorHwPaasUnauthorized("用户未登录或登录已过期")
   }
   ```
   - 检查用户是否为正式用户
   - 返回标准化错误信息

### 3.4 签名验证中间件

**文件位置**: `internal/server/middleware/sign.go`

```go
package middleware

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	errorV1 "hw-paas-service/api/ai/v1"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/pkg/custom_context"
	"hw-paas-service/internal/pkg/utils"
	"strconv"
)

func Sign(signConf map[string]*conf.SignConf) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if header, ok := transport.FromServerContext(ctx); ok {
				// 获取签名相关参数
				deviceId := header.RequestHeader().Get("X-Genie-DeviceId")
				timeStamp := header.RequestHeader().Get("X-Genie-Timestamp")
				nonce := header.RequestHeader().Get("X-Genie-Nonce")
				sign := header.RequestHeader().Get("X-Genie-Sign")
				appId := header.RequestHeader().Get("X-Genie-AppId")
				version := header.RequestHeader().Get("X-Genie-Version")
				platform := header.RequestHeader().Get("X-Genie-Platform")

				// 参数完整性检查
				if sign == "" || timeStamp == "" || nonce == "" || deviceId == "" || appId == "" || version == "" || platform == "" {
					return nil, errorV1.ErrorHwPaasCorrectError("必选参数不合法")
				}
				
				// 时间戳格式验证
				_, err := strconv.Atoi(timeStamp)
				if err != nil {
					return nil, errorV1.ErrorHwPaasSignError("timestamp format error")
				}

				// 获取应用签名配置
				appSign, ok := signConf[appId]
				if !ok {
					return nil, errorV1.ErrorHwPaasAppidError("appid not exist")
				}
				
				// 构建签名字符串
				signStr := appSign.SignKey + "&X-Genie-Timestamp=" + timeStamp + "&X-Genie-Nonce=" + nonce + "&X-Genie-DeviceId=" + deviceId
				verifySign := utils.MD5HashString(signStr)
				
				// 签名验证
				if verifySign != sign {
					return nil, errorV1.ErrorHwPaasSignError("sign error")
				}
				
				// 将验证信息添加到上下文
				customContext := custom_context.SetDeviceId(ctx, deviceId)
				platformContext := custom_context.SetPlatform(customContext, platform)
				appIdContext := custom_context.SetAppId(platformContext, appId)
				versionContext := custom_context.SetVersion(appIdContext, version)
				
				return handler(versionContext, req)
			} else {
				return nil, errorV1.ErrorHwPaasSignError("transport.FromServerContext err")
			}
		}
	}
}
```

#### 签名验证分析：

1. **参数提取和验证**:
   ```go
   deviceId := header.RequestHeader().Get("X-Genie-DeviceId")
   timeStamp := header.RequestHeader().Get("X-Genie-Timestamp")
   // ... 其他参数
   
   if sign == "" || timeStamp == "" || /* ... */ {
       return nil, errorV1.ErrorHwPaasCorrectError("必选参数不合法")
   }
   ```
   - 提取所有签名相关参数
   - 检查参数完整性

2. **签名计算**:
   ```go
   signStr := appSign.SignKey + "&X-Genie-Timestamp=" + timeStamp + "&X-Genie-Nonce=" + nonce + "&X-Genie-DeviceId=" + deviceId
   verifySign := utils.MD5HashString(signStr)
   ```
   - 按照约定格式构建签名字符串
   - 使用MD5计算签名值

3. **签名比对**:
   ```go
   if verifySign != sign {
       return nil, errorV1.ErrorHwPaasSignError("sign error")
   }
   ```
   - 比对计算的签名和客户端传递的签名
   - 签名不匹配则返回错误

## 4. 上下文管理

### 4.1 自定义上下文

**文件位置**: `internal/pkg/custom_context/ctx_key.go`

```go
package custom_context

import (
	"context"
)

type contextKey string

const (
	DeviceIdKey contextKey = "device_id"
	PlatformKey contextKey = "platform"
	AppIdKey    contextKey = "app_id"
	VersionKey  contextKey = "version"
)

// 设置设备ID
func SetDeviceId(ctx context.Context, deviceId string) context.Context {
	return context.WithValue(ctx, DeviceIdKey, deviceId)
}

// 获取设备ID
func GetDeviceId(ctx context.Context) string {
	if value := ctx.Value(DeviceIdKey); value != nil {
		return value.(string)
	}
	return ""
}

// 设置平台信息
func SetPlatform(ctx context.Context, platform string) context.Context {
	return context.WithValue(ctx, PlatformKey, platform)
}

// 获取平台信息
func GetPlatform(ctx context.Context) string {
	if value := ctx.Value(PlatformKey); value != nil {
		return value.(string)
	}
	return ""
}

// 设置应用ID
func SetAppId(ctx context.Context, appId string) context.Context {
	return context.WithValue(ctx, AppIdKey, appId)
}

// 获取应用ID
func GetAppId(ctx context.Context) string {
	if value := ctx.Value(AppIdKey); value != nil {
		return value.(string)
	}
	return ""
}

// 设置版本信息
func SetVersion(ctx context.Context, version string) context.Context {
	return context.WithValue(ctx, VersionKey, version)
}

// 获取版本信息
func GetVersion(ctx context.Context) string {
	if value := ctx.Value(VersionKey); value != nil {
		return value.(string)
	}
	return ""
}

// 通用的设置方法
func WithValue(ctx context.Context, key string, value interface{}) context.Context {
	return context.WithValue(ctx, contextKey(key), value)
}

// 通用的获取方法
func GetValue(ctx context.Context, key string) interface{} {
	return ctx.Value(contextKey(key))
}
```

#### 上下文管理分析：

1. **类型安全的键**:
   ```go
   type contextKey string
   const (
       DeviceIdKey contextKey = "device_id"
       // ...
   )
   ```
   - 使用自定义类型避免键冲突
   - 提供类型安全的上下文操作

2. **便捷的访问方法**:
   ```go
   func GetDeviceId(ctx context.Context) string {
       if value := ctx.Value(DeviceIdKey); value != nil {
           return value.(string)
       }
       return ""
   }
   ```
   - 封装类型断言逻辑
   - 提供默认值处理

### 4.2 追踪上下文

**文件位置**: `internal/pkg/trace_context/trace.go`

```go
package trace_context

import (
	"context"
	"github.com/google/uuid"
)

type traceKey string

const TraceIdKey traceKey = "trace_id"

// 生成追踪ID
func GenerateTraceId() string {
	return uuid.New().String()
}

// 设置追踪ID
func WithTraceId(ctx context.Context, traceId string) context.Context {
	return context.WithValue(ctx, TraceIdKey, traceId)
}

// 获取追踪ID
func GetTraceId(ctx context.Context) string {
	if value := ctx.Value(TraceIdKey); value != nil {
		return value.(string)
	}
	return ""
}
```

## 5. 错误处理中间件

### 5.1 错误编码器

**文件位置**: `internal/server/error_decoder.go`

```go
package server

import (
	"context"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/transport/http"
	"hw-paas-service/internal/conf"
	"net/url"
	"strings"
)

func MyErrorEncoder(w http.ResponseWriter, r *http.Request, err error) {
	se := errors.FromError(err)
	codec, _ := http.CodecForRequest(r, "Accept")
	
	// 获取请求路径和方法
	method := r.Method
	path := r.URL.Path
	operation := method + "#" + path
	
	// 检查是否有自定义错误消息
	if errorHandle != nil && errorHandle.Handle != nil {
		if apiConfig, exists := errorHandle.Handle[operation]; exists {
			for _, errMsg := range apiConfig.ErrorMessages {
				if errMsg.ErrorReason == se.Reason {
					// 使用自定义错误消息
					se.Message = errMsg.Message
					break
				}
			}
		}
	}
	
	// 如果没有找到自定义消息，使用默认消息
	if se.Message == "" && errorHandle != nil {
		se.Message = errorHandle.Default
	}
	
	body, err := codec.Marshal(se)
	if err != nil {
		w.WriteHeader(500)
		return
	}
	
	w.Header().Set("Content-Type", "application/"+codec.Name())
	w.WriteHeader(int(se.Code))
	w.Write(body)
}
```

### 5.2 响应编码器

**文件位置**: `internal/server/response_encoder.go`

```go
package server

import (
	"github.com/go-kratos/kratos/v2/transport/http"
	"net/http"
)

func MyResponseEncoder(w http.ResponseWriter, r *http.Request, v interface{}) error {
	if v == nil {
		return nil
	}
	
	codec, _ := http.CodecForRequest(r, "Accept")
	data, err := codec.Marshal(v)
	if err != nil {
		return err
	}
	
	w.Header().Set("Content-Type", "application/"+codec.Name())
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(data)
	return err
}
```

## 6. 中间件测试

### 6.1 单元测试

```go
func TestAccessCheckMiddleware(t *testing.T) {
	// 创建模拟的用户中心仓库
	mockRepo := &MockUcenterRepo{}
	
	// 创建配置
	authConf := &conf.Auth{
		ProtectedUrls: []string{"/api/v1/protected"},
	}
	
	// 创建中间件
	mw := middleware.AccessCheck(authConf, mockRepo)
	
	// 创建测试处理器
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return "success", nil
	}
	
	// 包装处理器
	wrappedHandler := mw(handler)
	
	// 测试用例1：无需认证的接口
	ctx := createMockContext("/api/v1/public", "")
	resp, err := wrappedHandler(ctx, nil)
	assert.NoError(t, err)
	assert.Equal(t, "success", resp)
	
	// 测试用例2：需要认证但无token
	ctx = createMockContext("/api/v1/protected", "")
	resp, err = wrappedHandler(ctx, nil)
	assert.Error(t, err)
	
	// 测试用例3：有效token
	mockRepo.On("CheckLogin", mock.Anything, mock.Anything).Return(&data.CheckLoginResp{
		TalType: ucenter_go.USER_FORMAL,
	}, nil)
	
	ctx = createMockContext("/api/v1/protected", "valid-token")
	resp, err = wrappedHandler(ctx, nil)
	assert.NoError(t, err)
	assert.Equal(t, "success", resp)
}
```

### 6.2 集成测试

```go
func TestMiddlewareChain(t *testing.T) {
	// 创建测试服务器
	srv := httptest.NewServer(createTestHandler())
	defer srv.Close()
	
	// 测试请求
	req, _ := http.NewRequest("GET", srv.URL+"/api/v1/test", nil)
	req.Header.Set("X-Trace-Id", "test-trace-id")
	req.Header.Set("X-Genie-DeviceId", "test-device")
	
	client := &http.Client{}
	resp, err := client.Do(req)
	
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)
	
	// 验证响应头
	assert.Equal(t, "test-trace-id", resp.Header.Get("X-Trace-Id"))
}
```

## 7. 性能优化

### 7.1 中间件缓存

```go
// 缓存中间件结果
type CachedMiddleware struct {
	cache map[string]interface{}
	mu    sync.RWMutex
}

func (c *CachedMiddleware) CacheableMiddleware(key string, fn func() (interface{}, error)) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// 检查缓存
			c.mu.RLock()
			if cached, exists := c.cache[key]; exists {
				c.mu.RUnlock()
				// 使用缓存结果
				return handler(ctx, req)
			}
			c.mu.RUnlock()
			
			// 计算结果
			result, err := fn()
			if err != nil {
				return nil, err
			}
			
			// 存储到缓存
			c.mu.Lock()
			c.cache[key] = result
			c.mu.Unlock()
			
			return handler(ctx, req)
		}
	}
}
```

### 7.2 异步中间件

```go
// 异步处理中间件
func AsyncMiddleware(processor func(context.Context, interface{})) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// 异步处理
			go func() {
				// 创建新的上下文，避免取消传播
				asyncCtx := context.Background()
				processor(asyncCtx, req)
			}()
			
			// 继续处理请求
			return handler(ctx, req)
		}
	}
}
```

## 8. 最佳实践

### 8.1 中间件设计原则

1. **单一职责**: 每个中间件只负责一个功能
2. **无状态**: 中间件应该是无状态的
3. **可配置**: 通过配置控制中间件行为
4. **错误处理**: 合理处理和传播错误
5. **性能考虑**: 避免在中间件中进行重操作

### 8.2 中间件顺序

```go
// 推荐的中间件顺序
var middlewares = []middleware.Middleware{
	recovery.Recovery(),      // 1. 异常恢复（最外层）
	tracing.Server(),        // 2. 链路追踪
	logging.Server(logger),  // 3. 日志记录
	metrics.Server(...),     // 4. 指标收集
	rateLimit.Middleware(),  // 5. 限流
	auth.Middleware(),       // 6. 认证授权
	validate.Validator(),    // 7. 参数验证（最内层）
}
```

### 8.3 错误处理策略

```go
// 统一错误处理
func ErrorHandlingMiddleware() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			resp, err := handler(ctx, req)
			
			if err != nil {
				// 记录错误日志
				log.WithContext(ctx).Errorf("Request failed: %v", err)
				
				// 转换为标准错误格式
				if !errors.Is(err, errors.BadRequest("", "", "")) {
					err = errors.InternalServer("INTERNAL_ERROR", "Internal server error")
				}
			}
			
			return resp, err
		}
	}
}
```

## 9. 总结

中间件系统在 hw-paas-service 项目中提供了强大的横切关注点处理能力：

1. **模块化设计**: 每个中间件职责单一，可独立测试
2. **可组合性**: 支持灵活的中间件组合和排序
3. **配置化**: 通过配置控制中间件行为
4. **性能优化**: 合理的执行顺序和缓存策略
5. **错误处理**: 统一的错误处理和响应格式

通过合理设计和使用中间件，可以大大简化业务逻辑，提高代码的可维护性和可重用性。