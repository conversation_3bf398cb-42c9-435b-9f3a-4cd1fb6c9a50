# GORM ORM 框架详解

## 1. GORM 简介

GORM 是 Go 语言中最受欢迎的 ORM（Object-Relational Mapping）库，提供了友好的 API 来操作数据库。它支持多种数据库，包括 MySQL、PostgreSQL、SQLite、SQL Server 等。

### 1.1 核心特性

- **全功能 ORM**: 支持关联、钩子、事务、迁移等
- **链式 API**: 提供直观的查询构建器
- **自动迁移**: 自动创建和更新数据库表结构
- **关联处理**: 支持一对一、一对多、多对多关联
- **钩子支持**: 提供 BeforeCreate、AfterUpdate 等钩子
- **事务支持**: 完整的事务处理能力

## 2. 项目中的 GORM 配置

### 2.1 数据库连接配置

**文件位置**: `internal/data/data.go`

```go
package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/uptrace/opentelemetry-go-extra/otelgorm"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"hw-paas-service/internal/biz/biz_metrics"
	"hw-paas-service/internal/conf"
	"time"

	"github.com/redis/go-redis/extra/redisotel/v9"
	"github.com/redis/go-redis/v9"
)

// Data .
type Data struct {
	DB  *gorm.DB
	Rdb *redis.Client
}

// NewData .
func NewData(conf *conf.Data, logger log.Logger) (*Data, func(), error) {
	db, err := gorm.Open(mysql.Open(conf.Database.Source), &gorm.Config{})
	if err != nil {
		return nil, nil, err
	}
	if err := db.Use(otelgorm.NewPlugin()); err != nil {
		return nil, nil, errors.Wrap(err, "data: db.Use error")
	}

	// ... Redis 配置代码 ...

	d := &Data{
		DB:  db,
		Rdb: rdb,
	}

	return d, func() {
		_db, err := d.DB.DB()
		if err != nil {
			log.NewHelper(logger).Errorf("database close err:%+v", err)
		}
		_ = _db.Close()
		log.NewHelper(logger).Info("closing the mysql")

		d.Rdb.Close()
		log.NewHelper(logger).Info("closing the redis")
	}, nil
}
```

#### 逐行分析：

1. **数据库连接初始化**:
   ```go
   db, err := gorm.Open(mysql.Open(conf.Database.Source), &gorm.Config{})
   ```
   - `gorm.Open()`: GORM 的数据库连接函数
   - `mysql.Open()`: MySQL 驱动，解析连接字符串
   - `&gorm.Config{}`: GORM 配置，可以设置日志级别、命名策略等

2. **OpenTelemetry 集成**:
   ```go
   if err := db.Use(otelgorm.NewPlugin()); err != nil {
       return nil, nil, errors.Wrap(err, "data: db.Use error")
   }
   ```
   - `db.Use()`: 注册 GORM 插件
   - `otelgorm.NewPlugin()`: OpenTelemetry 追踪插件
   - 自动为数据库操作添加链路追踪

3. **资源清理**:
   ```go
   return d, func() {
       _db, err := d.DB.DB()
       if err != nil {
           log.NewHelper(logger).Errorf("database close err:%+v", err)
       }
       _ = _db.Close()
       log.NewHelper(logger).Info("closing the mysql")
   }, nil
   ```
   - 返回清理函数，用于应用关闭时释放数据库连接
   - `d.DB.DB()`: 获取底层的 `*sql.DB` 实例
   - `_db.Close()`: 关闭数据库连接池

### 2.2 数据库配置文件

**文件位置**: `configs/config.yaml`

```yaml
data:
  database:
    driver: mysql
    source: ai_tools_rw:jJ9yO_uD7lS4dK0q@tcp(sea-pad-mysql-test.mysql.database.chinacloudapi.cn:3306)/ai_tools?timeout=1s&readTimeout=1s&writeTimeout=1s&parseTime=true&loc=Local&charset=utf8mb4,utf8
```

#### 连接字符串解析：

- `ai_tools_rw:jJ9yO_uD7lS4dK0q`: 用户名和密码
- `sea-pad-mysql-test.mysql.database.chinacloudapi.cn:3306`: 主机和端口
- `ai_tools`: 数据库名
- `timeout=1s`: 连接超时时间
- `readTimeout=1s`: 读取超时时间
- `writeTimeout=1s`: 写入超时时间
- `parseTime=true`: 自动解析时间类型
- `loc=Local`: 时区设置
- `charset=utf8mb4,utf8`: 字符集设置

## 3. GORM 模型定义

### 3.1 基础模型示例

**文件位置**: `internal/data/model/hw_en_word.go`

```go
package model

import (
	"time"
)

type HwEnWord struct {
	ID                int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Word              string    `gorm:"column:word;type:varchar(255);not null" json:"word"`
	Pronunciation     string    `gorm:"column:pronunciation;type:text" json:"pronunciation"`
	Meanings          string    `gorm:"column:meanings;type:text" json:"meanings"`
	Synonyms          string    `gorm:"column:synonyms;type:text" json:"synonyms"`
	Antonyms          string    `gorm:"column:antonyms;type:text" json:"antonyms"`
	Phrases           string    `gorm:"column:phrases;type:text" json:"phrases"`
	Sentences         string    `gorm:"column:sentences;type:text" json:"sentences"`
	Inflections       string    `gorm:"column:inflections;type:text" json:"inflections"`
	RootVariations    string    `gorm:"column:root_variations;type:text" json:"root_variations"`
	CreatedAt         time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt         time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

func (HwEnWord) TableName() string {
	return "hw_en_word"
}
```

#### GORM 标签详解：

1. **主键设置**:
   ```go
   ID int `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
   ```
   - `column:id`: 指定数据库列名
   - `primaryKey`: 设置为主键
   - `autoIncrement`: 自动递增

2. **字段类型和约束**:
   ```go
   Word string `gorm:"column:word;type:varchar(255);not null" json:"word"`
   ```
   - `type:varchar(255)`: 指定数据库字段类型和长度
   - `not null`: 非空约束

3. **文本字段**:
   ```go
   Pronunciation string `gorm:"column:pronunciation;type:text" json:"pronunciation"`
   ```
   - `type:text`: 大文本字段类型

4. **时间字段**:
   ```go
   CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
   UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
   ```
   - `autoCreateTime`: 创建时自动设置时间
   - `autoUpdateTime`: 更新时自动设置时间

5. **表名设置**:
   ```go
   func (HwEnWord) TableName() string {
       return "hw_en_word"
   }
   ```
   - 自定义表名，覆盖 GORM 的默认命名规则

### 3.2 复杂模型示例

**文件位置**: `internal/data/model/ai_jzx_question.go`

```go
package model

import (
	"time"
)

type AiJzxQuestion struct {
	ID          int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	QuestionId  string    `gorm:"column:question_id;type:varchar(255);uniqueIndex" json:"question_id"`
	Title       string    `gorm:"column:title;type:varchar(500)" json:"title"`
	Content     string    `gorm:"column:content;type:longtext" json:"content"`
	Answer      string    `gorm:"column:answer;type:longtext" json:"answer"`
	Difficulty  int       `gorm:"column:difficulty;type:tinyint;default:1" json:"difficulty"`
	Subject     string    `gorm:"column:subject;type:varchar(100)" json:"subject"`
	Grade       string    `gorm:"column:grade;type:varchar(50)" json:"grade"`
	Tags        string    `gorm:"column:tags;type:text" json:"tags"`
	Status      int       `gorm:"column:status;type:tinyint;default:1" json:"status"`
	CreatedBy   string    `gorm:"column:created_by;type:varchar(100)" json:"created_by"`
	UpdatedBy   string    `gorm:"column:updated_by;type:varchar(100)" json:"updated_by"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	DeletedAt   *time.Time `gorm:"column:deleted_at;index" json:"deleted_at,omitempty"`
}

func (AiJzxQuestion) TableName() string {
	return "ai_jzx_question"
}
```

#### 高级特性：

1. **唯一索引**:
   ```go
   QuestionId string `gorm:"column:question_id;type:varchar(255);uniqueIndex" json:"question_id"`
   ```
   - `uniqueIndex`: 创建唯一索引

2. **默认值**:
   ```go
   Difficulty int `gorm:"column:difficulty;type:tinyint;default:1" json:"difficulty"`
   ```
   - `default:1`: 设置默认值

3. **软删除**:
   ```go
   DeletedAt *time.Time `gorm:"column:deleted_at;index" json:"deleted_at,omitempty"`
   ```
   - `*time.Time`: 指针类型，支持 NULL 值
   - `index`: 创建索引
   - GORM 会自动处理软删除逻辑

## 4. DAO 层实现

### 4.1 基础 DAO 实现

**文件位置**: `internal/data/dao/hw_en_word.go`

```go
package dao

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
)

type HwEnWordDao struct {
	data *data.Data
	log  *log.Helper
}

func NewGpt4oWordsDao(data *data.Data, logger log.Logger) *HwEnWordDao {
	return &HwEnWordDao{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (d *HwEnWordDao) GetByWord(ctx context.Context, word string) (*model.HwEnWord, error) {
	var result model.HwEnWord
	err := d.data.DB.WithContext(ctx).Where("word = ?", word).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (d *HwEnWordDao) Create(ctx context.Context, word *model.HwEnWord) error {
	return d.data.DB.WithContext(ctx).Create(word).Error
}

func (d *HwEnWordDao) Update(ctx context.Context, word *model.HwEnWord) error {
	return d.data.DB.WithContext(ctx).Save(word).Error
}

func (d *HwEnWordDao) Delete(ctx context.Context, id int) error {
	return d.data.DB.WithContext(ctx).Delete(&model.HwEnWord{}, id).Error
}

func (d *HwEnWordDao) List(ctx context.Context, offset, limit int) ([]*model.HwEnWord, error) {
	var results []*model.HwEnWord
	err := d.data.DB.WithContext(ctx).Offset(offset).Limit(limit).Find(&results).Error
	return results, err
}
```

#### GORM 操作详解：

1. **上下文传递**:
   ```go
   d.data.DB.WithContext(ctx)
   ```
   - 传递上下文，支持超时控制和链路追踪

2. **条件查询**:
   ```go
   err := d.data.DB.WithContext(ctx).Where("word = ?", word).First(&result).Error
   ```
   - `Where()`: 添加 WHERE 条件
   - `First()`: 查询第一条记录
   - 使用占位符 `?` 防止 SQL 注入

3. **创建记录**:
   ```go
   return d.data.DB.WithContext(ctx).Create(word).Error
   ```
   - `Create()`: 插入新记录
   - 自动设置 CreatedAt 和 UpdatedAt

4. **更新记录**:
   ```go
   return d.data.DB.WithContext(ctx).Save(word).Error
   ```
   - `Save()`: 保存记录，如果主键存在则更新，否则创建

5. **删除记录**:
   ```go
   return d.data.DB.WithContext(ctx).Delete(&model.HwEnWord{}, id).Error
   ```
   - `Delete()`: 删除记录
   - 如果模型有 DeletedAt 字段，执行软删除

6. **分页查询**:
   ```go
   err := d.data.DB.WithContext(ctx).Offset(offset).Limit(limit).Find(&results).Error
   ```
   - `Offset()`: 设置偏移量
   - `Limit()`: 设置限制数量
   - `Find()`: 查询多条记录

### 4.2 复杂查询示例

**文件位置**: `internal/data/dao/ai_jzx_question.go`

```go
package dao

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"hw-paas-service/internal/data"
	"hw-paas-service/internal/data/model"
)

type AiJzxQuestionDao struct {
	data *data.Data
	log  *log.Helper
}

func NewAiJzxQuestionDao(data *data.Data, logger log.Logger) *AiJzxQuestionDao {
	return &AiJzxQuestionDao{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (d *AiJzxQuestionDao) GetByQuestionId(ctx context.Context, questionId string) (*model.AiJzxQuestion, error) {
	var result model.AiJzxQuestion
	err := d.data.DB.WithContext(ctx).Where("question_id = ? AND status = ?", questionId, 1).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (d *AiJzxQuestionDao) ListByConditions(ctx context.Context, subject, grade string, difficulty int, offset, limit int) ([]*model.AiJzxQuestion, int64, error) {
	var results []*model.AiJzxQuestion
	var total int64
	
	query := d.data.DB.WithContext(ctx).Model(&model.AiJzxQuestion{}).Where("status = ?", 1)
	
	if subject != "" {
		query = query.Where("subject = ?", subject)
	}
	if grade != "" {
		query = query.Where("grade = ?", grade)
	}
	if difficulty > 0 {
		query = query.Where("difficulty = ?", difficulty)
	}
	
	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	err = query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&results).Error
	if err != nil {
		return nil, 0, err
	}
	
	return results, total, nil
}

func (d *AiJzxQuestionDao) BatchCreate(ctx context.Context, questions []*model.AiJzxQuestion) error {
	return d.data.DB.WithContext(ctx).CreateInBatches(questions, 100).Error
}

func (d *AiJzxQuestionDao) UpdateStatus(ctx context.Context, questionId string, status int) error {
	return d.data.DB.WithContext(ctx).Model(&model.AiJzxQuestion{}).
		Where("question_id = ?", questionId).
		Update("status", status).Error
}
```

#### 高级查询特性：

1. **动态条件构建**:
   ```go
   query := d.data.DB.WithContext(ctx).Model(&model.AiJzxQuestion{}).Where("status = ?", 1)
   
   if subject != "" {
       query = query.Where("subject = ?", subject)
   }
   ```
   - 根据参数动态添加查询条件
   - 链式调用构建复杂查询

2. **计数查询**:
   ```go
   err := query.Count(&total).Error
   ```
   - `Count()`: 获取符合条件的记录总数
   - 用于分页计算

3. **排序**:
   ```go
   err = query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&results).Error
   ```
   - `Order()`: 设置排序规则
   - 支持多字段排序

4. **批量插入**:
   ```go
   return d.data.DB.WithContext(ctx).CreateInBatches(questions, 100).Error
   ```
   - `CreateInBatches()`: 批量插入，提高性能
   - 第二个参数是批次大小

5. **部分更新**:
   ```go
   return d.data.DB.WithContext(ctx).Model(&model.AiJzxQuestion{}).
       Where("question_id = ?", questionId).
       Update("status", status).Error
   ```
   - `Update()`: 只更新指定字段
   - 避免全字段更新

## 5. 事务处理

### 5.1 手动事务

```go
func (d *SomeDao) TransactionExample(ctx context.Context) error {
	return d.data.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 在事务中执行操作
		if err := tx.Create(&model1).Error; err != nil {
			return err // 自动回滚
		}
		
		if err := tx.Create(&model2).Error; err != nil {
			return err // 自动回滚
		}
		
		// 返回 nil 提交事务
		return nil
	})
}
```

### 5.2 手动控制事务

```go
func (d *SomeDao) ManualTransaction(ctx context.Context) error {
	tx := d.data.DB.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	if err := tx.Create(&model1).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Create(&model2).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
```

## 6. GORM 钩子使用

### 6.1 模型钩子

```go
type User struct {
	ID        uint
	Name      string
	Email     string
	CreatedAt time.Time
	UpdatedAt time.Time
}

// BeforeCreate 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) (err error) {
	// 数据验证
	if u.Name == "" {
		return errors.New("name cannot be empty")
	}
	
	// 数据处理
	u.Email = strings.ToLower(u.Email)
	return
}

// AfterCreate 创建后钩子
func (u *User) AfterCreate(tx *gorm.DB) (err error) {
	// 记录日志
	log.Printf("User created: %d", u.ID)
	return
}

// BeforeUpdate 更新前钩子
func (u *User) BeforeUpdate(tx *gorm.DB) (err error) {
	// 检查权限
	if u.Email == "<EMAIL>" {
		return errors.New("cannot update admin user")
	}
	return
}
```

## 7. 性能优化

### 7.1 预加载关联

```go
// 预加载关联数据
var users []User
db.Preload("Orders").Find(&users)

// 条件预加载
db.Preload("Orders", "state = ?", "paid").Find(&users)

// 嵌套预加载
db.Preload("Orders.OrderItems").Find(&users)
```

### 7.2 选择字段

```go
// 只选择需要的字段
var users []User
db.Select("name", "email").Find(&users)

// 排除某些字段
db.Omit("password").Find(&users)
```

### 7.3 批量操作

```go
// 批量插入
var users []User
db.CreateInBatches(users, 100)

// 批量更新
db.Model(&User{}).Where("active = ?", false).Updates(User{Active: true, UpdatedAt: time.Now()})
```

## 8. 常见问题和最佳实践

### 8.1 避免 N+1 查询

```go
// 错误：会产生 N+1 查询
var users []User
db.Find(&users)
for _, user := range users {
	var orders []Order
	db.Where("user_id = ?", user.ID).Find(&orders)
}

// 正确：使用预加载
var users []User
db.Preload("Orders").Find(&users)
```

### 8.2 使用索引

```go
type User struct {
	ID    uint   `gorm:"primaryKey"`
	Email string `gorm:"uniqueIndex"`
	Name  string `gorm:"index"`
}
```

### 8.3 连接池配置

```go
sqlDB, err := db.DB()
if err != nil {
	return err
}

// 设置连接池参数
sqlDB.SetMaxIdleConns(10)           // 最大空闲连接数
sqlDB.SetMaxOpenConns(100)          // 最大打开连接数
sqlDB.SetConnMaxLifetime(time.Hour) // 连接最大生存时间
```

## 9. 总结

GORM 在 hw-paas-service 项目中提供了强大的 ORM 功能：

1. **简洁的 API**: 链式调用，直观易用
2. **类型安全**: 编译时检查，减少运行时错误
3. **功能完整**: 支持关联、事务、钩子等高级特性
4. **性能优化**: 支持预加载、批量操作等优化手段
5. **可扩展性**: 插件系统，支持自定义扩展

通过合理使用 GORM 的各种特性，可以大大提高开发效率，同时保证代码的可维护性和性能。