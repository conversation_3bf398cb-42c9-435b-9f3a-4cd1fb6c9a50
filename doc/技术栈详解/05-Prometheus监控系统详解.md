# Prometheus 监控系统详解

## 1. Prometheus 简介

Prometheus 是一个开源的监控和告警系统，最初由 SoundCloud 开发。它具有多维数据模型、灵活的查询语言、高效的时间序列数据库等特性，是云原生监控的事实标准。

### 1.1 核心特性

- **多维数据模型**: 使用标签（labels）区分不同的时间序列
- **PromQL 查询语言**: 强大的查询和聚合能力
- **拉取模式**: 主动拉取监控数据，而非被动接收
- **服务发现**: 支持多种服务发现机制
- **告警管理**: 集成 Alertmanager 进行告警处理
- **可视化**: 内置表达式浏览器，支持 Grafana 集成

### 1.2 数据模型

Prometheus 的数据模型基于时间序列，每个时间序列由以下部分组成：
- **指标名称**: 如 `http_requests_total`
- **标签集合**: 如 `{method="GET", handler="/api"}`
- **时间戳**: Unix 时间戳
- **数值**: 浮点数值

## 2. 项目中的 Prometheus 集成

### 2.1 监控指标定义

**文件位置**: `internal/biz/biz_metrics/metrics.go`

```go
package biz_metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/mem"
	"runtime"
	"time"
)

const (
	Namespace = "HwPaasService"
)

var MetricSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
	Namespace: Namespace,
	Subsystem: "http",
	Name:      "duration_sec",
	Help:      "server requests duratio(sec).",
	Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.250, 0.5, 1, 1.5, 2.0},
}, []string{"kind", "operation"})

var MetricRequests = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: Namespace,
	Subsystem: "http",
	Name:      "total",
	Help:      "The total number of processed requests",
}, []string{"kind", "operation", "code", "reason"})

var MetricClientSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
	Namespace: Namespace,
	Subsystem: "third_client_http",
	Name:      "duration_sec",
	Help:      "server requests duratio(sec).",
	Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.250, 0.5, 1, 1.5, 2.0},
}, []string{"kind", "operation"})

var (
	startTime = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Namespace: Namespace,
			Name:      "start_time",
			Help:      "start_time(s)",
		})
	allMemUsage = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Namespace: Namespace,
			Name:      "all_mem",
			Help:      "all_mem",
		})
)

var (
	cpuUsage = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "go_cpu_usage",
		Help: "The fraction of CPU time consumed by the process.",
	})
	usedMemUsage = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "go_used_mem_usage",
		Help: "使用的内存",
	})
)

// MetricRedisPoolStats Redis连接池统计
var MetricRedisPoolStats = prometheus.NewGaugeVec(prometheus.GaugeOpts{
	Namespace: "redis",
	Subsystem: "pool",
	Name:      "stats",
	Help:      "Redis connection pool statistics.",
}, []string{"operation"})
```

#### 指标类型详解：

1. **Histogram（直方图）**:
   ```go
   var MetricSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
       Namespace: Namespace,
       Subsystem: "http",
       Name:      "duration_sec",
       Help:      "server requests duratio(sec).",
       Buckets:   []float64{0.005, 0.01, 0.025, 0.05, 0.1, 0.250, 0.5, 1, 1.5, 2.0},
   }, []string{"kind", "operation"})
   ```
   - **用途**: 测量请求持续时间、响应大小等
   - **Buckets**: 定义时间区间，用于计算分位数
   - **标签**: `kind` 和 `operation` 用于区分不同的请求类型
   - **自动生成指标**:
     - `_bucket`: 各个桶的计数
     - `_count`: 总请求数
     - `_sum`: 总持续时间

2. **Counter（计数器）**:
   ```go
   var MetricRequests = prometheus.NewCounterVec(prometheus.CounterOpts{
       Namespace: Namespace,
       Subsystem: "http",
       Name:      "total",
       Help:      "The total number of processed requests",
   }, []string{"kind", "operation", "code", "reason"})
   ```
   - **用途**: 计算累计值，如请求总数、错误总数
   - **特性**: 只能增加，不能减少
   - **标签**: 包含请求类型、操作、状态码、错误原因

3. **Gauge（仪表盘）**:
   ```go
   cpuUsage = prometheus.NewGauge(prometheus.GaugeOpts{
       Name: "go_cpu_usage",
       Help: "The fraction of CPU time consumed by the process.",
   })
   ```
   - **用途**: 测量瞬时值，如 CPU 使用率、内存使用量
   - **特性**: 可以增加、减少或设置为任意值

4. **GaugeVec（带标签的仪表盘）**:
   ```go
   var MetricRedisPoolStats = prometheus.NewGaugeVec(prometheus.GaugeOpts{
       Namespace: "redis",
       Subsystem: "pool",
       Name:      "stats",
       Help:      "Redis connection pool statistics.",
   }, []string{"operation"})
   ```
   - **用途**: 多维度的仪表盘指标
   - **标签**: 通过 `operation` 标签区分不同的统计项

### 2.2 指标注册和初始化

```go
func init() {
	prometheus.MustRegister(MetricSeconds, MetricRequests, MetricClientSeconds, MetricRedisPoolStats)
}
```

#### 注册过程分析：

1. **MustRegister()**: 
   - 将指标注册到默认的 Prometheus 注册表
   - 如果注册失败（如重复注册），程序会 panic
   - 确保指标在程序启动时就可用

### 2.3 系统监控实现

```go
func updateUsage() {
	ticker := time.NewTicker(1 * time.Second) // 每秒更新一次 CPU 使用率百分比
	for range ticker.C {
		percent, _ := cpu.Percent(time.Second, false) // 获取 CPU 使用率百分比数组（通常是所有CPU核心的平均值）
		cpuUsage.Set(percent[0])                      // 取第一个值作为示例，实际应用中可能需要根据实际情况选择或计算平均值。
		usedMemUsage.Set(getMemoryUsage())            //当前使用内存
	}
}

func getMemoryUsage() float64 {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	return float64(bToMb(memStats.Alloc))
}

func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

func getTotalMemory() uint64 {
	v, _ := mem.VirtualMemory()
	return v.Total / 1024 / 1024
}
```

#### 系统监控详解：

1. **CPU 使用率监控**:
   ```go
   percent, _ := cpu.Percent(time.Second, false)
   cpuUsage.Set(percent[0])
   ```
   - 使用 `gopsutil` 库获取 CPU 使用率
   - `time.Second`: 采样时间间隔
   - `false`: 不分别获取每个 CPU 核心的使用率

2. **内存使用监控**:
   ```go
   var memStats runtime.MemStats
   runtime.ReadMemStats(&memStats)
   return float64(bToMb(memStats.Alloc))
   ```
   - 使用 Go 运行时获取内存统计信息
   - `memStats.Alloc`: 当前分配的内存大小
   - 转换为 MB 单位便于理解

3. **定时更新**:
   ```go
   ticker := time.NewTicker(1 * time.Second)
   for range ticker.C {
       // 更新指标
   }
   ```
   - 每秒更新一次系统指标
   - 使用 goroutine 异步执行，避免阻塞主流程

### 2.4 Redis 连接池监控

```go
func RedisPoolStats(hits, misses, timeouts, totalConns, idleConns, stalledConns uint32) {
	MetricRedisPoolStats.WithLabelValues("hits").Set(float64(hits))
	MetricRedisPoolStats.WithLabelValues("misses").Set(float64(misses))
	MetricRedisPoolStats.WithLabelValues("timeouts").Set(float64(timeouts))
	MetricRedisPoolStats.WithLabelValues("totalConns").Set(float64(totalConns))
	MetricRedisPoolStats.WithLabelValues("idleConns").Set(float64(idleConns))
	MetricRedisPoolStats.WithLabelValues("stalledConns").Set(float64(stalledConns))
}
```

#### Redis 监控指标说明：

1. **hits**: 连接池命中次数
   - 表示复用现有连接的次数
   - 高命中率说明连接池配置合理

2. **misses**: 连接池未命中次数
   - 表示需要创建新连接的次数
   - 过高可能需要增加连接池大小

3. **timeouts**: 连接超时次数
   - 表示获取连接超时的次数
   - 可能表明连接池不足或网络问题

4. **totalConns**: 总连接数
   - 当前连接池中的总连接数
   - 用于监控连接池使用情况

5. **idleConns**: 空闲连接数
   - 当前空闲的连接数
   - 过多可能表明连接池配置过大

6. **stalledConns**: 阻塞连接数
   - 等待获取连接的请求数
   - 过多表明连接池不足

## 3. HTTP 中间件集成

### 3.1 Kratos 中间件集成

**文件位置**: `internal/server/http.go`

```go
package server

import (
	prom "github.com/go-kratos/kratos/contrib/metrics/prometheus/v2"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"hw-paas-service/internal/biz/biz_metrics"
)

func NewHTTPServer(c *conf.Server, /* ... 其他参数 */) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			tracing.Server(),
			middleware.AddTraceToRequest(),
			middleware.Header2Ctx(middleware.Options),
			middleware.AccessCheck(authConf, ucenterRepo),
			logging.Server(logger),
			validate.Validator(),
			metrics.Server(
				metrics.WithSeconds(prom.NewHistogram(biz_metrics.MetricSeconds)),
				metrics.WithRequests(prom.NewCounter(biz_metrics.MetricRequests)),
			),
		),
		// ... 其他配置
	}
	
	srv := http.NewServer(opts...)
	// ... 注册路由
	return srv
}
```

#### 中间件集成分析：

1. **Prometheus 适配器**:
   ```go
   metrics.Server(
       metrics.WithSeconds(prom.NewHistogram(biz_metrics.MetricSeconds)),
       metrics.WithRequests(prom.NewCounter(biz_metrics.MetricRequests)),
   )
   ```
   - `prom.NewHistogram()`: 将自定义 Histogram 包装为 Kratos 兼容的格式
   - `prom.NewCounter()`: 将自定义 Counter 包装为 Kratos 兼容的格式

2. **自动指标收集**:
   - 中间件自动为每个 HTTP 请求收集指标
   - 包括请求持续时间、请求总数、状态码等
   - 无需在业务代码中手动添加指标收集逻辑

## 4. 指标暴露和采集

### 4.1 指标端点暴露

虽然代码中没有直接展示，但通常会有如下实现：

```go
// 示例：暴露 Prometheus 指标端点
import (
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"net/http"
)

func setupMetricsEndpoint() {
	http.Handle("/metrics", promhttp.Handler())
	go func() {
		log.Fatal(http.ListenAndServe(":9090", nil))
	}()
}
```

### 4.2 指标格式示例

访问 `/metrics` 端点会返回如下格式的指标：

```
# HELP HwPaasService_http_duration_sec server requests duratio(sec).
# TYPE HwPaasService_http_duration_sec histogram
HwPaasService_http_duration_sec_bucket{kind="http",operation="/api/v1/users",le="0.005"} 100
HwPaasService_http_duration_sec_bucket{kind="http",operation="/api/v1/users",le="0.01"} 200
HwPaasService_http_duration_sec_bucket{kind="http",operation="/api/v1/users",le="0.025"} 300
HwPaasService_http_duration_sec_bucket{kind="http",operation="/api/v1/users",le="+Inf"} 500
HwPaasService_http_duration_sec_sum{kind="http",operation="/api/v1/users"} 12.5
HwPaasService_http_duration_sec_count{kind="http",operation="/api/v1/users"} 500

# HELP HwPaasService_http_total The total number of processed requests
# TYPE HwPaasService_http_total counter
HwPaasService_http_total{kind="http",operation="/api/v1/users",code="200",reason=""} 450
HwPaasService_http_total{kind="http",operation="/api/v1/users",code="400",reason="bad_request"} 30
HwPaasService_http_total{kind="http",operation="/api/v1/users",code="500",reason="internal_error"} 20

# HELP redis_pool_stats Redis connection pool statistics.
# TYPE redis_pool_stats gauge
redis_pool_stats{operation="hits"} 1500
redis_pool_stats{operation="misses"} 100
redis_pool_stats{operation="timeouts"} 5
redis_pool_stats{operation="totalConns"} 20
redis_pool_stats{operation="idleConns"} 15
redis_pool_stats{operation="stalledConns"} 0
```

## 5. PromQL 查询示例

### 5.1 基础查询

```promql
# 查询当前 HTTP 请求总数
HwPaasService_http_total

# 查询特定操作的请求数
HwPaasService_http_total{operation="/api/v1/users"}

# 查询错误请求数
HwPaasService_http_total{code!="200"}
```

### 5.2 聚合查询

```promql
# 计算请求速率（每秒请求数）
rate(HwPaasService_http_total[5m])

# 计算平均响应时间
rate(HwPaasService_http_duration_sec_sum[5m]) / rate(HwPaasService_http_duration_sec_count[5m])

# 计算 95% 分位数响应时间
histogram_quantile(0.95, rate(HwPaasService_http_duration_sec_bucket[5m]))

# 计算错误率
rate(HwPaasService_http_total{code!="200"}[5m]) / rate(HwPaasService_http_total[5m])
```

### 5.3 Redis 监控查询

```promql
# Redis 连接池命中率
redis_pool_stats{operation="hits"} / (redis_pool_stats{operation="hits"} + redis_pool_stats{operation="misses"})

# Redis 连接池使用率
redis_pool_stats{operation="totalConns"} - redis_pool_stats{operation="idleConns"}

# Redis 连接超时率
rate(redis_pool_stats{operation="timeouts"}[5m])
```

## 6. 告警规则配置

### 6.1 响应时间告警

```yaml
groups:
- name: hw-paas-service.rules
  rules:
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(HwPaasService_http_duration_sec_bucket[5m])) > 1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95% of requests are taking more than 1 second"
```

### 6.2 错误率告警

```yaml
  - alert: HighErrorRate
    expr: rate(HwPaasService_http_total{code!="200"}[5m]) / rate(HwPaasService_http_total[5m]) > 0.05
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is above 5%"
```

### 6.3 Redis 连接池告警

```yaml
  - alert: RedisPoolExhaustion
    expr: redis_pool_stats{operation="idleConns"} / redis_pool_stats{operation="totalConns"} < 0.1
    for: 30s
    labels:
      severity: warning
    annotations:
      summary: "Redis connection pool nearly exhausted"
      description: "Less than 10% of Redis connections are idle"
```

## 7. Grafana 仪表板

### 7.1 HTTP 请求监控面板

```json
{
  "dashboard": {
    "title": "HW PaaS Service Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(HwPaasService_http_total[5m])",
            "legendFormat": "{{operation}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(HwPaasService_http_duration_sec_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(HwPaasService_http_duration_sec_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      }
    ]
  }
}
```

### 7.2 系统资源监控面板

```json
{
  "panels": [
    {
      "title": "CPU Usage",
      "type": "singlestat",
      "targets": [
        {
          "expr": "go_cpu_usage",
          "legendFormat": "CPU %"
        }
      ]
    },
    {
      "title": "Memory Usage",
      "type": "singlestat",
      "targets": [
        {
          "expr": "go_used_mem_usage",
          "legendFormat": "Memory MB"
        }
      ]
    }
  ]
}
```

## 8. 性能优化建议

### 8.1 指标优化

```go
// 避免高基数标签
// 错误示例：使用用户ID作为标签
badMetric := prometheus.NewCounterVec(
    prometheus.CounterOpts{Name: "user_requests"},
    []string{"user_id"}, // 高基数标签
)

// 正确示例：使用用户类型作为标签
goodMetric := prometheus.NewCounterVec(
    prometheus.CounterOpts{Name: "user_requests"},
    []string{"user_type"}, // 低基数标签
)
```

### 8.2 采样优化

```go
// 对于高频操作，考虑采样
func recordMetricWithSampling(operation string, duration time.Duration) {
    // 只记录 10% 的指标
    if rand.Float32() < 0.1 {
        MetricSeconds.WithLabelValues("http", operation).Observe(duration.Seconds())
    }
}
```

### 8.3 批量更新

```go
// 批量更新指标，减少锁竞争
type MetricsBatch struct {
    requests map[string]int
    mu       sync.Mutex
}

func (mb *MetricsBatch) Increment(operation string) {
    mb.mu.Lock()
    mb.requests[operation]++
    mb.mu.Unlock()
}

func (mb *MetricsBatch) Flush() {
    mb.mu.Lock()
    defer mb.mu.Unlock()
    
    for operation, count := range mb.requests {
        MetricRequests.WithLabelValues("http", operation, "200", "").Add(float64(count))
        mb.requests[operation] = 0
    }
}
```

## 9. 故障排查

### 9.1 常见问题

1. **指标不更新**:
   ```bash
   # 检查指标是否注册
   curl http://localhost:8080/metrics | grep MetricName
   
   # 检查标签是否正确
   curl http://localhost:8080/metrics | grep -A5 -B5 MetricName
   ```

2. **内存泄漏**:
   ```promql
   # 监控指标数量
   prometheus_tsdb_symbol_table_size_bytes
   
   # 监控时间序列数量
   prometheus_tsdb_head_series
   ```

3. **性能问题**:
   ```promql
   # 监控采集时间
   prometheus_target_scrape_duration_seconds
   
   # 监控采集频率
   rate(prometheus_target_scrapes_total[5m])
   ```

### 9.2 调试技巧

```go
// 添加调试日志
func debugMetrics() {
    mfs, _ := prometheus.DefaultGatherer.Gather()
    for _, mf := range mfs {
        fmt.Printf("Metric: %s, Type: %s\n", mf.GetName(), mf.GetType())
        for _, m := range mf.GetMetric() {
            fmt.Printf("  Labels: %v, Value: %v\n", m.GetLabel(), m.GetCounter().GetValue())
        }
    }
}
```

## 10. 总结

Prometheus 在 hw-paas-service 项目中提供了全面的监控能力：

1. **多维度指标**: 通过标签区分不同维度的监控数据
2. **自动化收集**: 通过中间件自动收集 HTTP 请求指标
3. **系统监控**: 监控 CPU、内存等系统资源
4. **组件监控**: 监控 Redis 连接池等关键组件
5. **可视化**: 支持 Grafana 仪表板展示
6. **告警**: 支持基于指标的告警规则

通过合理设计指标体系和查询规则，可以实现对应用性能和健康状况的全面监控，及时发现和解决问题。