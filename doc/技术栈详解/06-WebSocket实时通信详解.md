# WebSocket 实时通信详解

## 1. WebSocket 简介

WebSocket 是一种在单个 TCP 连接上进行全双工通信的协议。它使得客户端和服务器之间的数据交换变得更加简单，允许服务端主动向客户端推送数据。

### 1.1 核心特性

- **全双工通信**: 客户端和服务器可以同时发送数据
- **持久连接**: 连接建立后保持开放状态
- **低延迟**: 避免了 HTTP 的请求-响应开销
- **协议升级**: 从 HTTP 协议升级而来
- **帧格式**: 使用帧来传输数据
- **扩展支持**: 支持压缩等扩展

### 1.2 应用场景

- **实时聊天**: 即时消息传递
- **实时通知**: 服务器推送通知
- **在线游戏**: 实时游戏状态同步
- **实时监控**: 系统状态实时展示
- **协作编辑**: 多人实时协作
- **音视频通话**: 实时音视频传输

## 2. 项目中的 WebSocket 实现

### 2.1 WebSocket 服务实现

**文件位置**: `internal/service/evaluate/websocket.go`

```go
package evaluate

import (
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"hw-paas-service/internal/conf"
	"hw-paas-service/internal/data/services"
	"hw-paas-service/pkg/zlog"
	"net/http"
	"runtime/debug"
	"strings"
	"time"
)

const (
	// buffer
	bufferSize = 65535
)

// close reason
const (
	soundErrClose      = "sound_err_close"       //  先声返回结果异常关闭
	clientClose        = "client_close"          //  client 主动关闭
	goServerErrClose   = "go_server_err_close"   //  go server err close
	javaServerErrClose = "java_server_err_close" //  java server err close
	normallyClose      = "normally_close"        //  正常一次评测业务结束关闭
)

type EvalService struct {
	conf     *conf.Biz
	services *services.Services
}

func NewEvaluateService(cf *conf.Biz, services *services.Services) *EvalService {
	return &EvalService{
		conf:     cf,
		services: services,
	}
}
```

#### 基础结构分析：

1. **缓冲区大小**:
   ```go
   const (
       bufferSize = 65535
   )
   ```
   - 设置 64KB 的缓冲区大小
   - 用于 WebSocket 的读写缓冲区
   - 影响内存使用和传输效率

2. **连接关闭原因**:
   ```go
   const (
       soundErrClose      = "sound_err_close"       
       clientClose        = "client_close"          
       goServerErrClose   = "go_server_err_close"   
       javaServerErrClose = "java_server_err_close" 
       normallyClose      = "normally_close"        
   )
   ```
   - 定义不同的连接关闭原因
   - 便于问题排查和统计分析
   - 区分主动关闭和异常关闭

### 2.2 WebSocket 升级处理

```go
func (eval *EvalService) Upgrade(w http.ResponseWriter, r *http.Request, header http.Header) (*websocket.Conn, error) {
	upgrader := &websocket.Upgrader{
		ReadBufferSize:  bufferSize,
		WriteBufferSize: bufferSize,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		eval.log(header).Errorf("websocket upgrader conn fail, req url : %s ip : %s, err is %v", r.URL.Path, r.RemoteAddr, err)
		return nil, err
	}
	return conn, nil
}
```

#### 升级过程详解：

1. **Upgrader 配置**:
   ```go
   upgrader := &websocket.Upgrader{
       ReadBufferSize:  bufferSize,
       WriteBufferSize: bufferSize,
       CheckOrigin: func(r *http.Request) bool {
           return true
       },
   }
   ```
   - `ReadBufferSize`: 读缓冲区大小
   - `WriteBufferSize`: 写缓冲区大小
   - `CheckOrigin`: 跨域检查函数，这里允许所有来源

2. **协议升级**:
   ```go
   conn, err := upgrader.Upgrade(w, r, nil)
   ```
   - 将 HTTP 连接升级为 WebSocket 连接
   - 返回 `*websocket.Conn` 对象用于后续通信
   - 升级失败时返回错误

3. **错误处理**:
   ```go
   if err != nil {
       eval.log(header).Errorf("websocket upgrader conn fail, req url : %s ip : %s, err is %v", r.URL.Path, r.RemoteAddr, err)
       return nil, err
   }
   ```
   - 记录升级失败的详细信息
   - 包含请求 URL、客户端 IP 等上下文信息

### 2.3 WebSocket 处理器

```go
// WsHandler 建立websocket连接，并和中台进行连接
func (eval *EvalService) WsHandler(w http.ResponseWriter, r *http.Request) {
	now := time.Now()
	// 捕获可能得panic，防止程序异常
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("go routine panic错误：", err, "stack:", debug.Stack())
			return
		}
	}()

	var (
		err  error
		conn *websocket.Conn
	)

	// 解析请求头
	header := eval.parseHeader(r)
	//todo 临时验签

	// 升级连接为websocket
	conn, err = eval.Upgrade(w, r, header)

	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(http.StatusText(http.StatusBadRequest)))
		return
	}
	// 客户端的连接
	// 创建一个新的transfer对象负责读写和下游服务的通信
	transfer := NewWsTransfer(conn)
	// 组装下游连接，根据请求头和请求url请求中台
	err = transfer.AssembleDownstream(eval.conf, eval.services, header)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		_, _ = w.Write([]byte(http.StatusText(http.StatusBadRequest)))
		return
	}

	transfer.deviceId = header.Get("deviceId")
	transfer.reqId = header.Get("req_id")
	transfer.From = header.Get("from")
	transfer.talId = header.Get("talId")
	eval.log(header).Info("websocket new conn create...")
	// run
	transfer.run()
	eval.log(header).Infof("websocket conn cost %d", time.Since(now).Milliseconds())
}
```

#### 处理流程分析：

1. **性能监控**:
   ```go
   now := time.Now()
   // ... 处理逻辑 ...
   eval.log(header).Infof("websocket conn cost %d", time.Since(now).Milliseconds())
   ```
   - 记录连接建立的耗时
   - 用于性能监控和优化

2. **异常捕获**:
   ```go
   defer func() {
       if err := recover(); err != nil {
           fmt.Println("go routine panic错误：", err, "stack:", debug.Stack())
           return
       }
   }()
   ```
   - 捕获 panic 异常，防止程序崩溃
   - 打印堆栈信息便于调试
   - 确保服务的稳定性

3. **请求头解析**:
   ```go
   header := eval.parseHeader(r)
   ```
   - 解析 HTTP 请求头，提取必要信息
   - 为后续处理提供上下文

4. **Transfer 对象创建**:
   ```go
   transfer := NewWsTransfer(conn)
   err = transfer.AssembleDownstream(eval.conf, eval.services, header)
   ```
   - 创建传输对象管理 WebSocket 连接
   - 组装下游服务连接
   - 实现代理模式，转发消息

### 2.4 请求头解析

```go
func (eval *EvalService) parseHeader(r *http.Request) http.Header {
	_ = r.ParseForm()
	header := http.Header{}

	deviceId := r.Form.Get("X-Genie-DeviceId")
	reqId := r.Form.Get("req_id")

	if deviceId == "" {
		deviceId = r.Header.Get("X-Tal-Sn")
	}
	if reqId == "" {
		reqId = strings.Replace(uuid.NewString(), "-", "", -1)
	}

	talId := r.Header.Get("talId")

	header.Set("deviceId", deviceId)
	header.Set("req_id", reqId)
	header.Set("from", r.Form.Get("from"))
	header.Set("talId", talId)
	return header
}
```

#### 解析逻辑分析：

1. **表单解析**:
   ```go
   _ = r.ParseForm()
   ```
   - 解析 URL 查询参数和表单数据
   - 忽略解析错误（使用 `_` 丢弃错误）

2. **设备ID获取**:
   ```go
   deviceId := r.Form.Get("X-Genie-DeviceId")
   if deviceId == "" {
       deviceId = r.Header.Get("X-Tal-Sn")
   }
   ```
   - 优先从表单参数获取设备ID
   - 如果没有，则从请求头获取
   - 提供多种获取方式的兼容性

3. **请求ID生成**:
   ```go
   if reqId == "" {
       reqId = strings.Replace(uuid.NewString(), "-", "", -1)
   }
   ```
   - 如果没有提供请求ID，自动生成一个
   - 使用 UUID 确保唯一性
   - 移除连字符，简化ID格式

### 2.5 日志记录

```go
func (eval *EvalService) log(header http.Header) *zap.SugaredLogger {
	instance := zlog.SugaredInstance()
	return instance.With("deviceId", header.Get("deviceId")).With("reqId", header.Get("req_id")).
		With("talId", header.Get("talId"))
}

func (eval *EvalService) logInfo() *zap.SugaredLogger {
	instance := zlog.SugaredInstance()
	return instance
}
```

#### 日志设计分析：

1. **结构化日志**:
   ```go
   return instance.With("deviceId", header.Get("deviceId")).With("reqId", header.Get("req_id")).
       With("talId", header.Get("talId"))
   ```
   - 使用结构化日志记录关键信息
   - 包含设备ID、请求ID、用户ID等
   - 便于日志查询和问题排查

2. **上下文传递**:
   - 将请求上下文信息附加到日志中
   - 支持分布式追踪和调试
   - 提高问题定位效率

## 3. WebSocket 传输层实现

虽然项目中没有直接展示 Transfer 的实现，但根据使用方式可以推断其设计：

### 3.1 Transfer 结构设计

```go
// 推断的 Transfer 结构
type WsTransfer struct {
	conn        *websocket.Conn
	close       chan struct{}
	writeQueue  chan TransferWriteMsg
	lastPkgTime int64
	deviceId    string
	reqId       string
	From        string
	talId       string
	downstream  *DownstreamConn
}

type TransferWriteMsg struct {
	MessageType int
	Data        []byte
}
```

### 3.2 Transfer 方法实现

```go
func NewWsTransfer(conn *websocket.Conn) *WsTransfer {
	return &WsTransfer{
		conn:        conn,
		close:       make(chan struct{}, 1),
		writeQueue:  make(chan TransferWriteMsg, 1000),
		lastPkgTime: time.Now().Unix(),
	}
}

func (t *WsTransfer) run() {
	go t.readLoop()
	go t.writeLoop()
	go t.heartbeat()
	
	// 等待连接关闭
	<-t.close
}

func (t *WsTransfer) readLoop() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("readLoop panic: %v", r)
		}
		t.closeConnection()
	}()
	
	for {
		messageType, data, err := t.conn.ReadMessage()
		if err != nil {
			log.Errorf("read message error: %v", err)
			return
		}
		
		t.lastPkgTime = time.Now().Unix()
		
		// 转发到下游服务
		if t.downstream != nil {
			t.downstream.SendMessage(messageType, data)
		}
	}
}

func (t *WsTransfer) writeLoop() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("writeLoop panic: %v", r)
		}
		t.closeConnection()
	}()
	
	for {
		select {
		case msg := <-t.writeQueue:
			err := t.conn.WriteMessage(msg.MessageType, msg.Data)
			if err != nil {
				log.Errorf("write message error: %v", err)
				return
			}
		case <-t.close:
			return
		}
	}
}

func (t *WsTransfer) heartbeat() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			// 检查心跳超时
			if time.Now().Unix()-t.lastPkgTime > 60 {
				log.Warn("heartbeat timeout, closing connection")
				t.closeConnection()
				return
			}
			
			// 发送心跳包
			t.writeQueue <- TransferWriteMsg{
				MessageType: websocket.PingMessage,
				Data:        []byte("ping"),
			}
		case <-t.close:
			return
		}
	}
}

func (t *WsTransfer) closeConnection() {
	select {
	case t.close <- struct{}{}:
	default:
	}
	
	if t.conn != nil {
		t.conn.Close()
	}
	
	if t.downstream != nil {
		t.downstream.Close()
	}
}
```

## 4. WebSocket 消息类型

### 4.1 控制帧

```go
const (
	// WebSocket 消息类型
	TextMessage   = 1  // 文本消息
	BinaryMessage = 2  // 二进制消息
	CloseMessage  = 8  // 关闭消息
	PingMessage   = 9  // Ping 消息
	PongMessage   = 10 // Pong 消息
)
```

### 4.2 消息处理

```go
func (t *WsTransfer) handleMessage(messageType int, data []byte) {
	switch messageType {
	case websocket.TextMessage:
		t.handleTextMessage(data)
	case websocket.BinaryMessage:
		t.handleBinaryMessage(data)
	case websocket.CloseMessage:
		t.handleCloseMessage(data)
	case websocket.PingMessage:
		t.handlePingMessage(data)
	case websocket.PongMessage:
		t.handlePongMessage(data)
	default:
		log.Warnf("unknown message type: %d", messageType)
	}
}

func (t *WsTransfer) handleTextMessage(data []byte) {
	// 解析文本消息
	var msg map[string]interface{}
	if err := json.Unmarshal(data, &msg); err != nil {
		log.Errorf("parse text message error: %v", err)
		return
	}
	
	// 处理业务逻辑
	t.processBusinessMessage(msg)
}

func (t *WsTransfer) handlePingMessage(data []byte) {
	// 响应 Pong 消息
	t.writeQueue <- TransferWriteMsg{
		MessageType: websocket.PongMessage,
		Data:        data,
	}
}
```

## 5. 错误处理和重连机制

### 5.1 客户端重连

```javascript
// 客户端重连示例
class WebSocketClient {
    constructor(url) {
        this.url = url;
        this.reconnectInterval = 1000;
        this.maxReconnectAttempts = 5;
        this.reconnectAttempts = 0;
        this.connect();
    }
    
    connect() {
        this.ws = new WebSocket(this.url);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.reconnectAttempts = 0;
        };
        
        this.ws.onmessage = (event) => {
            this.handleMessage(event.data);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.reconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }
    
    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectInterval * this.reconnectAttempts);
        } else {
            console.error('Max reconnect attempts reached');
        }
    }
    
    send(data) {
        if (this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        } else {
            console.warn('WebSocket is not connected');
        }
    }
}
```

### 5.2 服务端错误处理

```go
func (t *WsTransfer) handleError(err error) {
	log.Errorf("WebSocket error: %v", err)
	
	// 根据错误类型采取不同处理策略
	if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
		log.Errorf("Unexpected close error: %v", err)
	}
	
	// 发送错误消息给客户端
	errorMsg := map[string]interface{}{
		"type":    "error",
		"message": "Internal server error",
		"code":    500,
	}
	
	data, _ := json.Marshal(errorMsg)
	t.writeQueue <- TransferWriteMsg{
		MessageType: websocket.TextMessage,
		Data:        data,
	}
	
	// 关闭连接
	t.closeConnection()
}
```

## 6. 性能优化

### 6.1 连接池管理

```go
type WebSocketPool struct {
	connections map[string]*WsTransfer
	mu          sync.RWMutex
	maxConns    int
}

func NewWebSocketPool(maxConns int) *WebSocketPool {
	return &WebSocketPool{
		connections: make(map[string]*WsTransfer),
		maxConns:    maxConns,
	}
}

func (p *WebSocketPool) AddConnection(id string, transfer *WsTransfer) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	if len(p.connections) >= p.maxConns {
		return errors.New("connection pool full")
	}
	
	p.connections[id] = transfer
	return nil
}

func (p *WebSocketPool) RemoveConnection(id string) {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	if transfer, exists := p.connections[id]; exists {
		transfer.closeConnection()
		delete(p.connections, id)
	}
}

func (p *WebSocketPool) Broadcast(message []byte) {
	p.mu.RLock()
	defer p.mu.RUnlock()
	
	for _, transfer := range p.connections {
		select {
		case transfer.writeQueue <- TransferWriteMsg{
			MessageType: websocket.TextMessage,
			Data:        message,
		}:
		default:
			// 队列满，跳过这个连接
			log.Warn("write queue full, skipping connection")
		}
	}
}
```

### 6.2 消息压缩

```go
func (eval *EvalService) UpgradeWithCompression(w http.ResponseWriter, r *http.Request, header http.Header) (*websocket.Conn, error) {
	upgrader := &websocket.Upgrader{
		ReadBufferSize:  bufferSize,
		WriteBufferSize: bufferSize,
		EnableCompression: true, // 启用压缩
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
	
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		return nil, err
	}
	
	// 设置压缩级别
	conn.EnableWriteCompression(true)
	
	return conn, nil
}
```

### 6.3 批量消息处理

```go
type MessageBatch struct {
	messages []TransferWriteMsg
	timer    *time.Timer
	mu       sync.Mutex
}

func (t *WsTransfer) batchWrite() {
	batch := &MessageBatch{
		messages: make([]TransferWriteMsg, 0, 100),
		timer:    time.NewTimer(10 * time.Millisecond),
	}
	
	for {
		select {
		case msg := <-t.writeQueue:
			batch.mu.Lock()
			batch.messages = append(batch.messages, msg)
			
			if len(batch.messages) >= 100 {
				t.flushBatch(batch)
				batch.timer.Reset(10 * time.Millisecond)
			}
			batch.mu.Unlock()
			
		case <-batch.timer.C:
			batch.mu.Lock()
			if len(batch.messages) > 0 {
				t.flushBatch(batch)
			}
			batch.timer.Reset(10 * time.Millisecond)
			batch.mu.Unlock()
			
		case <-t.close:
			return
		}
	}
}

func (t *WsTransfer) flushBatch(batch *MessageBatch) {
	for _, msg := range batch.messages {
		if err := t.conn.WriteMessage(msg.MessageType, msg.Data); err != nil {
			log.Errorf("batch write error: %v", err)
			t.closeConnection()
			return
		}
	}
	batch.messages = batch.messages[:0]
}
```

## 7. 监控和指标

### 7.1 WebSocket 指标

```go
var (
	wsConnections = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "websocket_connections_total",
			Help: "Total number of WebSocket connections",
		},
		[]string{"status"},
	)
	
	wsMessages = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "websocket_messages_total",
			Help: "Total number of WebSocket messages",
		},
		[]string{"direction", "type"},
	)
	
	wsMessageSize = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "websocket_message_size_bytes",
			Help:    "Size of WebSocket messages in bytes",
			Buckets: prometheus.ExponentialBuckets(64, 2, 10),
		},
		[]string{"direction"},
	)
)

func (t *WsTransfer) recordMetrics(messageType int, data []byte, direction string) {
	wsMessages.WithLabelValues(direction, getMessageTypeName(messageType)).Inc()
	wsMessageSize.WithLabelValues(direction).Observe(float64(len(data)))
}

func getMessageTypeName(messageType int) string {
	switch messageType {
	case websocket.TextMessage:
		return "text"
	case websocket.BinaryMessage:
		return "binary"
	case websocket.CloseMessage:
		return "close"
	case websocket.PingMessage:
		return "ping"
	case websocket.PongMessage:
		return "pong"
	default:
		return "unknown"
	}
}
```

### 7.2 健康检查

```go
func (eval *EvalService) HealthCheck() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		status := map[string]interface{}{
			"status":      "healthy",
			"timestamp":   time.Now().Unix(),
			"connections": eval.getConnectionCount(),
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(status)
	}
}

func (eval *EvalService) getConnectionCount() int {
	// 返回当前活跃连接数
	return len(eval.connections)
}
```

## 8. 安全考虑

### 8.1 认证和授权

```go
func (eval *EvalService) authenticateWebSocket(r *http.Request) error {
	token := r.Header.Get("Authorization")
	if token == "" {
		token = r.URL.Query().Get("token")
	}
	
	if token == "" {
		return errors.New("missing authentication token")
	}
	
	// 验证 token
	if !eval.validateToken(token) {
		return errors.New("invalid authentication token")
	}
	
	return nil
}

func (eval *EvalService) WsHandlerWithAuth(w http.ResponseWriter, r *http.Request) {
	// 认证检查
	if err := eval.authenticateWebSocket(r); err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}
	
	// 继续 WebSocket 处理
	eval.WsHandler(w, r)
}
```

### 8.2 速率限制

```go
type RateLimiter struct {
	connections map[string]*rate.Limiter
	mu          sync.RWMutex
}

func (rl *RateLimiter) Allow(clientID string) bool {
	rl.mu.RLock()
	limiter, exists := rl.connections[clientID]
	rl.mu.RUnlock()
	
	if !exists {
		rl.mu.Lock()
		limiter = rate.NewLimiter(rate.Limit(10), 100) // 每秒10个请求，突发100个
		rl.connections[clientID] = limiter
		rl.mu.Unlock()
	}
	
	return limiter.Allow()
}

func (t *WsTransfer) readLoopWithRateLimit() {
	rateLimiter := NewRateLimiter()
	
	for {
		messageType, data, err := t.conn.ReadMessage()
		if err != nil {
			return
		}
		
		// 速率限制检查
		if !rateLimiter.Allow(t.deviceId) {
			log.Warn("rate limit exceeded for device:", t.deviceId)
			continue
		}
		
		// 处理消息
		t.handleMessage(messageType, data)
	}
}
```

## 9. 总结

WebSocket 在 hw-paas-service 项目中提供了实时通信能力：

1. **实时性**: 支持双向实时通信，延迟低
2. **可靠性**: 包含心跳检测、错误处理、重连机制
3. **可扩展性**: 支持连接池管理、消息批处理
4. **监控性**: 集成指标监控和健康检查
5. **安全性**: 支持认证授权和速率限制

通过合理的架构设计和优化策略，WebSocket 可以为应用提供高性能、高可靠的实时通信服务，满足各种实时交互场景的需求。