# Azure Blob Storage 详解

## 1. Azure Blob Storage 简介

Azure Blob Storage 是微软 Azure 云平台提供的对象存储服务，专为存储大量非结构化数据而设计。它可以存储文本或二进制数据，如文档、媒体文件或应用程序安装程序。

### 1.1 核心特性

- **可扩展性**: 支持 PB 级数据存储
- **高可用性**: 99.9% 的可用性 SLA
- **多种访问层**: 热存储、冷存储、归档存储
- **全球分布**: 支持全球多个区域
- **安全性**: 支持加密、访问控制和审计
- **成本效益**: 按使用量付费

### 1.2 存储类型

- **Block Blob**: 适合存储文本和二进制文件
- **Append Blob**: 适合日志文件等追加操作
- **Page Blob**: 适合虚拟机磁盘等随机读写

### 1.3 访问层级

- **Hot**: 频繁访问的数据
- **Cool**: 不常访问但需要快速检索的数据
- **Archive**: 很少访问的数据，检索时间较长

## 2. 项目中的 Azure Blob Storage 实现

### 2.1 Azure Blob 客户端实现

**文件位置**: `internal/data/services/oss/azure_blob.go`

```go
package oss

import (
	"context"
	"fmt"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-module/carbon"
)

const serviceUrl = "***************************************************?sp=racwli&st=2025-07-02T10:56:38Z&se=2025-12-31T16:00:00Z&spr=https&sv=2024-11-04&sr=c&sig=HujV8Tsz67a0uSWKCt7Q1%2FFtd4uJ0qOhblLaDunLkGQ%3D"

type AzureBlob struct {
	log    *log.Helper
	client *azblob.Client
}

func NewAzureBlob(logger log.Logger) *AzureBlob {
	client, err := azblob.NewClientWithNoCredential(serviceUrl, &azblob.ClientOptions{})
	if err != nil {
		panic(err) // Handle error appropriately in production code
	}
	return &AzureBlob{
		client: client,
		log:    log.NewHelper(logger),
	}
}
```

#### 客户端初始化分析：

1. **服务 URL 配置**:
   ```go
   const serviceUrl = "***************************************************?sp=racwli&st=2025-07-02T10:56:38Z&se=2025-12-31T16:00:00Z&spr=https&sv=2024-11-04&sr=c&sig=HujV8Tsz67a0uSWKCt7Q1%2FFtd4uJ0qOhblLaDunLkGQ%3D"
   ```
   - 包含存储账户名: `taloversea`
   - 容器名: `edu-com101`
   - SAS Token 参数:
     - `sp=racwli`: 权限（读、添加、创建、写、列表、不可变）
     - `st/se`: 开始和结束时间
     - `spr=https`: 仅允许 HTTPS
     - `sv`: API 版本
     - `sr=c`: 资源类型（容器）
     - `sig`: 签名

2. **客户端创建**:
   ```go
   client, err := azblob.NewClientWithNoCredential(serviceUrl, &azblob.ClientOptions{})
   ```
   - 使用 SAS Token 认证，无需额外凭据
   - 配置客户端选项

3. **错误处理**:
   ```go
   if err != nil {
       panic(err) // Handle error appropriately in production code
   }
   ```
   - 初始化失败时直接 panic
   - 生产环境应该有更优雅的错误处理

### 2.2 文件上传实现

```go
// https://yach-doc-shimo.zhiyinlou.com/docs/dPkpKZKxJDhNnrqO/ 《Azure blob桌面客户端-使用手册》
func (s *AzureBlob) UploadFile(ctx context.Context, fileName string, fileBytes []byte) (string, error) {
	containerName := fmt.Sprintf("%s/%s", "pdf", carbon.Now().Layout(carbon.ShortDateLayout))
	_, err := s.client.UploadBuffer(ctx, containerName, fileName, fileBytes, &azblob.UploadBufferOptions{})

	url := fmt.Sprintf("%s/%s/%s", "https://static.thinkbuddycdn.com/edu-com101", containerName, fileName)
	return url, err
}
```

#### 上传实现分析：

1. **路径组织**:
   ```go
   containerName := fmt.Sprintf("%s/%s", "pdf", carbon.Now().Layout(carbon.ShortDateLayout))
   ```
   - 使用 `pdf` 作为基础路径
   - 按日期组织文件：`pdf/2024-01-15`
   - 使用 carbon 库格式化日期

2. **文件上传**:
   ```go
   _, err := s.client.UploadBuffer(ctx, containerName, fileName, fileBytes, &azblob.UploadBufferOptions{})
   ```
   - `UploadBuffer`: 从内存缓冲区上传
   - 支持上下文取消
   - 可配置上传选项

3. **URL 构建**:
   ```go
   url := fmt.Sprintf("%s/%s/%s", "https://static.thinkbuddycdn.com/edu-com101", containerName, fileName)
   ```
   - 使用 CDN 域名而非直接的 Blob 存储域名
   - 提高访问速度和用户体验

## 3. Azure Blob Storage 配置

### 3.1 SAS Token 详解

SAS (Shared Access Signature) Token 提供了对 Azure 存储资源的安全访问：

```
sp=racwli&st=2025-07-02T10:56:38Z&se=2025-12-31T16:00:00Z&spr=https&sv=2024-11-04&sr=c&sig=HujV8Tsz67a0uSWKCt7Q1%2FFtd4uJ0qOhblLaDunLkGQ%3D
```

#### 参数说明：

- **sp (Permissions)**: `racwli`
  - `r`: Read（读取）
  - `a`: Add（添加）
  - `c`: Create（创建）
  - `w`: Write（写入）
  - `l`: List（列表）
  - `i`: Immutable（不可变）

- **st (Start Time)**: `2025-07-02T10:56:38Z`
  - Token 生效时间

- **se (Expiry Time)**: `2025-12-31T16:00:00Z`
  - Token 过期时间

- **spr (Protocol)**: `https`
  - 仅允许 HTTPS 协议

- **sv (Service Version)**: `2024-11-04`
  - Azure Storage API 版本

- **sr (Resource)**: `c`
  - 资源类型：容器级别

- **sig (Signature)**: 签名字符串
  - 用于验证请求的合法性

### 3.2 存储账户配置

```go
// 完整的存储账户配置示例
type AzureBlobConfig struct {
    AccountName   string `yaml:"account_name"`
    AccountKey    string `yaml:"account_key"`
    ContainerName string `yaml:"container_name"`
    Endpoint      string `yaml:"endpoint"`
    CDNEndpoint   string `yaml:"cdn_endpoint"`
}

// 使用账户密钥的客户端
func NewAzureBlobWithKey(config *AzureBlobConfig) (*AzureBlob, error) {
    credential, err := azblob.NewSharedKeyCredential(config.AccountName, config.AccountKey)
    if err != nil {
        return nil, err
    }
    
    serviceURL := fmt.Sprintf("https://%s.blob.core.windows.net/", config.AccountName)
    client, err := azblob.NewClientWithSharedKeyCredential(serviceURL, credential, nil)
    if err != nil {
        return nil, err
    }
    
    return &AzureBlob{
        client: client,
        config: config,
    }, nil
}
```

## 4. 高级功能实现

### 4.1 批量上传

```go
// 批量上传文件
func (s *AzureBlob) UploadFiles(ctx context.Context, files map[string][]byte) (map[string]string, error) {
    results := make(map[string]string)
    errors := make([]error, 0)
    
    // 使用 goroutine 并发上传
    var wg sync.WaitGroup
    var mu sync.Mutex
    
    semaphore := make(chan struct{}, 10) // 限制并发数
    
    for fileName, fileBytes := range files {
        wg.Add(1)
        go func(name string, data []byte) {
            defer wg.Done()
            
            semaphore <- struct{}{} // 获取信号量
            defer func() { <-semaphore }() // 释放信号量
            
            url, err := s.UploadFile(ctx, name, data)
            
            mu.Lock()
            if err != nil {
                errors = append(errors, fmt.Errorf("upload %s failed: %v", name, err))
            } else {
                results[name] = url
            }
            mu.Unlock()
        }(fileName, fileBytes)
    }
    
    wg.Wait()
    
    if len(errors) > 0 {
        return results, fmt.Errorf("some uploads failed: %v", errors)
    }
    
    return results, nil
}
```

### 4.2 大文件分块上传

```go
// 大文件分块上传
func (s *AzureBlob) UploadLargeFile(ctx context.Context, fileName string, reader io.Reader) (string, error) {
    containerName := fmt.Sprintf("%s/%s", "pdf", carbon.Now().Layout(carbon.ShortDateLayout))
    
    // 使用分块上传
    _, err := s.client.UploadStream(ctx, containerName, fileName, reader, &azblob.UploadStreamOptions{
        BlockSize:   4 * 1024 * 1024, // 4MB 块大小
        Concurrency: 3,               // 并发数
        Progress: func(bytesTransferred int64) {
            s.log.Infof("Uploaded %d bytes", bytesTransferred)
        },
    })
    
    if err != nil {
        return "", err
    }
    
    url := fmt.Sprintf("%s/%s/%s", "https://static.thinkbuddycdn.com/edu-com101", containerName, fileName)
    return url, nil
}
```

### 4.3 文件下载

```go
// 下载文件
func (s *AzureBlob) DownloadFile(ctx context.Context, blobPath string) ([]byte, error) {
    // 解析路径
    parts := strings.SplitN(blobPath, "/", 2)
    if len(parts) != 2 {
        return nil, fmt.Errorf("invalid blob path: %s", blobPath)
    }
    
    containerName := parts[0]
    blobName := parts[1]
    
    // 下载文件
    downloadResponse, err := s.client.DownloadStream(ctx, containerName, blobName, nil)
    if err != nil {
        return nil, err
    }
    defer downloadResponse.Body.Close()
    
    // 读取数据
    data, err := io.ReadAll(downloadResponse.Body)
    if err != nil {
        return nil, err
    }
    
    return data, nil
}
```

### 4.4 文件元数据管理

```go
// 设置文件元数据
func (s *AzureBlob) SetBlobMetadata(ctx context.Context, containerName, blobName string, metadata map[string]*string) error {
    _, err := s.client.SetBlobMetadata(ctx, containerName, blobName, metadata, nil)
    return err
}

// 获取文件属性
func (s *AzureBlob) GetBlobProperties(ctx context.Context, containerName, blobName string) (*azblob.GetBlobPropertiesResponse, error) {
    return s.client.GetBlobProperties(ctx, containerName, blobName, nil)
}

// 设置文件访问层级
func (s *AzureBlob) SetBlobTier(ctx context.Context, containerName, blobName string, tier azblob.AccessTier) error {
    _, err := s.client.SetBlobTier(ctx, containerName, blobName, tier, nil)
    return err
}
```

## 5. 安全和权限管理

### 5.1 访问控制

```go
// 生成临时访问 URL
func (s *AzureBlob) GeneratePresignedURL(containerName, blobName string, expiry time.Duration) (string, error) {
    // 创建 SAS Token
    sasURL, err := s.client.GetSASURL(
        azblob.BlobSASPermissions{Read: true},
        time.Now().Add(expiry),
        &azblob.GetSASURLOptions{},
    )
    
    if err != nil {
        return "", err
    }
    
    return fmt.Sprintf("%s/%s/%s?%s", sasURL, containerName, blobName, "sas_token"), nil
}
```

### 5.2 数据加密

```go
// 客户端加密上传
func (s *AzureBlob) UploadEncryptedFile(ctx context.Context, fileName string, fileBytes []byte, encryptionKey []byte) (string, error) {
    // 使用 AES 加密数据
    block, err := aes.NewCipher(encryptionKey)
    if err != nil {
        return "", err
    }
    
    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }
    
    nonce := make([]byte, gcm.NonceSize())
    if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }
    
    encryptedData := gcm.Seal(nonce, nonce, fileBytes, nil)
    
    // 上传加密后的数据
    return s.UploadFile(ctx, fileName, encryptedData)
}
```

## 6. 监控和日志

### 6.1 操作日志

```go
// 带日志的上传方法
func (s *AzureBlob) UploadFileWithLogging(ctx context.Context, fileName string, fileBytes []byte) (string, error) {
    start := time.Now()
    
    s.log.WithContext(ctx).Infof("Starting upload: %s, size: %d bytes", fileName, len(fileBytes))
    
    url, err := s.UploadFile(ctx, fileName, fileBytes)
    
    duration := time.Since(start)
    
    if err != nil {
        s.log.WithContext(ctx).Errorf("Upload failed: %s, error: %v, duration: %v", fileName, err, duration)
        return "", err
    }
    
    s.log.WithContext(ctx).Infof("Upload successful: %s, url: %s, duration: %v", fileName, url, duration)
    return url, nil
}
```

### 6.2 性能监控

```go
// 性能指标收集
type BlobMetrics struct {
    UploadCount    prometheus.Counter
    UploadDuration prometheus.Histogram
    UploadSize     prometheus.Histogram
    ErrorCount     prometheus.Counter
}

func NewBlobMetrics() *BlobMetrics {
    return &BlobMetrics{
        UploadCount: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "azure_blob_uploads_total",
            Help: "Total number of blob uploads",
        }),
        UploadDuration: prometheus.NewHistogram(prometheus.HistogramOpts{
            Name:    "azure_blob_upload_duration_seconds",
            Help:    "Duration of blob uploads",
            Buckets: prometheus.DefBuckets,
        }),
        UploadSize: prometheus.NewHistogram(prometheus.HistogramOpts{
            Name:    "azure_blob_upload_size_bytes",
            Help:    "Size of uploaded blobs",
            Buckets: prometheus.ExponentialBuckets(1024, 2, 20),
        }),
        ErrorCount: prometheus.NewCounter(prometheus.CounterOpts{
            Name: "azure_blob_errors_total",
            Help: "Total number of blob operation errors",
        }),
    }
}

func (s *AzureBlob) UploadFileWithMetrics(ctx context.Context, fileName string, fileBytes []byte) (string, error) {
    start := time.Now()
    
    s.metrics.UploadCount.Inc()
    s.metrics.UploadSize.Observe(float64(len(fileBytes)))
    
    url, err := s.UploadFile(ctx, fileName, fileBytes)
    
    s.metrics.UploadDuration.Observe(time.Since(start).Seconds())
    
    if err != nil {
        s.metrics.ErrorCount.Inc()
    }
    
    return url, err
}
```

## 7. 错误处理和重试

### 7.1 重试机制

```go
// 带重试的上传
func (s *AzureBlob) UploadFileWithRetry(ctx context.Context, fileName string, fileBytes []byte, maxRetries int) (string, error) {
    var lastErr error
    
    for i := 0; i <= maxRetries; i++ {
        if i > 0 {
            // 指数退避
            backoff := time.Duration(math.Pow(2, float64(i-1))) * time.Second
            s.log.Infof("Retrying upload after %v (attempt %d/%d)", backoff, i+1, maxRetries+1)
            
            select {
            case <-time.After(backoff):
            case <-ctx.Done():
                return "", ctx.Err()
            }
        }
        
        url, err := s.UploadFile(ctx, fileName, fileBytes)
        if err == nil {
            if i > 0 {
                s.log.Infof("Upload succeeded on retry %d", i)
            }
            return url, nil
        }
        
        lastErr = err
        
        // 检查是否为可重试错误
        if !isRetryableError(err) {
            break
        }
    }
    
    return "", fmt.Errorf("upload failed after %d retries: %v", maxRetries+1, lastErr)
}

func isRetryableError(err error) bool {
    // 检查错误类型，判断是否可重试
    var respErr *azblob.ResponseError
    if errors.As(err, &respErr) {
        switch respErr.StatusCode {
        case 408, 429, 500, 502, 503, 504:
            return true
        }
    }
    
    // 网络错误通常可重试
    if errors.Is(err, context.DeadlineExceeded) || 
       errors.Is(err, context.Canceled) {
        return false
    }
    
    return true
}
```

### 7.2 错误分类处理

```go
// 错误分类处理
func (s *AzureBlob) handleBlobError(err error, operation string) error {
    if err == nil {
        return nil
    }
    
    var respErr *azblob.ResponseError
    if errors.As(err, &respErr) {
        switch respErr.StatusCode {
        case 404:
            return fmt.Errorf("blob not found during %s", operation)
        case 409:
            return fmt.Errorf("blob already exists during %s", operation)
        case 403:
            return fmt.Errorf("access denied during %s: check permissions", operation)
        case 413:
            return fmt.Errorf("blob too large during %s", operation)
        case 429:
            return fmt.Errorf("rate limited during %s: too many requests", operation)
        default:
            return fmt.Errorf("azure blob error during %s: %v", operation, err)
        }
    }
    
    return fmt.Errorf("unknown error during %s: %v", operation, err)
}
```

## 8. 最佳实践

### 8.1 文件命名规范

```go
// 文件命名最佳实践
func generateBlobName(userID, fileType, originalName string) string {
    // 使用 UUID 避免冲突
    uuid := uuid.New().String()
    
    // 提取文件扩展名
    ext := filepath.Ext(originalName)
    
    // 构建规范的文件名
    timestamp := time.Now().Format("20060102150405")
    
    return fmt.Sprintf("%s/%s/%s_%s%s", userID, fileType, timestamp, uuid, ext)
}
```

### 8.2 成本优化

```go
// 自动设置访问层级
func (s *AzureBlob) UploadWithOptimalTier(ctx context.Context, fileName string, fileBytes []byte, accessPattern string) (string, error) {
    // 先上传到热存储
    url, err := s.UploadFile(ctx, fileName, fileBytes)
    if err != nil {
        return "", err
    }
    
    // 根据访问模式设置合适的存储层级
    var tier azblob.AccessTier
    switch accessPattern {
    case "frequent":
        tier = azblob.AccessTierHot
    case "infrequent":
        tier = azblob.AccessTierCool
    case "archive":
        tier = azblob.AccessTierArchive
    default:
        tier = azblob.AccessTierHot
    }
    
    // 设置存储层级
    containerName := fmt.Sprintf("%s/%s", "pdf", carbon.Now().Layout(carbon.ShortDateLayout))
    err = s.SetBlobTier(ctx, containerName, fileName, tier)
    if err != nil {
        s.log.Warnf("Failed to set blob tier: %v", err)
    }
    
    return url, nil
}
```

### 8.3 CDN 集成

```go
// CDN URL 生成
func (s *AzureBlob) GetCDNUrl(blobUrl string) string {
    // 替换存储域名为 CDN 域名
    cdnDomain := "https://static.thinkbuddycdn.com"
    storageDomain := "https://taloversea.blob.core.windows.net"
    
    return strings.Replace(blobUrl, storageDomain, cdnDomain, 1)
}
```

## 9. 总结

Azure Blob Storage 在 hw-paas-service 项目中提供了可靠的文件存储服务：

1. **高可用性**: 99.9% 的可用性保证
2. **可扩展性**: 支持 PB 级数据存储
3. **成本效益**: 多种存储层级，按需付费
4. **全球分布**: CDN 加速，提升访问速度
5. **安全性**: 支持加密和访问控制

通过合理的配置和使用策略，Azure Blob Storage 可以为应用提供高性能、低成本的文件存储解决方案。