# Elasticsearch 搜索引擎详解

## 1. Elasticsearch 简介

Elasticsearch 是一个基于 Lucene 的分布式搜索和分析引擎，能够实现近实时搜索。它提供了 RESTful API，支持多种数据类型，广泛应用于全文搜索、日志分析、实时数据分析等场景。

### 1.1 核心特性

- **分布式架构**: 支持水平扩展，自动分片和副本
- **近实时搜索**: 文档索引后几乎立即可搜索
- **RESTful API**: 通过 HTTP 进行所有操作
- **多租户**: 支持多个索引和类型
- **聚合分析**: 强大的数据聚合和分析能力
- **高可用性**: 自动故障转移和数据恢复

### 1.2 基本概念

- **Index（索引）**: 类似数据库，存储相关文档的集合
- **Document（文档）**: 基本信息单元，JSON 格式
- **Field（字段）**: 文档中的键值对
- **Mapping（映射）**: 定义文档字段类型和索引方式
- **Shard（分片）**: 索引的水平分割单元
- **Replica（副本）**: 分片的备份

## 2. 项目中的 Elasticsearch 配置

### 2.1 ES 客户端初始化

**文件位置**: `internal/data/dao/es.go`

```go
package dao

import (
	"context"
	"errors"
	"git.100tal.com/znxx_xpp/go-libs/traces/xelastic"
	jsoniter "github.com/json-iterator/go"
	"hw-paas-service/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/olivere/elastic/v7"
)

type ESHits struct {
	Score  float64
	Source []byte
}

type ES interface {
	Search(ctx context.Context, index string, query *elastic.BoolQuery, page, pageSize int) (int64, []*ESHits, error)
	Get(ctx context.Context, index, id string) (*structpb.Struct, error)
	Save(ctx context.Context, index, id string, info interface{}) error
	Delete(ctx context.Context, index, id string) error
	CreateIndex(ctx context.Context, index, mappings string) (bool, error)
	DelIndex(ctx context.Context, index string) (bool, error)
}

type es struct {
	conf  *conf.Data
	log   *log.Helper
	esClt *elastic.Client
}
```

#### 接口设计分析：

1. **ES 接口定义**:
   - `Search`: 搜索功能，支持分页
   - `Get`: 根据ID获取单个文档
   - `Save`: 保存或更新文档
   - `Delete`: 删除文档
   - `CreateIndex`: 创建索引
   - `DelIndex`: 删除索引

2. **ESHits 结构**:
   ```go
   type ESHits struct {
       Score  float64  // 搜索相关性分数
       Source []byte   // 文档原始数据
   }
   ```

### 2.2 ES 客户端创建

```go
func NewEsRepo(conf *conf.Data, logger log.Logger) ES {
	client, err := xelastic.MakeEsClient(&xelastic.ESConfig{
		Host:     conf.Es.GetHost(),
		UserName: conf.Es.GetUsername(),
		Password: conf.Es.GetPassword(),
	}, log.NewHelper(logger))
	if err != nil {
		log.NewHelper(logger).Errorf("getEsClient error, [err]:%+v", err)
		return nil
	}
	return &es{
		conf:  conf,
		log:   log.NewHelper(logger),
		esClt: client,
	}
}
```

#### 客户端初始化分析：

1. **配置获取**:
   - 从配置文件获取 ES 连接信息
   - 包括主机地址、用户名、密码

2. **客户端创建**:
   - 使用第三方库 `xelastic` 创建客户端
   - 集成了链路追踪功能

3. **错误处理**:
   - 创建失败时记录错误日志
   - 返回 nil 而非 panic

## 3. ES 配置文件

**文件位置**: `configs/config.yaml`

```yaml
data:
  es:
    host: "http://**********:9200"
    username: "superuser"
    password: "d737da0188348DD1d^hkda"
```

### 配置说明：
- `host`: ES 集群地址
- `username`: 认证用户名
- `password`: 认证密码

## 4. ES 核心操作实现

### 4.1 搜索功能

```go
func (e *es) Search(ctx context.Context, index string, query *elastic.BoolQuery, page, pageSize int) (int64, []*ESHits, error) {
	if e.esClt == nil {
		return 0, nil, errors.New("es client not found")
	}

	total, err := e.esClt.Count().Index(index).Query(query).Do(ctx)
	if total == 0 {
		return 0, nil, nil
	}

	srv := e.esClt.Search().Index(index).Query(query)
	if page > 0 && pageSize > 0 {
		srv = srv.From((page - 1) * pageSize).Size(pageSize)
	}
	resp, err := srv.Do(ctx)
	if err != nil {
		return 0, nil, err
	}

	if len(resp.Hits.Hits) == 0 {
		return 0, nil, nil
	}

	list := make([]*ESHits, 0)
	for k := range resp.Hits.Hits {
		item := ESHits{
			Score: *resp.Hits.Hits[k].Score,
		}
		source, _ := resp.Hits.Hits[k].Source.MarshalJSON()
		item.Source = source
		list = append(list, &item)
	}

	return total, list, nil
}
```

#### 搜索实现详解：

1. **客户端检查**:
   ```go
   if e.esClt == nil {
       return 0, nil, errors.New("es client not found")
   }
   ```

2. **总数统计**:
   ```go
   total, err := e.esClt.Count().Index(index).Query(query).Do(ctx)
   ```
   - 先获取匹配文档总数
   - 用于分页计算

3. **分页处理**:
   ```go
   if page > 0 && pageSize > 0 {
       srv = srv.From((page - 1) * pageSize).Size(pageSize)
   }
   ```
   - `From`: 设置起始位置
   - `Size`: 设置返回数量

4. **结果处理**:
   ```go
   for k := range resp.Hits.Hits {
       item := ESHits{
           Score: *resp.Hits.Hits[k].Score,
       }
       source, _ := resp.Hits.Hits[k].Source.MarshalJSON()
       item.Source = source
       list = append(list, &item)
   }
   ```
   - 提取相关性分数
   - 序列化文档源数据

### 4.2 文档获取

```go
func (e *es) Get(ctx context.Context, index, id string) (*structpb.Struct, error) {
	if id == "" {
		return nil, nil
	}
	resp, err := e.esClt.Get().Index(index).Id(id).Do(ctx)
	if err != nil {
		return nil, err
	}
	resJson, _ := resp.Source.MarshalJSON()
	st := &structpb.Struct{}
	err = jsoniter.Unmarshal(resJson, &st)
	if err != nil {
		return nil, err
	}
	return st, nil
}
```

#### 获取操作分析：

1. **参数验证**:
   ```go
   if id == "" {
       return nil, nil
   }
   ```

2. **文档获取**:
   ```go
   resp, err := e.esClt.Get().Index(index).Id(id).Do(ctx)
   ```

3. **数据转换**:
   ```go
   resJson, _ := resp.Source.MarshalJSON()
   st := &structpb.Struct{}
   err = jsoniter.Unmarshal(resJson, &st)
   ```
   - 转换为 protobuf Struct 格式
   - 便于 gRPC 传输

### 4.3 文档保存

```go
func (e *es) Save(ctx context.Context, index, id string, info interface{}) error {
	if id == "" || info == nil {
		return nil
	}
	_, err := e.esClt.Index().Index(index).Id(id).BodyJson(info).Do(ctx)
	if err != nil {
		return err
	}

	return nil
}
```

#### 保存操作特点：

1. **参数校验**: 检查ID和数据是否为空
2. **索引操作**: 使用 `Index()` 方法保存文档
3. **JSON序列化**: `BodyJson()` 自动序列化对象

### 4.4 文档删除

```go
func (e *es) Delete(ctx context.Context, index, id string) error {
	if id == "" {
		return nil
	}
	_, err := e.esClt.Delete().Index(index).Id(id).Do(ctx)
	if err != nil {
		return err
	}

	return nil
}
```

## 5. 索引管理

### 5.1 创建索引

```go
func (e *es) CreateIndex(ctx context.Context, index, mappings string) (bool, error) {
	exists, err := e.esClt.IndexExists(index).Do(ctx)
	if err != nil {
		return false, err
	}

	if exists {
		_, err = e.esClt.IndexGetSettings(index).Do(ctx)
		return true, nil
	}

	resCreate, err := e.esClt.CreateIndex(index).BodyJson(mappings).Do(ctx)
	if err != nil || !resCreate.Acknowledged {
		return false, err
	}

	resAlias, err := e.esClt.Alias().Add(index, index+"_alias").Do(ctx)
	if err != nil || !resAlias.Acknowledged {
		return false, err
	}

	return resCreate.Acknowledged && resAlias.Acknowledged, nil
}
```

#### 索引创建流程：

1. **存在性检查**:
   ```go
   exists, err := e.esClt.IndexExists(index).Do(ctx)
   ```

2. **索引创建**:
   ```go
   resCreate, err := e.esClt.CreateIndex(index).BodyJson(mappings).Do(ctx)
   ```

3. **别名创建**:
   ```go
   resAlias, err := e.esClt.Alias().Add(index, index+"_alias").Do(ctx)
   ```
   - 为索引创建别名
   - 便于索引切换和管理

### 5.2 删除索引

```go
func (e *es) DelIndex(ctx context.Context, index string) (bool, error) {
	resp, err := e.esClt.DeleteIndex(index).Do(ctx)
	if err != nil || !resp.Acknowledged {
		return false, err
	}

	return resp.Acknowledged, nil
}
```

## 6. ES 查询构建

### 6.1 Bool 查询示例

```go
// 构建复合查询
func buildComplexQuery(keyword string, filters map[string]interface{}) *elastic.BoolQuery {
	boolQuery := elastic.NewBoolQuery()
	
	// 全文搜索
	if keyword != "" {
		multiMatchQuery := elastic.NewMultiMatchQuery(keyword, "title", "content", "tags")
		boolQuery.Must(multiMatchQuery)
	}
	
	// 精确匹配过滤
	for field, value := range filters {
		termQuery := elastic.NewTermQuery(field, value)
		boolQuery.Filter(termQuery)
	}
	
	// 范围查询
	rangeQuery := elastic.NewRangeQuery("created_at").Gte("2023-01-01")
	boolQuery.Filter(rangeQuery)
	
	return boolQuery
}
```

### 6.2 聚合查询示例

```go
// 聚合统计
func performAggregation(ctx context.Context, client *elastic.Client, index string) error {
	agg := elastic.NewTermsAggregation().Field("category.keyword").Size(10)
	
	searchResult, err := client.Search().
		Index(index).
		Query(elastic.NewMatchAllQuery()).
		Aggregation("categories", agg).
		Size(0). // 不返回文档，只返回聚合结果
		Do(ctx)
	
	if err != nil {
		return err
	}
	
	// 处理聚合结果
	if agg, found := searchResult.Aggregations.Terms("categories"); found {
		for _, bucket := range agg.Buckets {
			fmt.Printf("Category: %s, Count: %d\n", bucket.Key, bucket.DocCount)
		}
	}
	
	return nil
}
```

## 7. 性能优化

### 7.1 批量操作

```go
// 批量索引文档
func bulkIndex(ctx context.Context, client *elastic.Client, index string, docs []interface{}) error {
	bulkRequest := client.Bulk()
	
	for i, doc := range docs {
		req := elastic.NewBulkIndexRequest().
			Index(index).
			Id(fmt.Sprintf("doc_%d", i)).
			Doc(doc)
		bulkRequest = bulkRequest.Add(req)
	}
	
	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		return err
	}
	
	// 检查失败的操作
	if bulkResponse.Errors {
		for _, item := range bulkResponse.Items {
			for operation, result := range item {
				if result.Error != nil {
					log.Printf("Bulk %s failed: %v", operation, result.Error)
				}
			}
		}
	}
	
	return nil
}
```

### 7.2 搜索优化

```go
// 优化搜索性能
func optimizedSearch(ctx context.Context, client *elastic.Client, index string, query elastic.Query) (*elastic.SearchResult, error) {
	return client.Search().
		Index(index).
		Query(query).
		Size(20).                    // 限制返回数量
		From(0).                     // 分页起始位置
		Sort("_score", false).       // 按相关性排序
		Source("title", "summary").  // 只返回需要的字段
		Timeout("5s").               // 设置超时时间
		Do(ctx)
}
```

## 8. 监控和调试

### 8.1 健康检查

```go
// ES 集群健康检查
func checkESHealth(ctx context.Context, client *elastic.Client) error {
	health, err := client.ClusterHealth().Do(ctx)
	if err != nil {
		return err
	}
	
	log.Printf("Cluster status: %s", health.Status)
	log.Printf("Number of nodes: %d", health.NumberOfNodes)
	log.Printf("Active shards: %d", health.ActiveShards)
	
	if health.Status == "red" {
		return errors.New("cluster status is red")
	}
	
	return nil
}
```

### 8.2 性能指标

```go
// 获取索引统计信息
func getIndexStats(ctx context.Context, client *elastic.Client, index string) error {
	stats, err := client.IndexStats(index).Do(ctx)
	if err != nil {
		return err
	}
	
	for indexName, indexStats := range stats.Indices {
		log.Printf("Index: %s", indexName)
		log.Printf("Documents: %d", indexStats.Total.Docs.Count)
		log.Printf("Store size: %s", indexStats.Total.Store.Size)
		log.Printf("Search queries: %d", indexStats.Total.Search.QueryTotal)
	}
	
	return nil
}
```

## 9. 最佳实践

### 9.1 映射设计

```json
{
  "mappings": {
    "properties": {
      "title": {
        "type": "text",
        "analyzer": "standard",
        "fields": {
          "keyword": {
            "type": "keyword"
          }
        }
      },
      "content": {
        "type": "text",
        "analyzer": "standard"
      },
      "tags": {
        "type": "keyword"
      },
      "created_at": {
        "type": "date",
        "format": "yyyy-MM-dd HH:mm:ss"
      },
      "status": {
        "type": "integer"
      }
    }
  }
}
```

### 9.2 查询优化

```go
// 使用过滤器而非查询
func useFilterInsteadOfQuery() *elastic.BoolQuery {
	return elastic.NewBoolQuery().
		Must(elastic.NewMatchQuery("content", "search term")).  // 影响相关性分数
		Filter(elastic.NewTermQuery("status", 1)).              // 不影响分数，可缓存
		Filter(elastic.NewRangeQuery("created_at").Gte("2023-01-01"))
}
```

### 9.3 错误处理

```go
// 统一错误处理
func handleESError(err error) error {
	if elastic.IsNotFound(err) {
		return errors.New("document not found")
	}
	if elastic.IsTimeout(err) {
		return errors.New("elasticsearch timeout")
	}
	if elastic.IsConnErr(err) {
		return errors.New("elasticsearch connection error")
	}
	return err
}
```

## 10. 总结

Elasticsearch 在 hw-paas-service 项目中提供了强大的搜索能力：

1. **全文搜索**: 支持复杂的文本搜索和相关性排序
2. **实时性**: 近实时的索引和搜索能力
3. **可扩展性**: 分布式架构，支持水平扩展
4. **灵活性**: 丰富的查询DSL和聚合功能
5. **高可用**: 自动分片和副本机制

通过合理的索引设计、查询优化和监控机制，ES 可以为应用提供高性能、高可用的搜索服务。