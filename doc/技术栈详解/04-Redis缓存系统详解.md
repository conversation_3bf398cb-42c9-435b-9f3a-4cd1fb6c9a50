# Redis 缓存系统详解

## 1. Redis 简介

Redis（Remote Dictionary Server）是一个开源的内存数据结构存储系统，可以用作数据库、缓存和消息代理。它支持多种数据结构，如字符串、哈希、列表、集合、有序集合等。

### 1.1 核心特性

- **内存存储**: 所有数据存储在内存中，读写速度极快
- **持久化**: 支持 RDB 和 AOF 两种持久化方式
- **数据结构丰富**: 支持字符串、哈希、列表、集合等多种数据类型
- **原子操作**: 所有操作都是原子性的
- **发布订阅**: 支持消息发布订阅模式
- **事务支持**: 支持事务操作
- **集群支持**: 支持主从复制和集群模式

## 2. 项目中的 Redis 配置

### 2.1 Redis 连接配置

**文件位置**: `internal/data/data.go`

```go
package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/uptrace/opentelemetry-go-extra/otelgorm"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"hw-paas-service/internal/biz/biz_metrics"
	"hw-paas-service/internal/conf"
	"time"

	"github.com/redis/go-redis/extra/redisotel/v9"
	"github.com/redis/go-redis/v9"
)

// Data .
type Data struct {
	DB  *gorm.DB
	Rdb *redis.Client
}

// NewData .
func NewData(conf *conf.Data, logger log.Logger) (*Data, func(), error) {
	// ... MySQL 配置代码 ...

	rdb := redis.NewClient(&redis.Options{
		Addr:         conf.Redis.Addr,
		Password:     conf.Redis.Password,
		DB:           int(conf.Redis.Db),
		DialTimeout:  conf.Redis.DialTimeout.AsDuration(),
		WriteTimeout: conf.Redis.WriteTimeout.AsDuration(),
		ReadTimeout:  conf.Redis.ReadTimeout.AsDuration(),
		PoolSize:     int(conf.Redis.PoolSize),
	})

	_, err = rdb.Ping(context.Background()).Result()
	if err != nil {
		return nil, nil, err
	}

	if err := redisotel.InstrumentTracing(rdb); err != nil {
		return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentTracing error")
	}
	if err := redisotel.InstrumentMetrics(rdb); err != nil {
		return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentMetrics error")
	}
	metricRedisPoolStats(rdb)

	d := &Data{
		DB:  db,
		Rdb: rdb,
	}

	return d, func() {
		// ... MySQL 清理代码 ...

		d.Rdb.Close()
		log.NewHelper(logger).Info("closing the redis")
	}, nil
}

// metricRedisPoolStats 监控redis连接池状态
func metricRedisPoolStats(rdb *redis.Client) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("metricRedisPoolStats panic: %v", r)
			}
		}()
		for range time.Tick(time.Second * 5) {
			stats := rdb.PoolStats()
			biz_metrics.RedisPoolStats(stats.Hits, stats.Misses, stats.Timeouts, stats.TotalConns, stats.IdleConns, stats.StaleConns)
		}
	}()
}
```

#### 逐行分析：

1. **Redis 客户端初始化**:
   ```go
   rdb := redis.NewClient(&redis.Options{
       Addr:         conf.Redis.Addr,
       Password:     conf.Redis.Password,
       DB:           int(conf.Redis.Db),
       DialTimeout:  conf.Redis.DialTimeout.AsDuration(),
       WriteTimeout: conf.Redis.WriteTimeout.AsDuration(),
       ReadTimeout:  conf.Redis.ReadTimeout.AsDuration(),
       PoolSize:     int(conf.Redis.PoolSize),
   })
   ```
   - `Addr`: Redis 服务器地址和端口
   - `Password`: Redis 密码（如果设置了）
   - `DB`: 数据库编号（0-15）
   - `DialTimeout`: 连接超时时间
   - `WriteTimeout`: 写入超时时间
   - `ReadTimeout`: 读取超时时间
   - `PoolSize`: 连接池大小

2. **连接测试**:
   ```go
   _, err = rdb.Ping(context.Background()).Result()
   if err != nil {
       return nil, nil, err
   }
   ```
   - 使用 `Ping()` 命令测试 Redis 连接是否正常
   - 如果连接失败，立即返回错误

3. **OpenTelemetry 集成**:
   ```go
   if err := redisotel.InstrumentTracing(rdb); err != nil {
       return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentTracing error")
   }
   if err := redisotel.InstrumentMetrics(rdb); err != nil {
       return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentMetrics error")
   }
   ```
   - `InstrumentTracing()`: 为 Redis 操作添加链路追踪
   - `InstrumentMetrics()`: 为 Redis 操作添加指标监控

4. **连接池监控**:
   ```go
   func metricRedisPoolStats(rdb *redis.Client) {
       go func() {
           defer func() {
               if r := recover(); r != nil {
                   log.Errorf("metricRedisPoolStats panic: %v", r)
               }
           }()
           for range time.Tick(time.Second * 5) {
               stats := rdb.PoolStats()
               biz_metrics.RedisPoolStats(stats.Hits, stats.Misses, stats.Timeouts, stats.TotalConns, stats.IdleConns, stats.StaleConns)
           }
       }()
   }
   ```
   - 每 5 秒收集一次连接池统计信息
   - 监控命中率、超时次数、连接数等指标
   - 使用 goroutine 异步执行，避免阻塞主流程

### 2.2 Redis 配置文件

**文件位置**: `configs/config.yaml`

```yaml
data:
  redis:
    network: tcp
    addr: 127.0.0.1:6379
    db: 0
    dial_timeout: 2s
    read_timeout: 2s
    write_timeout: 2s
    pool_size: 20
```

#### 配置参数说明：

- `network`: 网络类型，通常是 tcp
- `addr`: Redis 服务器地址
- `db`: 数据库编号
- `dial_timeout`: 连接超时时间
- `read_timeout`: 读取超时时间
- `write_timeout`: 写入超时时间
- `pool_size`: 连接池大小

## 3. Redis 监控指标

### 3.1 连接池监控实现

**文件位置**: `internal/biz/biz_metrics/metrics.go`

```go
package biz_metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/mem"
	"runtime"
	"time"
)

// MetricRedisPoolStats Redis连接池统计
var MetricRedisPoolStats = prometheus.NewGaugeVec(prometheus.GaugeOpts{
	Namespace: "redis",
	Subsystem: "pool",
	Name:      "stats",
	Help:      "Redis connection pool statistics.",
}, []string{"operation"})

func RedisPoolStats(hits, misses, timeouts, totalConns, idleConns, stalledConns uint32) {
	MetricRedisPoolStats.WithLabelValues("hits").Set(float64(hits))
	MetricRedisPoolStats.WithLabelValues("misses").Set(float64(misses))
	MetricRedisPoolStats.WithLabelValues("timeouts").Set(float64(timeouts))
	MetricRedisPoolStats.WithLabelValues("totalConns").Set(float64(totalConns))
	MetricRedisPoolStats.WithLabelValues("idleConns").Set(float64(idleConns))
	MetricRedisPoolStats.WithLabelValues("stalledConns").Set(float64(stalledConns))
}

func init() {
	prometheus.MustRegister(MetricSeconds, MetricRequests, MetricClientSeconds, MetricRedisPoolStats)
}
```

#### 监控指标详解：

1. **连接池统计指标**:
   ```go
   var MetricRedisPoolStats = prometheus.NewGaugeVec(prometheus.GaugeOpts{
       Namespace: "redis",
       Subsystem: "pool",
       Name:      "stats",
       Help:      "Redis connection pool statistics.",
   }, []string{"operation"})
   ```
   - 使用 Prometheus Gauge 类型记录连接池状态
   - 通过 `operation` 标签区分不同的统计项

2. **统计项说明**:
   - `hits`: 连接池命中次数（复用现有连接）
   - `misses`: 连接池未命中次数（需要创建新连接）
   - `timeouts`: 连接超时次数
   - `totalConns`: 总连接数
   - `idleConns`: 空闲连接数
   - `stalledConns`: 阻塞连接数

## 4. Redis 在项目中的应用场景

### 4.1 缓存使用示例

虽然项目代码中没有直接展示 Redis 的具体使用，但根据项目结构，Redis 主要用于以下场景：

#### 4.1.1 用户会话缓存

```go
// 示例：用户登录状态缓存
func (r *UserRepo) SetUserSession(ctx context.Context, userID string, sessionData string, expiration time.Duration) error {
	key := fmt.Sprintf("user:session:%s", userID)
	return r.data.Rdb.Set(ctx, key, sessionData, expiration).Err()
}

func (r *UserRepo) GetUserSession(ctx context.Context, userID string) (string, error) {
	key := fmt.Sprintf("user:session:%s", userID)
	return r.data.Rdb.Get(ctx, key).Result()
}

func (r *UserRepo) DeleteUserSession(ctx context.Context, userID string) error {
	key := fmt.Sprintf("user:session:%s", userID)
	return r.data.Rdb.Del(ctx, key).Err()
}
```

#### 4.1.2 API 限流

```go
// 示例：API 限流实现
func (r *RateLimitRepo) CheckRateLimit(ctx context.Context, key string, limit int, window time.Duration) (bool, error) {
	pipe := r.data.Rdb.Pipeline()
	
	// 使用滑动窗口算法
	now := time.Now().Unix()
	windowStart := now - int64(window.Seconds())
	
	// 删除窗口外的记录
	pipe.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", windowStart))
	
	// 添加当前请求
	pipe.ZAdd(ctx, key, redis.Z{Score: float64(now), Member: now})
	
	// 获取窗口内的请求数
	pipe.ZCard(ctx, key)
	
	// 设置过期时间
	pipe.Expire(ctx, key, window)
	
	results, err := pipe.Exec(ctx)
	if err != nil {
		return false, err
	}
	
	count := results[2].(*redis.IntCmd).Val()
	return count <= int64(limit), nil
}
```

#### 4.1.3 分布式锁

```go
// 示例：分布式锁实现
func (r *LockRepo) AcquireLock(ctx context.Context, key string, value string, expiration time.Duration) (bool, error) {
	result := r.data.Rdb.SetNX(ctx, key, value, expiration)
	return result.Val(), result.Err()
}

func (r *LockRepo) ReleaseLock(ctx context.Context, key string, value string) error {
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`
	return r.data.Rdb.Eval(ctx, script, []string{key}, value).Err()
}
```

#### 4.1.4 数据缓存

```go
// 示例：查询结果缓存
func (r *WordRepo) GetWordFromCache(ctx context.Context, word string) (*model.HwEnWord, error) {
	key := fmt.Sprintf("word:cache:%s", word)
	result, err := r.data.Rdb.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		return nil, err
	}
	
	var wordData model.HwEnWord
	err = json.Unmarshal([]byte(result), &wordData)
	if err != nil {
		return nil, err
	}
	
	return &wordData, nil
}

func (r *WordRepo) SetWordCache(ctx context.Context, word string, data *model.HwEnWord, expiration time.Duration) error {
	key := fmt.Sprintf("word:cache:%s", word)
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	
	return r.data.Rdb.Set(ctx, key, jsonData, expiration).Err()
}
```

## 5. Redis 数据类型和操作

### 5.1 字符串（String）

```go
// 设置值
err := rdb.Set(ctx, "key", "value", time.Hour).Err()

// 获取值
val, err := rdb.Get(ctx, "key").Result()

// 递增
newVal, err := rdb.Incr(ctx, "counter").Result()

// 设置过期时间
err := rdb.Expire(ctx, "key", time.Minute*10).Err()
```

### 5.2 哈希（Hash）

```go
// 设置哈希字段
err := rdb.HSet(ctx, "user:1", "name", "John", "age", 30).Err()

// 获取哈希字段
name, err := rdb.HGet(ctx, "user:1", "name").Result()

// 获取所有字段
fields, err := rdb.HGetAll(ctx, "user:1").Result()

// 删除哈希字段
err := rdb.HDel(ctx, "user:1", "age").Err()
```

### 5.3 列表（List）

```go
// 左侧推入
err := rdb.LPush(ctx, "queue", "item1", "item2").Err()

// 右侧弹出
item, err := rdb.RPop(ctx, "queue").Result()

// 获取列表长度
length, err := rdb.LLen(ctx, "queue").Result()

// 获取范围内的元素
items, err := rdb.LRange(ctx, "queue", 0, -1).Result()
```

### 5.4 集合（Set）

```go
// 添加成员
err := rdb.SAdd(ctx, "tags", "go", "redis", "cache").Err()

// 检查成员是否存在
exists, err := rdb.SIsMember(ctx, "tags", "go").Result()

// 获取所有成员
members, err := rdb.SMembers(ctx, "tags").Result()

// 集合运算
intersection, err := rdb.SInter(ctx, "set1", "set2").Result()
```

### 5.5 有序集合（Sorted Set）

```go
// 添加成员和分数
err := rdb.ZAdd(ctx, "leaderboard", redis.Z{Score: 100, Member: "player1"}).Err()

// 获取排名范围
players, err := rdb.ZRevRange(ctx, "leaderboard", 0, 9).Result()

// 获取分数范围
players, err := rdb.ZRangeByScore(ctx, "leaderboard", &redis.ZRangeBy{
	Min: "80",
	Max: "100",
}).Result()

// 获取成员排名
rank, err := rdb.ZRevRank(ctx, "leaderboard", "player1").Result()
```

## 6. Redis 事务和管道

### 6.1 事务（Transaction）

```go
// 使用事务
err := rdb.Watch(ctx, func(tx *redis.Tx) error {
	// 在事务中执行多个命令
	pipe := tx.TxPipeline()
	
	pipe.Set(ctx, "key1", "value1", 0)
	pipe.Set(ctx, "key2", "value2", 0)
	pipe.Incr(ctx, "counter")
	
	_, err := pipe.Exec(ctx)
	return err
}, "watched_key")
```

### 6.2 管道（Pipeline）

```go
// 使用管道批量执行命令
pipe := rdb.Pipeline()

pipe.Set(ctx, "key1", "value1", 0)
pipe.Set(ctx, "key2", "value2", 0)
pipe.Get(ctx, "key1")
pipe.Get(ctx, "key2")

// 执行所有命令
cmds, err := pipe.Exec(ctx)
if err != nil {
	return err
}

// 获取结果
for _, cmd := range cmds {
	fmt.Println(cmd.(*redis.StringCmd).Val())
}
```

## 7. Redis 性能优化

### 7.1 连接池优化

```go
rdb := redis.NewClient(&redis.Options{
	Addr:         "localhost:6379",
	PoolSize:     20,              // 连接池大小
	MinIdleConns: 5,               // 最小空闲连接数
	MaxConnAge:   time.Hour,       // 连接最大生存时间
	PoolTimeout:  time.Second * 4, // 获取连接超时时间
	IdleTimeout:  time.Minute * 5, // 空闲连接超时时间
})
```

### 7.2 批量操作

```go
// 批量设置
pipe := rdb.Pipeline()
for i := 0; i < 1000; i++ {
	pipe.Set(ctx, fmt.Sprintf("key:%d", i), fmt.Sprintf("value:%d", i), 0)
}
_, err := pipe.Exec(ctx)
```

### 7.3 合理设置过期时间

```go
// 避免同时过期，添加随机时间
baseExpiration := time.Hour
randomExpiration := time.Duration(rand.Intn(300)) * time.Second
finalExpiration := baseExpiration + randomExpiration

err := rdb.Set(ctx, key, value, finalExpiration).Err()
```

## 8. Redis 监控和调试

### 8.1 性能监控

```go
// 监控 Redis 操作延迟
func (r *Repository) monitorRedisLatency() {
	rdb.AddHook(&RedisHook{})
}

type RedisHook struct{}

func (h *RedisHook) BeforeProcess(ctx context.Context, cmd redis.Cmder) (context.Context, error) {
	ctx = context.WithValue(ctx, "start_time", time.Now())
	return ctx, nil
}

func (h *RedisHook) AfterProcess(ctx context.Context, cmd redis.Cmder) error {
	startTime := ctx.Value("start_time").(time.Time)
	duration := time.Since(startTime)
	
	// 记录延迟指标
	redisLatencyHistogram.WithLabelValues(cmd.Name()).Observe(duration.Seconds())
	
	return nil
}
```

### 8.2 错误处理

```go
func handleRedisError(err error) {
	if err == redis.Nil {
		// 键不存在
		log.Info("Key not found")
	} else if err != nil {
		// 其他错误
		log.Errorf("Redis error: %v", err)
	}
}
```

## 9. Redis 最佳实践

### 9.1 键命名规范

```go
// 使用有意义的前缀和分隔符
const (
	UserSessionPrefix = "user:session:"
	CachePrefix      = "cache:"
	LockPrefix       = "lock:"
)

func getUserSessionKey(userID string) string {
	return UserSessionPrefix + userID
}
```

### 9.2 避免大键

```go
// 避免存储大对象，考虑分片
func (r *Repository) setLargeObject(ctx context.Context, key string, data []byte) error {
	const chunkSize = 1024 * 1024 // 1MB
	
	if len(data) <= chunkSize {
		return r.data.Rdb.Set(ctx, key, data, time.Hour).Err()
	}
	
	// 分片存储
	chunks := len(data)/chunkSize + 1
	pipe := r.data.Rdb.Pipeline()
	
	for i := 0; i < chunks; i++ {
		start := i * chunkSize
		end := start + chunkSize
		if end > len(data) {
			end = len(data)
		}
		
		chunkKey := fmt.Sprintf("%s:chunk:%d", key, i)
		pipe.Set(ctx, chunkKey, data[start:end], time.Hour)
	}
	
	// 存储元数据
	metadata := map[string]interface{}{
		"chunks": chunks,
		"size":   len(data),
	}
	pipe.HMSet(ctx, key+":meta", metadata)
	
	_, err := pipe.Exec(ctx)
	return err
}
```

### 9.3 合理使用过期时间

```go
// 根据数据特性设置不同的过期时间
const (
	ShortCache  = time.Minute * 5   // 热点数据
	MediumCache = time.Hour         // 一般数据
	LongCache   = time.Hour * 24    // 冷数据
)

func (r *Repository) setCacheWithTTL(ctx context.Context, key string, value interface{}, dataType string) error {
	var ttl time.Duration
	
	switch dataType {
	case "hot":
		ttl = ShortCache
	case "warm":
		ttl = MediumCache
	case "cold":
		ttl = LongCache
	default:
		ttl = MediumCache
	}
	
	return r.data.Rdb.Set(ctx, key, value, ttl).Err()
}
```

## 10. 总结

Redis 在 hw-paas-service 项目中作为重要的缓存和数据存储组件：

1. **高性能**: 内存存储，读写速度极快
2. **丰富的数据类型**: 支持多种数据结构，满足不同场景需求
3. **完善的监控**: 集成 OpenTelemetry，提供详细的性能指标
4. **连接池管理**: 合理配置连接池，提高并发性能
5. **可扩展性**: 支持集群模式，可水平扩展

通过合理使用 Redis 的各种特性，可以显著提升应用的性能和用户体验，同时减少数据库的负载压力。