# Go语言上下文(Context)详解

## 1. Context基础概念

### 1.1 Context的作用
Context是Go语言中用于在goroutine之间传递截止时间、取消信号和请求范围值的标准方式。

### 1.2 项目中的Context使用
**位置**: `internal/service/ai.go:40`
```go
func (s *AiService) QueryCorrect(ctx context.Context, req *pb.QueryCorrectRequest) (*structpb.Struct, error) {
    traceId := custom_context.GetTraceId(ctx)
    sn := custom_context.GetDeviceId(ctx)
    // ...
}
```

**知识点解析**:
- `context.Context` - 标准库的Context接口
- 作为函数的第一个参数传递
- 用于传递请求范围的数据

## 2. Context的创建

### 2.1 根Context
```go
import "context"

// 创建空的根context
ctx := context.Background()

// 创建TODO context（开发时使用）
ctx := context.TODO()
```

### 2.2 带值的Context
**位置**: `internal/pkg/custom_context/ctx_key.go:12-14`
```go
func SetJwtUserId(ctx context.Context, userId string) context.Context {
    return context.WithValue(ctx, common.JwtUserId, userId)
}
```

**知识点解析**:
- `context.WithValue()` - 在context中存储键值对
- 返回新的context，原context不变
- 键通常使用自定义类型避免冲突

### 2.3 带超时的Context
```go
// 5秒后超时
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()

// 指定截止时间
deadline := time.Now().Add(10 * time.Second)
ctx, cancel := context.WithDeadline(context.Background(), deadline)
defer cancel()
```

### 2.4 可取消的Context
```go
ctx, cancel := context.WithCancel(context.Background())
defer cancel()

// 在另一个goroutine中取消
go func() {
    time.Sleep(2 * time.Second)
    cancel()  // 取消context
}()
```

## 3. Context值的存取

### 3.1 存储值到Context
**位置**: `internal/pkg/custom_context/ctx_key.go:20-22, 36-38, 80-82`
```go
func SetDeviceId(ctx context.Context, deviceId string) context.Context {
    return context.WithValue(ctx, common.JwtDeviceId, deviceId)
}

func SetToken(ctx context.Context, token string) context.Context {
    return context.WithValue(ctx, common.JwtToken, token)
}

func SetTraceId(ctx context.Context, traceId string) context.Context {
    return context.WithValue(ctx, common.TraceId, traceId)
}
```

### 3.2 从Context获取值
**位置**: `internal/pkg/custom_context/ctx_key.go:8-10, 16-18, 76-78`
```go
func GetJwtUserId(ctx context.Context) (userId string) {
    return ctx.Value(common.JwtUserId).(string)
}

func GetDeviceId(ctx context.Context) (deviceId string) {
    return cast.ToString(ctx.Value(common.JwtDeviceId))
}

func GetTraceId(ctx context.Context) (traceId string) {
    return cast.ToString(ctx.Value(common.TraceId))
}
```

**知识点解析**:
- `ctx.Value(key)` - 获取存储的值
- 返回`interface{}`类型，需要类型断言
- 使用`cast.ToString()`进行安全类型转换

### 3.3 安全的值获取
**位置**: `internal/pkg/custom_context/ctx_key.go:68-74`
```go
func GetAppId(ctx context.Context) (appId string) {
    value := ctx.Value(common.JwtAppId)
    if value != nil {
        return value.(string)
    }
    return ""
}
```

**知识点解析**:
- 检查值是否为nil
- 避免对nil值进行类型断言导致panic
- 提供默认值

## 4. Context的传播

### 4.1 在函数调用中传播Context
**位置**: `internal/biz/biz.correct.go:52-89`
```go
func (uc *CorrectUseCase) QueryCorrect(ctx context.Context, imageUrl string) (res interface{}, err error) {
    // ...
    imgBytes, err := uc.downloadImage(ctx, imageUrl)  // 传递context
    // ...
    correct, err := uc.kousuanClient.KousuanCorrect(ctx, imgBytes)  // 传递context
    // ...
}

func (uc *CorrectUseCase) downloadImage(ctx context.Context, imageURL string) (imgBytes []byte, err error) {
    uc.log.WithContext(ctx).Infof("downloading image: %s", imageURL)  // 使用context
    // ...
}
```

**知识点解析**:
- Context在函数调用链中传递
- 每个需要Context的函数都应该接收Context参数
- 日志记录时使用Context获取追踪信息

### 4.2 在goroutine中传播Context
**位置**: `internal/biz/biz.correct.go:61-66`
```go
defer func() {
    gCtx := util.NewTraceContext(nil, traceId)
    sync.Go(gCtx, uc.log, func() {
        traceB, _ := jsoniter.Marshal(traceLog)
        uc.log.WithContext(gCtx).Info(...)
        return
    })
}()
```

**知识点解析**:
- 在新的goroutine中创建新的Context
- 保持追踪信息的连续性
- 异步操作中使用Context

## 5. Context的取消和超时

### 5.1 检查Context是否被取消
```go
func longRunningTask(ctx context.Context) error {
    for i := 0; i < 1000; i++ {
        select {
        case <-ctx.Done():
            return ctx.Err()  // 返回取消原因
        default:
            // 执行工作
            time.Sleep(10 * time.Millisecond)
        }
    }
    return nil
}
```

### 5.2 Context错误类型
```go
func handleContextError(err error) {
    switch err {
    case context.Canceled:
        fmt.Println("操作被取消")
    case context.DeadlineExceeded:
        fmt.Println("操作超时")
    default:
        fmt.Printf("其他错误: %v\n", err)
    }
}
```

Context是Go语言并发编程的重要组成部分，正确使用Context可以让程序更加健壮和可控。