# Go语言时间处理详解

## 1. 时间基础

### 1.1 time包简介
Go语言的`time`包提供了时间的测量和显示功能。

### 1.2 项目中的时间使用
**位置**: `internal/biz/biz.correct.go:69-73, 79-84`
```go
//图片下载
start := time.Now().UnixNano() / 1e6
uc.log.WithContext(ctx).Infof("downloading image start: %d", start)
imgBytes, err := uc.downloadImage(ctx, imageUrl)
end := time.Now().UnixNano() / 1e6
uc.log.WithContext(ctx).Infof("downloading image sub: %d", end-start)

//图片下载
startCorrect := time.Now().UnixNano() / 1e6
uc.log.WithContext(ctx).Infof("KousuanCorrect start: %d", startCorrect)
correct, err := uc.kousuanClient.KousuanCorrect(ctx, imgBytes)
endCorrect := time.Now().UnixNano() / 1e6
uc.log.WithContext(ctx).Infof("KousuanCorrect sub: %d", endCorrect-startCorrect)
```

**知识点解析**:
- `time.Now()` - 获取当前时间
- `UnixNano()` - 获取纳秒时间戳
- `/1e6` - 转换为毫秒
- 用于性能监控和日志记录

## 2. 时间的创建和获取

### 2.1 获取当前时间
```go
now := time.Now()                    // 当前本地时间
utc := time.Now().UTC()              // 当前UTC时间
unix := time.Now().Unix()            // Unix时间戳（秒）
unixNano := time.Now().UnixNano()    // Unix时间戳（纳秒）
```

### 2.2 创建特定时间
```go
// 使用Date函数创建时间
t := time.Date(2023, 12, 25, 15, 30, 0, 0, time.UTC)

// 解析时间字符串
layout := "2006-01-02 15:04:05"
t, err := time.Parse(layout, "2023-12-25 15:30:00")

// 解析带时区的时间
t, err := time.ParseInLocation(layout, "2023-12-25 15:30:00", time.Local)
```

### 2.3 时间零值
```go
var zero time.Time
fmt.Println(zero.IsZero())  // true

// 检查时间是否为零值
if t.IsZero() {
    fmt.Println("时间为零值")
}
```

## 3. 时间格式化

### 3.1 Go时间格式化的特殊性
Go使用特定的参考时间进行格式化：`Mon Jan 2 15:04:05 MST 2006`，即`01/02 03:04:05PM '06 -0700`

```go
now := time.Now()

// 常用格式
fmt.Println(now.Format("2006-01-02"))           // 2023-12-25
fmt.Println(now.Format("2006-01-02 15:04:05"))  // 2023-12-25 15:30:00
fmt.Println(now.Format("15:04:05"))             // 15:30:00
fmt.Println(now.Format("2006/01/02"))           // 2023/12/25
```

### 3.2 项目中可能的时间格式化
**位置**: `internal/data/model/hw_en_word.go:38-39`
```go
CreatedAt   time.Time    `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`
UpdatedAt   time.Time    `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;column:updated_at" json:"updated_at"`
```

**知识点解析**:
- 数据库中的时间字段
- JSON序列化时的时间格式
- GORM自动管理创建和更新时间

### 3.3 自定义时间格式
```go
// 中文格式
fmt.Println(now.Format("2006年01月02日 15时04分05秒"))

// 12小时制
fmt.Println(now.Format("2006-01-02 03:04:05 PM"))

// 带时区
fmt.Println(now.Format("2006-01-02 15:04:05 MST"))
```

## 4. 时间计算

### 4.1 时间加减
```go
now := time.Now()

// 加时间
future := now.Add(24 * time.Hour)        // 加24小时
future = now.Add(30 * time.Minute)       // 加30分钟
future = now.Add(10 * time.Second)       // 加10秒

// 减时间
past := now.Add(-24 * time.Hour)         // 减24小时

// 使用AddDate
future = now.AddDate(1, 2, 3)            // 加1年2月3天
```

### 4.2 时间差计算
**位置**: `internal/biz/biz.correct.go:69-73`
```go
start := time.Now().UnixNano() / 1e6
// ... 执行操作
end := time.Now().UnixNano() / 1e6
duration := end - start  // 计算耗时（毫秒）
```

**更标准的时间差计算**:
```go
start := time.Now()
// ... 执行操作
elapsed := time.Since(start)             // 返回Duration类型
fmt.Printf("耗时: %v\n", elapsed)        // 耗时: 1.234567ms

// 或者
end := time.Now()
duration := end.Sub(start)
```

### 4.3 Duration类型
```go
d := 5 * time.Second
fmt.Println(d.Seconds())      // 5
fmt.Println(d.Milliseconds()) // 5000
fmt.Println(d.Nanoseconds())  // 5000000000

// Duration的字符串表示
fmt.Println(d.String())       // "5s"
```

## 5. 时间比较

### 5.1 时间比较操作
```go
t1 := time.Now()
time.Sleep(1 * time.Second)
t2 := time.Now()

// 比较操作
fmt.Println(t1.Before(t2))    // true
fmt.Println(t1.After(t2))     // false
fmt.Println(t1.Equal(t2))     // false

// 使用Compare（Go 1.20+）
switch t1.Compare(t2) {
case -1:
    fmt.Println("t1 在 t2 之前")
case 0:
    fmt.Println("t1 等于 t2")
case 1:
    fmt.Println("t1 在 t2 之后")
}
```

## 6. 定时器和周期任务

### 6.1 Timer（一次性定时器）
```go
timer := time.NewTimer(5 * time.Second)
defer timer.Stop()

select {
case <-timer.C:
    fmt.Println("定时器触发")
case <-ctx.Done():
    fmt.Println("操作被取消")
}
```

### 6.2 Ticker（周期性定时器）
**位置**: `internal/data/data.go:86`
```go
for range time.Tick(time.Second * 5) {
    stats := rdb.PoolStats()
    biz_metrics.RedisPoolStats(stats.Hits, stats.Misses, stats.Timeouts, stats.TotalConns, stats.IdleConns, stats.StaleConns)
}
```

**更安全的Ticker使用**:
```go
ticker := time.NewTicker(5 * time.Second)
defer ticker.Stop()

for {
    select {
    case <-ticker.C:
        // 执行周期性任务
        stats := rdb.PoolStats()
        // 处理统计信息
    case <-ctx.Done():
        return
    }
}
```

### 6.3 Sleep
```go
time.Sleep(1 * time.Second)      // 睡眠1秒
time.Sleep(500 * time.Millisecond) // 睡眠500毫秒
```

## 7. 时区处理

### 7.1 时区基础
```go
// 加载时区
loc, err := time.LoadLocation("Asia/Shanghai")
if err != nil {
    log.Fatal(err)
}

// 在指定时区创建时间
t := time.Date(2023, 12, 25, 15, 30, 0, 0, loc)

// 转换时区
utc := t.UTC()
local := t.Local()
```

### 7.2 常用时区
```go
// UTC时区
utc := time.Now().UTC()

// 本地时区
local := time.Now().Local()

// 指定时区
tokyo, _ := time.LoadLocation("Asia/Tokyo")
tokyoTime := time.Now().In(tokyo)
```

## 8. 时间解析

### 8.1 解析时间字符串
```go
// 标准格式
t, err := time.Parse("2006-01-02", "2023-12-25")
t, err = time.Parse("2006-01-02 15:04:05", "2023-12-25 15:30:00")

// RFC3339格式（ISO 8601）
t, err = time.Parse(time.RFC3339, "2023-12-25T15:30:00Z")

// 自定义格式
t, err = time.Parse("01/02/2006", "12/25/2023")
```

### 8.2 解析带时区的时间
```go
loc, _ := time.LoadLocation("Asia/Shanghai")
t, err := time.ParseInLocation("2006-01-02 15:04:05", "2023-12-25 15:30:00", loc)
```

## 9. 时间的JSON处理

### 9.1 默认JSON格式
```go
type Event struct {
    Name string    `json:"name"`
    Time time.Time `json:"time"`
}

event := Event{
    Name: "会议",
    Time: time.Now(),
}

// JSON序列化
data, _ := json.Marshal(event)
// {"name":"会议","time":"2023-12-25T15:30:00.123456789Z"}
```

### 9.2 自定义时间JSON格式
```go
type CustomTime struct {
    time.Time
}

func (ct CustomTime) MarshalJSON() ([]byte, error) {
    return json.Marshal(ct.Format("2006-01-02 15:04:05"))
}

func (ct *CustomTime) UnmarshalJSON(data []byte) error {
    var timeStr string
    if err := json.Unmarshal(data, &timeStr); err != nil {
        return err
    }
    
    t, err := time.Parse("2006-01-02 15:04:05", timeStr)
    if err != nil {
        return err
    }
    
    ct.Time = t
    return nil
}
```

## 10. 时间性能优化

### 10.1 避免频繁的时间操作
```go
// 低效：每次都调用time.Now()
for i := 0; i < 1000; i++ {
    log.Printf("处理第%d项，时间：%v", i, time.Now())
}

// 高效：缓存时间
now := time.Now()
for i := 0; i < 1000; i++ {
    log.Printf("处理第%d项，时间：%v", i, now)
}
```

### 10.2 使用Unix时间戳进行比较
```go
// 对于大量时间比较，使用Unix时间戳更高效
t1Unix := t1.Unix()
t2Unix := t2.Unix()

if t1Unix < t2Unix {
    // t1 在 t2 之前
}
```

## 11. 时间的最佳实践

### 11.1 时间存储建议
- 数据库中存储UTC时间
- 显示时转换为用户本地时间
- 使用ISO 8601格式进行时间传输

### 11.2 时间比较
```go
// 比较时间时考虑精度
func timeEqual(t1, t2 time.Time) bool {
    return t1.Truncate(time.Second).Equal(t2.Truncate(time.Second))
}
```

### 11.3 超时处理
```go
func processWithTimeout(ctx context.Context, timeout time.Duration) error {
    ctx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()
    
    // 执行操作
    return doWork(ctx)
}
```

## 12. 常见错误和陷阱

### 12.1 时区混淆
```go
// 错误：假设所有时间都是本地时间
t, _ := time.Parse("2006-01-02 15:04:05", "2023-12-25 15:30:00")
// t是UTC时间，不是本地时间

// 正确：明确指定时区
t, _ := time.ParseInLocation("2006-01-02 15:04:05", "2023-12-25 15:30:00", time.Local)
```

### 12.2 时间比较精度问题
```go
t1 := time.Now()
t2 := time.Now()

// 可能为false，因为纳秒级差异
fmt.Println(t1.Equal(t2))

// 截断到秒级比较
fmt.Println(t1.Truncate(time.Second).Equal(t2.Truncate(time.Second)))
```

### 12.3 Ticker内存泄漏
```go
// 错误：忘记停止ticker
ticker := time.NewTicker(1 * time.Second)
// 忘记调用ticker.Stop()

// 正确：确保停止ticker
ticker := time.NewTicker(1 * time.Second)
defer ticker.Stop()
```

时间处理是Go语言编程中的重要技能，正确处理时间可以避免很多与时区、格式化和性能相关的问题。