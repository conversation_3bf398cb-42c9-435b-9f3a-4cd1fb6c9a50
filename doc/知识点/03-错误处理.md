# Go语言错误处理详解

## 1. 错误处理基础

### 1.1 error接口
Go语言中的错误处理基于内置的error接口：
```go
type error interface {
    Error() string
}
```

### 1.2 基本错误处理模式
**位置**: `internal/data/data.go:29-32`
```go
func NewData(conf *conf.Data, logger log.Logger) (*Data, func(), error) {
    db, err := gorm.Open(mysql.Open(conf.Database.Source), &gorm.Config{})
    if err != nil {
        return nil, nil, err  // 返回错误
    }
    // ...
}
```

**知识点解析**:
- Go函数通常返回`(结果, error)`的模式
- `if err != nil` - 标准的错误检查模式
- 错误发生时立即返回，避免继续执行

### 1.3 多重返回值的错误处理
**位置**: `internal/service/ai.go:40-53`
```go
func (s *AiService) QueryCorrect(ctx context.Context, req *pb.QueryCorrectRequest) (*structpb.Struct, error) {
    traceId := custom_context.GetTraceId(ctx)
    sn := custom_context.GetDeviceId(ctx)
    // ...
    data, err := s.correct.QueryCorrect(ctx, req.ImageUrl)
    return utils.ReplyAny(data, err)  // 传播错误
}
```

**知识点解析**:
- 函数返回两个值：结果和错误
- 调用者负责检查和处理错误
- 错误可以向上传播

## 2. 错误创建和包装

### 2.1 使用errors.Wrap包装错误
**位置**: `internal/data/data.go:33-35`
```go
if err := db.Use(otelgorm.NewPlugin()); err != nil {
    return nil, nil, errors.Wrap(err, "data: db.Use error")
}
```

**知识点解析**:
- `errors.Wrap()` - 包装原始错误并添加上下文信息
- 来自`github.com/pkg/errors`包
- 保留原始错误信息，添加调用栈

### 2.2 更多错误包装示例
**位置**: `internal/data/data.go:52-54, 55-57`
```go
if err := redisotel.InstrumentTracing(rdb); err != nil {
    return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentTracing error")
}
if err := redisotel.InstrumentMetrics(rdb); err != nil {
    return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentMetrics error")
}
```

## 3. 自定义错误

### 3.1 业务错误定义
**位置**: `internal/biz/biz.correct.go:117-118`
```go
if req.Biz == 0 || req.TraceId == "" || req.Feedback == "" {
    return nil, pb.ErrorHwPaasParamError("反馈参数错误")
}
```

**知识点解析**:
- `pb.ErrorHwPaasParamError()` - 自定义错误创建函数
- 通常由protobuf生成，包含错误码和错误信息
- 用于API层的错误响应

### 3.2 更多自定义错误示例
**位置**: `internal/biz/biz.correct.go:160`
```go
return &pb.TaskDriveResp{}, pb.ErrorHwPaasDefaultErr("不支持此app")
```

**位置**: `internal/service/ai.go:175`
```go
return nil, pb.ErrorHwPaasUnauthorized(err.Error())
```

## 4. 错误检查和处理

### 4.1 条件错误检查
**位置**: `internal/biz/biz.correct.go:158-161`
```go
if _, ok := TaskDriveMap[app]; !ok {
    uc.log.WithContext(ctx).Errorf("TaskDrive 不支持此app: %v", app)
    return &pb.TaskDriveResp{}, pb.ErrorHwPaasDefaultErr("不支持此app")
}
```

**知识点解析**:
- 使用map查找的ok模式检查键是否存在
- 记录错误日志
- 返回业务相关的错误信息

### 4.2 错误日志记录
**位置**: `internal/biz/biz.correct.go:74-77`
```go
if err != nil {
    uc.log.WithContext(ctx).Errorf("download image error: %v", err)
    return nil, err
}
```

**知识点解析**:
- 使用结构化日志记录错误
- `%v` - 打印错误的默认格式
- 包含上下文信息便于调试

### 4.3 错误转换
**位置**: `internal/service/ai.go:172-176`
```go
res, err := s.login.CheckLogin(ctx, req.TalToken)
if err != nil {
    return nil, pb.ErrorHwPaasUnauthorized(err.Error())
}
```

**知识点解析**:
- 将底层错误转换为API层错误
- `err.Error()` - 获取错误的字符串表示
- 保持错误信息的同时改变错误类型

## 5. defer和错误处理

### 5.1 defer中的错误处理
**位置**: `internal/biz/biz.correct.go:101-103`
```go
defer func() {
    _ = resp.Body.Close()  // 忽略Close的错误
}()
```

**知识点解析**:
- `_ = resp.Body.Close()` - 使用空标识符忽略错误
- 在defer中处理资源清理
- 某些清理操作的错误可以安全忽略

### 5.2 defer中的panic恢复
**位置**: `internal/data/data.go:81-85`
```go
defer func() {
    if r := recover(); r != nil {
        log.Errorf("metricRedisPoolStats panic: %v", r)
    }
}()
```

**知识点解析**:
- `recover()` - 捕获panic
- 只能在defer函数中使用
- 防止goroutine因panic而崩溃

## 6. 错误传播模式

### 6.1 直接传播
**位置**: `internal/biz/biz.correct.go:85-88`
```go
correct, err := uc.kousuanClient.KousuanCorrect(ctx, imgBytes)
if err != nil {
    uc.log.WithContext(ctx).Errorf("kousuan correct error: %v", err)
    return nil, err  // 直接返回原错误
}
```

### 6.2 包装后传播
**位置**: `internal/data/data.go:33-35`
```go
if err := db.Use(otelgorm.NewPlugin()); err != nil {
    return nil, nil, errors.Wrap(err, "data: db.Use error")  // 包装后返回
}
```

### 6.3 转换后传播
**位置**: `internal/service/ai.go:184-189`
```go
isExists, err := s.login.CheckEmailExists(ctx, req.Email)
if err != nil {
    return &pb.CheckEmailExistsResp{Exists: false}, err  // 返回默认值和错误
}
return &pb.CheckEmailExistsResp{Exists: isExists}, nil
```

## 7. 错误处理的最佳实践

### 7.1 错误信息的上下文
**位置**: `internal/biz/biz.correct.go:94-95`
```go
uc.log.WithContext(ctx).Infof("downloading image: %s", imageURL)
```

**知识点解析**:
- 在错误信息中包含足够的上下文
- 使用结构化日志
- 便于问题定位和调试

### 7.2 错误处理的层次
```go
// 数据层：包装技术错误
func (dao *UserDao) GetUser(id int) (*User, error) {
    user := &User{}
    err := dao.db.Where("id = ?", id).First(user).Error
    if err != nil {
        return nil, errors.Wrap(err, "failed to get user from database")
    }
    return user, nil
}

// 业务层：转换为业务错误
func (uc *UserUseCase) GetUser(id int) (*User, error) {
    user, err := uc.userDao.GetUser(id)
    if err != nil {
        return nil, errors.Wrap(err, "failed to get user")
    }
    return user, nil
}

// 服务层：转换为API错误
func (s *UserService) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.User, error) {
    user, err := s.userUC.GetUser(int(req.Id))
    if err != nil {
        return nil, pb.ErrorUserNotFound("用户不存在")
    }
    return convertToProto(user), nil
}
```

## 8. 特殊错误处理场景

### 8.1 忽略特定错误
**位置**: `internal/data/data.go:66-71`
```go
_db, err := d.DB.DB()
if err != nil {
    log.NewHelper(logger).Errorf("database close err:%+v", err)
}
_ = _db.Close()  // 忽略Close错误
```

### 8.2 JSON序列化错误处理
**位置**: `internal/biz/biz.correct.go:49, 63`
```go
baseJson, _ := jsoniter.Marshal(base)  // 忽略序列化错误
// ...
traceB, _ := jsoniter.Marshal(traceLog)  // 忽略序列化错误
```

**知识点解析**:
- 某些场景下可以安全忽略错误
- 日志序列化失败不应影响主要业务逻辑
- 使用空标识符明确表示忽略错误

## 9. 错误处理的性能考虑

### 9.1 避免频繁的错误创建
```go
// 不好的做法
func validateInput(input string) error {
    if input == "" {
        return errors.New("input is empty")  // 每次都创建新错误
    }
    return nil
}

// 更好的做法
var ErrInputEmpty = errors.New("input is empty")

func validateInput(input string) error {
    if input == "" {
        return ErrInputEmpty  // 重用预定义错误
    }
    return nil
}
```

### 9.2 错误检查的优化
```go
// 快速失败模式
func processData(data []string) error {
    for _, item := range data {
        if err := validateItem(item); err != nil {
            return err  // 遇到错误立即返回
        }
    }
    return nil
}
```

## 10. 错误处理总结

### 10.1 Go错误处理的特点
- 显式错误处理，不隐藏错误
- 错误是值，可以被检查、包装、传播
- 鼓励在错误发生的地方处理
- 通过多返回值传递错误

### 10.2 错误处理原则
1. **及时检查**: 每个可能出错的操作都要检查错误
2. **适当包装**: 添加上下文信息，便于调试
3. **合理传播**: 决定是处理、包装还是直接传播
4. **记录日志**: 在适当的层次记录错误信息
5. **优雅降级**: 在可能的情况下提供默认值或备选方案

Go的错误处理虽然看起来冗长，但提供了明确、可控的错误管理机制，有助于构建健壮的应用程序。