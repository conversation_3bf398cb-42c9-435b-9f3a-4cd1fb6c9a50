# Go语言控制流程详解

## 1. 条件语句

### 1.1 if语句基础
**位置**: `internal/data/data.go:30-32`
```go
if err != nil {
    return nil, nil, err
}
```

**位置**: `internal/biz/biz.correct.go:74-77`
```go
if err != nil {
    uc.log.WithContext(ctx).Errorf("download image error: %v", err)
    return nil, err
}
```

**知识点解析**:
- Go语言的if语句不需要括号
- 条件表达式必须是布尔类型
- 常用于错误处理

### 1.2 if语句的初始化
**位置**: `internal/pkg/custom_context/ctx_key.go:69-73`
```go
value := ctx.Value(common.JwtAppId)
if value != nil {
    return value.(string)
}
return ""
```

**更简洁的写法**:
```go
if value := ctx.Value(common.JwtAppId); value != nil {
    return value.(string)
}
return ""
```

**知识点解析**:
- if语句可以包含初始化语句
- 初始化的变量只在if语句块内可见
- 用分号分隔初始化语句和条件表达式

### 1.3 if-else语句
```go
if condition {
    // 条件为真时执行
} else if anotherCondition {
    // 另一个条件为真时执行
} else {
    // 所有条件都为假时执行
}
```

## 2. switch语句

### 2.1 基本switch语句
**位置**: `internal/biz/biz.correct.go:120-127`
```go
switch req.Biz { //1:口算批改;2:指尖查词;3:作业批改;4:指尖查词;5:语音查词;6:绘本指读
case 4:
    biz = "finger_words"
case 5:
    biz = "audio_words"
case 6:
    biz = "reading_book"
}
```

**知识点解析**:
- switch语句不需要break，默认会跳出
- 每个case只会执行一个分支
- 可以有多个值匹配同一个case

### 2.2 多值case
```go
switch day {
case "Saturday", "Sunday":
    fmt.Println("周末")
case "Monday", "Tuesday", "Wednesday", "Thursday", "Friday":
    fmt.Println("工作日")
default:
    fmt.Println("无效的日期")
}
```

### 2.3 无表达式switch
```go
switch {
case score >= 90:
    grade = "A"
case score >= 80:
    grade = "B"
case score >= 70:
    grade = "C"
default:
    grade = "F"
}
```

### 2.4 类型switch
```go
func processValue(v interface{}) {
    switch val := v.(type) {
    case string:
        fmt.Printf("字符串: %s\n", val)
    case int:
        fmt.Printf("整数: %d\n", val)
    case bool:
        fmt.Printf("布尔值: %t\n", val)
    default:
        fmt.Printf("未知类型: %T\n", val)
    }
}
```

### 2.5 fallthrough关键字
```go
switch num {
case 1:
    fmt.Println("一")
    fallthrough  // 继续执行下一个case
case 2:
    fmt.Println("二")
case 3:
    fmt.Println("三")
}
```

## 3. 循环语句

### 3.1 for循环基础
Go语言只有for循环，没有while循环：

```go
// 传统的for循环
for i := 0; i < 10; i++ {
    fmt.Println(i)
}

// while风格的for循环
i := 0
for i < 10 {
    fmt.Println(i)
    i++
}

// 无限循环
for {
    // 无限循环，需要break跳出
}
```

### 3.2 for-range循环
**位置**: `internal/data/data.go:86`
```go
for range time.Tick(time.Second * 5) {
    stats := rdb.PoolStats()
    biz_metrics.RedisPoolStats(stats.Hits, stats.Misses, stats.Timeouts, stats.TotalConns, stats.IdleConns, stats.StaleConns)
}
```

**知识点解析**:
- `for range` 用于遍历数组、切片、map、通道等
- 这里遍历time.Tick返回的通道
- 每5秒执行一次循环体

### 3.3 遍历切片和数组
```go
slice := []string{"apple", "banana", "cherry"}

// 遍历索引和值
for i, value := range slice {
    fmt.Printf("索引: %d, 值: %s\n", i, value)
}

// 只遍历值
for _, value := range slice {
    fmt.Printf("值: %s\n", value)
}

// 只遍历索引
for i := range slice {
    fmt.Printf("索引: %d\n", i)
}
```

### 3.4 遍历map
```go
m := map[string]int{
    "apple":  5,
    "banana": 3,
    "cherry": 8,
}

for key, value := range m {
    fmt.Printf("键: %s, 值: %d\n", key, value)
}
```

### 3.5 遍历字符串
```go
s := "Hello, 世界"

// 遍历字节
for i := 0; i < len(s); i++ {
    fmt.Printf("字节 %d: %c\n", i, s[i])
}

// 遍历rune（字符）
for i, r := range s {
    fmt.Printf("位置 %d: %c\n", i, r)
}
```

## 4. 跳转语句

### 4.1 break语句
```go
for i := 0; i < 10; i++ {
    if i == 5 {
        break  // 跳出循环
    }
    fmt.Println(i)
}

// 带标签的break
outer:
for i := 0; i < 3; i++ {
    for j := 0; j < 3; j++ {
        if i == 1 && j == 1 {
            break outer  // 跳出外层循环
        }
        fmt.Printf("i=%d, j=%d\n", i, j)
    }
}
```

### 4.2 continue语句
```go
for i := 0; i < 10; i++ {
    if i%2 == 0 {
        continue  // 跳过偶数
    }
    fmt.Println(i)  // 只打印奇数
}

// 带标签的continue
outer:
for i := 0; i < 3; i++ {
    for j := 0; j < 3; j++ {
        if j == 1 {
            continue outer  // 继续外层循环的下一次迭代
        }
        fmt.Printf("i=%d, j=%d\n", i, j)
    }
}
```

### 4.3 goto语句（不推荐）
```go
func example() {
    i := 0
loop:
    if i < 5 {
        fmt.Println(i)
        i++
        goto loop
    }
}
```

## 5. select语句

### 5.1 select基础
select语句用于处理多个通道操作：

```go
select {
case msg1 := <-ch1:
    fmt.Println("收到ch1:", msg1)
case msg2 := <-ch2:
    fmt.Println("收到ch2:", msg2)
case <-time.After(1 * time.Second):
    fmt.Println("超时")
default:
    fmt.Println("没有通道准备好")
}
```

### 5.2 非阻塞通道操作
```go
select {
case ch <- value:
    fmt.Println("发送成功")
default:
    fmt.Println("通道已满，发送失败")
}

select {
case value := <-ch:
    fmt.Println("接收到:", value)
default:
    fmt.Println("通道为空，接收失败")
}
```

## 6. 错误处理控制流

### 6.1 标准错误处理模式
**位置**: `internal/biz/biz.correct.go:96-100`
```go
resp, err := http.Get(imageURL)
if err != nil {
    uc.log.WithContext(ctx).Errorf("error downloading image: %v", err)
    return
}
```

**知识点解析**:
- 函数返回错误时立即检查
- 错误处理后决定是否继续执行
- 记录错误日志便于调试

### 6.2 多重错误检查
**位置**: `internal/data/data.go:33-35, 52-54`
```go
if err := db.Use(otelgorm.NewPlugin()); err != nil {
    return nil, nil, errors.Wrap(err, "data: db.Use error")
}

if err := redisotel.InstrumentTracing(rdb); err != nil {
    return nil, nil, errors.Wrap(err, "data: redisotel.InstrumentTracing error")
}
```

### 6.3 defer中的错误处理
**位置**: `internal/biz/biz.correct.go:101-103`
```go
defer func() {
    _ = resp.Body.Close()  // 忽略Close的错误
}()
```

## 7. 控制流的最佳实践

### 7.1 早期返回模式
```go
// 好的做法：早期返回
func processUser(userID string) error {
    if userID == "" {
        return errors.New("用户ID不能为空")
    }
    
    user, err := getUserByID(userID)
    if err != nil {
        return err
    }
    
    if !user.IsActive {
        return errors.New("用户未激活")
    }
    
    // 主要逻辑
    return processActiveUser(user)
}

// 不好的做法：深层嵌套
func processUserBad(userID string) error {
    if userID != "" {
        user, err := getUserByID(userID)
        if err == nil {
            if user.IsActive {
                // 主要逻辑深层嵌套
                return processActiveUser(user)
            } else {
                return errors.New("用户未激活")
            }
        } else {
            return err
        }
    } else {
        return errors.New("用户ID不能为空")
    }
}
```

### 7.2 避免深层嵌套
```go
// 使用switch简化多重if-else
func getGrade(score int) string {
    switch {
    case score >= 90:
        return "A"
    case score >= 80:
        return "B"
    case score >= 70:
        return "C"
    case score >= 60:
        return "D"
    default:
        return "F"
    }
}
```

### 7.3 循环中的资源管理
```go
func processFiles(filenames []string) error {
    for _, filename := range filenames {
        file, err := os.Open(filename)
        if err != nil {
            continue  // 跳过无法打开的文件
        }
        
        func() {
            defer file.Close()  // 确保文件被关闭
            // 处理文件
            processFile(file)
        }()
    }
    return nil
}
```

## 8. 常见错误和陷阱

### 8.1 循环变量捕获
```go
// 错误的做法
var funcs []func()
for i := 0; i < 3; i++ {
    funcs = append(funcs, func() {
        fmt.Println(i)  // 所有函数都会打印3
    })
}

// 正确的做法
var funcs []func()
for i := 0; i < 3; i++ {
    i := i  // 创建新的变量
    funcs = append(funcs, func() {
        fmt.Println(i)  // 打印0, 1, 2
    })
}
```

### 8.2 range循环的值复制
```go
type User struct {
    Name string
    Age  int
}

users := []User{
    {Name: "Alice", Age: 30},
    {Name: "Bob", Age: 25},
}

// 错误：修改的是副本
for _, user := range users {
    user.Age++  // 不会修改原始切片
}

// 正确：使用索引
for i := range users {
    users[i].Age++
}

// 或者使用指针切片
userPtrs := []*User{
    {Name: "Alice", Age: 30},
    {Name: "Bob", Age: 25},
}

for _, user := range userPtrs {
    user.Age++  // 修改原始对象
}
```

### 8.3 switch语句的fallthrough陷阱
```go
// 容易出错的代码
switch value {
case 1:
    fmt.Println("一")
    // 忘记break，但Go默认不会fallthrough
case 2:
    fmt.Println("二")
}

// 如果需要fallthrough，必须显式声明
switch value {
case 1:
    fmt.Println("一")
    fallthrough
case 2:
    fmt.Println("二")
}
```

## 9. 性能考虑

### 9.1 循环优化
```go
// 避免在循环中重复计算
// 低效
for i := 0; i < len(slice); i++ {
    // len(slice)在每次迭代时都会调用
}

// 高效
length := len(slice)
for i := 0; i < length; i++ {
    // len只计算一次
}

// 或者使用range
for i := range slice {
    // 更简洁，性能也很好
}
```

### 9.2 字符串比较优化
```go
// 对于大量字符串比较，使用switch比多个if更高效
func categorizeString(s string) string {
    switch s {
    case "apple", "banana", "cherry":
        return "fruit"
    case "carrot", "broccoli", "spinach":
        return "vegetable"
    default:
        return "unknown"
    }
}
```

控制流程是编程的基础，Go语言的控制流程语句简洁而强大，正确使用它们可以编写出清晰、高效的代码。