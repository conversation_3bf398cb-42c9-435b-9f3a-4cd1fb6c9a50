# Go语言字符串处理详解

## 1. 字符串基础

### 1.1 字符串的定义
Go语言中字符串是不可变的字节序列：

```go
var s1 string = "Hello, World!"
s2 := "Go语言"
s3 := `多行字符串
可以包含换行
和特殊字符 "quotes"`
```

### 1.2 项目中的字符串使用
**位置**: `internal/biz/biz.correct.go:119-127`
```go
biz := ""
switch req.Biz { //1:口算批改;2:指尖查词;3:作业批改;4:指尖查词;5:语音查词;6:绘本指读
case 4:
    biz = "finger_words"
case 5:
    biz = "audio_words"
case 6:
    biz = "reading_book"
}
```

**知识点解析**:
- 字符串字面量使用双引号
- 字符串比较使用`==`操作符
- switch语句中的字符串匹配

### 1.3 字符串的内部结构
```go
// 字符串的内部结构（概念性）
type string struct {
    data uintptr  // 指向字符串数据的指针
    len  int      // 字符串长度
}
```

## 2. 字符串操作

### 2.1 字符串长度
```go
s := "Hello, 世界"
fmt.Println(len(s))           // 字节长度：13
fmt.Println(utf8.RuneCountInString(s))  // 字符长度：9
```

### 2.2 字符串拼接
**位置**: `internal/biz/biz.correct.go:133`
```go
uc.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(req.TraceId, biz, "base_info_feedback", "{\"feedback\": \""+feedback.Result+"\"}"))
```

**知识点解析**:
- 使用`+`操作符拼接字符串
- 字符串拼接会创建新的字符串
- 大量拼接时考虑使用`strings.Builder`

### 2.3 高效的字符串拼接
```go
// 使用strings.Builder
var builder strings.Builder
builder.WriteString("Hello")
builder.WriteString(", ")
builder.WriteString("World")
result := builder.String()

// 使用fmt.Sprintf
result := fmt.Sprintf("Hello, %s", "World")
```

## 3. 字符串格式化

### 3.1 fmt包的格式化
**位置**: `internal/biz/biz.correct.go:69-73`
```go
start := time.Now().UnixNano() / 1e6
uc.log.WithContext(ctx).Infof("downloading image start: %d", start)
// ...
end := time.Now().UnixNano() / 1e6
uc.log.WithContext(ctx).Infof("downloading image sub: %d", end-start)
```

**知识点解析**:
- `%d` - 整数格式化
- `%s` - 字符串格式化
- `%v` - 默认格式化
- `%+v` - 带字段名的结构体格式化

### 3.2 常用格式化动词
```go
name := "Alice"
age := 30
height := 1.68

fmt.Printf("姓名: %s\n", name)        // 字符串
fmt.Printf("年龄: %d\n", age)         // 十进制整数
fmt.Printf("身高: %.2f\n", height)    // 浮点数，保留2位小数
fmt.Printf("详细: %+v\n", person)     // 结构体详细信息
fmt.Printf("类型: %T\n", person)      // 类型信息
```

## 4. 字符串转换

### 4.1 字符串与数字转换
```go
import "strconv"

// 字符串转整数
num, err := strconv.Atoi("123")
num64, err := strconv.ParseInt("123", 10, 64)

// 整数转字符串
str := strconv.Itoa(123)
str := strconv.FormatInt(123, 10)

// 字符串转浮点数
f, err := strconv.ParseFloat("3.14", 64)

// 浮点数转字符串
str := strconv.FormatFloat(3.14, 'f', 2, 64)
```

### 4.2 使用cast库进行转换
**位置**: `internal/pkg/custom_context/ctx_key.go:4, 17, 85`
```go
import "github.com/spf13/cast"

func GetDeviceId(ctx context.Context) (deviceId string) {
    return cast.ToString(ctx.Value(common.JwtDeviceId))
}

func GetTalIdType(ctx context.Context) (talIdType int) {
    return cast.ToInt(ctx.Value(common.TalIdType))
}
```

**知识点解析**:
- `cast.ToString()` - 安全的字符串转换
- `cast.ToInt()` - 安全的整数转换
- 转换失败时返回零值而不是panic

## 5. 字符串查找和替换

### 5.1 字符串查找
```go
import "strings"

s := "Hello, World!"

// 检查是否包含子串
if strings.Contains(s, "World") {
    fmt.Println("包含World")
}

// 查找子串位置
index := strings.Index(s, "World")  // 返回7
lastIndex := strings.LastIndex(s, "l")  // 返回10

// 检查前缀和后缀
if strings.HasPrefix(s, "Hello") {
    fmt.Println("以Hello开头")
}

if strings.HasSuffix(s, "!") {
    fmt.Println("以!结尾")
}
```

### 5.2 字符串替换
```go
s := "Hello, World!"

// 替换所有匹配
newS := strings.ReplaceAll(s, "World", "Go")  // "Hello, Go!"

// 替换指定次数
newS := strings.Replace(s, "l", "L", 2)  // "HeLLo, World!"
```

## 6. 字符串分割和连接

### 6.1 字符串分割
```go
s := "apple,banana,cherry"

// 按分隔符分割
parts := strings.Split(s, ",")  // ["apple", "banana", "cherry"]

// 按空白字符分割
text := "hello world go"
words := strings.Fields(text)  // ["hello", "world", "go"]

// 限制分割次数
parts := strings.SplitN(s, ",", 2)  // ["apple", "banana,cherry"]
```

### 6.2 字符串连接
```go
parts := []string{"apple", "banana", "cherry"}

// 使用分隔符连接
result := strings.Join(parts, ", ")  // "apple, banana, cherry"
```

## 7. 字符串修剪和清理

### 7.1 去除空白字符
```go
s := "  hello world  "

trimmed := strings.TrimSpace(s)  // "hello world"
leftTrimmed := strings.TrimLeft(s, " ")  // "hello world  "
rightTrimmed := strings.TrimRight(s, " ")  // "  hello world"
```

### 7.2 去除指定字符
```go
s := "!!!hello world!!!"

trimmed := strings.Trim(s, "!")  // "hello world"
trimmed := strings.TrimPrefix(s, "!!!")  // "hello world!!!"
trimmed := strings.TrimSuffix(s, "!!!")  // "!!!hello world"
```

## 8. 字符串大小写转换

### 8.1 大小写转换
```go
s := "Hello World"

upper := strings.ToUpper(s)  // "HELLO WORLD"
lower := strings.ToLower(s)  // "hello world"
title := strings.Title(s)   // "Hello World"
```

### 8.2 Unicode大小写转换
```go
import "unicode"

s := "Hello 世界"
upper := strings.ToUpper(s)  // "HELLO 世界"
```

## 9. 正则表达式

### 9.1 基本正则表达式
```go
import "regexp"

// 编译正则表达式
re, err := regexp.Compile(`\d+`)
if err != nil {
    log.Fatal(err)
}

// 查找匹配
text := "价格是100元"
match := re.FindString(text)  // "100"

// 查找所有匹配
matches := re.FindAllString(text, -1)

// 替换匹配
result := re.ReplaceAllString(text, "XXX")  // "价格是XXX元"
```

### 9.2 命名捕获组
```go
re := regexp.MustCompile(`(?P<year>\d{4})-(?P<month>\d{2})-(?P<day>\d{2})`)
text := "今天是2023-12-25"

matches := re.FindStringSubmatch(text)
names := re.SubexpNames()

for i, match := range matches {
    if i > 0 {  // 跳过完整匹配
        fmt.Printf("%s: %s\n", names[i], match)
    }
}
```

## 10. 字符串与字节

### 10.1 字符串与字节切片转换
```go
s := "Hello, 世界"

// 字符串转字节切片
bytes := []byte(s)
fmt.Printf("字节: %v\n", bytes)

// 字节切片转字符串
newS := string(bytes)
fmt.Printf("字符串: %s\n", newS)
```

### 10.2 UTF-8处理
```go
import "unicode/utf8"

s := "Hello, 世界"

// 检查是否是有效的UTF-8
if utf8.ValidString(s) {
    fmt.Println("有效的UTF-8字符串")
}

// 遍历rune
for i, r := range s {
    fmt.Printf("位置%d: %c (Unicode: %U)\n", i, r, r)
}

// 获取第一个rune
r, size := utf8.DecodeRuneInString(s)
fmt.Printf("第一个字符: %c, 字节大小: %d\n", r, size)
```

## 11. 字符串性能优化

### 11.1 避免频繁的字符串拼接
```go
// 低效的方式
var result string
for i := 0; i < 1000; i++ {
    result += fmt.Sprintf("item%d ", i)  // 每次都创建新字符串
}

// 高效的方式
var builder strings.Builder
for i := 0; i < 1000; i++ {
    builder.WriteString(fmt.Sprintf("item%d ", i))
}
result := builder.String()
```

### 11.2 字符串池化
```go
var stringPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 0, 1024)
    },
}

func processString(s string) string {
    buf := stringPool.Get().([]byte)
    defer stringPool.Put(buf[:0])
    
    // 使用buf处理字符串
    buf = append(buf, s...)
    // 处理逻辑...
    
    return string(buf)
}
```

## 12. 字符串模板

### 12.1 text/template
```go
import "text/template"

tmpl := `Hello, {{.Name}}! You are {{.Age}} years old.`

t, err := template.New("greeting").Parse(tmpl)
if err != nil {
    log.Fatal(err)
}

data := struct {
    Name string
    Age  int
}{
    Name: "Alice",
    Age:  30,
}

var buf bytes.Buffer
err = t.Execute(&buf, data)
if err != nil {
    log.Fatal(err)
}

fmt.Println(buf.String())  // "Hello, Alice! You are 30 years old."
```

## 13. 字符串的最佳实践

### 13.1 字符串比较
```go
// 区分大小写比较
if s1 == s2 {
    // 相等
}

// 不区分大小写比较
if strings.EqualFold(s1, s2) {
    // 相等（忽略大小写）
}
```

### 13.2 字符串验证
```go
func isValidEmail(email string) bool {
    re := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
    return re.MatchString(email)
}

func isEmpty(s string) bool {
    return strings.TrimSpace(s) == ""
}
```

### 13.3 安全的字符串处理
```go
// 避免SQL注入
func sanitizeInput(input string) string {
    // 移除危险字符
    input = strings.ReplaceAll(input, "'", "''")
    input = strings.ReplaceAll(input, ";", "")
    return input
}

// 限制字符串长度
func truncateString(s string, maxLen int) string {
    if len(s) <= maxLen {
        return s
    }
    return s[:maxLen] + "..."
}
```

## 14. 常见错误和陷阱

### 14.1 字符串索引陷阱
```go
s := "Hello, 世界"
// 错误：直接索引可能得到无效的UTF-8字节
// fmt.Println(s[7])  // 可能输出乱码

// 正确：转换为rune切片
runes := []rune(s)
fmt.Printf("%c\n", runes[7])  // 正确输出中文字符
```

### 14.2 字符串不可变性
```go
s := "Hello"
// s[0] = 'h'  // 编译错误：字符串不可变

// 正确的修改方式
bytes := []byte(s)
bytes[0] = 'h'
s = string(bytes)  // "hello"
```

### 14.3 字符串长度混淆
```go
s := "Hello, 世界"
fmt.Println(len(s))                    // 13 (字节长度)
fmt.Println(utf8.RuneCountInString(s)) // 9 (字符长度)
```

字符串处理是Go语言编程中的基础技能，正确理解和使用字符串操作对于处理文本数据和构建用户界面至关重要。