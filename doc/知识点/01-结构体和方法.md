# Go语言结构体和方法详解

## 1. 结构体(Struct)基础

### 1.1 结构体定义
结构体是Go语言中用于组合数据的复合类型，类似于Java中的类。

**位置**: `internal/service/ai.go:15-22`
```go
type AiService struct {
    pb.UnimplementedAiServer  // 嵌入类型(匿名字段)
    correct  *biz.CorrectUseCase  // 指针类型字段
    log      *log.Helper          // 指针类型字段
    question *biz.JzxQuestionUseCase
    edu      *biz.EduUseCase
    login    *biz.CodeLoginUseCase
}
```

**知识点解析**:
- `type AiService struct` - 定义名为AiService的结构体
- `pb.UnimplementedAiServer` - 嵌入类型，类似Java的继承
- `*biz.CorrectUseCase` - 指针类型字段，存储指向CorrectUseCase的指针

### 1.2 结构体字段标签(Tags)
**位置**: `internal/data/model/hw_en_word.go:18-40`
```go
type HwEnWord struct {
    ID          int64        `gorm:"primary_key;AUTO_INCREMENT;column:id" json:"id"`
    Word        string       `gorm:"type:varchar(50);not null;default:'';column:word" json:"word"`
    British     string       `gorm:"type:varchar(10);not null;default:'';column:british" json:"british"`
    American    string       `gorm:"type:varchar(10);not null;default:'';column:american" json:"american"`
    Meanings    string       `gorm:"type:varchar(2048);not null;default:'';column:meanings" json:"meanings"`
    Status      EnWordStatus `gorm:"type:tinyint(1) unsigned;not null;column:status" json:"status"`
    CreatedAt   time.Time    `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`
}
```

**知识点解析**:
- 反引号`` ` ``包围的是结构体标签
- `gorm:"..."` - GORM ORM框架的标签，定义数据库映射
- `json:"..."` - JSON序列化标签，定义JSON字段名
- 标签用于反射和第三方库的元数据

### 1.3 自定义类型
**位置**: `internal/data/model/hw_en_word.go:7-15`
```go
// EnWordStatus 英文单词状态, 1:待机审;2:待上架;3:已上架;4:已下架;5:违禁词
type EnWordStatus uint8

const (
    EnWordStatusWaitAudit EnWordStatus = iota + 1  // iota从1开始
    EnWordStatusWaitShelf
    EnWordStatusShelf
    EnWordStatusUnShelf
    EnWordStatusCurse
)
```

**知识点解析**:
- `type EnWordStatus uint8` - 基于uint8定义新类型
- `iota` - 常量生成器，自动递增
- `iota + 1` - 从1开始而不是0

## 2. 方法(Methods)

### 2.1 值接收者方法
**位置**: `internal/data/model/hw_en_word.go:42-45`
```go
// TableName sets the insert table name for this struct type
func (HwEnWord) TableName() string {
    return "hw_en_word"
}
```

**知识点解析**:
- `(HwEnWord)` - 值接收者，方法操作的是结构体的副本
- 值接收者适用于不需要修改结构体的方法
- 类似Java中的实例方法

### 2.2 指针接收者方法
**位置**: `internal/service/ai.go:40-53`
```go
func (s *AiService) QueryCorrect(ctx context.Context, req *pb.QueryCorrectRequest) (*structpb.Struct, error) {
    traceId := custom_context.GetTraceId(ctx)
    sn := custom_context.GetDeviceId(ctx)
    // 打印基础日志信息
    base := map[string]interface{}{
        "tal_id":         traceId,
        "device_sn":      sn,
        "channel_source": "口算批改",
    }
    baseJson, _ := jsoniter.Marshal(base)
    s.log.WithContext(ctx).Info(common.MakeLogBackFlowMsgInfo(sn, "kousuan_query_correct", "base_info", string(baseJson)))
    data, err := s.correct.QueryCorrect(ctx, req.ImageUrl)
    return utils.ReplyAny(data, err)
}
```

**知识点解析**:
- `(s *AiService)` - 指针接收者，方法操作的是结构体的指针
- 可以修改结构体的字段
- 避免大结构体的复制，提高性能
- `s.log` - 通过接收者访问结构体字段

### 2.3 构造函数模式
**位置**: `internal/service/ai.go:24-38`
```go
func NewAiService(
    correct *biz.CorrectUseCase,
    question *biz.JzxQuestionUseCase,
    edu *biz.EduUseCase,
    logger log.Logger,
    login *biz.CodeLoginUseCase,
) *AiService {
    return &AiService{
        correct:  correct,
        question: question,
        edu:      edu,
        log:      log.NewHelper(logger),
        login:    login,
    }
}
```

**知识点解析**:
- Go没有构造函数，使用`NewXxx`函数模拟
- `&AiService{...}` - 创建结构体并返回指针
- 字段初始化使用`字段名: 值`的语法

## 3. 嵌入类型(Embedding)

### 3.1 匿名字段
**位置**: `internal/service/ai.go:15-16`
```go
type AiService struct {
    pb.UnimplementedAiServer  // 嵌入类型，没有字段名
    // 其他字段...
}
```

**知识点解析**:
- 嵌入类型提供了类似继承的功能
- 可以直接调用嵌入类型的方法
- Go的组合优于继承的体现

### 3.2 嵌入类型的使用
嵌入`pb.UnimplementedAiServer`后，AiService自动实现了gRPC服务接口的所有方法。

## 4. 结构体的内存布局

### 4.1 字段对齐
**位置**: `internal/data/data.go:21-25`
```go
type Data struct {
    DB  *gorm.DB      // 8字节(64位系统指针)
    Rdb *redis.Client // 8字节(64位系统指针)
}
```

**知识点解析**:
- Go编译器会自动进行字段对齐
- 指针类型在64位系统上占8字节
- 字段顺序影响结构体大小

## 5. 复杂结构体示例

### 5.1 包含多种类型的结构体
**位置**: `internal/biz/biz.correct.go:23-29`
```go
type CorrectUseCase struct {
    log           *log.Helper           // 指针类型
    biz           *conf.Biz            // 指针类型
    kousuanClient *kousuan.Client      // 指针类型
    ftDao         *dao.FeedTraceDao    // 指针类型
    services      *services.Services   // 指针类型
}
```

### 5.2 嵌套结构体
**位置**: `internal/biz/biz.correct.go:30-36`
```go
type traceCorrectResult struct {
    TraceID  string      `json:"trace_id"`  // 字符串类型
    UserId   string      `json:"user_id"`   // 字符串类型
    DeviceId string      `json:"device_id"` // 字符串类型
    ImageUrl string      `json:"image_url"` // 字符串类型
    Resp     interface{} `json:"resp"`      // 空接口类型
}
```

**知识点解析**:
- `interface{}` - 空接口，可以存储任何类型的值
- JSON标签用于序列化时的字段名映射

## 6. 方法集(Method Set)

### 6.1 值类型和指针类型的方法集
```go
// 值接收者方法，值类型和指针类型都可以调用
func (h HwEnWord) TableName() string { ... }

// 指针接收者方法，只有指针类型可以调用
func (s *AiService) QueryCorrect(...) { ... }
```

### 6.2 接口实现
当结构体实现了接口的所有方法时，就自动实现了该接口（隐式实现）。

## 7. 最佳实践

### 7.1 何时使用指针接收者
- 需要修改结构体字段时
- 结构体较大，避免复制开销时
- 保持方法集一致性时

### 7.2 何时使用值接收者
- 结构体较小且不需要修改时
- 方法是纯函数时
- 基本类型的自定义类型时

### 7.3 字段命名规范
- 公开字段首字母大写（如：`ID`, `Word`）
- 私有字段首字母小写（如：`log`, `biz`）
- 使用有意义的字段名

### 7.4 结构体标签使用
- 合理使用标签进行序列化控制
- 数据库映射使用GORM标签
- API响应使用JSON标签

这些结构体和方法的概念是Go语言面向对象编程的基础，通过组合而非继承的方式实现代码复用。