# Go语言常量和变量详解

## 1. 变量基础

### 1.1 变量声明
Go语言提供多种变量声明方式：

```go
// 方式1：var关键字声明
var name string
var age int
var isActive bool

// 方式2：声明并初始化
var name string = "Alice"
var age int = 30

// 方式3：类型推断
var name = "Alice"  // 自动推断为string类型
var age = 30        // 自动推断为int类型

// 方式4：短变量声明（只能在函数内使用）
name := "Alice"
age := 30
```

### 1.2 项目中的变量使用
**位置**: `internal/biz/biz.correct.go:119`
```go
biz := ""
```

**位置**: `internal/service/ai.go:44-48`
```go
base := map[string]interface{}{
    "tal_id":         traceId,
    "device_sn":      sn,
    "channel_source": "口算批改",
}
```

**知识点解析**:
- `biz := ""` - 短变量声明，初始化为空字符串
- `base := map[...]` - 声明并初始化map类型变量
- Go编译器自动推断变量类型

### 1.3 多变量声明
```go
// 同类型多变量声明
var a, b, c int

// 不同类型多变量声明
var (
    name string
    age  int
    addr string
)

// 多变量初始化
var a, b, c = 1, 2, 3
x, y, z := 10, 20, 30
```

## 2. 变量的零值

### 2.1 Go语言的零值概念
Go语言中每种类型都有零值（zero value）：

```go
var i int        // 0
var f float64    // 0.0
var b bool       // false
var s string     // ""
var p *int       // nil
var slice []int  // nil
var m map[string]int // nil
var ch chan int  // nil
var fn func()    // nil
```

### 2.2 结构体的零值
```go
type Person struct {
    Name string
    Age  int
}

var p Person  // {Name: "", Age: 0}
```

## 3. 常量基础

### 3.1 常量声明
**位置**: `internal/data/model/hw_en_word.go:9-15`
```go
const (
    EnWordStatusWaitAudit EnWordStatus = iota + 1
    EnWordStatusWaitShelf
    EnWordStatusShelf
    EnWordStatusUnShelf
    EnWordStatusCurse
)
```

**知识点解析**:
- `const` 关键字声明常量
- `iota` 常量生成器，自动递增
- `iota + 1` 从1开始而不是0

### 3.2 常量的类型
```go
// 无类型常量
const Pi = 3.14159
const Message = "Hello, World!"

// 有类型常量
const Pi float64 = 3.14159
const Count int = 100

// 常量组
const (
    Red   = 0
    Green = 1
    Blue  = 2
)
```

### 3.3 iota常量生成器
**位置**: `internal/data/model/hw_en_word.go:9-15`
```go
type EnWordStatus uint8

const (
    EnWordStatusWaitAudit EnWordStatus = iota + 1  // 1
    EnWordStatusWaitShelf                          // 2
    EnWordStatusShelf                              // 3
    EnWordStatusUnShelf                            // 4
    EnWordStatusCurse                              // 5
)
```

**更多iota示例**:
```go
const (
    _  = iota      // 0，使用空标识符忽略
    KB = 1 << (10 * iota)  // 1024
    MB             // 1048576
    GB             // 1073741824
)

const (
    Sunday = iota  // 0
    Monday         // 1
    Tuesday        // 2
    Wednesday      // 3
    Thursday       // 4
    Friday         // 5
    Saturday       // 6
)
```

## 4. 变量作用域

### 4.1 包级别变量
**位置**: `internal/biz/biz.correct.go:151-155`
```go
var TaskDriveMap = map[string]string{
    "snap word":  "novice_task_5",
    "read along": "novice_task_6",
    "snap math":  "novice_task_7",
}
```

**知识点解析**:
- 包级别变量在整个包中可见
- 首字母大写的变量可以被其他包访问
- 首字母小写的变量只能在当前包内访问

### 4.2 函数级别变量
```go
func example() {
    var localVar int = 10  // 函数级别变量
    
    if true {
        var blockVar int = 20  // 块级别变量
        fmt.Println(localVar)  // 可以访问
        fmt.Println(blockVar)  // 可以访问
    }
    
    // fmt.Println(blockVar)  // 编译错误：超出作用域
}
```

### 4.3 变量遮蔽(Shadowing)
```go
var global = "global"

func example() {
    var global = "function"  // 遮蔽包级别变量
    fmt.Println(global)      // 输出: function
    
    {
        var global = "block"  // 遮蔽函数级别变量
        fmt.Println(global)   // 输出: block
    }
    
    fmt.Println(global)      // 输出: function
}
```

## 5. 指针变量

### 5.1 指针变量声明
```go
var p *int        // 声明int指针，零值为nil
var x int = 42
p = &x           // p指向x的地址

fmt.Println(*p)  // 42，解引用获取值
*p = 100         // 通过指针修改值
fmt.Println(x)   // 100
```

### 5.2 项目中的指针变量
**位置**: `internal/service/ai.go:17-21`
```go
type AiService struct {
    pb.UnimplementedAiServer
    correct  *biz.CorrectUseCase  // 指针类型字段
    log      *log.Helper
    question *biz.JzxQuestionUseCase
    edu      *biz.EduUseCase
    login    *biz.CodeLoginUseCase
}
```

## 6. 类型转换

### 6.1 基本类型转换
```go
var i int = 42
var f float64 = float64(i)  // int转float64
var u uint = uint(i)        // int转uint

// 字符串转换
s := string(65)             // "A"
```

### 6.2 使用strconv包转换
```go
import "strconv"

// 字符串和数字转换
i, err := strconv.Atoi("123")        // 字符串转int
s := strconv.Itoa(123)               // int转字符串
f, err := strconv.ParseFloat("3.14", 64)  // 字符串转float64
```

### 6.3 使用cast库转换
**位置**: `internal/pkg/custom_context/ctx_key.go:17, 85`
```go
import "github.com/spf13/cast"

func GetDeviceId(ctx context.Context) (deviceId string) {
    return cast.ToString(ctx.Value(common.JwtDeviceId))
}

func GetTalIdType(ctx context.Context) (talIdType int) {
    return cast.ToInt(ctx.Value(common.TalIdType))
}
```

## 7. 变量的生命周期

### 7.1 栈变量
```go
func stackExample() {
    var x int = 10  // 栈变量，函数结束时销毁
    fmt.Println(x)
}
```

### 7.2 堆变量
```go
func heapExample() *int {
    var x int = 10  // 因为返回了地址，会分配到堆上
    return &x       // Go编译器会进行逃逸分析
}
```

## 8. 常量的特殊用法

### 8.1 常量表达式
```go
const (
    Size = 1024
    Mask = Size - 1     // 1023
    Max  = Size * 2     // 2048
)
```

### 8.2 字符串常量
```go
const (
    StatusOK    = "OK"
    StatusError = "ERROR"
    StatusPending = "PENDING"
)
```

### 8.3 常量用于数组大小
```go
const ArraySize = 10
var arr [ArraySize]int  // 使用常量定义数组大小
```

## 9. 变量初始化

### 9.1 包级别变量初始化顺序
```go
var (
    a = b + c   // 依赖b和c
    b = f()     // 依赖f()的返回值
    c = 1       // 独立的常量
)

func f() int {
    return 2
}
// 初始化顺序：c -> b -> a
```

### 9.2 init函数
```go
var global int

func init() {
    global = computeInitialValue()
}

func computeInitialValue() int {
    // 复杂的初始化逻辑
    return 42
}
```

## 10. 变量的最佳实践

### 10.1 变量命名规范
```go
// 好的命名
var userCount int
var maxRetryAttempts int
var isAuthenticated bool

// 不好的命名
var n int
var x bool
var data interface{}
```

### 10.2 变量声明位置
```go
// 在使用前声明
func processUser(userID string) error {
    // 在需要时声明变量
    user, err := getUserByID(userID)
    if err != nil {
        return err
    }
    
    // 处理user...
    return nil
}
```

### 10.3 避免变量遮蔽
```go
// 容易出错的代码
func problematic() error {
    var err error
    
    if someCondition {
        data, err := fetchData()  // 这里的err是新变量
        if err != nil {
            return err
        }
        // 使用data...
    }
    
    return err  // 这里返回的是外层的err（nil）
}

// 正确的写法
func correct() error {
    var err error
    var data SomeType
    
    if someCondition {
        data, err = fetchData()  // 使用外层的err
        if err != nil {
            return err
        }
        // 使用data...
    }
    
    return err
}
```

## 11. 常见错误和陷阱

### 11.1 短变量声明的陷阱
```go
var err error

if someCondition {
    data, err := fetchData()  // 这里声明了新的err变量
    // ...
}

// 外层的err仍然是nil
```

### 11.2 循环变量的陷阱
```go
// 错误的做法
var funcs []func()
for i := 0; i < 3; i++ {
    funcs = append(funcs, func() {
        fmt.Println(i)  // 所有函数都会打印3
    })
}

// 正确的做法
var funcs []func()
for i := 0; i < 3; i++ {
    i := i  // 创建新的变量
    funcs = append(funcs, func() {
        fmt.Println(i)  // 打印0, 1, 2
    })
}
```

### 11.3 nil指针解引用
```go
var p *int
// *p = 10  // panic: runtime error: invalid memory address

// 正确的做法
var x int
p = &x
*p = 10  // 安全
```

## 12. 性能考虑

### 12.1 变量重用
```go
// 重用切片避免重复分配
var buffer []byte

func processData(data string) {
    buffer = buffer[:0]  // 重置长度但保留容量
    buffer = append(buffer, data...)
    // 处理buffer...
}
```

### 12.2 避免不必要的变量
```go
// 不好的做法
func calculate() int {
    temp := expensive_operation()
    result := temp * 2
    return result
}

// 更好的做法
func calculate() int {
    return expensive_operation() * 2
}
```

常量和变量是Go语言的基础概念，正确理解和使用它们对于编写高质量的Go代码至关重要。