# Go语言映射(Map)详解

## 1. Map基础

### 1.1 Map的定义和创建
Map是Go语言中的键值对数据结构，类似于Java中的HashMap：

```go
// 声明map
var m map[string]int

// 使用make创建map
m = make(map[string]int)

// 直接创建并初始化
m2 := map[string]int{
    "apple":  5,
    "banana": 3,
    "orange": 8,
}

// 创建空map
m3 := make(map[string]int)
```

### 1.2 项目中的Map使用
**位置**: `internal/service/ai.go:44-48`
```go
base := map[string]interface{}{
    "tal_id":         traceId,
    "device_sn":      sn,
    "channel_source": "口算批改",
}
```

**知识点解析**:
- `map[string]interface{}` - 键为string，值为interface{}的map
- `interface{}` 可以存储任何类型的值
- 使用字面量语法初始化map

### 1.3 更多项目中的Map示例
**位置**: `internal/biz/biz.correct.go:151-155`
```go
var TaskDriveMap = map[string]string{
    "snap word":  "novice_task_5",
    "read along": "novice_task_6",
    "snap math":  "novice_task_7",
}
```

**知识点解析**:
- 全局变量map的定义
- `map[string]string` - 键值都是string类型
- 用于配置映射关系

## 2. Map的基本操作

### 2.1 添加和修改元素
```go
m := make(map[string]int)
m["key1"] = 100        // 添加元素
m["key1"] = 200        // 修改元素（覆盖原值）
```

### 2.2 获取元素
```go
value := m["key1"]     // 获取值，如果key不存在返回零值

// 安全获取（推荐方式）
value, ok := m["key1"]
if ok {
    fmt.Printf("key1的值是: %d\n", value)
} else {
    fmt.Println("key1不存在")
}
```

### 2.3 删除元素
```go
delete(m, "key1")      // 删除键为"key1"的元素
```

### 2.4 检查键是否存在
**位置**: `internal/biz/biz.correct.go:158`
```go
if _, ok := TaskDriveMap[app]; !ok {
    uc.log.WithContext(ctx).Errorf("TaskDrive 不支持此app: %v", app)
    return &pb.TaskDriveResp{}, pb.ErrorHwPaasDefaultErr("不支持此app")
}
```

**知识点解析**:
- `_, ok := map[key]` - 检查键是否存在的标准模式
- `ok` 为true表示键存在，false表示不存在
- 使用空标识符`_`忽略值，只关心键是否存在

## 3. Map的遍历

### 3.1 遍历键值对
```go
m := map[string]int{
    "apple":  5,
    "banana": 3,
    "orange": 8,
}

// 遍历键值对
for key, value := range m {
    fmt.Printf("key: %s, value: %d\n", key, value)
}

// 只遍历键
for key := range m {
    fmt.Printf("key: %s\n", key)
}

// 只遍历值
for _, value := range m {
    fmt.Printf("value: %d\n", value)
}
```

### 3.2 Map遍历的随机性
```go
// Map的遍历顺序是随机的，不要依赖遍历顺序
m := map[string]int{"a": 1, "b": 2, "c": 3}
for k, v := range m {
    fmt.Printf("%s: %d\n", k, v)  // 每次运行顺序可能不同
}
```

## 4. Map的零值和nil

### 4.1 nil map
```go
var m map[string]int  // m是nil map
fmt.Println(m == nil) // true

// 不能向nil map写入数据
// m["key"] = 1  // 这会panic

// 但可以从nil map读取数据
value := m["key"]     // 返回零值0
value, ok := m["key"] // value=0, ok=false
```

### 4.2 空map vs nil map
```go
var nilMap map[string]int           // nil map
emptyMap := make(map[string]int)    // 空map，但不是nil

fmt.Println(nilMap == nil)   // true
fmt.Println(emptyMap == nil) // false
fmt.Println(len(nilMap))     // 0
fmt.Println(len(emptyMap))   // 0
```

## 5. Map作为函数参数

### 5.1 Map是引用类型
```go
func modifyMap(m map[string]int) {
    m["new_key"] = 100  // 修改会影响原map
}

func main() {
    m := map[string]int{"a": 1}
    modifyMap(m)
    fmt.Println(m)  // map[a:1 new_key:100]
}
```

### 5.2 检查map参数
```go
func safeMapOperation(m map[string]int) {
    if m == nil {
        fmt.Println("map is nil")
        return
    }
    
    // 安全操作map
    m["key"] = 100
}
```

## 6. 复杂Map类型

### 6.1 嵌套Map
```go
// map的值也是map
userRoles := map[string]map[string]bool{
    "user1": {
        "admin": true,
        "read":  true,
        "write": false,
    },
    "user2": {
        "admin": false,
        "read":  true,
        "write": true,
    },
}

// 访问嵌套map
if roles, ok := userRoles["user1"]; ok {
    if isAdmin, exists := roles["admin"]; exists && isAdmin {
        fmt.Println("user1 is admin")
    }
}
```

### 6.2 Map的值为切片
```go
// map的值为切片
groups := map[string][]string{
    "admins": {"alice", "bob"},
    "users":  {"charlie", "david", "eve"},
}

// 添加元素到切片
groups["admins"] = append(groups["admins"], "frank")
```

### 6.3 Map的值为结构体
```go
type User struct {
    Name string
    Age  int
}

users := map[int]User{
    1: {Name: "Alice", Age: 30},
    2: {Name: "Bob", Age: 25},
}

// 修改结构体字段（注意：不能直接修改）
// users[1].Age = 31  // 编译错误

// 正确的修改方式
user := users[1]
user.Age = 31
users[1] = user
```

## 7. Map的并发安全

### 7.1 Map不是并发安全的
```go
// 危险：并发读写map会导致panic
m := make(map[string]int)

go func() {
    for {
        m["key"] = 1  // 写操作
    }
}()

go func() {
    for {
        _ = m["key"]  // 读操作
    }
}()
```

### 7.2 使用sync.Map
```go
import "sync"

var m sync.Map

// 存储
m.Store("key", "value")

// 加载
value, ok := m.Load("key")
if ok {
    fmt.Println(value.(string))
}

// 删除
m.Delete("key")

// 遍历
m.Range(func(key, value interface{}) bool {
    fmt.Printf("%s: %s\n", key.(string), value.(string))
    return true  // 继续遍历
})
```

### 7.3 使用互斥锁保护Map
```go
type SafeMap struct {
    mu sync.RWMutex
    m  map[string]int
}

func (sm *SafeMap) Set(key string, value int) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    sm.m[key] = value
}

func (sm *SafeMap) Get(key string) (int, bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    value, ok := sm.m[key]
    return value, ok
}
```

## 8. Map的性能特性

### 8.1 时间复杂度
- 查找：平均O(1)，最坏O(n)
- 插入：平均O(1)，最坏O(n)
- 删除：平均O(1)，最坏O(n)

### 8.2 内存使用
```go
// 预分配容量（Go 1.14+）
m := make(map[string]int, 1000)  // 预分配空间给1000个元素
```

### 8.3 Map的扩容
Go的map会自动扩容，当负载因子过高时会重新哈希：

```go
m := make(map[int]int)
for i := 0; i < 1000000; i++ {
    m[i] = i  // map会自动扩容
}
```

## 9. Map的实际应用场景

### 9.1 缓存实现
```go
type Cache struct {
    data map[string]interface{}
    mu   sync.RWMutex
}

func (c *Cache) Get(key string) (interface{}, bool) {
    c.mu.RLock()
    defer c.mu.RUnlock()
    value, ok := c.data[key]
    return value, ok
}

func (c *Cache) Set(key string, value interface{}) {
    c.mu.Lock()
    defer c.mu.Unlock()
    c.data[key] = value
}
```

### 9.2 计数器
```go
func countWords(text string) map[string]int {
    words := strings.Fields(text)
    counts := make(map[string]int)
    
    for _, word := range words {
        counts[word]++  // 自动初始化为0然后加1
    }
    
    return counts
}
```

### 9.3 去重
```go
func removeDuplicates(slice []string) []string {
    seen := make(map[string]bool)
    result := make([]string, 0)
    
    for _, item := range slice {
        if !seen[item] {
            seen[item] = true
            result = append(result, item)
        }
    }
    
    return result
}
```

## 10. Map的最佳实践

### 10.1 键的选择
- 键类型必须是可比较的（支持==和!=操作）
- 不能使用切片、map、函数作为键
- 字符串、数字、布尔值、数组、结构体（字段都可比较）可以作为键

### 10.2 值的nil检查
```go
func safeMapAccess(m map[string]*User, key string) *User {
    if m == nil {
        return nil
    }
    
    user, ok := m[key]
    if !ok {
        return nil
    }
    
    return user
}
```

### 10.3 Map的复制
```go
// 浅复制
func copyMap(original map[string]int) map[string]int {
    copy := make(map[string]int)
    for k, v := range original {
        copy[k] = v
    }
    return copy
}
```

### 10.4 清空Map
```go
// 方法1：重新创建
m = make(map[string]int)

// 方法2：逐个删除
for k := range m {
    delete(m, k)
}
```

## 11. 常见错误和陷阱

### 11.1 向nil map写入
```go
var m map[string]int
m["key"] = 1  // panic: assignment to entry in nil map
```

### 11.2 并发访问
```go
// 错误：并发读写
go func() { m["key"] = 1 }()
go func() { _ = m["key"] }()  // 可能panic
```

### 11.3 Map的比较
```go
m1 := map[string]int{"a": 1}
m2 := map[string]int{"a": 1}
// fmt.Println(m1 == m2)  // 编译错误：map不能直接比较
```

Map是Go语言中非常重要的数据结构，理解其特性和正确使用方式对于编写高效、安全的Go程序至关重要。