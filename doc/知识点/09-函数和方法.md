# Go语言函数和方法详解

## 1. 函数基础

### 1.1 函数定义语法
Go语言函数的基本语法：
```go
func functionName(parameter1 type1, parameter2 type2) (returnType1, returnType2) {
    // 函数体
    return value1, value2
}
```

### 1.2 项目中的函数示例
**位置**: `internal/biz/biz.correct.go:42-50`
```go
func NewCorrectUseCase(conf *conf.Biz, logger log.Logger, ftDao *dao.FeedTraceDao, services *services.Services) *CorrectUseCase {
    return &CorrectUseCase{
        log:           log.NewHelper(logger),
        biz:           conf,
        kousuanClient: kousuan.NewClient(conf, logger),
        ftDao:         ftDao,
        services:      services,
    }
}
```

**知识点解析**:
- 函数名`NewCorrectUseCase`（首字母大写，公开函数）
- 多个参数：`conf *conf.Biz, logger log.Logger, ftDao *dao.FeedTraceDao, services *services.Services`
- 返回类型：`*CorrectUseCase`
- 构造函数模式：`NewXxx`命名约定

### 1.3 多返回值函数
**位置**: `internal/biz/biz.correct.go:52`
```go
func (uc *CorrectUseCase) QueryCorrect(ctx context.Context, imageUrl string) (res interface{}, err error) {
    // 函数实现...
    return correct, nil
}
```

**知识点解析**:
- 命名返回值：`(res interface{}, err error)`
- Go语言支持多返回值
- 错误处理模式：最后一个返回值通常是error

## 2. 函数参数

### 2.1 值传递
```go
func modifyValue(x int) {
    x = 100  // 不会影响原始值
}

func main() {
    a := 10
    modifyValue(a)
    fmt.Println(a)  // 输出: 10
}
```

### 2.2 指针传递
**位置**: `internal/service/ai.go:24-30`
```go
func NewAiService(
    correct *biz.CorrectUseCase,    // 指针参数
    question *biz.JzxQuestionUseCase,
    edu *biz.EduUseCase,
    logger log.Logger,              // 接口参数
    login *biz.CodeLoginUseCase,
) *AiService {
    // ...
}
```

**知识点解析**:
- 指针参数避免大对象复制
- 允许函数修改原始数据
- 接口参数提供多态性

### 2.3 可变参数
```go
func sum(numbers ...int) int {
    total := 0
    for _, num := range numbers {
        total += num
    }
    return total
}

// 调用
result := sum(1, 2, 3, 4, 5)
```

## 3. 方法(Methods)

### 3.1 方法定义
**位置**: `internal/service/ai.go:40`
```go
func (s *AiService) QueryCorrect(ctx context.Context, req *pb.QueryCorrectRequest) (*structpb.Struct, error) {
    traceId := custom_context.GetTraceId(ctx)
    sn := custom_context.GetDeviceId(ctx)
    // ...
}
```

**知识点解析**:
- `(s *AiService)` - 接收者(receiver)
- 方法是绑定到特定类型的函数
- 通过接收者访问结构体字段

### 3.2 值接收者 vs 指针接收者
**位置**: `internal/data/model/hw_en_word.go:42-45`
```go
// 值接收者
func (HwEnWord) TableName() string {
    return "hw_en_word"
}
```

**位置**: `internal/service/ai.go:40`
```go
// 指针接收者
func (s *AiService) QueryCorrect(ctx context.Context, req *pb.QueryCorrectRequest) (*structpb.Struct, error) {
    // ...
}
```

**知识点解析**:
- 值接收者：操作副本，不能修改原始数据
- 指针接收者：操作原始数据，可以修改字段
- 大结构体推荐使用指针接收者

## 4. 函数类型和变量

### 4.1 函数作为类型
```go
type Handler func(ctx context.Context, req interface{}) (interface{}, error)

func processRequest(h Handler, ctx context.Context, req interface{}) (interface{}, error) {
    return h(ctx, req)
}
```

### 4.2 函数作为参数
**位置**: `internal/pkg/sync/go.go:10`
```go
func Go(ctx context.Context, log *log.Helper, f func()) {
    go func() {
        defer func() {
            if err := recover(); err != nil {
                log.WithContext(ctx).Errorf("go routine panic错误：%v\n %s", err, debug.Stack())
                return
            }
        }()
        f()  // 执行传入的函数
    }()
}
```

**知识点解析**:
- `f func()` - 函数类型参数
- 函数可以作为参数传递
- 支持高阶函数编程

## 5. 匿名函数和闭包

### 5.1 匿名函数
**位置**: `internal/data/data.go:80-91`
```go
go func() {  // 匿名函数
    defer func() {
        if r := recover(); r != nil {
            log.Errorf("metricRedisPoolStats panic: %v", r)
        }
    }()
    for range time.Tick(time.Second * 5) {
        stats := rdb.PoolStats()
        biz_metrics.RedisPoolStats(stats.Hits, stats.Misses, stats.Timeouts, stats.TotalConns, stats.IdleConns, stats.StaleConns)
    }
}()
```

**知识点解析**:
- `func() { ... }()` - 定义并立即调用匿名函数
- 常用于goroutine启动
- 可以访问外部变量（闭包特性）

### 5.2 闭包
**位置**: `internal/biz/biz.correct.go:60-67`
```go
defer func() {
    gCtx := util.NewTraceContext(nil, traceId)  // 访问外部变量traceId
    sync.Go(gCtx, uc.log, func() {
        traceB, _ := jsoniter.Marshal(traceLog)  // 访问外部变量traceLog
        uc.log.WithContext(gCtx).Info(common.MakeLogBackFlowMsgInfo(traceId, "kousuan_query_correct", "dw_query_correct_trace", string(traceB)))
        return
    })
}()
```

**知识点解析**:
- 闭包可以访问外部作用域的变量
- 变量的生命周期会延长
- 常用于回调函数和异步处理

## 6. defer语句

### 6.1 defer基础
**位置**: `internal/biz/biz.correct.go:101-103`
```go
defer func() {
    _ = resp.Body.Close()
}()
```

**知识点解析**:
- `defer` 延迟执行，函数返回前执行
- 常用于资源清理
- 多个defer按LIFO顺序执行

### 6.2 defer的执行时机
```go
func example() {
    defer fmt.Println("1")
    defer fmt.Println("2")
    defer fmt.Println("3")
    fmt.Println("function body")
}
// 输出顺序：function body, 3, 2, 1
```

### 6.3 defer与返回值
```go
func deferReturn() (result int) {
    defer func() {
        result++  // 修改命名返回值
    }()
    return 5  // 实际返回6
}
```

## 7. 错误处理函数

### 7.1 错误返回模式
**位置**: `internal/data/data.go:28-32`
```go
func NewData(conf *conf.Data, logger log.Logger) (*Data, func(), error) {
    db, err := gorm.Open(mysql.Open(conf.Database.Source), &gorm.Config{})
    if err != nil {
        return nil, nil, err  // 错误时返回nil和错误
    }
    // ...
}
```

### 7.2 错误处理函数
```go
func handleError(err error, message string) {
    if err != nil {
        log.Printf("%s: %v", message, err)
    }
}

func processFile(filename string) error {
    file, err := os.Open(filename)
    if err != nil {
        return fmt.Errorf("failed to open file %s: %w", filename, err)
    }
    defer file.Close()
    
    // 处理文件...
    return nil
}
```

## 8. 函数的高级特性

### 8.1 方法表达式
```go
type Calculator struct {
    value int
}

func (c Calculator) Add(x int) int {
    return c.value + x
}

// 方法表达式
add := Calculator.Add
result := add(Calculator{value: 10}, 5)  // 15
```

### 8.2 方法值
```go
calc := Calculator{value: 10}
add := calc.Add  // 方法值
result := add(5)  // 15
```

## 9. 函数的性能优化

### 9.1 内联函数
```go
// 简单函数可能被编译器内联
func add(a, b int) int {
    return a + b
}
```

### 9.2 避免不必要的函数调用
```go
// 低效
for i := 0; i < len(slice); i++ {
    // len(slice)在每次迭代时都会调用
}

// 高效
length := len(slice)
for i := 0; i < length; i++ {
    // len只调用一次
}
```

## 10. 函数测试

### 10.1 单元测试
```go
func TestAdd(t *testing.T) {
    result := add(2, 3)
    if result != 5 {
        t.Errorf("Expected 5, got %d", result)
    }
}
```

### 10.2 基准测试
```go
func BenchmarkAdd(b *testing.B) {
    for i := 0; i < b.N; i++ {
        add(2, 3)
    }
}
```

## 11. 函数的最佳实践

### 11.1 函数命名
- 使用动词或动词短语
- 公开函数首字母大写
- 私有函数首字母小写
- 构造函数使用`NewXxx`模式

### 11.2 函数设计原则
1. **单一职责**：每个函数只做一件事
2. **参数数量**：避免过多参数（建议不超过3-4个）
3. **返回值**：使用命名返回值提高可读性
4. **错误处理**：始终检查和处理错误

### 11.3 函数文档
```go
// QueryCorrect 查询口算批改结果
// 参数:
//   ctx: 上下文
//   imageUrl: 图片URL
// 返回:
//   res: 批改结果
//   err: 错误信息
func (uc *CorrectUseCase) QueryCorrect(ctx context.Context, imageUrl string) (res interface{}, err error) {
    // 实现...
}
```

## 12. 常见错误和陷阱

### 12.1 defer中的循环变量
```go
// 错误
for i := 0; i < 3; i++ {
    defer fmt.Println(i)  // 都会打印3
}

// 正确
for i := 0; i < 3; i++ {
    defer func(x int) {
        fmt.Println(x)
    }(i)
}
```

### 12.2 方法接收者的选择
```go
// 错误：大结构体使用值接收者
type LargeStruct struct {
    data [1000]int
}

func (ls LargeStruct) Process() {  // 会复制整个结构体
    // ...
}

// 正确：使用指针接收者
func (ls *LargeStruct) Process() {
    // ...
}
```

### 12.3 函数返回值的陷阱
```go
func problematic() (err error) {
    if someCondition {
        err := someFunction()  // 声明了新的局部变量err
        if err != nil {
            return  // 返回的是命名返回值err（nil），不是局部变量err
        }
    }
    return
}
```

函数和方法是Go语言的核心特性，理解它们的工作原理和最佳实践对于编写高质量的Go代码至关重要。