# Go语言并发编程详解

## 1. Goroutine基础

### 1.1 启动Goroutine
**位置**: `internal/data/data.go:80-91`
```go
func metricRedisPoolStats(rdb *redis.Client) {
    go func() {  // 启动一个新的goroutine
        defer func() {
            if r := recover(); r != nil {
                log.Errorf("metricRedisPoolStats panic: %v", r)
            }
        }()
        for range time.Tick(time.Second * 5) {  // 每5秒执行一次
            stats := rdb.PoolStats()
            biz_metrics.RedisPoolStats(stats.Hits, stats.Misses, stats.Timeouts, stats.TotalConns, stats.IdleConns, stats.StaleConns)
        }
    }()
}
```

**知识点解析**:
- `go func() { ... }()` - 启动匿名函数作为goroutine
- goroutine是Go的轻量级线程
- 比操作系统线程更轻量，可以创建数百万个
- 由Go运行时调度器管理

### 1.2 Goroutine的生命周期
```go
func main() {
    // 主goroutine
    go worker()  // 启动工作goroutine
    
    // 主goroutine结束时，所有其他goroutine也会结束
    time.Sleep(time.Second)  // 等待工作goroutine完成
}

func worker() {
    fmt.Println("Worker goroutine running")
}
```

## 2. 定时器和周期任务

### 2.1 使用time.Tick创建周期任务
**位置**: `internal/data/data.go:86`
```go
for range time.Tick(time.Second * 5) {
    stats := rdb.PoolStats()
    biz_metrics.RedisPoolStats(stats.Hits, stats.Misses, stats.Timeouts, stats.TotalConns, stats.IdleConns, stats.StaleConns)
}
```

**知识点解析**:
- `time.Tick(duration)` - 返回一个通道，定期发送时间值
- `for range` - 循环接收通道中的值
- 创建周期性执行的任务
- 注意：time.Tick会导致内存泄漏，生产环境建议使用time.NewTicker

### 2.2 更安全的定时器使用
```go
func betterMetricRedisPoolStats(rdb *redis.Client) {
    ticker := time.NewTicker(time.Second * 5)
    defer ticker.Stop()  // 确保释放资源
    
    go func() {
        for {
            select {
            case <-ticker.C:
                stats := rdb.PoolStats()
                // 处理统计信息
            case <-ctx.Done():  // 支持取消
                return
            }
        }
    }()
}
```

## 3. 自定义并发工具

### 3.1 自定义Go函数
**位置**: `internal/pkg/sync/go.go:10-20`
```go
// Go start routine with recover
func Go(ctx context.Context, log *log.Helper, f func()) {
    go func() {
        defer func() {
            if err := recover(); err != nil {
                log.WithContext(ctx).Errorf("go routine panic错误：%v\n %s", err, debug.Stack())
                return
            }
        }()
        f()
    }()
}
```

**知识点解析**:
- 封装了goroutine启动逻辑
- 自动处理panic恢复
- `debug.Stack()` - 获取调用栈信息
- 提供统一的错误处理和日志记录

### 3.2 自定义Go函数的使用
**位置**: `internal/biz/biz.correct.go:62-66`
```go
defer func() {
    gCtx := util.NewTraceContext(nil, traceId)
    sync.Go(gCtx, uc.log, func() {
        traceB, _ := jsoniter.Marshal(traceLog)
        uc.log.WithContext(gCtx).Info(common.MakeLogBackFlowMsgInfo(traceId, "kousuan_query_correct", "dw_query_correct_trace", string(traceB)))
        return
    })
}()
```

**知识点解析**:
- 在defer中启动异步任务
- 传递上下文和日志器
- 异步记录追踪日志，不阻塞主流程