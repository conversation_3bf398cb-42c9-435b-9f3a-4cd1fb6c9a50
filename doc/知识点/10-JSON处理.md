# Go语言JSON处理详解

## 1. JSON基础

### 1.1 JSON序列化(Marshal)
**位置**: `internal/biz/biz.correct.go:49`
```go
baseJson, _ := jsoniter.Marshal(base)
```

**位置**: `internal/biz/biz.correct.go:63`
```go
traceB, _ := jsoniter.Marshal(traceLog)
```

**知识点解析**:
- 使用`jsoniter`库进行JSON序列化
- `jsoniter`是`encoding/json`的高性能替代品
- `Marshal`将Go数据结构转换为JSON字节数组

### 1.2 标准库JSON处理
**位置**: `internal/biz/biz.correct.go:4, 131-132, 145`
```go
import (
    "encoding/json"  // 标准库JSON包
)

// JSON反序列化
feedback := &Feedback{}
_ = json.Unmarshal([]byte(req.Feedback), feedback)

// JSON序列化
commonConfig, _ := json.Marshal(uc.biz.CommonConfig)
```

**知识点解析**:
- `json.Unmarshal` - JSON反序列化
- `json.Marshal` - JSON序列化
- 处理字节切片和Go结构体之间的转换

## 2. 结构体标签(Struct Tags)

### 2.1 JSON标签的使用
**位置**: `internal/data/model/hw_en_word.go:19-40`
```go
type HwEnWord struct {
    ID          int64        `gorm:"primary_key;AUTO_INCREMENT;column:id" json:"id"`
    Word        string       `gorm:"type:varchar(50);not null;default:'';column:word" json:"word"`
    British     string       `gorm:"type:varchar(10);not null;default:'';column:british" json:"british"`
    American    string       `gorm:"type:varchar(10);not null;default:'';column:american" json:"american"`
    Meanings    string       `gorm:"type:varchar(2048);not null;default:'';column:meanings" json:"meanings"`
    // ...
}
```

**知识点解析**:
- `json:"id"` - 指定JSON字段名
- 如果不指定，默认使用字段名
- 支持多种标签选项

### 2.2 JSON标签选项
```go
type User struct {
    ID       int    `json:"id"`                    // 普通映射
    Name     string `json:"name,omitempty"`        // 空值时忽略
    Password string `json:"-"`                     // 忽略该字段
    Email    string `json:"email_address"`         // 自定义字段名
    Age      int    `json:"age,string"`            // 转换为字符串
}
```

### 2.3 复杂结构体的JSON标签
**位置**: `internal/biz/biz.correct.go:30-36`
```go
type traceCorrectResult struct {
    TraceID  string      `json:"trace_id"`   // 下划线命名
    UserId   string      `json:"user_id"`    // 下划线命名
    DeviceId string      `json:"device_id"`  // 下划线命名
    ImageUrl string      `json:"image_url"`  // 下划线命名
    Resp     interface{} `json:"resp"`       // 接口类型
}
```

## 3. JSON序列化详解

### 3.1 基本类型序列化
```go
// 基本类型
num := 42
str := "hello"
flag := true

numJson, _ := json.Marshal(num)   // "42"
strJson, _ := json.Marshal(str)   // "\"hello\""
flagJson, _ := json.Marshal(flag) // "true"
```

### 3.2 复合类型序列化
**位置**: `internal/service/ai.go:44-48`
```go
base := map[string]interface{}{
    "tal_id":         traceId,
    "device_sn":      sn,
    "channel_source": "口算批改",
}
baseJson, _ := jsoniter.Marshal(base)
```

**知识点解析**:
- `map[string]interface{}` - 常用的JSON对象表示
- `interface{}` 可以存储任何类型的值
- 序列化后生成JSON对象

### 3.3 切片和数组序列化
```go
slice := []string{"apple", "banana", "cherry"}
sliceJson, _ := json.Marshal(slice)
// 结果: ["apple","banana","cherry"]

array := [3]int{1, 2, 3}
arrayJson, _ := json.Marshal(array)
// 结果: [1,2,3]
```

## 4. JSON反序列化详解

### 4.1 反序列化到结构体
**位置**: `internal/biz/biz.correct.go:131-132`
```go
feedback := &Feedback{}
_ = json.Unmarshal([]byte(req.Feedback), feedback)
```

### 4.2 反序列化到map
```go
jsonStr := `{"name":"Alice","age":30,"city":"New York"}`
var result map[string]interface{}
err := json.Unmarshal([]byte(jsonStr), &result)

// 访问数据
name := result["name"].(string)
age := result["age"].(float64)  // JSON数字默认解析为float64
```

### 4.3 反序列化到切片
```go
jsonStr := `["apple","banana","cherry"]`
var fruits []string
err := json.Unmarshal([]byte(jsonStr), &fruits)
```

## 5. 自定义JSON序列化

### 5.1 实现MarshalJSON接口
```go
type Time struct {
    time.Time
}

func (t Time) MarshalJSON() ([]byte, error) {
    return json.Marshal(t.Format("2006-01-02 15:04:05"))
}

func (t *Time) UnmarshalJSON(data []byte) error {
    var timeStr string
    if err := json.Unmarshal(data, &timeStr); err != nil {
        return err
    }
    
    parsedTime, err := time.Parse("2006-01-02 15:04:05", timeStr)
    if err != nil {
        return err
    }
    
    t.Time = parsedTime
    return nil
}
```

### 5.2 自定义枚举类型的JSON处理
```go
type Status int

const (
    StatusPending Status = iota
    StatusRunning
    StatusCompleted
    StatusFailed
)

var statusNames = map[Status]string{
    StatusPending:   "pending",
    StatusRunning:   "running",
    StatusCompleted: "completed",
    StatusFailed:    "failed",
}

func (s Status) MarshalJSON() ([]byte, error) {
    return json.Marshal(statusNames[s])
}

func (s *Status) UnmarshalJSON(data []byte) error {
    var name string
    if err := json.Unmarshal(data, &name); err != nil {
        return err
    }
    
    for status, statusName := range statusNames {
        if statusName == name {
            *s = status
            return nil
        }
    }
    
    return fmt.Errorf("unknown status: %s", name)
}
```

## 6. JSON流处理

### 6.1 JSON编码器(Encoder)
```go
func writeJSON(w io.Writer, data interface{}) error {
    encoder := json.NewEncoder(w)
    encoder.SetIndent("", "  ")  // 设置缩进
    return encoder.Encode(data)
}
```

### 6.2 JSON解码器(Decoder)
```go
func readJSON(r io.Reader, v interface{}) error {
    decoder := json.NewDecoder(r)
    decoder.DisallowUnknownFields()  // 不允许未知字段
    return decoder.Decode(v)
}
```

## 7. JSON性能优化

### 7.1 使用jsoniter库
**位置**: `internal/biz/biz.correct.go:7`
```go
jsoniter "github.com/json-iterator/go"
```

**优势**:
- 比标准库快2-3倍
- 100%兼容标准库API
- 支持更多配置选项

### 7.2 预分配和重用
```go
// 重用编码器
var encoder = json.NewEncoder(os.Stdout)

func writeData(data interface{}) {
    encoder.Encode(data)  // 重用编码器
}
```

### 7.3 避免反射
```go
// 使用具体类型而不是interface{}
type Response struct {
    Code int    `json:"code"`
    Msg  string `json:"msg"`
    Data User   `json:"data"`  // 具体类型而不是interface{}
}
```

## 8. JSON验证和错误处理

### 8.1 JSON格式验证
```go
func isValidJSON(data []byte) bool {
    var js json.RawMessage
    return json.Unmarshal(data, &js) == nil
}
```

### 8.2 详细错误处理
```go
func parseUser(data []byte) (*User, error) {
    var user User
    err := json.Unmarshal(data, &user)
    if err != nil {
        if syntaxErr, ok := err.(*json.SyntaxError); ok {
            return nil, fmt.Errorf("JSON语法错误在位置%d: %v", syntaxErr.Offset, err)
        }
        if typeErr, ok := err.(*json.UnmarshalTypeError); ok {
            return nil, fmt.Errorf("类型错误: 字段%s期望%s类型，得到%s", typeErr.Field, typeErr.Type, typeErr.Value)
        }
        return nil, err
    }
    return &user, nil
}
```

## 9. JSON的常见用法

### 9.1 配置文件处理
```go
type Config struct {
    Database struct {
        Host     string `json:"host"`
        Port     int    `json:"port"`
        Username string `json:"username"`
        Password string `json:"password"`
    } `json:"database"`
    
    Redis struct {
        Addr     string `json:"addr"`
        Password string `json:"password"`
        DB       int    `json:"db"`
    } `json:"redis"`
}

func loadConfig(filename string) (*Config, error) {
    data, err := ioutil.ReadFile(filename)
    if err != nil {
        return nil, err
    }
    
    var config Config
    err = json.Unmarshal(data, &config)
    if err != nil {
        return nil, err
    }
    
    return &config, nil
}
```

### 9.2 API响应处理
```go
type APIResponse struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

func sendResponse(w http.ResponseWriter, code int, message string, data interface{}) {
    response := APIResponse{
        Code:    code,
        Message: message,
        Data:    data,
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}
```

## 10. JSON的最佳实践

### 10.1 字段命名约定
- 使用snake_case（下划线）或camelCase
- 保持一致性
- 考虑前端约定

### 10.2 错误处理
- 始终检查序列化/反序列化错误
- 提供有意义的错误信息
- 验证输入数据

### 10.3 性能考虑
- 对于高频操作，考虑使用jsoniter
- 避免不必要的序列化/反序列化
- 重用编码器/解码器

### 10.4 安全考虑
```go
// 限制JSON大小
func parseJSON(r io.Reader, v interface{}) error {
    decoder := json.NewDecoder(io.LimitReader(r, 1<<20)) // 限制1MB
    return decoder.Decode(v)
}
```

## 11. 常见错误和陷阱

### 11.1 数字类型陷阱
```go
// JSON中的数字默认解析为float64
var data map[string]interface{}
json.Unmarshal([]byte(`{"age": 25}`), &data)
age := data["age"].(float64)  // 不是int
```

### 11.2 时间处理陷阱
```go
// time.Time的默认JSON格式是RFC3339
type Event struct {
    Name string    `json:"name"`
    Time time.Time `json:"time"`
}

// 自定义时间格式
type Event struct {
    Name string `json:"name"`
    Time string `json:"time"`  // 使用字符串，手动处理格式
}
```

### 11.3 空值处理
```go
type User struct {
    Name  string  `json:"name"`
    Email *string `json:"email,omitempty"`  // 使用指针处理可选字段
}
```

JSON处理是Go语言中非常重要的技能，特别是在Web开发和API设计中。理解JSON的序列化、反序列化机制以及性能优化技巧对于构建高效的Go应用程序至关重要。