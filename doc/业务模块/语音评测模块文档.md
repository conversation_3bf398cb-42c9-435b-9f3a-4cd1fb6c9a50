# 语音评测模块文档

## 模块概述

语音评测模块是hw-paas-service项目中的实时通信模块，通过WebSocket连接提供英语口语评测服务。该模块支持实时语音传输、AI语音评分、发音纠正等功能，主要用于英语学习场景。

## API接口定义

### WebSocket连接
- **路径**: `/intelligence/v1/evaluate`
- **协议**: WebSocket
- **功能**: 建立实时语音评测连接

### 连接参数
```javascript
// WebSocket连接参数
{
  "X-Genie-DeviceId": "设备ID",
  "req_id": "请求ID", 
  "from": "来源标识",
  "talId": "用户TAL ID"
}
```

## 数据模型

### 1. 评测请求消息
```go
type EvaluateRequest struct {
    Type      string `json:"type"`       // 消息类型: start/audio/end
    AudioData []byte `json:"audio_data"` // 音频数据(base64编码)
    Text      string `json:"text"`       // 待评测文本
    Language  string `json:"language"`   // 语言类型: en/zh
    Mode      string `json:"mode"`       // 评测模式: word/sentence/paragraph
}
```

### 2. 评测响应消息
```go
type EvaluateResponse struct {
    Type       string      `json:"type"`        // 响应类型: result/error/progress
    Score      float64     `json:"score"`       // 总分(0-100)
    Fluency    float64     `json:"fluency"`     // 流利度
    Accuracy   float64     `json:"accuracy"`    // 准确度
    Integrity  float64     `json:"integrity"`   // 完整度
    Rhythm     float64     `json:"rhythm"`      // 节奏感
    WordScores []WordScore `json:"word_scores"` // 单词评分详情
    Suggestion string      `json:"suggestion"`  // 改进建议
}

type WordScore struct {
    Word     string  `json:"word"`     // 单词
    Score    float64 `json:"score"`    // 单词得分
    Phonemes []PhonemeScore `json:"phonemes"` // 音素评分
}

type PhonemeScore struct {
    Phoneme string  `json:"phoneme"` // 音素
    Score   float64 `json:"score"`   // 音素得分
}
```

## 业务逻辑层

### EvaluateService

**主要功能**:
- WebSocket连接管理
- 音频流处理
- 实时评测
- 结果返回

**核心方法**:
```go
func (eval *EvalService) WsHandler(w http.ResponseWriter, r *http.Request)
func (eval *EvalService) Upgrade(w http.ResponseWriter, r *http.Request, header http.Header) (*websocket.Conn, error)
func (eval *EvalService) parseHeader(r *http.Request) http.Header
```

### WebSocket传输层

#### Transfer结构
```go
type Transfer struct {
    conn        *websocket.Conn           // WebSocket连接
    close       chan struct{}             // 关闭信号
    writeQueue  chan TransferWriteMsg     // 写消息队列
    lastPkgTime int64                     // 最后包时间
    deviceId    string                    // 设备ID
    reqId       string                    // 请求ID
    talId       string                    // 用户ID
    From        string                    // 来源
}

type TransferWriteMsg struct {
    MessageType int    // 消息类型
    Data        []byte // 消息数据
}
```

#### 连接生命周期
```go
func (t *Transfer) run() {
    defer func() {
        if err := recover(); err != nil {
            log.Errorf("Transfer run panic: %v", err)
        }
        t.cleanup()
    }()

    // 启动读写协程
    go t.readLoop()
    go t.writeLoop()
    go t.heartbeatLoop()

    // 等待关闭信号
    <-t.close
}

func (t *Transfer) readLoop() {
    defer func() {
        t.close <- struct{}{}
    }()

    for {
        messageType, data, err := t.conn.ReadMessage()
        if err != nil {
            log.Errorf("WebSocket read error: %v", err)
            return
        }

        // 处理接收到的消息
        t.handleMessage(messageType, data)
        t.lastPkgTime = time.Now().Unix()
    }
}

func (t *Transfer) writeLoop() {
    for {
        select {
        case msg := <-t.writeQueue:
            err := t.conn.WriteMessage(msg.MessageType, msg.Data)
            if err != nil {
                log.Errorf("WebSocket write error: %v", err)
                return
            }
        case <-t.close:
            return
        }
    }
}
```

## 语音评测算法

### 1. 评分权重配置
```go
type EnStandardParam struct {
    PronWeight     float32 `json:"pron_weight"`      // 发音权重 0.35
    FluencyWeight  float32 `json:"fluency_weight"`   // 流利度权重 0.25
    IntegrityWeight float32 `json:"integrity_weight"` // 完整度权重 0.30
    VolumeWeight   float32 `json:"volume_weight"`    // 音量权重 0.10
    NeedUrl        bool    `json:"need_url"`         // 是否需要音频URL
    VadMaxSec      float32 `json:"vad_max_sec"`      // VAD最大时长 30.0
    VadPauseSec    float32 `json:"vad_pause_sec"`    // VAD暂停时长 -1.0
    VadStSilSec    float32 `json:"vad_st_sil_sec"`   // VAD开始静音时长 10.0
}
```

### 2. 评分计算
```go
func (eval *EvalService) calculateScore(result *EvaluateResult) float64 {
    param := eval.conf.EnStandard
    
    totalScore := result.Pronunciation*float64(param.PronWeight) +
                  result.Fluency*float64(param.FluencyWeight) +
                  result.Integrity*float64(param.IntegrityWeight) +
                  result.Volume*float64(param.VolumeWeight)
    
    return math.Min(totalScore, 100.0)
}
```

### 3. 等级评定
```go
func (eval *EvalService) getScoreLevel(score float64) string {
    switch {
    case score >= 75:
        return "Excellent"
    case score >= 50:
        return "Wonderful" 
    case score >= 25:
        return "Great"
    default:
        return "Nice Try"
    }
}
```

## 外部服务集成

### TAL英语评测服务
```go
type TalEnStandard struct {
    URL    string `json:"url"`    // WebSocket服务地址
    AppKey string `json:"appkey"` // 应用密钥
    Secret string `json:"secret"` // 签名密钥
}
```

**连接配置**:
```yaml
biz:
  tal_en_standard:
    url: "ws://openai-us-sea.100tal.com/aispeech/evl-realtime/en-standard-next"
    appkey: "1313896727794679808"
    secret: "8376f56ddea4472297d3d3e41e7063c6"
```

### 下游服务连接
```go
func (t *Transfer) AssembleDownstream(conf *conf.Biz, services *services.Services, header http.Header) error {
    // 构建下游WebSocket连接
    downstreamURL := conf.TalEnStandard.Url
    
    // 添加认证参数
    params := url.Values{}
    params.Add("appkey", conf.TalEnStandard.AppKey)
    params.Add("timestamp", strconv.FormatInt(time.Now().Unix(), 10))
    params.Add("signature", t.generateSignature(conf.TalEnStandard.Secret))
    
    fullURL := downstreamURL + "?" + params.Encode()
    
    // 建立下游连接
    downstreamConn, _, err := websocket.DefaultDialer.Dial(fullURL, nil)
    if err != nil {
        return fmt.Errorf("failed to connect downstream: %v", err)
    }
    
    t.downstreamConn = downstreamConn
    return nil
}
```

## 消息处理流程

### 1. 消息路由
```go
func (t *Transfer) handleMessage(messageType int, data []byte) {
    switch messageType {
    case websocket.TextMessage:
        t.handleTextMessage(data)
    case websocket.BinaryMessage:
        t.handleBinaryMessage(data)
    case websocket.CloseMessage:
        t.handleCloseMessage()
    case websocket.PingMessage:
        t.handlePingMessage()
    case websocket.PongMessage:
        t.handlePongMessage()
    }
}
```

### 2. 音频数据处理
```go
func (t *Transfer) handleBinaryMessage(data []byte) {
    // 转发音频数据到下游服务
    if t.downstreamConn != nil {
        err := t.downstreamConn.WriteMessage(websocket.BinaryMessage, data)
        if err != nil {
            log.Errorf("Failed to forward audio data: %v", err)
        }
    }
    
    // 记录音频数据统计
    t.audioDataSize += len(data)
    t.audioPacketCount++
}
```

### 3. 评测结果处理
```go
func (t *Transfer) handleEvaluateResult(result []byte) {
    // 解析评测结果
    var evalResult EvaluateResponse
    err := json.Unmarshal(result, &evalResult)
    if err != nil {
        log.Errorf("Failed to parse evaluate result: %v", err)
        return
    }
    
    // 添加设备信息
    evalResult.DeviceId = t.deviceId
    evalResult.RequestId = t.reqId
    
    // 发送给客户端
    resultData, _ := json.Marshal(evalResult)
    t.writeQueue <- TransferWriteMsg{
        MessageType: websocket.TextMessage,
        Data:        resultData,
    }
}
```

## 连接管理

### 1. 心跳检测
```go
func (t *Transfer) heartbeatLoop() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            // 检查最后包时间
            if time.Now().Unix()-t.lastPkgTime > 60 {
                log.Warnf("Connection timeout, closing...")
                t.close <- struct{}{}
                return
            }
            
            // 发送心跳
            t.writeQueue <- TransferWriteMsg{
                MessageType: websocket.PingMessage,
                Data:        []byte("ping"),
            }
        case <-t.close:
            return
        }
    }
}
```

### 2. 连接清理
```go
func (t *Transfer) cleanup() {
    // 关闭WebSocket连接
    if t.conn != nil {
        t.conn.Close()
    }
    
    // 关闭下游连接
    if t.downstreamConn != nil {
        t.downstreamConn.Close()
    }
    
    // 清理资源
    close(t.writeQueue)
    
    // 记录连接统计
    t.logConnectionStats()
}
```

## 错误处理

### 关闭原因定义
```go
const (
    SoundErrClose      = "sound_err_close"       // 语音服务异常关闭
    ClientClose        = "client_close"          // 客户端主动关闭
    GoServerErrClose   = "go_server_err_close"   // Go服务异常关闭
    JavaServerErrClose = "java_server_err_close" // Java服务异常关闭
    NormallyClose      = "normally_close"        // 正常业务结束关闭
)
```

### 异常处理
```go
func (t *Transfer) handleError(err error, closeReason string) {
    log.Errorf("WebSocket error: %v, reason: %s", err, closeReason)
    
    // 发送错误消息给客户端
    errorMsg := map[string]interface{}{
        "type":   "error",
        "reason": closeReason,
        "message": err.Error(),
    }
    
    errorData, _ := json.Marshal(errorMsg)
    t.writeQueue <- TransferWriteMsg{
        MessageType: websocket.TextMessage,
        Data:        errorData,
    }
    
    // 关闭连接
    t.close <- struct{}{}
}
```

## 性能优化

### 1. 连接池管理
```go
type ConnectionPool struct {
    connections map[string]*Transfer
    mutex       sync.RWMutex
    maxConn     int
}

func (pool *ConnectionPool) AddConnection(deviceId string, transfer *Transfer) error {
    pool.mutex.Lock()
    defer pool.mutex.Unlock()
    
    if len(pool.connections) >= pool.maxConn {
        return errors.New("connection pool full")
    }
    
    pool.connections[deviceId] = transfer
    return nil
}
```

### 2. 消息队列优化
```go
func NewTransfer(conn *websocket.Conn) *Transfer {
    return &Transfer{
        conn:        conn,
        close:       make(chan struct{}, 1),
        writeQueue:  make(chan TransferWriteMsg, 1000), // 增大队列容量
        lastPkgTime: time.Now().Unix(),
    }
}
```

### 3. 内存管理
```go
func (t *Transfer) handleLargeAudioData(data []byte) {
    // 分块处理大音频数据
    chunkSize := 8192
    for i := 0; i < len(data); i += chunkSize {
        end := i + chunkSize
        if end > len(data) {
            end = len(data)
        }
        
        chunk := data[i:end]
        t.processAudioChunk(chunk)
    }
}
```

## 监控和日志

### 连接统计
```go
func (t *Transfer) logConnectionStats() {
    stats := map[string]interface{}{
        "device_id":         t.deviceId,
        "request_id":        t.reqId,
        "duration_seconds":  time.Now().Unix() - t.startTime,
        "audio_data_size":   t.audioDataSize,
        "audio_packet_count": t.audioPacketCount,
        "close_reason":      t.closeReason,
    }
    
    statsJson, _ := json.Marshal(stats)
    log.Info("connection_stats", string(statsJson))
}
```

### 性能指标
- 并发连接数
- 平均连接时长
- 音频数据传输量
- 评测响应时间
- 错误率统计

## 配置项

### 评测参数配置
```yaml
biz:
  en_standard:
    pron_weight: 0.35           # 发音权重
    fluency_weight: 0.25        # 流利度权重
    integrity_weight: 0.30      # 完整度权重
    volume_weight: 0.10         # 音量权重
    need_url: false             # 是否需要音频URL
    vad_max_sec: 30.0          # VAD最大时长
    vad_pause_sec: -1.0        # VAD暂停时长
    vad_st_sil_sec: 10.0       # VAD开始静音时长
    suffix_penal_quick: -1      # 后缀惩罚
    high_score_threshold: 50.0  # 高分阈值
    high_stop_low_threshold: 30.0 # 低分停止阈值
```

## 安全特性

### 1. 连接验证
- 设备ID验证
- 用户身份认证
- 请求签名验证

### 2. 数据保护
- 音频数据加密传输
- 敏感信息脱敏
- 连接超时保护

### 3. 资源限制
- 连接数量限制
- 音频数据大小限制
- 评测时长限制

这个语音评测模块为英语学习应用提供了实时的口语评测功能，通过WebSocket实现低延迟的音频传输和评测反馈。