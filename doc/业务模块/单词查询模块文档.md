# 单词查询模块文档

## 模块概述

单词查询模块提供强大的英文单词检索功能，支持多维度查询条件，包括单词本身、音标、释义、同义词、反义词等。该模块基于Elasticsearch实现高性能的全文搜索和相关性排序。

## API接口定义

### 服务定义 (api/query_words/v1/query_words.proto)

```protobuf
service QueryWords {
  rpc QueryWordsList (QueryWordsListRequest) returns (QueryWordsListReply) {
    option (google.api.http) = {
      post: "/intelligence/v1/query_words/list"
      body: "*"
    };
  };
}
```

### 主要接口

#### 单词列表查询接口
- **路径**: `POST /intelligence/v1/query_words/list`
- **功能**: 根据多种条件查询单词列表，支持模糊匹配和相关性排序
- **请求参数**:
  ```protobuf
  message QueryWordsListRequest {
    string word = 1;                    // 主查询单词
    QueryWordsListQuery query = 2;      // 详细查询条件
  }
  
  message QueryWordsListQuery {
    string word = 1;        // 单词
    string british = 2;     // 英式发音
    string american = 3;    // 美式发音
    string synonym = 4;     // 同义词
    string antonym = 5;     // 反义词
    string inflection = 6;  // 变形词
    string prefix = 7;      // 前缀
    string suffix = 8;      // 后缀
    string phrase = 9;      // 短语
    string meaning = 10;    // 释义
    string sentence = 11;   // 例句
  }
  ```
- **响应参数**:
  ```protobuf
  message QueryWordsListReply {
    int32 total = 1;                           // 总数量
    repeated QueryWordsListItem list = 2;       // 结果列表
    bool curse = 3;                            // 是否包含违禁词
  }
  
  message QueryWordsListItem {
    google.protobuf.Struct detail = 1;  // 单词详情
    float score = 2;                    // 相关性评分
  }
  ```

## 数据模型

### 使用hw_en_word表
单词查询模块复用指尖查词模块的数据表结构，详见指尖查词模块文档。

### 查询条件结构

```go
type QueryWordsListQuery struct {
    Word       string `json:"word"`       // 单词
    British    string `json:"british"`    // 英式音标
    American   string `json:"american"`   // 美式音标
    Synonym    string `json:"synonym"`    // 同义词
    Antonym    string `json:"antonym"`    // 反义词
    Inflection string `json:"inflection"` // 变形词
    Prefix     string `json:"prefix"`     // 前缀
    Suffix     string `json:"suffix"`     // 后缀
    Phrase     string `json:"phrase"`     // 短语
    Meaning    string `json:"meaning"`    // 释义
    Sentence   string `json:"sentence"`   // 例句
}
```

## 业务逻辑层

### QueryWordsUseCase

**主要功能**:
- 多条件组合查询
- Elasticsearch全文检索
- 违禁词检测
- 结果排序和分页
- 查询结果缓存

**核心方法**:
```go
func (uc *QueryWordsUseCase) QueryWordsList(ctx context.Context, req *pb.QueryWordsListRequest) (*pb.QueryWordsListReply, error)
func (uc *QueryWordsUseCase) buildElasticsearchQuery(query *pb.QueryWordsListQuery) map[string]interface{}
func (uc *QueryWordsUseCase) checkCurseWords(ctx context.Context, word string) bool
func (uc *QueryWordsUseCase) formatSearchResults(hits []map[string]interface{}) []*pb.QueryWordsListItem
```

**查询流程**:
1. **参数验证**: 检查查询参数的合法性
2. **违禁词检测**: 检查查询词是否为违禁词
3. **构建查询**: 根据条件构建Elasticsearch查询语句
4. **执行搜索**: 调用Elasticsearch进行全文检索
5. **结果处理**: 格式化搜索结果并计算相关性评分
6. **缓存更新**: 更新热门查询的缓存

## Elasticsearch集成

### 索引结构

**索引名称**: `oversea_en_word`

**字段映射**:
```json
{
  "mappings": {
    "properties": {
      "word": {
        "type": "text",
        "analyzer": "standard",
        "fields": {
          "keyword": {
            "type": "keyword"
          }
        }
      },
      "british": {
        "type": "text",
        "analyzer": "keyword"
      },
      "american": {
        "type": "text",
        "analyzer": "keyword"
      },
      "meanings": {
        "type": "text",
        "analyzer": "standard"
      },
      "synonyms": {
        "type": "text",
        "analyzer": "standard"
      },
      "antonyms": {
        "type": "text",
        "analyzer": "standard"
      },
      "sentences": {
        "type": "text",
        "analyzer": "standard"
      },
      "inflections": {
        "type": "text",
        "analyzer": "standard"
      },
      "phrases": {
        "type": "text",
        "analyzer": "standard"
      },
      "frequency": {
        "type": "long"
      },
      "status": {
        "type": "integer"
      }
    }
  }
}
```

### 查询策略

#### 1. 多字段查询
```go
func (uc *QueryWordsUseCase) buildMultiFieldQuery(query *pb.QueryWordsListQuery) map[string]interface{} {
    boolQuery := map[string]interface{}{
        "bool": map[string]interface{}{
            "should": []map[string]interface{}{},
            "filter": []map[string]interface{}{
                {
                    "term": map[string]interface{}{
                        "status": model.EnWordStatusShelf,
                    },
                },
            },
        },
    }
    
    // 添加各种查询条件
    if query.Word != "" {
        boolQuery["bool"].(map[string]interface{})["should"] = append(
            boolQuery["bool"].(map[string]interface{})["should"].([]map[string]interface{}),
            map[string]interface{}{
                "match": map[string]interface{}{
                    "word": map[string]interface{}{
                        "query": query.Word,
                        "boost": 3.0,
                    },
                },
            },
        )
    }
    
    return boolQuery
}
```

#### 2. 模糊匹配
- 支持前缀匹配
- 支持通配符查询
- 支持编辑距离模糊匹配

#### 3. 相关性评分
- 精确匹配权重最高
- 前缀匹配次之
- 模糊匹配权重最低
- 词频影响排序

## 数据访问层

### EsRepo (Elasticsearch仓库)

**主要方法**:
```go
func (r *EsRepo) SearchWords(ctx context.Context, query map[string]interface{}) (*elastic.SearchResult, error)
func (r *EsRepo) IndexWord(ctx context.Context, word *model.HwEnWord) error
func (r *EsRepo) BulkIndexWords(ctx context.Context, words []*model.HwEnWord) error
func (r *EsRepo) DeleteWord(ctx context.Context, id string) error
func (r *EsRepo) UpdateWord(ctx context.Context, id string, doc map[string]interface{}) error
```

**连接配置**:
```go
type EsRepo struct {
    client *elastic.Client
    index  string
    log    *log.Helper
}

func NewEsRepo(conf *conf.Data, logger log.Logger) *EsRepo {
    client, err := elastic.NewClient(
        elastic.SetURL(conf.Es.Host),
        elastic.SetBasicAuth(conf.Es.Username, conf.Es.Password),
        elastic.SetSniff(false),
    )
    
    return &EsRepo{
        client: client,
        index:  "oversea_en_word",
        log:    log.NewHelper(logger),
    }
}
```

## 违禁词检测

### 检测机制
```go
func (uc *QueryWordsUseCase) checkCurseWords(ctx context.Context, word string) bool {
    // 查询违禁词表
    curseWords, err := uc.fingerWordsDao.FindFingerWords(ctx, &model.HwEnWordSearch{
        Word:   word,
        Status: model.EnWordStatusCurse,
    })
    
    if err != nil {
        uc.log.WithContext(ctx).Errorf("check curse words error: %v", err)
        return false
    }
    
    return len(curseWords) > 0
}
```

### 违禁词处理策略
- 直接查询违禁词返回空结果
- 包含违禁词的组合查询过滤相关结果
- 记录违禁词查询日志用于监控

## 缓存策略

### Redis缓存
```go
func (uc *QueryWordsUseCase) getCachedResult(ctx context.Context, cacheKey string) (*pb.QueryWordsListReply, error) {
    cached, err := uc.rdb.Get(ctx, cacheKey).Result()
    if err != nil {
        return nil, err
    }
    
    var result pb.QueryWordsListReply
    err = json.Unmarshal([]byte(cached), &result)
    return &result, err
}

func (uc *QueryWordsUseCase) setCachedResult(ctx context.Context, cacheKey string, result *pb.QueryWordsListReply) error {
    data, err := json.Marshal(result)
    if err != nil {
        return err
    }
    
    return uc.rdb.Set(ctx, cacheKey, data, time.Hour).Err()
}
```

### 缓存策略
- 热门查询结果缓存1小时
- 空结果缓存5分钟避免重复查询
- 缓存键基于查询参数MD5生成

## 性能优化

### 1. 查询优化
- 使用bool查询组合多个条件
- 合理设置字段权重
- 限制返回字段减少网络传输

### 2. 索引优化
- 定期优化索引结构
- 合理设置分片和副本数量
- 使用别名支持无缝索引切换

### 3. 缓存优化
- 多级缓存策略
- 异步更新缓存
- 缓存预热机制

## 监控和指标

### 关键指标
- 查询QPS和响应时间
- Elasticsearch集群状态
- 缓存命中率
- 违禁词查询统计

### 日志记录
```go
func (uc *QueryWordsUseCase) logQuery(ctx context.Context, req *pb.QueryWordsListRequest, result *pb.QueryWordsListReply, duration time.Duration) {
    logData := map[string]interface{}{
        "query_word": req.Word,
        "total_results": result.Total,
        "has_curse": result.Curse,
        "duration_ms": duration.Milliseconds(),
        "trace_id": custom_context.GetTraceId(ctx),
    }
    
    logJson, _ := json.Marshal(logData)
    uc.log.WithContext(ctx).Info(string(logJson))
}
```

## 配置项

### Elasticsearch配置
```yaml
data:
  es:
    host: "http://**********:9200"
    username: "superuser"
    password: "d737da0188348DD1d^hkda"

biz:
  en_word_index: "oversea_en_word"
```

## 错误处理

### 常见错误
1. **Elasticsearch连接失败**: 集群不可用或网络问题
2. **查询语法错误**: 构建的查询语句不合法
3. **索引不存在**: 目标索引未创建或已删除
4. **查询超时**: 复杂查询执行时间过长
5. **内存不足**: 大结果集导致内存溢出

### 容错机制
- 自动重试机制
- 降级到数据库查询
- 默认空结果返回
- 异常情况告警

## 安全特性

### 1. 查询安全
- 参数长度限制
- 特殊字符过滤
- SQL注入防护

### 2. 访问控制
- 查询频率限制
- IP白名单机制
- 用户权限验证

### 3. 数据安全
- 敏感词过滤
- 查询日志脱敏
- 数据传输加密

这个单词查询模块为用户提供了强大而灵活的英文单词检索功能，是英语学习应用的重要组成部分。