# 评论系统模块文档

## 模块概述

评论系统模块提供多级评论功能，支持对博客、工作表、词汇表、涂色页等不同类型内容的评论管理。该模块采用树形结构设计，支持评论回复和嵌套显示。

## API接口定义

### 服务定义 (api/comment/v1/comment.proto)

```protobuf
service Comment {
  rpc ListComment (ListCommentRequest) returns (ListCommentReply) {
    option (google.api.http) = {
      get: "/intelligence/api/comment/v1/comments"
    };
  };
}
```

### 主要接口

#### 评论列表查询接口
- **路径**: `GET /intelligence/api/comment/v1/comments`
- **功能**: 根据主题类型和ID查询评论列表，支持多级评论和分页
- **请求参数**:
  ```protobuf
  message ListCommentRequest {
    int64 subject_type = 1;   // 主题类型
    string subject_id = 2;    // 主题ID
    int64 parent_id = 3;      // 父评论ID
    int32 page = 4;           // 页码
    int32 page_size = 5;      // 每页大小
  }
  ```
- **响应参数**:
  ```protobuf
  message ListCommentReply {
    repeated CommentInfo comments = 1;  // 评论列表
    int32 total = 2;                   // 总数量
  }
  
  message CommentInfo {
    int64 id = 1;              // 评论ID
    int32 subject_type = 2;    // 主题类型
    string subject_id = 3;     // 主题ID
    int64 parent_id = 4;       // 父评论ID
    string user_avatar = 5;    // 用户头像
    string user_name = 6;      // 用户名
    string tal_id = 7;         // 用户TAL ID
    string content = 8;        // 评论内容
    string created_at = 9;     // 创建时间
  }
  ```

## 数据模型

### 评论表 (comments)

```sql
CREATE TABLE comments (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '评论ID',
  subject_type INT NOT NULL COMMENT '主题类型: 1=blog, 2=worksheet, 3=glossary, 4=coloring_pages',
  subject_id VARCHAR(100) NOT NULL COMMENT '主题ID',
  parent_id BIGINT NOT NULL DEFAULT 0 COMMENT '父评论ID，0为一级评论',
  user_avatar VARCHAR(500) DEFAULT '' COMMENT '用户头像URL',
  user_name VARCHAR(100) DEFAULT '' COMMENT '用户名',
  tal_id VARCHAR(50) DEFAULT '' COMMENT '用户TAL ID',
  content TEXT NOT NULL COMMENT '评论内容',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_subject (subject_type, subject_id),
  INDEX idx_parent (parent_id),
  INDEX idx_created (created_at)
);
```

### 主题类型枚举

```go
const (
    SubjectTypeBlog         = 1  // 博客
    SubjectTypeWorksheet    = 2  // 工作表
    SubjectTypeGlossary     = 3  // 词汇表
    SubjectTypeColoringPage = 4  // 涂色页
)
```

### 数据结构

```go
type Comment struct {
    ID          int       `gorm:"primaryKey;autoIncrement"`
    SubjectType int       `gorm:"column:subject_type"`
    SubjectID   string    `gorm:"column:subject_id"`
    ParentID    uint64    `gorm:"column:parent_id"`
    UserAvatar  string    `gorm:"column:user_avatar"`
    UserName    string    `gorm:"column:user_name"`
    TalID       string    `gorm:"column:tal_id"`
    Content     string    `gorm:"column:content"`
    CreatedAt   time.Time `gorm:"column:created_at"`
    UpdatedAt   time.Time `gorm:"column:updated_at"`
}

func (Comment) TableName() string {
    return "comments"
}
```

## 业务逻辑层

### CommentUseCase

**主要功能**:
- 评论列表查询
- 多级评论支持
- 分页和排序
- 评论统计

**核心方法**:
```go
func (uc *CommentUseCase) ListComments(ctx context.Context, subjectType int, subjectId string, parentID uint64, page, pageSize int) (*pb.ListCommentReply, error)
```

**业务逻辑**:
```go
func (uc *CommentUseCase) ListComments(ctx context.Context, subjectType int, subjectId string, parentID uint64, page, pageSize int) (*pb.ListCommentReply, error) {
    // 查询评论列表
    comments, total, err := uc.commentRepo.ListComments(ctx, subjectType, subjectId, parentID, page, pageSize)
    if err != nil {
        return nil, err
    }

    // 转换为响应格式
    commentInfos := make([]*pb.CommentInfo, 0)
    for _, comment := range comments {
        commentInfos = append(commentInfos, &pb.CommentInfo{
            Id:          int64(comment.ID),
            SubjectType: int32(comment.SubjectType),
            SubjectId:   comment.SubjectID,
            ParentId:    int64(comment.ParentID),
            UserAvatar:  comment.UserAvatar,
            UserName:    comment.UserName,
            TalId:       comment.TalID,
            Content:     comment.Content,
            CreatedAt:   comment.CreatedAt.Format("2006-01-02 15:04:05"),
        })
    }
    
    return &pb.ListCommentReply{
        Comments: commentInfos,
        Total:    int32(total),
    }, nil
}
```

## 数据访问层

### CommentRepo

**主要方法**:
```go
func (d *CommentRepo) ListComments(ctx context.Context, subjectType int, subjectId string, parentID uint64, page, pageSize int) ([]*model.Comment, int, error)
func (d *CommentRepo) CreateComment(ctx context.Context, comment *model.Comment) error
func (d *CommentRepo) UpdateComment(ctx context.Context, comment *model.Comment) error
func (d *CommentRepo) DeleteComment(ctx context.Context, id int64) error
func (d *CommentRepo) GetCommentById(ctx context.Context, id int64) (*model.Comment, error)
func (d *CommentRepo) GetCommentCount(ctx context.Context, subjectType int, subjectId string) (int, error)
```

**查询实现**:
```go
func (d *CommentRepo) ListComments(ctx context.Context, subjectType int, subjectId string, parentID uint64, page, pageSize int) ([]*model.Comment, int, error) {
    var comments []*model.Comment

    db := d.data.DB.WithContext(ctx).Model(&model.Comment{})

    // 构建查询条件
    if subjectType > 0 {
        db = db.Where("subject_type in ?", []int{subjectType, 0})
    }

    if subjectId != "" {
        db = db.Where("subject_id = ?", subjectId)
    }

    if parentID > 0 {
        db = db.Where("parent_id = ?", parentID)
    } else {
        // 如果parentID为0，查询一级评论
        db = db.Where("parent_id = ?", 0)
    }

    // 查询总数
    var total int64
    if err := db.Count(&total).Error; err != nil {
        return nil, 0, fmt.Errorf("failed to query comments: %w", err)
    }

    // 分页
    if page > 0 && pageSize > 0 {
        offset := (page - 1) * pageSize
        db = db.Offset(offset).Limit(pageSize)
    }

    // 按创建时间倒序排列
    db = db.Order("created_at DESC")

    if err := db.Find(&comments).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return []*model.Comment{}, 0, nil
        }
        return nil, 0, fmt.Errorf("failed to query comments: %w", err)
    }

    return comments, int(total), nil
}
```

## 评论层级设计

### 1. 树形结构
```
一级评论 (parent_id = 0)
├── 二级评论 (parent_id = 一级评论ID)
│   ├── 三级评论 (parent_id = 二级评论ID)
│   └── 三级评论 (parent_id = 二级评论ID)
└── 二级评论 (parent_id = 一级评论ID)
```

### 2. 查询策略
- **一级评论**: `parent_id = 0`
- **子评论**: `parent_id = 指定评论ID`
- **全部评论**: 递归查询所有层级

### 3. 显示逻辑
```go
func (uc *CommentUseCase) BuildCommentTree(ctx context.Context, subjectType int, subjectId string) ([]*CommentNode, error) {
    // 查询所有一级评论
    rootComments, _, err := uc.commentRepo.ListComments(ctx, subjectType, subjectId, 0, 0, 0)
    if err != nil {
        return nil, err
    }

    var commentTree []*CommentNode
    for _, comment := range rootComments {
        node := &CommentNode{
            Comment:  comment,
            Children: []*CommentNode{},
        }
        
        // 递归查询子评论
        children, err := uc.getChildComments(ctx, comment.ID)
        if err != nil {
            continue
        }
        node.Children = children
        
        commentTree = append(commentTree, node)
    }

    return commentTree, nil
}
```

## 内容安全

### 1. 内容审核
```go
func (uc *CommentUseCase) validateContent(content string) error {
    // 长度检查
    if len(content) > 1000 {
        return errors.New("评论内容过长")
    }
    
    // 敏感词检查
    if uc.containsSensitiveWords(content) {
        return errors.New("评论包含敏感词")
    }
    
    // HTML标签过滤
    content = uc.sanitizeHTML(content)
    
    return nil
}

func (uc *CommentUseCase) containsSensitiveWords(content string) bool {
    sensitiveWords := []string{"spam", "abuse", "inappropriate"}
    for _, word := range sensitiveWords {
        if strings.Contains(strings.ToLower(content), word) {
            return true
        }
    }
    return false
}
```

### 2. 用户验证
```go
func (uc *CommentUseCase) validateUser(ctx context.Context, talId string) error {
    // 检查用户是否存在
    user, err := uc.userRepo.GetUserByTalId(ctx, talId)
    if err != nil {
        return errors.New("用户不存在")
    }
    
    // 检查用户状态
    if user.Status != UserStatusActive {
        return errors.New("用户状态异常")
    }
    
    return nil
}
```

## 缓存策略

### 1. 评论缓存
```go
func (uc *CommentUseCase) getCachedComments(ctx context.Context, cacheKey string) ([]*model.Comment, bool) {
    cached, err := uc.rdb.Get(ctx, cacheKey).Result()
    if err != nil {
        return nil, false
    }
    
    var comments []*model.Comment
    err = json.Unmarshal([]byte(cached), &comments)
    if err != nil {
        return nil, false
    }
    
    return comments, true
}

func (uc *CommentUseCase) setCachedComments(ctx context.Context, cacheKey string, comments []*model.Comment) {
    data, err := json.Marshal(comments)
    if err != nil {
        return
    }
    
    uc.rdb.Set(ctx, cacheKey, data, time.Minute*10)
}
```

### 2. 缓存键设计
```go
func (uc *CommentUseCase) buildCacheKey(subjectType int, subjectId string, parentId uint64, page, pageSize int) string {
    return fmt.Sprintf("comments:%d:%s:%d:%d:%d", subjectType, subjectId, parentId, page, pageSize)
}
```

## 性能优化

### 1. 数据库优化
```sql
-- 复合索引优化查询
CREATE INDEX idx_subject_parent ON comments(subject_type, subject_id, parent_id);
CREATE INDEX idx_created_desc ON comments(created_at DESC);

-- 分区表（按时间分区）
ALTER TABLE comments PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 2. 查询优化
- 分页查询避免大数据量
- 索引覆盖减少回表
- 批量查询减少数据库访问

### 3. 缓存优化
- 热门评论预加载
- 分层缓存策略
- 缓存失效机制

## 监控和统计

### 1. 关键指标
```go
type CommentMetrics struct {
    TotalComments    int64 `json:"total_comments"`
    DailyComments    int64 `json:"daily_comments"`
    ActiveUsers      int64 `json:"active_users"`
    AverageLength    int   `json:"average_length"`
    ResponseRate     float64 `json:"response_rate"`
}

func (uc *CommentUseCase) GetCommentMetrics(ctx context.Context, subjectType int, subjectId string) (*CommentMetrics, error) {
    // 统计总评论数
    total, err := uc.commentRepo.GetCommentCount(ctx, subjectType, subjectId)
    if err != nil {
        return nil, err
    }
    
    // 统计今日评论数
    daily, err := uc.commentRepo.GetDailyCommentCount(ctx, subjectType, subjectId)
    if err != nil {
        return nil, err
    }
    
    return &CommentMetrics{
        TotalComments: int64(total),
        DailyComments: int64(daily),
    }, nil
}
```

### 2. 日志记录
```go
func (uc *CommentUseCase) logCommentAction(ctx context.Context, action string, commentId int64, userId string) {
    logData := map[string]interface{}{
        "action":     action,
        "comment_id": commentId,
        "user_id":    userId,
        "timestamp":  time.Now().Unix(),
        "trace_id":   custom_context.GetTraceId(ctx),
    }
    
    uc.log.WithContext(ctx).Info("comment_action", logData)
}
```

## 错误处理

### 常见错误
1. **参数错误**: 主题类型或ID不合法
2. **评论不存在**: 请求的评论ID不存在
3. **权限不足**: 用户无权限操作评论
4. **内容违规**: 评论内容包含敏感词
5. **频率限制**: 评论发布过于频繁

### 容错机制
```go
func (uc *CommentUseCase) handleError(err error) error {
    switch {
    case errors.Is(err, gorm.ErrRecordNotFound):
        return pb.ErrorCommentNotFound("评论不存在")
    case strings.Contains(err.Error(), "sensitive"):
        return pb.ErrorCommentContentInvalid("评论内容包含敏感词")
    default:
        return pb.ErrorCommentSystemError("系统错误")
    }
}
```

## 安全特性

### 1. 输入验证
- 内容长度限制
- HTML标签过滤
- SQL注入防护

### 2. 权限控制
- 用户身份验证
- 操作权限检查
- 频率限制

### 3. 数据保护
- 敏感信息脱敏
- 数据传输加密
- 审计日志记录

这个评论系统模块为教育平台提供了完整的用户互动功能，支持多级评论和安全的内容管理。