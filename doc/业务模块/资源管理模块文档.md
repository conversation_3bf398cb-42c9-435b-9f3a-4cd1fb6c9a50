# 资源管理模块文档

## 模块概述

资源管理模块负责管理教育资源，包括工作表(worksheet)、视频(video)、涂色页(coloring page)等多种类型的教育内容。该模块提供资源的分类管理、搜索筛选、元数据管理等功能。

## API接口定义

### 服务定义 (api/resource/v1/resource.proto)

```protobuf
service Resource {
  // 获取年级列表
  rpc GetGrades(google.protobuf.Empty) returns (GradesResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/grades"
    };
  };

  // 获取学科列表
  rpc GetSubjects(google.protobuf.Empty) returns (SubjectsResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/subjects"
    };
  };

  // 获取资源类型列表
  rpc GetResourceType(google.protobuf.Empty) returns (ResourceTypeResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/types"
    };
  };

  // 获取资源元数据
  rpc GetResourceMeta(google.protobuf.Empty) returns (ResourceMetaResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/meta"
    };
  };

  // 获取资源分组
  rpc GetResourceGroups(GetResourceGroupsReq) returns (GetResourceGroupsResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/groups"
    };
  };

  // 获取资源列表
  rpc GetResourceList(GetResourceListReq) returns (GetResourceListResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/list"
    };
  };

  // 获取资源详情
  rpc GetResourceDetail(GetResourceDetailReq) returns (ResourceDetail) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/detail"
    };
  };

  // 获取随机资源列表
  rpc ListRandResource(ListRandResourceReq) returns (ListRandResourceResp) {
    option (google.api.http) = {
      get: "/intelligence/api/v1/resource/random"
    };
  };
}
```

## 详细接口说明

### 1. 获取年级列表接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/grades`
- **功能**: 获取系统支持的所有年级列表
- **认证**: 无需认证

#### 请求参数
```protobuf
// 无需参数，使用 google.protobuf.Empty
```

#### 响应参数
```protobuf
message GradesResp {
  repeated EduGrade grades = 1;
}

message EduGrade {
  string name = 1;   // 年级显示名称，如 "Pre-Kindergarten"
  string value = 2;  // 年级值，如 "Pre-K"
}
```

#### 返回值样例
```json
{
  "grades": [
    {
      "name": "Pre-Kindergarten",
      "value": "Pre-K"
    },
    {
      "name": "Kindergarten", 
      "value": "K"
    },
    {
      "name": "Grade 1",
      "value": "1"
    },
    {
      "name": "Grade 2",
      "value": "2"
    },
    {
      "name": "Grade 3",
      "value": "3"
    },
    {
      "name": "Grade 4",
      "value": "4"
    },
    {
      "name": "Grade 5",
      "value": "5"
    },
    {
      "name": "Grade 6",
      "value": "6"
    }
  ]
}
```

#### 业务逻辑实现
```go
func (uc *ResourceUseCase) GetGrades(ctx context.Context) (*pb.GradesResp, error) {
    // 1. 从缓存获取年级数据
    grades, err := uc.repo.GetGrade(ctx)
    if err != nil {
        return nil, fmt.Errorf("failed to get grades: %w", err)
    }

    // 2. 构建响应数据
    rb := &responseBuilder{}
    return rb.buildGradesResponse(grades), nil
}

// 数据访问层实现
func (d *EduResourceDao) GetGrade(ctx context.Context) ([]*model.Grade, error) {
    var result []*model.Grade

    // 尝试从Redis缓存获取
    cm := &cacheManager{rdb: d.data}
    if cm.getFromCache(ctx, cacheKeyGrades, &result) {
        return result, nil
    }

    // 从配置文件生成年级列表
    result = d.buildGradeList()

    // 写入缓存，过期时间12小时
    cm.setCache(ctx, cacheKeyGrades, result)

    return result, nil
}
```

### 2. 获取学科列表接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/subjects`
- **功能**: 获取学科层级树结构
- **认证**: 无需认证

#### 请求参数
```protobuf
// 无需参数，使用 google.protobuf.Empty
```

#### 响应参数
```protobuf
message SubjectsResp {
  repeated EduSubjectNode subjects = 1;
}

message EduSubjectNode {
  string name = 1;                        // 节点名称
  repeated EduSubjectNode nodes = 2;      // 子节点
}
```

#### 返回值样例
```json
{
  "subjects": [
    {
      "name": "Math",
      "nodes": [
        {
          "name": "Number and Operations",
          "nodes": [
            {
              "name": "Addition",
              "nodes": []
            },
            {
              "name": "Subtraction", 
              "nodes": []
            }
          ]
        },
        {
          "name": "Geometry",
          "nodes": [
            {
              "name": "Shapes",
              "nodes": []
            }
          ]
        }
      ]
    },
    {
      "name": "English",
      "nodes": [
        {
          "name": "Reading",
          "nodes": [
            {
              "name": "Phonics",
              "nodes": []
            }
          ]
        }
      ]
    }
  ]
}
```

#### 业务逻辑实现
```go
func (uc *ResourceUseCase) GetSubjects(ctx context.Context) (*pb.SubjectsResp, error) {
    // 1. 从缓存或数据库获取学科数据
    subjects, err := uc.repo.GetSubject(ctx)
    if err != nil {
        return nil, fmt.Errorf("failed to get subjects: %w", err)
    }

    // 2. 构建树形结构响应
    rb := &responseBuilder{}
    return rb.buildSubjectsResponse(subjects), nil
}

// 构建学科树
func (d *EduResourceDao) buildSubjectTree(ctx context.Context) ([]*model.SubjectNode, error) {
    var results []subjectResult
    db := d.data.DB.WithContext(ctx).Model(&model.EduResources{})

    // 查询所有学科、主题、学习模块组合
    if err := db.Distinct("subject", "topic", "learning_module").
        Order("subject, topic, learning_module desc").
        Find(&results).Error; err != nil {
        return nil, err
    }

    return d.buildSubjectNodes(results), nil
}
```

### 3. 获取资源分组接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/groups`
- **功能**: 根据筛选条件获取分组的资源列表
- **认证**: 无需认证

#### 请求参数
```protobuf
message GetResourceGroupsReq {
  string path = 1;           // 页面路径，用于获取页面配置
  string grade = 2;          // 年级筛选
  string subject = 3;        // 学科筛选（支持层级，如 "Math/Number"）
  string resource_type = 4;  // 资源类型筛选
  int32 page = 5;           // 页码
  int32 page_size = 6;      // 每页大小
}
```

#### 响应参数
```protobuf
message GetResourceGroupsResp {
  repeated EduResourceGroup groups = 1;  // 资源分组列表
  EduResourceTitle title = 2;            // 页面标题信息
}

message EduResourceGroup {
  string title = 1;                      // 分组标题
  repeated EduResource resources = 2;     // 资源列表
  int32 total = 3;                       // 该分组总数量
}

message EduResource {
  uint32 id = 1;                         // 资源ID
  string title = 2;                      // 资源标题
  string resource_type = 3;              // 资源类型
  string subject = 4;                    // 学科
  string grade = 5;                      // 年级
  string learning_topic = 6;             // 学习主题
  string learning_module = 7;            // 学习模块
  string resource = 8;                   // 资源分类
  string resource_detail = 9;            // 资源详情
  string standards = 10;                 // 标准
  string resource_description = 11;      // 资源描述
  google.protobuf.Struct extra_data = 12; // 扩展数据
  string meta_description = 13;          // SEO描述
  string url = 14;                       // 资源URL
  string meta_title = 15;                // SEO标题
  string meta_keywords = 16;             // SEO关键词
  string schemas = 17;                   // 结构化数据
  string updated_at = 18;                // 更新时间
}
```

#### 返回值样例
```json
{
  "groups": [
    {
      "title": "Addition",
      "total": 25,
      "resources": [
        {
          "id": 1001,
          "title": "Basic Addition Worksheet",
          "resource_type": "worksheets",
          "subject": "Math",
          "grade": "Grade 1,Grade 2",
          "learning_topic": "Number and Operations",
          "learning_module": "Addition",
          "resource_detail": "Single Digit Addition",
          "standards": "1.OA.1",
          "resource_description": "Practice basic addition with single digits",
          "url": "/math/grade-1/addition/basic-addition-worksheet",
          "meta_title": "Basic Addition Worksheet - Grade 1 Math",
          "meta_description": "Free printable addition worksheet for grade 1 students",
          "meta_keywords": "addition, worksheet, grade 1, math",
          "updated_at": "2024-01-15 10:30:00",
          "extra_data": {
            "pdf_path": "/files/worksheets/addition_001.pdf",
            "answer_key": "/files/answers/addition_001_key.pdf",
            "difficulty_level": "beginner"
          }
        }
      ]
    },
    {
      "title": "Subtraction", 
      "total": 18,
      "resources": [
        {
          "id": 1002,
          "title": "Basic Subtraction Worksheet",
          "resource_type": "worksheets",
          "subject": "Math",
          "grade": "Grade 1,Grade 2",
          "learning_topic": "Number and Operations",
          "learning_module": "Subtraction",
          "resource_detail": "Single Digit Subtraction",
          "standards": "1.OA.1",
          "resource_description": "Practice basic subtraction with single digits",
          "url": "/math/grade-1/subtraction/basic-subtraction-worksheet",
          "meta_title": "Basic Subtraction Worksheet - Grade 1 Math",
          "meta_description": "Free printable subtraction worksheet for grade 1 students",
          "meta_keywords": "subtraction, worksheet, grade 1, math",
          "updated_at": "2024-01-15 11:00:00",
          "extra_data": {
            "pdf_path": "/files/worksheets/subtraction_001.pdf",
            "answer_key": "/files/answers/subtraction_001_key.pdf",
            "difficulty_level": "beginner"
          }
        }
      ]
    }
  ],
  "title": {
    "url": "/math/grade-1",
    "h1_title": "Grade 1 Math Worksheets",
    "description": "Free printable math worksheets for grade 1 students",
    "meta_title": "Grade 1 Math Worksheets - Free Printable Resources",
    "meta_description": "Download free grade 1 math worksheets covering addition, subtraction, and more",
    "meta_keywords": "grade 1, math, worksheets, printable, free",
    "schemas": "{\"@type\": \"EducationalResource\"}"
  }
}
```

#### 业务逻辑实现
```go
func (uc *ResourceUseCase) GetResourceGroups(ctx context.Context, req *pb.GetResourceGroupsReq) (*pb.GetResourceGroupsResp, error) {
    // 1. 解析请求参数
    rp := &requestParser{}
    resourceReq := rp.buildResourceReq(req)

    // 2. 根据页面路径获取筛选配置
    titleData, err := uc.repo.GetResourceTitle(ctx, req.Path)
    if err != nil {
        return nil, fmt.Errorf("failed to get resource title: %w", err)
    }

    // 3. 解析筛选条件
    filter := titleData.Filter
    rf := &ResourceFilter{}
    err = json.Unmarshal([]byte(filter), rf)
    if err != nil {
        return nil, fmt.Errorf("failed to unmarshal filter: %w", err)
    }

    // 4. 应用筛选条件
    resourceReq.ResourceType = rf.ResourceType
    resourceReq.Subject = rf.Subject
    resourceReq.Grade = rf.Grade
    resourceReq.Topic = rf.Topic
    resourceReq.LearningModule = rf.LearningModule
    resourceReq.Resource = rf.Resource
    resourceReq.ResourceDetail = rf.ResourceDetail

    // 5. 获取分组资源数据
    resourceGroups, err := uc.repo.GetResourceGroup(ctx, resourceReq)
    if err != nil {
        return nil, fmt.Errorf("failed to get resource groups: %w", err)
    }

    // 6. 构建响应数据
    rb := &responseBuilder{}
    respResourceGroups := make([]*pb.EduResourceGroup, 0, len(resourceGroups))

    for _, r := range resourceGroups {
        resources := make([]*pb.EduResource, 0, len(r.Resources))
        for _, r1 := range r.Resources {
            dto, err := uc.DataToDTO(ctx, r1)
            if err != nil {
                uc.log.WithContext(ctx).Errorf("DataToDTO error: %v", err)
                continue
            }
            resources = append(resources, dto)
        }
        respResourceGroups = append(respResourceGroups, &pb.EduResourceGroup{
            Title:     r.Title,
            Resources: resources,
            Total:     int32(r.Count),
        })
    }

    return &pb.GetResourceGroupsResp{
        Groups: respResourceGroups,
        Title:  rb.buildResourceTitleResponse(titleData),
    }, nil
}
```

### 4. 获取资源列表接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/list`
- **功能**: 获取符合条件的资源列表（不分组）
- **认证**: 无需认证

#### 请求参数
```protobuf
message GetResourceListReq {
  string grade = 1;          // 年级筛选
  string subject = 2;        // 学科筛选
  string resource_type = 3;  // 资源类型筛选
  int32 page = 4;           // 页码
  int32 page_size = 5;      // 每页大小
}
```

#### 响应参数
```protobuf
message GetResourceListResp {
  repeated EduResource resources = 1;  // 资源列表
  int32 total = 2;                    // 总数量
  EduResourceTitle title = 3;         // 页面标题信息
}
```

#### 返回值样例
```json
{
  "resources": [
    {
      "id": 1001,
      "title": "Basic Addition Worksheet",
      "resource_type": "worksheets",
      "subject": "Math",
      "grade": "Grade 1,Grade 2",
      "learning_topic": "Number and Operations",
      "learning_module": "Addition",
      "resource_detail": "Single Digit Addition",
      "standards": "1.OA.1",
      "resource_description": "Practice basic addition with single digits",
      "url": "/math/grade-1/addition/basic-addition-worksheet",
      "meta_title": "Basic Addition Worksheet - Grade 1 Math",
      "meta_description": "Free printable addition worksheet for grade 1 students",
      "meta_keywords": "addition, worksheet, grade 1, math",
      "updated_at": "2024-01-15 10:30:00",
      "extra_data": {
        "pdf_path": "/files/worksheets/addition_001.pdf",
        "answer_key": "/files/answers/addition_001_key.pdf",
        "difficulty_level": "beginner"
      }
    }
  ],
  "total": 156,
  "title": {
    "url": "/math/worksheets",
    "h1_title": "Math Worksheets",
    "description": "Free printable math worksheets for all grades",
    "meta_title": "Math Worksheets - Free Printable Resources",
    "meta_description": "Download free math worksheets for students of all grades",
    "meta_keywords": "math, worksheets, printable, free, education"
  }
}
```

### 5. 获取资源详情接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/detail`
- **功能**: 获取单个资源的详细信息，包括上一个和下一个资源的链接
- **认证**: 可选（影响PDF文件访问权限）

#### 请求参数
```protobuf
message GetResourceDetailReq {
  string url = 1;            // 资源URL
  string grade = 2;          // 年级（用于导航）
  string subject = 3;        // 学科（用于导航）
  string resource_type = 4;  // 资源类型（用于导航）
}
```

#### 响应参数
```protobuf
message ResourceDetail {
  string pre_url = 1;        // 上一个资源URL
  EduResource resource = 2;  // 当前资源详情
  string next_url = 3;       // 下一个资源URL
}
```

#### 返回值样例
```json
{
  "pre_url": "/math/grade-1/addition/counting-worksheet",
  "resource": {
    "id": 1001,
    "title": "Basic Addition Worksheet",
    "resource_type": "worksheets",
    "subject": "Math",
    "grade": "Grade 1,Grade 2",
    "learning_topic": "Number and Operations",
    "learning_module": "Addition",
    "resource_detail": "Single Digit Addition",
    "standards": "1.OA.1",
    "resource_description": "Practice basic addition with single digits. This worksheet includes 20 problems focusing on adding numbers 1-10.",
    "url": "/math/grade-1/addition/basic-addition-worksheet",
    "meta_title": "Basic Addition Worksheet - Grade 1 Math",
    "meta_description": "Free printable addition worksheet for grade 1 students",
    "meta_keywords": "addition, worksheet, grade 1, math",
    "updated_at": "2024-01-15 10:30:00",
    "extra_data": {
      "pdf_path": "/files/worksheets/addition_001.pdf",
      "answer_key": "/files/answers/addition_001_key.pdf",
      "difficulty_level": "beginner",
      "problem_count": 20,
      "estimated_time": "15 minutes"
    }
  },
  "next_url": "/math/grade-1/addition/advanced-addition-worksheet"
}
```

#### 业务逻辑实现
```go
func (uc *ResourceUseCase) GetResourceDetail(ctx context.Context, req *pb.GetResourceDetailReq) (*pb.ResourceDetail, error) {
    // 1. 参数处理和转换
    gc := &gradeConverter{}
    rp := &requestParser{}

    grade := gc.getGradeName(req.Grade)
    subject, topic, learningModule, resourceDetail := rp.parseSubjectLevels(req.Subject)
    resourceType, _ := rp.parseResourceType(req.ResourceType)

    // 处理URL中的连字符
    topic = strings.ReplaceAll(topic, "-", " ")
    learningModule = strings.ReplaceAll(learningModule, "-", " ")
    resourceDetail = strings.ReplaceAll(resourceDetail, "-", " ")

    // 2. 根据URL获取资源详情
    detail, err := uc.repo.GetResourceByUrl(ctx, req.Url)
    if err != nil {
        return nil, fmt.Errorf("failed to get resource by url: %w", err)
    }

    if detail == nil {
        return &pb.ResourceDetail{
            PreUrl:   "",
            Resource: nil,
            NextUrl:  "",
        }, nil
    }

    // 3. 转换为DTO
    dto, err := uc.DataToDTO(ctx, detail)
    if err != nil {
        return nil, fmt.Errorf("failed to convert data to DTO: %w", err)
    }

    result := &pb.ResourceDetail{
        PreUrl:   "",
        Resource: dto,
        NextUrl:  "",
    }

    // 4. 获取上一个资源
    resourcePre, err := uc.repo.GetResourcePre(ctx, &model.ResourceReq{
        ResourceType:   resourceType,
        Grade:          grade,
        Subject:        subject,
        Topic:          topic,
        LearningModule: learningModule,
    }, detail.ID)

    if err == nil && resourcePre != nil {
        result.PreUrl = resourcePre.Url
    }

    // 5. 获取下一个资源
    resourceNext, err := uc.repo.GetResourceNext(ctx, &model.ResourceReq{
        ResourceType:   resourceType,
        Grade:          grade,
        Subject:        subject,
        Topic:          topic,
        LearningModule: learningModule,
    }, detail.ID)

    if err == nil && resourceNext != nil {
        result.NextUrl = resourceNext.Url
    }

    return result, nil
}
```

### 6. 获取随机资源列表接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/random`
- **功能**: 获取随机推荐的教育资源，包括工作表、涂色页和词汇表
- **认证**: 无需认证

#### 请求参数
```protobuf
message ListRandResourceReq {
  string grade = 1;           // 年级筛选
  string subject = 2;         // 学科筛选（支持 "all" 获取所有学科）
  string activity_topic = 3;  // 活动主题（用于涂色页筛选）
  int32 page_size = 4;        // 每种资源类型的数量
  string exclude_urls = 5;    // 排除的资源URL列表（逗号分隔）
}
```

#### 响应参数
```protobuf
message ListRandResourceResp {
  repeated EduResource worksheets = 1;     // 工作表列表
  repeated EduResource coloring_pages = 2; // 涂色页列表
  repeated EduTermInfo terms = 3;          // 词汇表列表
}

message EduTermInfo {
  uint32 id = 1;              // 词汇ID
  string term = 2;            // 词汇
  string definition = 3;      // 定义
  string subject = 4;         // 学科
  string grade = 5;           // 年级
  string url = 6;             // 词汇URL
}
```

#### 返回值样例
```json
{
  "worksheets": [
    {
      "id": 1001,
      "title": "Basic Addition Worksheet",
      "resource_type": "worksheets",
      "subject": "Math",
      "grade": "Grade 1,Grade 2",
      "learning_topic": "Number and Operations",
      "learning_module": "Addition",
      "url": "/math/grade-1/addition/basic-addition-worksheet",
      "meta_title": "Basic Addition Worksheet - Grade 1 Math",
      "updated_at": "2024-01-15 10:30:00"
    },
    {
      "id": 1015,
      "title": "Phonics Practice Sheet",
      "resource_type": "worksheets", 
      "subject": "English",
      "grade": "Grade 1,Grade 2",
      "learning_topic": "Reading",
      "learning_module": "Phonics",
      "url": "/english/grade-1/phonics/practice-sheet",
      "meta_title": "Phonics Practice Sheet - Grade 1 English",
      "updated_at": "2024-01-15 11:15:00"
    }
  ],
  "coloring_pages": [
    {
      "id": 2001,
      "title": "Animals Coloring Page",
      "resource_type": "coloring Pages",
      "subject": "Art",
      "grade": "Grade K,Grade 1,Grade 2",
      "learning_topic": "Animals",
      "url": "/coloring-pages/animals/farm-animals",
      "meta_title": "Farm Animals Coloring Page",
      "updated_at": "2024-01-14 09:30:00"
    }
  ],
  "terms": [
    {
      "id": 3001,
      "term": "Addition",
      "definition": "The process of combining two or more numbers to find their total sum",
      "subject": "Math",
      "grade": "Grade 1,Grade 2,Grade 3",
      "url": "/glossary/math/addition"
    },
    {
      "id": 3002,
      "term": "Phoneme",
      "definition": "The smallest unit of sound in a language that can change the meaning of a word",
      "subject": "English",
      "grade": "Grade K,Grade 1,Grade 2",
      "url": "/glossary/english/phoneme"
    }
  ]
}
```

#### 业务逻辑实现
```go
func (uc *ResourceUseCase) ListRandResource(ctx context.Context, req *pb.ListRandResourceReq) (*pb.ListRandResourceResp, error) {
    rp := &requestParser{}
    gc := &gradeConverter{}

    subject, topic, learningModule, resourceDetail := rp.parseSubjectLevels(req.Subject)
    grade := gc.getGradeName(req.Grade)

    worksheets := make([]*model.EduResources, 0)
    
    // 处理全学科查询
    if req.Subject == "all" {
        // 获取数学工作表（一半数量）
        mathWorksheets, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
            ResourceType:   "worksheets",
            Grade:          grade,
            Subject:        "math",
            Topic:          topic,
            LearningModule: learningModule,
            ResourceDetail: resourceDetail,
            PageSize:       int(req.PageSize / 2),
        })
        if err != nil {
            uc.log.WithContext(ctx).Warnf("failed to get math worksheets: %v", err)
        }

        // 获取英语工作表（一半数量）
        elaWorksheets, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
            ResourceType:   "worksheets",
            Grade:          grade,
            Subject:        "english",
            Topic:          topic,
            LearningModule: learningModule,
            ResourceDetail: resourceDetail,
            PageSize:       int(req.PageSize / 2),
        })
        if err != nil {
            uc.log.WithContext(ctx).Warnf("failed to get english worksheets: %v", err)
        }

        pageSize := int(req.PageSize)
        if pageSize == 0 {
            pageSize = 6
        }
        worksheets = append(worksheets, mathWorksheets[:int(pageSize/2)]...)
        worksheets = append(worksheets, elaWorksheets[:int(pageSize/2)]...)
    } else {
        // 获取指定学科工作表
        worksheets1, err1 := uc.repo.ListRandResources(ctx, &model.ResourceReq{
            ResourceType:   "worksheets",
            Grade:          grade,
            Subject:        subject,
            Topic:          topic,
            LearningModule: learningModule,
            ResourceDetail: resourceDetail,
            PageSize:       int(req.PageSize),
        })
        if err1 != nil {
            uc.log.WithContext(ctx).Errorf("failed to get worksheets: %v", err1)
        }
        worksheets = append(worksheets, worksheets1...)
    }

    // 获取涂色页资源
    coloringPages, err := uc.repo.ListRandResources(ctx, &model.ResourceReq{
        ResourceType: "coloring Pages",
        Topic:        req.ActivityTopic,
        PageSize:     int(req.PageSize),
    })
    if err != nil {
        uc.log.WithContext(ctx).Errorf("failed to get coloring pages: %v", err)
    }

    // 获取词汇表
    if subject == "all" {
        subject = ""
    }
    glossarys, err := uc.eduRepo.ListRandTerm(ctx, subject, int(req.PageSize))
    if err != nil {
        uc.log.WithContext(ctx).Errorf("failed to get glossarys: %v", err)
    }

    if req.PageSize == 0 {
        req.PageSize = 10
    }
    
    // 构建响应，按年级排序
    pbWorksheets := uc.buildResourceList(ctx, worksheets, req.ExcludeUrls, int(req.PageSize))
    slices.SortFunc(pbWorksheets, func(a, b *pb.EduResource) int {
        gradeA, errA := MinGradeNumber(a.Grade)
        gradeB, errB := MinGradeNumber(b.Grade)
        if errA != nil && errB != nil {
            return 0
        }
        if errA != nil {
            return 1 // 把非法年级排后面
        }
        if errB != nil {
            return -1
        }
        return gradeA - gradeB
    })
    
    pbColoringPages := uc.buildResourceList(ctx, coloringPages, req.ExcludeUrls, int(req.PageSize))
    slices.SortFunc(pbColoringPages, func(a, b *pb.EduResource) int {
        gradeA, errA := MinGradeNumber(a.Grade)
        gradeB, errB := MinGradeNumber(b.Grade)
        if errA != nil && errB != nil {
            return 0
        }
        if errA != nil {
            return 1
        }
        if errB != nil {
            return -1
        }
        return gradeA - gradeB
    })
    
    pbGlossarys := uc.buildTermList(glossarys)

    return &pb.ListRandResourceResp{
        Worksheets:    pbWorksheets,
        ColoringPages: pbColoringPages,
        Terms:         pbGlossarys,
    }, nil
}

// 年级排序辅助函数
func MinGradeNumber(grades string) (int, error) {
    gradeMap := map[string]int{
        "grade pre-k": 0,
        "grade k":     1,
        "grade 1":     2,
        "grade 2":     3,
        "grade 3":     4,
        "grade 4":     5,
        "grade 5":     6,
        "grade 6":     7,
    }
    minGrade := -1
    for _, g := range strings.Split(grades, ",") {
        grade := strings.ToLower(strings.TrimSpace(g))
        if val, ok := gradeMap[grade]; ok {
            if minGrade == -1 || val < minGrade {
                minGrade = val
            }
        }
    }
    if minGrade == -1 {
        return 0, fmt.Errorf("invalid grade: %s", grades)
    }
    return minGrade, nil
}
```

### 7. 获取资源元数据接口

#### 接口信息
- **路径**: `GET /intelligence/api/v1/resource/meta`
- **功能**: 一次性获取所有元数据信息，包括年级、学科、资源类型
- **认证**: 无需认证

#### 请求参数
```protobuf
// 无需参数，使用 google.protobuf.Empty
```

#### 响应参数
```protobuf
message ResourceMetaResp {
  repeated ResourceMeta metas = 1;
}

message ResourceMeta {
  string name = 1;                    // 元数据名称
  ResourceMeta_Children children = 2; // 子数据
}

message ResourceMeta_Children {
  repeated EduGrade grades = 1;       // 年级列表
  repeated EduSubjectNode subjects = 2; // 学科列表
  repeated ResourceType types = 3;    // 资源类型列表
}
```

#### 返回值样例
```json
{
  "metas": [
    {
      "name": "Resources",
      "children": {
        "grades": [
          {
            "name": "Pre-Kindergarten",
            "value": "Pre-K"
          },
          {
            "name": "Kindergarten",
            "value": "K"
          }
        ],
        "subjects": [
          {
            "name": "Math",
            "nodes": [
              {
                "name": "Number and Operations",
                "nodes": [
                  {
                    "name": "Addition",
                    "nodes": []
                  }
                ]
              }
            ]
          }
        ],
        "types": [
          {
            "name": "worksheets",
            "nodes": []
          },
          {
            "name": "coloring Pages",
            "nodes": [
              {
                "name": "Animals"
              }
            ]
          }
        ]
      }
    }
  ]
}
```

## 数据模型

### 1. 教育资源主表 (edu_resources)

```sql
CREATE TABLE edu_resources (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  resource_type VARCHAR(20) DEFAULT '' NOT NULL COMMENT '资源类型 worksheet video coloringpage',
  resource_code VARCHAR(50) DEFAULT '' NOT NULL COMMENT '资源编码',
  subject VARCHAR(20) DEFAULT '' NOT NULL COMMENT '学科',
  grade VARCHAR(10) DEFAULT '' NOT NULL COMMENT '年级',
  title VARCHAR(500) DEFAULT '' NOT NULL COMMENT '标题',
  topic VARCHAR(50) DEFAULT '' NOT NULL COMMENT '二级知识点',
  learning_module VARCHAR(50) DEFAULT '' NOT NULL COMMENT '三级知识点',
  resource VARCHAR(50) DEFAULT '' NOT NULL COMMENT '资源分类',
  resource_detail VARCHAR(500) DEFAULT '' NOT NULL COMMENT '四级知识点',
  standards VARCHAR(100) DEFAULT '' NOT NULL COMMENT '最细粒度知识点',
  resource_description VARCHAR(1024) DEFAULT '' NOT NULL COMMENT '简介',
  extra_data TEXT DEFAULT '' NOT NULL COMMENT '内容 以json格式存储',
  status INT DEFAULT 1 NOT NULL COMMENT '状态 1 上线 2 删除',
  url VARCHAR(500) DEFAULT '' NOT NULL COMMENT '资源URL',
  meta_description VARCHAR(1000) DEFAULT '' NOT NULL COMMENT 'SEO描述',
  meta_title VARCHAR(500) DEFAULT '' NOT NULL COMMENT 'SEO标题',
  meta_keywords VARCHAR(500) DEFAULT '' NOT NULL COMMENT 'SEO关键词',
  schemas TEXT DEFAULT '' NOT NULL COMMENT '结构化数据',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间'
);
```

### 2. 资源标题配置表 (edu_resources_title)

```sql
CREATE TABLE edu_resources_title (
  id INT PRIMARY KEY AUTO_INCREMENT,
  url VARCHAR(500) NOT NULL COMMENT '页面URL',
  h1_title VARCHAR(500) NOT NULL COMMENT 'H1标题',
  description TEXT NOT NULL COMMENT '页面描述',
  meta_title VARCHAR(500) NOT NULL COMMENT 'SEO标题',
  meta_description VARCHAR(1000) NOT NULL COMMENT 'SEO描述',
  meta_keywords VARCHAR(500) NOT NULL COMMENT 'SEO关键词',
  schemas TEXT NOT NULL COMMENT '结构化数据',
  filter TEXT NOT NULL COMMENT '筛选条件',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. 资源URL映射表 (edu_resources_url)

```sql
CREATE TABLE edu_resources_url (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  resource_code VARCHAR(50) NOT NULL COMMENT '资源编码',
  url VARCHAR(500) NOT NULL COMMENT '资源URL',
  url_md5 VARCHAR(32) NOT NULL COMMENT 'URL的MD5值',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 4. 数据结构定义

```go
// 年级结构
type Grade struct {
    Name  string `json:"name"`   // 年级名称
    Value string `json:"value"`  // 年级值
}

// 资源主题结构
type ResourceTopic struct {
    Name    string   `json:"name"`    // 主题名称
    Modules []string `json:"modules"` // 包含的模块
}

// 学科节点结构
type SubjectNode struct {
    Name  string         `json:"name"`  // 节点名称
    Nodes []*SubjectNode `json:"nodes"` // 子节点
}

// 学科结构
type Subject struct {
    Nodes []*SubjectNode `json:"nodes"` // 学科树
}

// 资源类型结构
type ResourceType struct {
    Name  string `json:"name"`  // 类型名称
    Topic string `json:"topic"` // 关联主题
}

// 资源元数据
type EduResourceMeta struct {
    Grades        []*Grade        `json:"grades"`         // 年级列表
    Subjects      []*Subject      `json:"subjects"`       // 学科列表
    ResourceTypes []*ResourceType `json:"resource_types"` // 资源类型列表
}

// 资源分组
type EduResourceGroup struct {
    Title     string          `json:"title"`     // 分组标题
    Count     int             `json:"count"`     // 资源数量
    Resources []*EduResources `json:"resources"` // 资源列表
}
```

## 业务逻辑层

### ResourceUseCase

**主要功能**:
- 资源多条件查询
- 分页和排序
- 资源分组
- SEO信息管理
- URL路由管理

**核心方法**:
```go
func (uc *ResourceUseCase) GetResourceList(ctx context.Context, req *ResourceReq) ([]*EduResourceGroup, int, error)
func (uc *ResourceUseCase) GetResourceMeta(ctx context.Context) (*EduResourceMeta, error)
func (uc *ResourceUseCase) GetResourceDetail(ctx context.Context, code string) (*EduResources, error)
func (uc *ResourceUseCase) GetResourceByURL(ctx context.Context, url string) (*EduResources, error)
func (uc *ResourceUseCase) GetResourceTitle(ctx context.Context, url string) (*EduResourcesTitle, error)
```

**查询逻辑**:
```go
func (uc *ResourceUseCase) buildResourceQuery(req *ResourceReq) *gorm.DB {
    query := uc.eduResourceDao.DB.Model(&model.EduResources{}).Where("status = ?", 1)
    
    if req.ResourceType != "" {
        query = query.Where("resource_type = ?", req.ResourceType)
    }
    if req.Grade != "" {
        query = query.Where("grade = ?", req.Grade)
    }
    if req.Subject != "" {
        query = query.Where("subject = ?", req.Subject)
    }
    if req.Topic != "" {
        query = query.Where("topic = ?", req.Topic)
    }
    if req.LearningModule != "" {
        query = query.Where("learning_module = ?", req.LearningModule)
    }
    
    return query
}
```

## 数据访问层

### EduResourceDao

**主要方法**:
```go
// 基础CRUD操作
func (d *EduResourceDao) GetResourceList(ctx context.Context, req *model.ResourceReq) ([]*model.EduResources, error)
func (d *EduResourceDao) GetResourceGroup(ctx context.Context, req *model.ResourceReq) ([]*model.EduResourceGroup, error)
func (d *EduResourceDao) GetResourceByUrl(ctx context.Context, url string) (*model.EduResources, error)
func (d *EduResourceDao) GetResourceTitle(ctx context.Context, url string) (*model.EduResourcesTitle, error)
func (d *EduResourceDao) GetResourcePre(ctx context.Context, req *model.ResourceReq, id int64) (*model.EduResources, error)
func (d *EduResourceDao) GetResourceNext(ctx context.Context, req *model.ResourceReq, id int64) (*model.EduResources, error)

// 元数据获取
func (d *EduResourceDao) GetGrade(ctx context.Context) ([]*model.Grade, error)
func (d *EduResourceDao) GetSubject(ctx context.Context) ([]*model.SubjectNode, error)
func (d *EduResourceDao) GetResourceType(ctx context.Context) ([]*model.ResourceType, error)

// 随机资源获取
func (d *EduResourceDao) ListRandResources(ctx context.Context, req *model.ResourceReq) ([]*model.EduResources, error)

// 缓存管理
func (d *EduResourceDao) ClearCache(ctx context.Context) error
func (d *EduResourceDao) RefreshCache(ctx context.Context) error
```

### 查询构建器实现

#### queryBuilder 设计模式
```go
type queryBuilder struct {
    db *gorm.DB
}

func newQueryBuilder(db *gorm.DB) *queryBuilder {
    return &queryBuilder{db: db}
}

// 链式调用构建查询条件
func (qb *queryBuilder) applyGradeFilter(grade string) *queryBuilder {
    if grade != "" {
        // 使用 FIND_IN_SET 支持多年级查询
        qb.db = qb.db.Where("FIND_IN_SET(?,grade)", grade)
    }
    return qb
}

func (qb *queryBuilder) applySubjectFilter(subject string) *queryBuilder {
    if subject != "" {
        // 支持通用学科和特定学科
        qb.db = qb.db.Where("subject in ?", []string{"SubjectAll", subject})
    }
    return qb
}

func (qb *queryBuilder) applyResourceTypeFilter(resourceType string) *queryBuilder {
    if resourceType != "" {
        qb.db = qb.db.Where("resource_type = ?", resourceType)
    }
    return qb
}

func (qb *queryBuilder) applyTopicFilter(topic string) *queryBuilder {
    if topic != "" {
        qb.db = qb.db.Where("topic = ?", topic)
    }
    return qb
}

func (qb *queryBuilder) applyLearningModuleFilter(learningModule string) *queryBuilder {
    if learningModule != "" {
        qb.db = qb.db.Where("learning_module = ?", learningModule)
    }
    return qb
}

func (qb *queryBuilder) applyPagination(page, pageSize int) *queryBuilder {
    if page > 0 && pageSize > 0 {
        offset := (page - 1) * pageSize
        qb.db = qb.db.Offset(offset).Limit(pageSize)
    }
    return qb
}

func (qb *queryBuilder) build() *gorm.DB {
    return qb.db
}
```

#### 使用示例
```go
func (d *EduResourceDao) GetResourceList(ctx context.Context, req *model.ResourceReq) ([]*model.EduResources, error) {
    var resources []*model.EduResources

    // 使用查询构建器链式构建查询
    qb := newQueryBuilder(d.data.DB.WithContext(ctx).Model(&model.EduResources{}))
    qb.applyGradeFilter(req.Grade).
        applySubjectFilter(req.Subject).
        applyResourceTypeFilter(req.ResourceType).
        applyTopicFilter(req.Topic).
        applyLearningModuleFilter(req.LearningModule).
        applyResourceDetailFilter(req.ResourceDetail).
        applyResourceFilter(req.Resource).
        applyPagination(req.Page, req.PageSize).
        applyOrderBy("created_at desc")

    if err := qb.build().Find(&resources).Error; err != nil {
        return nil, err
    }

    return resources, nil
}
```

### 分组查询实现

#### 动态分组逻辑
```go
func (d *EduResourceDao) GetResourceGroup(ctx context.Context, req *model.ResourceReq) ([]*model.EduResourceGroup, error) {
    // 1. 确定分组字段
    groupField := d.determineGroupField(req)

    if groupField == "" {
        // 无分组，直接返回资源列表
        return d.getResourceGroupWithoutGrouping(ctx, req)
    }

    // 有分组，按字段分组返回
    return d.getResourceGroupWithGrouping(ctx, req, groupField)
}

// 分组字段决策逻辑
func (d *EduResourceDao) determineGroupField(req *model.ResourceReq) string {
    // 如果没有指定主题，按主题分组
    if req.Topic == "" {
        return "topic"
    }

    // 涂色页不按学习模块分组
    if strings.Contains(strings.ToLower(strings.ReplaceAll(req.ResourceType, " ", "")), "coloringpages") {
        return ""
    }

    // 如果没有指定学习模块，按学习模块分组
    if req.LearningModule == "" {
        return "learning_module"
    }

    return ""
}
```

#### 分组查询实现
```go
func (d *EduResourceDao) getResourceGroupWithGrouping(ctx context.Context, req *model.ResourceReq, groupField string) ([]*model.EduResourceGroup, error) {
    var groupResults []*GroupResult
    var resources []*model.EduResources

    // 1. 获取分组统计信息
    fields := fmt.Sprintf("%s as group_name, count(id) as count, GROUP_CONCAT(id) as ids", groupField)
    qb := newQueryBuilder(d.data.DB.WithContext(ctx).Model(&model.EduResources{}))
    qb.applyGradeFilter(req.Grade).
        applySubjectFilter(req.Subject).
        applyResourceTypeFilter(req.ResourceType).
        applyTopicFilter(req.Topic).
        applyLearningModuleFilter(req.LearningModule)

    if err := qb.build().Select(fields).Group(groupField).Find(&groupResults).Error; err != nil {
        return nil, err
    }

    // 2. 获取每组的资源详情（限制每组4个）
    idList := d.extractResourceIds(groupResults)
    if err := d.data.DB.Where("id in ?", idList).Find(&resources).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return []*model.EduResourceGroup{}, nil
        }
        return nil, err
    }

    // 3. 构建分组数据
    titleCountMap := make(map[string]int)
    for _, resource := range groupResults {
        titleCountMap[resource.GroupName] = resource.Count
    }

    return d.buildResourceGroups(resources, req, titleCountMap), nil
}

// 提取每组前4个资源ID
func (d *EduResourceDao) extractResourceIds(groupResults []*GroupResult) []int {
    idList := make([]int, 0)

    for _, groupResult := range groupResults {
        ids := strings.Split(groupResult.Ids, ",")
        count := 0
        for _, id := range ids {
            if idInt, err := strconv.Atoi(id); err == nil {
                idList = append(idList, idInt)
                count++
                if count >= 4 { // 每组最多4个资源
                    break
                }
            }
        }
    }

    return idList
}
```

### 随机查询实现

#### SQL随机查询优化
```go
func (d *EduResourceDao) ListRandResources(ctx context.Context, req *model.ResourceReq) ([]*model.EduResources, error) {
    var resources []*model.EduResources

    // 构建筛选条件
    filter := d.buildRandomFilter(req)

    if req.PageSize == 0 {
        req.PageSize = defaultPageSize
    }
    pageSize := req.PageSize + randomExtraCount // 多查询几个作为备选

    // 使用原生SQL进行随机查询（性能更好）
    query := fmt.Sprintf("SELECT * FROM edu_resources WHERE %s ORDER BY RAND() LIMIT %d", filter, pageSize)

    if err := d.data.DB.WithContext(ctx).Raw(query).Scan(&resources).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return []*model.EduResources{}, nil
        }
        return nil, err
    }

    // 如果结果不够，降级查询（去掉部分筛选条件）
    if len(resources) < pageSize {
        req.Topic = ""
        req.LearningModule = ""
        req.ResourceDetail = ""
        req.Resource = ""

        filter1 := d.buildRandomFilter(req)
        query := fmt.Sprintf("SELECT * FROM edu_resources WHERE %s ORDER BY RAND() LIMIT %d", filter1, pageSize)

        if err := d.data.DB.WithContext(ctx).Raw(query).Scan(&resources).Error; err != nil {
            if errors.Is(err, gorm.ErrRecordNotFound) {
                return []*model.EduResources{}, nil
            }
            return nil, err
        }
    }

    return resources, nil
}

// 构建随机查询的筛选条件
func (d *EduResourceDao) buildRandomFilter(req *model.ResourceReq) string {
    filters := []string{"1=1"} // 基础条件

    if req.Grade != "" {
        filters = append(filters, fmt.Sprintf("FIND_IN_SET('%s',grade)", req.Grade))
    }
    if req.Subject != "" {
        filters = append(filters, fmt.Sprintf("subject in ('SubjectAll', '%s')", req.Subject))
    }
    if req.ResourceType != "" {
        filters = append(filters, fmt.Sprintf("resource_type = '%s'", req.ResourceType))
    }
    if req.Topic != "" {
        filters = append(filters, fmt.Sprintf("topic = '%s'", req.Topic))
    }
    if req.LearningModule != "" {
        filters = append(filters, fmt.Sprintf("learning_module = '%s'", req.LearningModule))
    }

    return strings.Join(filters, " AND ")
}
```

### URL映射查询

#### 基于MD5的URL查询
```go
func (d *EduResourceDao) GetResourceByUrl(ctx context.Context, url string) (*model.EduResources, error) {
    // 1. 计算URL的MD5值
    urlMd5 := fmt.Sprintf("%x", md5.Sum([]byte(url)))

    // 2. 从URL映射表查询资源编码
    var urlRecord *model.EduResourcesURL
    db := d.data.DB.WithContext(ctx).Model(&model.EduResourcesURL{})
    
    if err := db.Where("url_md5 = ?", urlMd5).First(&urlRecord).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil // URL不存在
        }
        return nil, fmt.Errorf("failed to find resource by url_md5: %w", err)
    }

    // 3. 根据资源编码查询资源详情
    var resource *model.EduResources
    resourceDb := d.data.DB.WithContext(ctx).Model(&model.EduResources{})

    if err := resourceDb.Where("resource_code = ?", urlRecord.ResourceCode).First(&resource).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil
        }
        return nil, fmt.Errorf("failed to find resource by resource_code: %w", err)
    }

    return resource, nil
}
```

### 缓存管理实现

#### 缓存管理器
```go
type cacheManager struct {
    rdb *data.Data
}

// 从缓存获取数据
func (cm *cacheManager) getFromCache(ctx context.Context, key string, result interface{}) bool {
    val, err := cm.rdb.Rdb.Get(ctx, key).Result()
    if err != nil || val == "" {
        return false
    }

    if err := json.Unmarshal([]byte(val), result); err != nil {
        return false
    }
    return true
}

// 设置缓存数据
func (cm *cacheManager) setCache(ctx context.Context, key string, data interface{}) {
    if marshaled, err := json.Marshal(data); err == nil {
        _ = cm.rdb.Rdb.Set(ctx, key, marshaled, cacheExpiration).Err()
    }
}

// 删除指定缓存
func (cm *cacheManager) deleteCache(ctx context.Context, key string) error {
    return cm.rdb.Rdb.Del(ctx, key).Err()
}

// 清除所有相关缓存
func (cm *cacheManager) clearAllCache(ctx context.Context) error {
    keys := []string{cacheKeyGrades, cacheKeySubjects, cacheKeyTypes}
    return cm.rdb.Rdb.Del(ctx, keys...).Err()
}
```

#### 缓存键设计
```go
const (
    cacheKeyGrades   = "hw_paas_service:edu_resource:grades"
    cacheKeySubjects = "hw_paas_service:edu_resource:subjects"
    cacheKeyTypes    = "hw_paas_service:edu_resource:types"
    
    // 缓存过期时间
    cacheExpiration = 12 * time.Hour
)
```

#### 缓存刷新机制
```go
func (d *EduResourceDao) RefreshCache(ctx context.Context) error {
    // 1. 清除所有缓存
    if err := d.ClearCache(ctx); err != nil {
        return fmt.Errorf("failed to clear cache: %w", err)
    }

    // 2. 重新构建缓存
    if _, err := d.GetGrade(ctx); err != nil {
        d.log.Errorf("failed to refresh grade cache: %v", err)
    }

    if _, err := d.GetSubject(ctx); err != nil {
        d.log.Errorf("failed to refresh subject cache: %v", err)
    }

    if _, err := d.GetResourceType(ctx); err != nil {
        d.log.Errorf("failed to refresh resource type cache: %v", err)
    }

    return nil
}
```

**查询优化特性**:
- 复合索引优化多条件查询
- 查询构建器模式提高代码复用性
- 分组查询减少数据传输量
- 随机查询使用原生SQL提高性能
- 多级缓存策略减少数据库压力
- URL MD5映射提高查询效率

## 资源分类体系

### 1. 年级分类
```go
var DefaultGrades = []*Grade{
    {Name: "Pre-Kindergarten", Value: "Pre-K"},
    {Name: "Kindergarten", Value: "K"},
    {Name: "Grade 1", Value: "1"},
    {Name: "Grade 2", Value: "2"},
    {Name: "Grade 3", Value: "3"},
    {Name: "Grade 4", Value: "4"},
    {Name: "Grade 5", Value: "5"},
    {Name: "Grade 6", Value: "6"},
}
```

### 2. 学科分类
- **Math**: 数学
- **English**: 英语
- **Science**: 科学
- **Social Studies**: 社会研究
- **Art**: 艺术

### 3. 资源类型
- **worksheet**: 工作表
- **video**: 视频
- **coloringpage**: 涂色页

### 4. 知识点层级
1. **Topic**: 二级知识点（主题）
2. **Learning Module**: 三级知识点（学习模块）
3. **Resource**: 资源分类
4. **Resource Detail**: 四级知识点
5. **Standards**: 最细粒度知识点

## SEO优化

### 1. 元数据管理
```go
type SEOData struct {
    Title       string `json:"title"`       // 页面标题
    Description string `json:"description"` // 页面描述
    Keywords    string `json:"keywords"`    // 关键词
    Schemas     string `json:"schemas"`     // 结构化数据
}

func (uc *ResourceUseCase) generateSEOData(resource *EduResources) *SEOData {
    return &SEOData{
        Title:       fmt.Sprintf("%s - %s %s Resources", resource.Title, resource.Grade, resource.Subject),
        Description: resource.ResourceDescription,
        Keywords:    fmt.Sprintf("%s, %s, %s, education", resource.Subject, resource.Grade, resource.ResourceType),
        Schemas:     uc.generateSchemas(resource),
    }
}
```

### 2. URL管理
- 友好URL结构
- 资源编码映射
- 重定向管理

### 3. 结构化数据
- Schema.org标准
- 教育资源标记
- 搜索引擎优化

## 缓存策略

### 1. 多级缓存
```go
// L1: 本地缓存（热门资源）
func (uc *ResourceUseCase) getFromLocalCache(key string) (*EduResources, bool) {
    if value, ok := uc.localCache.Get(key); ok {
        return value.(*EduResources), true
    }
    return nil, false
}

// L2: Redis缓存
func (uc *ResourceUseCase) getFromRedisCache(ctx context.Context, key string) (*EduResources, error) {
    cached, err := uc.rdb.Get(ctx, key).Result()
    if err != nil {
        return nil, err
    }
    
    var resource EduResources
    err = json.Unmarshal([]byte(cached), &resource)
    return &resource, err
}
```

### 2. 缓存策略
- 资源详情缓存24小时
- 元数据缓存1小时
- 查询结果缓存30分钟

## 性能优化

### 1. 数据库优化
```sql
-- 复合索引
CREATE INDEX idx_resource_type_grade_subject ON edu_resources(resource_type, grade, subject);
CREATE INDEX idx_topic_module ON edu_resources(topic, learning_module);
CREATE INDEX idx_status_created ON edu_resources(status, created_at);

-- URL映射索引
CREATE UNIQUE INDEX idx_url_md5 ON edu_resources_url(url_md5);
```

### 2. 查询优化
- 分页查询限制
- 字段选择优化
- 连表查询优化

### 3. 缓存预热
```go
func (uc *ResourceUseCase) warmupCache(ctx context.Context) {
    // 预热热门资源
    popularResources := uc.getPopularResources(ctx)
    for _, resource := range popularResources {
        uc.cacheResource(ctx, resource)
    }
    
    // 预热元数据
    uc.cacheMetadata(ctx)
}
```

## 监控和指标

### 关键指标
- 资源查询QPS
- 缓存命中率
- 平均响应时间
- 热门资源统计

### 日志记录
```go
func (uc *ResourceUseCase) logResourceAccess(ctx context.Context, resourceCode string, userAgent string) {
    logData := map[string]interface{}{
        "resource_code": resourceCode,
        "user_agent":   userAgent,
        "timestamp":    time.Now().Unix(),
        "trace_id":     custom_context.GetTraceId(ctx),
    }
    
    uc.log.WithContext(ctx).Info("resource_access", logData)
}
```

## 配置项

### 业务配置
```yaml
biz:
  grades:
    - name: "Pre-Kindergarten"
      value: "Pre-K"
    - name: "Kindergarten"
      value: "K"
    - name: "Grade 1"
      value: "1"
```

## 错误处理

### 常见错误
1. **资源不存在**: 请求的资源ID或编码不存在
2. **参数错误**: 筛选条件参数不合法
3. **权限不足**: 用户无权访问某些资源
4. **服务超时**: 数据库查询超时

### 容错机制
- 默认值处理
- 降级策略
- 重试机制
- 异常监控

## 安全特性

### 1. 访问控制
- 资源状态检查
- 用户权限验证
- IP访问限制

### 2. 数据安全
- 输入参数验证
- SQL注入防护
- XSS攻击防护

### 3. 内容安全
- 资源内容审核
- 敏感信息过滤
- 版权保护

这个资源管理模块为教育平台提供了完整的资源管理解决方案，支持多维度的资源组织和高效的检索功能。