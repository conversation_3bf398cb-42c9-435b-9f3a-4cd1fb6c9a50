# AI服务模块文档

## 模块概述

AI服务模块是hw-paas-service项目的核心模块，提供多种AI辅助教学功能，包括口算批改、英文纠错、题目管理、博客管理、用户认证等服务。

## API接口定义

### 服务定义 (api/ai/v1/ai.proto)

```protobuf
service Ai {
  // 口算批改
  rpc QueryCorrect (QueryCorrectRequest) returns (google.protobuf.Struct);
  
  // 反馈追踪
  rpc FeedbackTrace (FeedbackTraceRequest) returns (FeedbackTraceReply);
  
  // 题目管理
  rpc ListQuestions (ListQuestionsRequest) returns (ListQuestionsReply);
  rpc GetQuestion (GetQuestionRequest) returns (Question);
  rpc GetQuestionBatch (GetQuestionBatchRequest) returns (GetQuestionBatchReply);
  rpc UpsertQuestion (UpsertQuestionRequest) returns (google.protobuf.Struct);
  rpc UpdateQuestionWithKey (UpdateQuestionWithKeyRequest) returns (google.protobuf.Struct);
  
  // 博客管理
  rpc BlogList (BlogListReq) returns (BlogListResp);
  rpc BlogDetail (BlogDetailReq) returns (BlogDetailResp);
  rpc SetBlog (BlogArticle) returns (google.protobuf.Struct);
  rpc BlogCategory (BlogCategoryReq) returns (BlogCategoryResp);
  rpc BlogFeedback (BlogFeedbackReq) returns (google.protobuf.Struct);
  
  // 用户认证
  rpc CodeLogin (CodeLoginReq) returns (CodeLoginResp);
  rpc LoginOut (LoginOutReq) returns (LoginOutResp);
  rpc CheckLogin (CheckLoginReq) returns (CheckLoginResp);
  rpc CheckEmailExists (CheckEmailExistsReq) returns (CheckEmailExistsResp);
  
  // 通用配置
  rpc CommonConfig (CommonConfigReq) returns (CommonConfigResp);
  rpc TaskDrive (TaskDriveReq) returns (TaskDriveResp);
}
```

### 主要接口

#### 1. 口算批改接口
- **路径**: `POST /intelligence/api/ai/v1/query_correct`
- **功能**: 对上传的口算题图片进行AI批改
- **请求参数**: 
  - `image_url`: 图片URL
- **响应**: 批改结果（JSON格式）

#### 2. 反馈追踪接口
- **路径**: `POST /intelligence/api/ai/v2/feedback/trace`
- **功能**: 收集用户对AI服务的反馈
- **请求参数**:
  - `biz`: 业务类型（1:口算批改;2:指尖查词;3:作业批改;4:指尖查词;5:语音查词;6:绘本指读）
  - `trace_id`: 追踪ID
  - `feedback`: 反馈内容

#### 3. 题目管理接口
- **列表查询**: `GET /intelligence/api/ai/v1/questions`
- **详情查询**: `GET /intelligence/api/ai/v1/questions/detail`
- **批量查询**: `GET /intelligence/api/ai/v1/questions/detail_batch`

#### 4. 博客管理接口
- **博客列表**: 支持分页和分类筛选
- **博客详情**: 根据路径获取博客内容
- **博客分类**: 获取所有博客分类

## 数据模型

### 1. 题目模型 (ai_jzx_question)

```sql
CREATE TABLE ai_jzx_question (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  q_type INT NOT NULL COMMENT '题目类型',
  difficulty INT NOT NULL COMMENT '难度等级',
  grade VARCHAR(20) NOT NULL COMMENT '年级',
  subject VARCHAR(50) NOT NULL COMMENT '学科',
  knowledge VARCHAR(100) NOT NULL COMMENT '知识点',
  knowledge_no VARCHAR(50) NOT NULL COMMENT '知识点编号',
  question TEXT NOT NULL COMMENT '题目内容',
  answer TEXT NOT NULL COMMENT '答案',
  solution TEXT COMMENT '解题步骤',
  note TEXT COMMENT '备注',
  status INT DEFAULT 1 COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 博客文章模型 (blog_articles)

```sql
CREATE TABLE blog_articles (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  path VARCHAR(255) NOT NULL COMMENT '文章路径',
  category VARCHAR(100) NOT NULL COMMENT '分类',
  article_title VARCHAR(500) NOT NULL COMMENT '标题',
  short_content TEXT COMMENT '简介',
  cover_img VARCHAR(500) COMMENT '封面图',
  author_avatar VARCHAR(500) COMMENT '作者头像',
  author_name VARCHAR(100) COMMENT '作者姓名',
  article_content LONGTEXT COMMENT '文章内容',
  page_title VARCHAR(500) COMMENT 'SEO标题',
  meta_keywords VARCHAR(500) COMMENT 'SEO关键词',
  meta_description TEXT COMMENT 'SEO描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. 反馈追踪模型 (feedback_trace)

```sql
CREATE TABLE feedback_trace (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  biz TINYINT NOT NULL COMMENT '业务类型',
  trace_id VARCHAR(100) NOT NULL COMMENT '追踪ID',
  content TEXT NOT NULL COMMENT '反馈内容',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 业务逻辑层

### CorrectUseCase (口算批改用例)

**主要功能**:
- 图片下载和处理
- 调用口算批改服务
- 结果追踪和日志记录

**核心方法**:
```go
func (uc *CorrectUseCase) QueryCorrect(ctx context.Context, imageUrl string) (res interface{}, err error)
func (uc *CorrectUseCase) FeedbackTrace(ctx context.Context, req *pb.FeedbackTraceRequest) (*pb.FeedbackTraceReply, error)
func (uc *CorrectUseCase) CommonConfig() (*pb.CommonConfigResp, error)
func (uc *CorrectUseCase) TaskDrive(ctx context.Context, app string) (*pb.TaskDriveResp, error)
```

### JzxQuestionUseCase (题目管理用例)

**主要功能**:
- 题目的增删改查
- 批量操作
- 题目导入导出
- 状态管理

### EduUseCase (教育内容用例)

**主要功能**:
- 博客文章管理
- 教育术语管理
- 内容分类管理
- SEO优化

### CodeLoginUseCase (登录认证用例)

**主要功能**:
- 用户登录验证
- Token管理
- 邮箱验证
- 登出处理

## 数据访问层

### AiJzxQuestionDao

**主要方法**:
```go
func (d *AiJzxQuestionDao) Create(ctx context.Context, record *model.AiJzxQuestion) error
func (d *AiJzxQuestionDao) Find(ctx context.Context, id int64) (*model.AiJzxQuestion, error)
func (d *AiJzxQuestionDao) Update(ctx context.Context, record *model.AiJzxQuestion) error
func (d *AiJzxQuestionDao) ListWithPage(ctx context.Context, ...) ([]*model.AiJzxQuestion, int64, error)
func (d *AiJzxQuestionDao) GetAiJzxQuestionBatch(ctx context.Context, ids []int64) ([]*model.AiJzxQuestion, error)
```

### FeedTraceDao

**主要方法**:
```go
func (d *FeedTraceDao) Create(ctx context.Context, record *model.FeedbackTrace) error
```

## 外部服务集成

### 1. 口算批改服务
- **服务地址**: 配置在 `biz.kousuan.host`
- **功能**: 对数学题图片进行AI识别和批改
- **超时设置**: 10秒

### 2. 任务驱动服务
- **服务地址**: 配置在 `services.task_drive.host`
- **功能**: 新手任务引导和完成追踪
- **支持应用**: snap word, read along, snap math

### 3. 用户中心服务
- **服务地址**: 配置在 `services.ucenter.host`
- **功能**: 用户认证和授权
- **支持功能**: 登录验证、Token管理

## 配置项

### 业务配置 (biz)
```yaml
biz:
  # 口算服务配置
  kousuan:
    host: "http://************:5030"
    timeout: 10s
  
  # 通用配置
  common_config:
    ranges:
      - word: "Excellent"
        min: 75
        max: 100
      - word: "Wonderful"
        min: 50
        max: 75
    right_line: 60
  
  # 博客分类
  blog_categories:
    - "Literacy Instruction"
    - "Lesson Plan"
    - "Classroom Management"
  
  # 年级配置
  grades:
    - name: "Pre-Kindergarten"
      value: "Pre-K"
    - name: "Kindergarten"
      value: "K"
```

## 监控和日志

### 关键指标
- 口算批改成功率
- 接口响应时间
- 用户反馈统计
- 题目查询频次

### 日志追踪
- 使用 `trace_id` 进行全链路追踪
- 记录关键业务操作
- 异常情况告警

### 性能优化
- 图片下载异步处理
- 数据库查询优化
- 缓存策略应用

## 错误处理

### 错误码定义
- `HwPaasParamError`: 参数错误
- `HwPaasDefaultErr`: 通用业务错误
- `HwPaasUnauthorized`: 未授权错误

### 错误处理策略
- 参数验证
- 业务逻辑检查
- 外部服务调用异常处理
- 统一错误响应格式

## 安全特性

### 1. 接口鉴权
- 基于Token的用户认证
- 接口访问权限控制

### 2. 数据安全
- 敏感信息加密存储
- SQL注入防护
- XSS攻击防护

### 3. 内容安全
- 用户输入内容审核
- 图片内容检测

这个AI服务模块是整个系统的核心，承担了大部分的业务逻辑处理，具有良好的扩展性和维护性。