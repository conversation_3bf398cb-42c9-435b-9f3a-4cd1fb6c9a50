# 指尖查词模块文档

## 模块概述

指尖查词模块是hw-paas-service项目的核心功能之一，通过OCR技术识别用户手指指向的英文单词，并提供详细的词典信息。该模块结合了图像识别、文字定位、词典查询等多项技术。

## API接口定义

### 服务定义 (api/finger_words/v1/finger_words.proto)

```protobuf
service FingerWords {
  // 指尖查词入口
  rpc FingerWordsEntry (FingerWordsEntryRequest) returns (FingerWordsEntryReply) {
    option (google.api.http) = {
      post: "/intelligence/v1/finger_words/entry"
      body: "*"
    };
  };
  
  // 单词查询
  rpc FingerWordsQuery (FingerWordsQueryRequest) returns (FingerWordsQueryReply) {
    option (google.api.http) = {
      post: "/intelligence/v1/finger_words/query"
      body: "*"
    };
  };
}
```

### 主要接口

#### 1. 指尖查词入口接口
- **路径**: `POST /intelligence/v1/finger_words/entry`
- **功能**: 处理用户上传的图片，识别手指位置和文字，返回最近的单词
- **请求参数**:
  ```protobuf
  message FingerWordsEntryRequest {
    string client_trace_id = 1;      // 客户端追踪ID
    int32 retry_status = 2;          // 重试状态
    string text_bmp = 3;             // 图片base64编码
    repeated int32 finger_pos_2ma = 4; // 手指位置坐标
    repeated int32 text_pos_2ma = 5;   // 文字位置坐标
    string version = 6;              // 版本号
    string app_version = 7;          // 应用版本
    string local_model_time = 8;     // 本地模型时间
  }
  ```
- **响应参数**:
  ```protobuf
  message FingerWordsEntryReply {
    string near_word = 1;            // 最近的单词
    repeated string line_words = 2;   // 行内所有单词
    int32 near_index = 3;            // 最近单词的索引
    string ocr = 4;                  // OCR识别结果
    int32 retry_status = 5;          // 重试状态
    google.protobuf.Struct result = 6; // 详细结果
    google.protobuf.Struct dispatch = 8; // 分发信息
  }
  ```

#### 2. 单词查询接口
- **路径**: `POST /intelligence/v1/finger_words/query`
- **功能**: 根据单词获取详细的词典信息
- **请求参数**:
  ```protobuf
  message FingerWordsQueryRequest {
    string word = 1;        // 要查询的单词
    string app_version = 2; // 应用版本
    string skill_name = 3;  // 技能名称
  }
  ```

## 数据模型

### 1. 英文单词模型 (hw_en_word)

```sql
CREATE TABLE hw_en_word (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增ID',
  word VARCHAR(50) NOT NULL DEFAULT '' COMMENT '单词',
  british VARCHAR(10) NOT NULL DEFAULT '' COMMENT '英式音标',
  american VARCHAR(10) NOT NULL DEFAULT '' COMMENT '美式音标',
  meanings VARCHAR(2048) NOT NULL DEFAULT '' COMMENT '释义',
  synonyms VARCHAR(256) NOT NULL DEFAULT '' COMMENT '同义词',
  antonyms VARCHAR(256) NOT NULL DEFAULT '' COMMENT '反义词',
  sentences VARCHAR(2048) NOT NULL DEFAULT '' COMMENT '例句',
  inflections VARCHAR(256) NOT NULL DEFAULT '' COMMENT '变形词',
  prefix VARCHAR(20) NOT NULL DEFAULT '' COMMENT '前缀',
  suffix VARCHAR(20) NOT NULL DEFAULT '' COMMENT '后缀',
  phrases VARCHAR(512) NOT NULL DEFAULT '' COMMENT '短语',
  frequency BIGINT NOT NULL COMMENT '词频',
  status TINYINT(1) NOT NULL COMMENT '状态,1:待机审;2:待上架;3:已上架;4:已下架;5:违禁词',
  opt_uid VARCHAR(20) NOT NULL DEFAULT '' COMMENT '创建人工号',
  opt_name VARCHAR(20) NOT NULL DEFAULT '' COMMENT '创建人名称',
  rel_word VARCHAR(50) NOT NULL DEFAULT '' COMMENT '关联的原形词',
  video_url VARCHAR(512) NOT NULL DEFAULT '' COMMENT '视频讲解',
  video_img VARCHAR(512) NOT NULL DEFAULT '' COMMENT '视频封面',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 2. 单词状态枚举

```go
type EnWordStatus uint8

const (
    EnWordStatusWaitAudit EnWordStatus = iota + 1  // 待机审
    EnWordStatusWaitShelf                          // 待上架
    EnWordStatusShelf                              // 已上架
    EnWordStatusUnShelf                            // 已下架
    EnWordStatusCurse                              // 违禁词
)
```

### 3. 单词详情结构

```go
type EnWordItem struct {
    Id          int64           `json:"id"`
    Word        string          `json:"word"`
    British     string          `json:"british"`
    American    string          `json:"american"`
    Inflections []string        `json:"inflections"`
    Prefix      string          `json:"prefix"`
    Suffix      string          `json:"suffix"`
    Phrases     []string        `json:"phrases"`
    Meanings    []EnWordMeaning `json:"meanings"`
    Synonyms    []string        `json:"synonyms"`
    Antonyms    []string        `json:"antonyms"`
    Sentences   []string        `json:"sentences"`
    Frequency   int64           `json:"frequency"`
    Status      EnWordStatus    `json:"status"`
    VideoUrl    string          `json:"video_url,omitempty"`
    Speech      string          `json:"speech,omitempty"`
    Msg         string          `json:"msg"`
}

type EnWordMeaning struct {
    PartOfSpeech string `json:"part_of_speech"`
    Definition   string `json:"definition"`
}
```

## 业务逻辑层

### FingerWordsUseCase

**主要功能**:
- OCR文字识别
- 手指位置计算
- 单词匹配和查询
- 词频统计
- 结果缓存

**核心方法**:
```go
func (uc *FingerWordsUseCase) FingerWordsEntry(ctx context.Context, req *pb.FingerWordsEntryRequest) (*pb.FingerWordsEntryReply, error)
func (uc *FingerWordsUseCase) FingerWordsQuery(ctx context.Context, req *pb.FingerWordsQueryRequest) (*pb.FingerWordsQueryReply, error)
func (uc *FingerWordsUseCase) findNearestWord(fingerPos []int32, words []WordPosition) (string, int)
func (uc *FingerWordsUseCase) calculateDistance(x1, y1, x2, y2 int32) float64
```

**业务流程**:
1. **图片处理**: 接收base64编码的图片数据
2. **OCR识别**: 调用OCR服务识别图片中的文字
3. **位置计算**: 根据手指坐标和文字位置计算最近的单词
4. **词典查询**: 从数据库或Elasticsearch中查询单词详情
5. **结果返回**: 组装完整的查词结果

## 数据访问层

### FingerWordsDao

**主要方法**:
```go
func (d *FingerWordsDao) Create(ctx context.Context, record *model.HwEnWord) error
func (d *FingerWordsDao) FindFingerWords(ctx context.Context, search *model.HwEnWordSearch) ([]*model.HwEnWord, error)
func (d *FingerWordsDao) FindHwEnWords(ctx context.Context, words []string) ([]*model.HwEnWord, error)
func (d *FingerWordsDao) IncrementHwEnWordsFrequency(ctx context.Context, words []string) error
func (d *FingerWordsDao) UpdateFingerWords(ctx context.Context, record *model.HwEnWord) error
func (d *FingerWordsDao) BatchCreate(ctx context.Context, records []*model.HwEnWord) error
```

**查询特性**:
- 支持大小写不敏感查询
- 批量单词查询优化
- 词频自动更新
- 状态过滤（只返回已上架的单词）

### Elasticsearch集成

**索引配置**:
- 索引名称: `oversea_en_word`
- 支持模糊匹配
- 同义词搜索
- 相关性评分

## 外部服务集成

### 1. OCR识别服务
- **服务名称**: Hybrid OCR
- **服务地址**: 配置在 `services.hybrid_ocr.host`
- **功能**: 识别图片中的英文文字和位置信息
- **超时设置**: 5秒

### 2. 英文纠错服务
- **服务名称**: English Correct
- **服务地址**: 配置在 `services.en_correct.host`
- **功能**: 对识别的英文进行拼写纠错
- **开关控制**: `biz.en_correct_switch`

## 算法实现

### 1. 距离计算算法
```go
func (uc *FingerWordsUseCase) calculateDistance(x1, y1, x2, y2 int32) float64 {
    dx := float64(x1 - x2)
    dy := float64(y1 - y2)
    return math.Sqrt(dx*dx + dy*dy)
}
```

### 2. 最近单词查找算法
```go
func (uc *FingerWordsUseCase) findNearestWord(fingerPos []int32, words []WordPosition) (string, int) {
    if len(fingerPos) < 2 || len(words) == 0 {
        return "", -1
    }
    
    fingerX, fingerY := fingerPos[0], fingerPos[1]
    minDistance := math.MaxFloat64
    nearestWord := ""
    nearestIndex := -1
    
    for i, word := range words {
        distance := uc.calculateDistance(fingerX, fingerY, word.X, word.Y)
        if distance < minDistance {
            minDistance = distance
            nearestWord = word.Text
            nearestIndex = i
        }
    }
    
    return nearestWord, nearestIndex
}
```

## 配置项

### 业务配置
```yaml
biz:
  # 英文纠错开关
  en_correct_switch: 0  # 0:关闭 1:开启
  
  # Elasticsearch索引
  en_word_index: "oversea_en_word"

services:
  # OCR服务配置
  hybrid_ocr:
    host: "http://**********:8089/det"
    timeout: "5s"
  
  # 英文纠错服务配置
  en_correct:
    host: "http://***********:80"
    timeout: "5s"
```

## 性能优化

### 1. 缓存策略
- Redis缓存热门单词查询结果
- 本地缓存常用单词列表
- OCR结果临时缓存

### 2. 数据库优化
- 单词表建立复合索引
- 批量查询减少数据库访问
- 读写分离优化查询性能

### 3. 算法优化
- 空间索引优化位置计算
- 预处理常用单词位置
- 并发处理提升响应速度

## 监控和日志

### 关键指标
- OCR识别成功率
- 单词匹配准确率
- 查询响应时间
- 用户查词频次

### 日志记录
- 每次查词请求的完整链路
- OCR识别结果和耗时
- 单词匹配算法执行情况
- 异常情况详细记录

## 错误处理

### 常见错误场景
1. **图片格式错误**: 无法解析base64图片数据
2. **OCR识别失败**: 图片质量差或无文字内容
3. **坐标参数错误**: 手指位置坐标不合法
4. **单词未找到**: 词典中不存在该单词
5. **服务超时**: OCR服务响应超时

### 容错机制
- 重试机制处理临时故障
- 降级策略保证基本功能
- 默认结果避免空响应

## 安全特性

### 1. 输入验证
- 图片大小限制
- 坐标范围检查
- 参数格式验证

### 2. 内容安全
- 违禁词过滤
- 不当内容检测
- 用户行为监控

### 3. 性能保护
- 请求频率限制
- 资源使用监控
- 异常流量告警

这个指尖查词模块是项目的核心功能，通过先进的OCR技术和智能算法，为用户提供了便捷的英文学习工具。