# hw-paas-service 项目模块总结

## 项目概览

hw-paas-service是一个基于Kratos微服务框架构建的海外教育智能服务平台，采用现代化的微服务架构设计，为海外用户提供多种AI辅助教学功能。

## 模块架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    hw-paas-service                          │
├─────────────────────────────────────────────────────────────┤
│  API Layer (Protocol Buffers + HTTP/gRPC)                  │
├─────────────────────────────────────────────────────────────┤
│  Service Layer (业务服务层)                                │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ AI Service  │ FingerWords │ QueryWords  │ Resource    │  │
│  │             │ Service     │ Service     │ Service     │  │
│  ├─────────────┼─────────────┼─────────────┼─────────────┤  │
│  │ Comment     │ ReadingBook │ Skill       │ Admin       │  │
│  │ Service     │ Service     │ Service     │ Service     │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  Business Layer (业务逻辑层)                               │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ Correct     │ FingerWords │ QueryWords  │ Resource    │  │
│  │ UseCase     │ UseCase     │ UseCase     │ UseCase     │  │
│  ├─────────────┼─────────────┼─────────────┼─────────────┤  │
│  │ Comment     │ ReadingBook │ JzxQuestion │ Admin       │  │
│  │ UseCase     │ UseCase     │ UseCase     │ UseCase     │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  Data Layer (数据访问层)                                   │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ MySQL       │ Redis       │ Elasticsearch│ External   │  │
│  │ Database    │ Cache       │ Search      │ Services    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块详解

### 1. AI服务模块 (AI Service)
**功能范围**: 项目的核心模块，提供多种AI辅助教学功能
- **口算批改**: 数学题图片识别和自动批改
- **题目管理**: AI题库的增删改查和导入导出
- **博客管理**: 教育博客内容管理和SEO优化
- **用户认证**: 基于UCenter的用户登录和权限管理
- **通用配置**: 系统配置和任务驱动

**核心表结构**:
- `ai_jzx_question`: AI题目库
- `blog_articles`: 博客文章
- `feedback_trace`: 用户反馈追踪

**关键技术**:
- 图像识别和OCR处理
- 外部AI服务集成
- 用户中心集成
- 配置动态更新

### 2. 指尖查词模块 (FingerWords)
**功能范围**: 通过OCR技术实现智能查词功能
- **图像识别**: 识别图片中的英文文字
- **位置计算**: 根据手指坐标定位最近单词
- **词典查询**: 从数据库和ES中查询单词详情
- **词频统计**: 自动更新单词查询频次

**核心表结构**:
- `hw_en_word`: 英文单词词典表

**关键算法**:
- 距离计算算法
- 最近单词匹配
- OCR结果处理

### 3. 单词查询模块 (QueryWords)
**功能范围**: 提供强大的英文单词检索功能
- **多维度查询**: 支持单词、音标、释义、同义词等查询
- **全文检索**: 基于Elasticsearch的高性能搜索
- **相关性排序**: 智能评分和结果排序
- **违禁词检测**: 内容安全过滤

**核心技术**:
- Elasticsearch全文搜索
- 多字段组合查询
- 相关性评分算法
- 缓存优化策略

### 4. 资源管理模块 (Resource)
**功能范围**: 教育资源的分类管理和检索
- **资源分类**: 工作表、视频、涂色页等多种类型
- **多维筛选**: 年级、学科、主题等条件筛选
- **SEO优化**: 元数据管理和搜索引擎优化
- **URL管理**: 友好URL和路由映射

**核心表结构**:
- `edu_resources`: 教育资源主表
- `edu_resources_title`: 资源标题配置
- `edu_resources_url`: URL映射表

**分类体系**:
- 年级: Pre-K到Grade 6
- 学科: Math, English, Science等
- 资源类型: worksheet, video, coloringpage

### 5. 评论系统模块 (Comment)
**功能范围**: 多级评论和用户互动功能
- **树形结构**: 支持多级评论和回复
- **多主题支持**: 博客、工作表、词汇表等
- **内容安全**: 敏感词过滤和内容审核
- **用户管理**: 用户身份验证和权限控制

**核心表结构**:
- `comments`: 评论主表

**设计特点**:
- 树形结构设计
- 分页查询优化
- 缓存策略应用

### 6. 语音评测模块 (Evaluate)
**功能范围**: 实时语音评测和口语练习
- **WebSocket连接**: 实时音频传输
- **AI评测**: 发音、流利度、完整度评分
- **多维度评分**: 音素级别的详细评分
- **实时反馈**: 即时评测结果返回

**核心技术**:
- WebSocket实时通信
- 音频流处理
- TAL语音评测服务集成
- 多维度评分算法

### 7. 阅读绘本模块 (ReadingBook)
**功能范围**: 绘本阅读和句子识别
- **句子识别**: OCR识别绘本中的句子
- **指读功能**: 手指定位和文字识别
- **阅读追踪**: 阅读进度和统计

### 8. 技能管理模块 (Skill)
**功能范围**: RN版本控制和技能包管理
- **版本控制**: React Native应用版本管理
- **技能包分发**: 不同技能的资源包管理
- **强制更新**: 版本强制更新控制

### 9. 管理后台模块 (Admin)
**功能范围**: 系统管理和数据统计
- **数据管理**: 各模块数据的后台管理
- **统计分析**: 业务数据统计和分析
- **系统配置**: 系统参数配置管理

## 技术架构特点

### 1. 微服务架构
- **分层设计**: API、Service、Business、Data四层架构
- **依赖注入**: 使用Google Wire进行依赖管理
- **接口标准化**: Protocol Buffers定义统一接口

### 2. 数据存储
- **MySQL**: 主要业务数据存储
- **Redis**: 缓存和会话管理
- **Elasticsearch**: 全文搜索和复杂查询

### 3. 外部服务集成
- **AI服务**: 口算批改、OCR识别、语音评测
- **基础服务**: 文件上传、内容安全、用户中心
- **第三方平台**: Azure Blob Storage、Nacos配置中心

### 4. 性能优化
- **多级缓存**: 本地缓存 + Redis缓存
- **数据库优化**: 索引优化、查询优化、读写分离
- **异步处理**: 消息队列和异步任务

### 5. 监控和日志
- **Prometheus**: 业务指标监控
- **OpenTelemetry**: 分布式链路追踪
- **结构化日志**: 统一日志格式和分析

## 数据库设计总览

### 核心数据表
```sql
-- 英文单词词典
hw_en_word (id, word, british, american, meanings, synonyms, ...)

-- AI题目库
ai_jzx_question (id, q_type, difficulty, grade, subject, question, answer, ...)

-- 博客文章
blog_articles (id, path, category, title, content, meta_title, ...)

-- 教育资源
edu_resources (id, resource_type, grade, subject, title, url, ...)

-- 评论系统
comments (id, subject_type, subject_id, parent_id, content, ...)

-- 反馈追踪
feedback_trace (id, biz, trace_id, content, created_at)
```

### 索引策略
- 复合索引优化多条件查询
- 覆盖索引减少回表操作
- 分区表处理大数据量

## 配置管理

### 配置层级
1. **本地配置**: config.yaml基础配置
2. **Nacos配置**: 动态配置和环境区分
3. **环境变量**: 敏感信息和部署配置

### 关键配置项
```yaml
# 数据库配置
data:
  database:
    source: "mysql连接串"
  redis:
    addr: "redis地址"
  es:
    host: "elasticsearch地址"

# 业务配置
biz:
  en_correct_switch: 1      # 英文纠错开关
  en_word_index: "索引名"   # ES索引配置
  common_config:            # 通用配置
    ranges: [...]           # 评分区间

# 外部服务
services:
  hybrid_ocr:              # OCR服务
    host: "服务地址"
  en_correct:              # 英文纠错
    host: "服务地址"
```

## 部署架构

### 容器化部署
```dockerfile
FROM golang:1.23-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o hw-paas-service cmd/hw-paas-service/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/hw-paas-service .
CMD ["./hw-paas-service"]
```

### 服务发现
- **Nacos**: 服务注册和发现
- **配置中心**: 动态配置管理
- **健康检查**: 服务健康状态监控

## 安全特性

### 1. 认证授权
- **用户认证**: 基于UCenter的统一认证
- **接口鉴权**: Token验证和权限控制
- **签名验证**: 接口请求签名验证

### 2. 数据安全
- **输入验证**: 参数格式和长度检查
- **SQL注入防护**: 参数化查询
- **XSS防护**: 输出内容转义

### 3. 内容安全
- **敏感词过滤**: 违禁词检测和过滤
- **内容审核**: 用户生成内容审核
- **频率限制**: 接口调用频率控制

## 监控体系

### 业务指标
- **用户活跃度**: DAU、MAU统计
- **功能使用率**: 各模块使用频次
- **性能指标**: 响应时间、成功率
- **错误监控**: 异常统计和告警

### 技术指标
- **系统资源**: CPU、内存、磁盘使用率
- **数据库性能**: 连接数、慢查询
- **缓存效果**: 命中率、过期策略
- **外部服务**: 调用成功率、响应时间

## 项目优势

### 1. 架构优势
- **高可扩展性**: 微服务架构支持独立扩展
- **高可维护性**: 清晰的分层设计和模块划分
- **高性能**: 多级缓存和数据库优化
- **高可用性**: 容错机制和降级策略

### 2. 技术优势
- **现代化技术栈**: Go + Kratos + gRPC
- **标准化接口**: Protocol Buffers统一定义
- **智能化功能**: AI技术深度集成
- **国际化支持**: 多语言和多地区适配

### 3. 业务优势
- **教育场景深度优化**: 针对海外教育需求设计
- **多模态交互**: 文字、语音、图像多种交互方式
- **个性化学习**: 基于用户行为的智能推荐
- **数据驱动**: 完整的数据收集和分析体系

## 未来发展方向

### 1. 技术演进
- **云原生**: Kubernetes部署和服务网格
- **AI增强**: 更多AI能力集成和优化
- **实时计算**: 流式数据处理和实时分析
- **边缘计算**: CDN和边缘节点部署

### 2. 功能扩展
- **多语言支持**: 支持更多语言学习
- **VR/AR集成**: 沉浸式学习体验
- **社交功能**: 学习社区和协作功能
- **游戏化学习**: 增加学习趣味性

### 3. 生态建设
- **开放平台**: API开放和第三方集成
- **插件系统**: 可扩展的功能插件
- **数据开放**: 教育数据共享和研究
- **合作伙伴**: 教育机构和内容提供商

这个项目展现了现代微服务架构在教育科技领域的成功实践，为海外用户提供了完整的智能教育解决方案。