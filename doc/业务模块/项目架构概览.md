# hw-paas-service 项目架构概览

## 项目简介

hw-paas-service 是一个基于 Kratos 微服务框架构建的海外教育智能服务平台，主要提供AI辅助教学功能，包括指尖查词、英文纠错、口算批改、阅读绘本等多种教育工具。

## 技术栈

### 核心框架
- **Kratos v2**: Go微服务框架，提供HTTP/gRPC服务
- **Google Wire**: 依赖注入框架
- **GORM**: ORM框架，用于数据库操作
- **Redis**: 缓存系统
- **Elasticsearch**: 搜索引擎，用于英文单词检索
- **WebSocket**: 实时通信，用于语音评测
- **Protocol Buffers**: 接口定义和数据序列化

### 中间件和工具
- **Prometheus**: 监控指标收集
- **Nacos**: 配置中心和服务发现
- **Azure Blob Storage**: 文件存储
- **OpenTelemetry**: 链路追踪

## 项目结构

```
hw-paas-service/
├── api/                    # API定义（protobuf）
│   ├── ai/v1/             # AI服务接口
│   ├── comment/v1/        # 评论服务接口
│   ├── finger_words/v1/   # 指尖查词接口
│   ├── query_words/v1/    # 单词查询接口
│   ├── reading_book/v1/   # 阅读绘本接口
│   ├── resource/v1/       # 资源管理接口
│   └── skill/v1/          # 技能管理接口
├── cmd/hw-paas-service/   # 应用入口
├── configs/               # 配置文件
├── internal/              # 内部代码
│   ├── biz/              # 业务逻辑层
│   ├── data/             # 数据访问层
│   ├── service/          # 服务层
│   ├── server/           # 服务器配置
│   └── pkg/              # 内部工具包
├── pkg/                   # 公共工具包
└── third_party/          # 第三方proto文件
```

## 架构设计

### 分层架构
1. **API层**: 定义服务接口，使用Protocol Buffers
2. **Service层**: 处理HTTP/gRPC请求，参数验证
3. **Business层**: 核心业务逻辑处理
4. **Data层**: 数据访问，包括数据库、缓存、外部服务调用

### 依赖注入
使用Google Wire进行依赖注入，确保各层之间的解耦：
- 通过ProviderSet定义依赖关系
- 自动生成依赖注入代码
- 支持接口和实现的分离

## 核心模块

### 1. AI服务模块 (ai)
- 口算批改
- 英文纠错
- 题目管理
- 博客管理
- 用户登录认证

### 2. 指尖查词模块 (finger_words)
- OCR文字识别
- 单词定位
- 词典查询

### 3. 单词查询模块 (query_words)
- 英文单词检索
- 同义词/反义词查询
- 词频统计

### 4. 阅读绘本模块 (reading_book)
- 句子识别
- 语音评测

### 5. 资源管理模块 (resource)
- 教育资源管理
- 分类筛选
- 元数据管理

### 6. 评论系统模块 (comment)
- 多级评论
- 评论管理

### 7. 管理后台模块 (admin)
- 系统管理
- 数据统计

## 数据库设计

### 主要数据表
- `hw_en_word`: 英文单词词典
- `comments`: 评论系统
- `blog_articles`: 博客文章
- `edu_resources`: 教育资源
- `ai_jzx_question`: AI题目库
- `feedback_trace`: 反馈追踪

## 外部服务集成

### AI服务
- 口算批改服务
- 英文纠错服务
- OCR识别服务
- 语音评测服务

### 基础服务
- 文件上传服务
- 内容安全审核
- 任务驱动服务
- 用户中心服务

## 配置管理

支持两种配置方式：
1. **本地配置**: 使用config.yaml文件
2. **Nacos配置中心**: 支持动态配置更新

## 监控和日志

- **Prometheus**: 业务指标监控
- **OpenTelemetry**: 分布式链路追踪
- **结构化日志**: 使用zap日志库
- **Redis连接池监控**: 实时监控连接状态

## 部署方式

- **Docker容器化部署**
- **支持Kubernetes编排**
- **多环境配置支持**（开发、测试、生产）

## 安全特性

- **接口签名验证**
- **用户身份认证**
- **内容安全审核**
- **敏感信息加密**

这个项目采用了现代化的微服务架构设计，具有良好的可扩展性和维护性，适合大规模的教育服务场景。