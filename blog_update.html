<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博客数据导入工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .environment-selector {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .environment-selector h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .env-buttons {
            display: flex;
            gap: 15px;
        }

        .env-btn {
            padding: 12px 24px;
            border: 2px solid #dee2e6;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .env-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .env-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .upload-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
            text-align: center;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #007bff;
            background: #f0f8ff;
        }

        .upload-section.dragover {
            border-color: #28a745;
            background: #f0fff4;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .file-info {
            margin-top: 15px;
            color: #6c757d;
        }

        .preview-section {
            margin-top: 30px;
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .preview-table th,
        .preview-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .preview-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .preview-table tr:hover {
            background: #f8f9fa;
        }

        .error-row {
            background: #fff5f5 !important;
            color: #e53e3e;
        }

        .success-row {
            background: #f0fff4 !important;
            color: #38a169;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            justify-content: center;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .progress-section {
            margin-top: 30px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            color: #6c757d;
            font-weight: 500;
        }

        .log-section {
            margin-top: 30px;
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #dee2e6;
        }

        .log-item {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .log-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .log-error {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .log-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        .hidden {
            display: none;
        }

        .preview-table img {
            max-height: 60px;
            max-width: 100px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }

        .preview-table .image-cell {
            width: 120px;
            text-align: center;
        }

        .preview-table .no-image {
            color: #6c757d;
            font-style: italic;
            font-size: 0.9em;
        }

        .sheet-selector {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .sheet-selector h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .sheet-info {
            margin-bottom: 15px;
            color: #6c757d;
        }

        .sheet-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .sheet-btn {
            padding: 10px 20px;
            border: 2px solid #dee2e6;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9em;
        }

        .sheet-btn:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }

        .sheet-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>博客数据导入工具</h1>
            <p>支持Excel文件批量导入博客数据到测试环境和线上环境</p>
        </div>

        <div class="content">
            <!-- 环境选择器 -->
            <div class="environment-selector">
                <h3>选择环境</h3>
                <div class="env-buttons">
                    <button class="env-btn active" data-env="test">测试环境</button>
                    <button class="env-btn" data-env="prod">线上环境</button>
                </div>
                <div id="env-info" style="margin-top: 15px; color: #6c757d;">
                    当前环境：测试环境
                </div>
            </div>

            <!-- 文件上传区域 -->
            <div class="upload-section" id="uploadSection">
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择Excel文件
                </button>
                <div class="file-info" id="fileInfo">
                    支持 .xlsx 和 .xls 格式，请确保Excel文件包含以下列：<br>
                    A:id, B:path, C:category, D:article_title, E:short_content, F:cover_img, G:author_name, 
                    H:article_content, I:page_title, J:meta_keywords, K:meta_description, L:created_at, M:updated_at<br>
                    <strong>注意：</strong>cover_img列会自动提取图片URL并添加CDN参数，article_content列中的图片URL也会自动添加CDN参数
                </div>
            </div>

            <!-- Sheet选择器 -->
            <div class="sheet-selector hidden" id="sheetSelector">
                <h3>选择工作表</h3>
                <div class="sheet-info" id="sheetInfo">
                    检测到多个工作表，请选择要处理的工作表：
                </div>
                <div class="sheet-buttons" id="sheetButtons">
                    <!-- 动态生成的sheet按钮 -->
                </div>
            </div>

            <!-- 数据预览区域 -->
            <div class="preview-section hidden" id="previewSection">
                <h3>数据预览</h3>
                <div id="previewTable"></div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons hidden" id="actionButtons">
                <button class="btn btn-primary" id="importBtn" onclick="startImport()">开始导入</button>
                <button class="btn btn-secondary" onclick="resetData()">重置数据</button>
            </div>

            <!-- 进度条 -->
            <div class="progress-section" id="progressSection">
                <h3>导入进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备中...</div>
            </div>

            <!-- 日志区域 -->
            <div class="log-section hidden" id="logSection">
                <h3>导入日志</h3>
                <div id="logContent"></div>
            </div>
        </div>
    </div>

    <script>
        let currentEnv = 'test';
        let excelData = [];
        let validatedData = [];
        let currentWorkbook = null; // 存储当前工作簿
        let currentSheetName = null; // 存储当前选中的工作表名

        // 环境配置
        const envConfig = {
            test: {
                name: '测试环境',
                domain: 'https://overseas-pad.chengjiukehu.com' // 这里需要替换为实际的测试环境域名
            },
            prod: {
                name: '线上环境',
                domain: 'https://pad-api-pre.thinkbuddy.com' // 这里需要替换为实际的线上环境域名
            }
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
        });

        function initializeEventListeners() {
            // 环境切换
            document.querySelectorAll('.env-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.env-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentEnv = this.dataset.env;
                    updateEnvInfo();
                });
            });

            // 文件上传
            const fileInput = document.getElementById('fileInput');
            const uploadSection = document.getElementById('uploadSection');

            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽上传
            uploadSection.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            uploadSection.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            uploadSection.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handleFileSelect();
                }
            });
        }

        function updateEnvInfo() {
            const envInfo = document.getElementById('env-info');
            envInfo.textContent = `当前环境：${envConfig[currentEnv].name}`;
        }

        function handleFileSelect() {
            const file = document.getElementById('fileInput').files[0];
            if (!file) return;

            const fileInfo = document.getElementById('fileInfo');
            fileInfo.textContent = `已选择文件：${file.name}`;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    currentWorkbook = workbook;
                    
                    // 检查是否有多个工作表
                    if (workbook.SheetNames.length > 1) {
                        showSheetSelector(workbook.SheetNames);
                    } else {
                        // 只有一个工作表，直接处理
                        processSheet(workbook.SheetNames[0]);
                    }
                } catch (error) {
                    showAlert('文件读取失败：' + error.message, 'error');
                }
            };
            reader.readAsArrayBuffer(file);
        }

        // 显示工作表选择器
        function showSheetSelector(sheetNames) {
            const sheetSelector = document.getElementById('sheetSelector');
            const sheetInfo = document.getElementById('sheetInfo');
            const sheetButtons = document.getElementById('sheetButtons');
            
            sheetInfo.textContent = `检测到 ${sheetNames.length} 个工作表，请选择要处理的工作表：`;
            
            // 生成工作表按钮
            sheetButtons.innerHTML = '';
            sheetNames.forEach((sheetName, index) => {
                const button = document.createElement('button');
                button.className = 'sheet-btn';
                button.textContent = sheetName;
                button.dataset.sheetName = sheetName;
                button.onclick = () => selectSheet(sheetName);
                
                // 默认选中第一个工作表
                if (index === 0) {
                    button.classList.add('active');
                }
                
                sheetButtons.appendChild(button);
            });
            
            sheetSelector.classList.remove('hidden');
        }

        // 选择工作表
        function selectSheet(sheetName) {
            currentSheetName = sheetName;
            
            // 更新按钮状态
            document.querySelectorAll('.sheet-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-sheet-name="${sheetName}"]`).classList.add('active');
            
            // 处理选中的工作表
            processSheet(sheetName);
        }

        // 处理指定的工作表
        function processSheet(sheetName) {
            if (!currentWorkbook) return;
            
            const worksheet = currentWorkbook.Sheets[sheetName];
            
            // 从第2行开始读取数据（跳过标题行）
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
                header: 1,
                range: 1 // 从第2行开始
            });

            excelData = jsonData;
            validateAndPreviewData();
        }

        function validateAndPreviewData() {
            if (excelData.length === 0) {
                showAlert('Excel文件中没有数据', 'error');
                return;
            }

            validatedData = [];
            const errors = [];

            excelData.forEach((row, index) => {
                const rowNumber = index + 2; // 实际行号（从第2行开始）
                
                // 检查是否有足够的列
                if (row.length < 13) {
                    errors.push(`第${rowNumber}行：数据列数不足，需要13列`);
                    return;
                }

                // 检查每个字段是否为空
                const fields = [
                    'id', 'path', 'category', 'article_title', 'short_content', 
                    'cover_img', 'author_name', 'article_content', 'page_title', 
                    'meta_keywords', 'meta_description', 'created_at', 'updated_at'
                ];

                const rowData = {};
                let hasEmptyField = false;

                fields.forEach((field, colIndex) => {
                    let value = row[colIndex];
                    
                    // 特殊处理cover_img字段，提取图片URL
                    if (field === 'cover_img' && value) {
                        value = extractImageUrl(value);
                        if (!value) {
                            hasEmptyField = true;
                            errors.push(`第${rowNumber}行：cover_img字段中未找到有效的图片地址`);
                            return;
                        }
                    }
                    
                    // 特殊处理article_content字段，替换其中的图片URL
                    if (field === 'article_content' && value) {
                        value = processArticleContentImages(value);
                    }
                    
                    if (value === undefined || value === null || value === '') {
                        hasEmptyField = true;
                        errors.push(`第${rowNumber}行：${field}字段为空`);
                    }
                    rowData[field] = value;
                });

                if (!hasEmptyField) {
                    validatedData.push({
                        ...rowData,
                        rowNumber: rowNumber,
                        isValid: true
                    });
                }
            });

            if (errors.length > 0) {
                showAlert(`发现${errors.length}个错误：<br>` + errors.join('<br>'), 'error');
            }

            if (validatedData.length > 0) {
                showAlert(`验证通过：${validatedData.length}条数据可以导入`, 'success');
                showPreview();
            } else {
                showAlert('没有可导入的数据', 'error');
            }
        }

        // 从cover_img字段中提取图片URL的正则表达式函数
        function extractImageUrl(text) {
            if (!text) return null;
            
            // 将输入转换为字符串
            const str = String(text);
            
            // 匹配各种图片URL格式的正则表达式
            const imageUrlPatterns = [
                // 匹配完整的HTTP/HTTPS图片URL
                /https?:\/\/[^\s<>"']+\.(?:jpg|jpeg|png|gif|webp|svg|bmp|tiff)(?:\?[^\s<>"']*)?/gi,
                // 匹配相对路径的图片
                /\/[^\s<>"']+\.(?:jpg|jpeg|png|gif|webp|svg|bmp|tiff)(?:\?[^\s<>"']*)?/gi,
                // 匹配以图片扩展名结尾的URL（更宽松的匹配）
                /https?:\/\/[^\s<>"']+\.(?:jpg|jpeg|png|gif|webp|svg|bmp|tiff)/gi
            ];
            
            for (const pattern of imageUrlPatterns) {
                const matches = str.match(pattern);
                if (matches && matches.length > 0) {
                    // 返回第一个匹配的图片URL，并添加CDN参数
                    const originalUrl = matches[0];
                    const processedUrl = addCDNParams(originalUrl, 600); // cover_img使用600宽度

                    console.log('原始图片URL:', originalUrl);
                    console.log('处理后图片URL:', processedUrl);

                    return processedUrl;
                }
            }
            
            // 如果没有找到图片URL，返回null
            return null;
        }

        // 添加CDN参数到URL（参考Go代码实现）
        function addCDNParams(url, width) {
            // 如果URL已经包含CDN参数，则直接返回
            if (url.includes('/cdn-cgi/image/')) {
                return url;
            }

            // 处理相对路径
            if (url.startsWith('/')) {
                // 相对路径，直接添加CDN参数
                return `/cdn-cgi/image/width=${width}${url}`;
            }

            try {
                // 使用URL对象来正确解析URL
                const urlObj = new URL(url);
                
                // 构建新的URL，在域名后添加CDN参数
                const newUrl = `${urlObj.protocol}//${urlObj.host}/cdn-cgi/image/width=${width}${urlObj.pathname}${urlObj.search}${urlObj.hash}`;
                
                console.log('CDN处理:', {
                    original: url,
                    processed: newUrl
                });
                
                return newUrl;
            } catch (error) {
                console.error('URL解析失败:', url, error);
                return url; // 如果解析失败，返回原URL
            }
        }

        // 处理文章内容中的图片URL
        function processArticleContentImages(content) {
            if (!content) return content;
            
            const str = String(content);
            
            // 匹配文章内容中的图片URL（支持括号包围的格式）
            const imageUrlPatterns = [
                // 匹配括号包围的图片URL: (https://example.com/image.jpg)
                /\(\s*(https?:\/\/[^\s<>"']+\.(?:jpg|jpeg|png|gif|webp|svg|bmp|tiff)(?:\?[^\s<>"']*)?)\s*\)/gi,
                // 匹配直接的图片URL: https://example.com/image.jpg
                /(https?:\/\/[^\s<>"']+\.(?:jpg|jpeg|png|gif|webp|svg|bmp|tiff)(?:\?[^\s<>"']*)?)/gi,
                // 匹配相对路径的图片: (/images/cover.jpg)
                /\(\s*(\/[^\s<>"']+\.(?:jpg|jpeg|png|gif|webp|svg|bmp|tiff)(?:\?[^\s<>"']*)?)\s*\)/gi,
                // 匹配直接的相对路径图片: /images/cover.jpg
                /(\/[^\s<>"']+\.(?:jpg|jpeg|png|gif|webp|svg|bmp|tiff)(?:\?[^\s<>"']*)?)/gi
            ];
            
            let result = str;
            
            for (const pattern of imageUrlPatterns) {
                result = result.replace(pattern, (match, url) => {
                    // 如果match包含括号，需要保持括号格式
                    const isWrapped = match.startsWith('(') && match.endsWith(')');
                    const originalUrl = url || match;
                    
                    // 添加CDN参数
                    const processedUrl = addCDNParams(originalUrl, 1200); // article_content使用1200宽度
                    
                    console.log('文章内容图片处理:', {
                        original: originalUrl,
                        processed: processedUrl
                    });
                    
                    // 返回处理后的URL，保持原有格式
                    return isWrapped ? `(${processedUrl})` : processedUrl;
                });
            }
            
            return result;
        }

        function showPreview() {
            const previewSection = document.getElementById('previewSection');
            const actionButtons = document.getElementById('actionButtons');
            const previewTable = document.getElementById('previewTable');

            // 创建预览表格
            let tableHTML = `
                <table class="preview-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Path</th>
                            <th>Category</th>
                            <th>Article Title</th>
                            <th>Author</th>
                            <th>Cover Image</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            validatedData.forEach(item => {
                // 截断过长的文本
                const truncateText = (text, maxLength = 30) => {
                    if (!text) return '';
                    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
                };

                // 处理图片URL显示
                const imageUrl = item.cover_img;
                const imageDisplay = imageUrl ? 
                    `<img src="${imageUrl}" alt="${truncateText(item.article_title, 40)}" title="${imageUrl}" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';" style="max-height: 60px; max-width: 100px; object-fit: cover; border-radius: 4px; border: 1px solid #dee2e6;"><span class="no-image" style="display: none;">图片加载失败</span>` : 
                    '<span class="no-image">无图片</span>';

                tableHTML += `
                    <tr class="success-row">
                        <td>${item.id}</td>
                        <td title="${item.path}">${truncateText(item.path, 25)}</td>
                        <td>${item.category}</td>
                        <td title="${item.article_title}">${truncateText(item.article_title, 25)}</td>
                        <td>${item.author_name}</td>
                        <td class="image-cell">${imageDisplay}</td>
                        <td>${item.created_at}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';

            // 添加工作表信息
            const sheetInfo = currentSheetName ? `<p style="margin-bottom: 15px; color: #6c757d; font-size: 0.9em;">当前工作表：<strong>${currentSheetName}</strong></p>` : '';
            
            previewTable.innerHTML = sheetInfo + tableHTML;
            previewSection.classList.remove('hidden');
            actionButtons.classList.remove('hidden');
        }

        async function startImport() {
            if (validatedData.length === 0) {
                showAlert('没有可导入的数据', 'error');
                return;
            }

            const importBtn = document.getElementById('importBtn');
            const progressSection = document.getElementById('progressSection');
            const logSection = document.getElementById('logSection');

            importBtn.disabled = true;
            progressSection.style.display = 'block';
            logSection.classList.remove('hidden');

            let successCount = 0;
            let errorCount = 0;

            for (let i = 0; i < validatedData.length; i++) {
                const item = validatedData[i];
                const progress = ((i + 1) / validatedData.length) * 100;

                updateProgress(progress, `正在导入第 ${i + 1}/${validatedData.length} 条数据...`);

                try {
                    const response = await importSingleItem(item);
                    if (response.success) {
                        successCount++;
                        addLog(`第${item.rowNumber}行：导入成功`, 'success');
                    } else {
                        errorCount++;
                        addLog(`第${item.rowNumber}行：${response.error}`, 'error');
                    }
                } catch (error) {
                    errorCount++;
                    addLog(`第${item.rowNumber}行：网络错误 - ${error.message}`, 'error');
                }

                // 添加延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            updateProgress(100, '导入完成');
            addLog(`导入完成！成功：${successCount}条，失败：${errorCount}条`, 'info');

            importBtn.disabled = false;
        }

        async function importSingleItem(item) {
            const apiUrl = `${envConfig[currentEnv].domain}/intelligence/api/blog/set`;
            
            const requestData = {
                id: parseInt(item.id),
                path: item.path,
                category: item.category,
                article_title: item.article_title,
                short_content: item.short_content,
                cover_img: item.cover_img, // 已经通过extractImageUrl处理过
                author_name: item.author_name,
                article_content: item.article_content,
                page_title: item.page_title,
                meta_keywords: item.meta_keywords,
                meta_description: item.meta_description,
                created_at: item.created_at,
                updated_at: item.updated_at
            };

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok) {
                    return { success: true, data: result };
                } else {
                    return { success: false, error: result.message || '请求失败' };
                }
            } catch (error) {
                throw error;
            }
        }

        function updateProgress(percentage, text) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressFill.style.width = percentage + '%';
            progressText.textContent = text;
        }

        function addLog(message, type) {
            const logContent = document.getElementById('logContent');
            const logItem = document.createElement('div');
            logItem.className = `log-item log-${type}`;
            logItem.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContent.appendChild(logItem);
            logContent.scrollTop = logContent.scrollHeight;
        }

        function showAlert(message, type) {
            // 移除现有的alert
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = message;

            const content = document.querySelector('.content');
            content.insertBefore(alert, content.firstChild);

            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        function resetData() {
            excelData = [];
            validatedData = [];
            currentWorkbook = null;
            currentSheetName = null;
            
            document.getElementById('fileInput').value = '';
            document.getElementById('fileInfo').textContent = '支持 .xlsx 和 .xls 格式，请确保Excel文件包含以下列：\nA:id, B:path, C:category, D:article_title, E:short_content, F:cover_img, G:author_name, \nH:article_content, I:page_title, J:meta_keywords, K:meta_description, L:created_at, M:updated_at';
            
            document.getElementById('sheetSelector').classList.add('hidden');
            document.getElementById('previewSection').classList.add('hidden');
            document.getElementById('actionButtons').classList.add('hidden');
            document.getElementById('progressSection').style.display = 'none';
            document.getElementById('logSection').classList.add('hidden');
            document.getElementById('logContent').innerHTML = '';
            
            updateProgress(0, '准备中...');
        }
    </script>
</body>
</html>